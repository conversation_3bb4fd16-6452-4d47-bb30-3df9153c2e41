# расхождение сумм между нелоговой накладной и расходной
import asyncio
import os
import sys

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

VIEW_NAME_TAX_INVOICES_DIFF = "v_one_tax_return_and_sale_amount_different"
VIEW_NAME_TAX_MEDOC_DIFF = "v_one_tax_return_and_medoc_amount_different"

SQL_CREATE_VIEW_TAX_INVOICES_DIFF = f"""
    -- DROP VIEW IF EXISTS {VIEW_NAME_TAX_INVOICES_DIFF} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME_TAX_INVOICES_DIFF} AS
    SELECT DISTINCT 
        organization_type = COALESCE(sale.is_accounting,FALSE) AS БухУчет,
        client.customer Контрагент,
        tax_sale.doc_date::text РК_дата,
        tax_sale.doc_number РК_номер,
        tax_sale.document_amount РК_сумма,
        sale.document_amount ВН_сумма,
        abs(tax_sale.document_amount + sale.document_amount) AS РазницаСумм,
        sale.doc_date::text ВН_дата,
        sale.doc_number ВН_номер
    FROM
        (
        SELECT
            doc_date::date AS doc_date,
            doc_number,
            right(doc_number,10)::int::text doc_number_short,
            document_amount,
            CASE 
                WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'  
                    AND COALESCE(document_base_key, '********-0000-0000-0000-************') 
                        <> '********-0000-0000-0000-************' THEN 
                            document_base_key  
                WHEN deal_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                    AND COALESCE(deal_key, '********-0000-0000-0000-************') 
                        <> '********-0000-0000-0000-************' THEN 
                            deal_key
            END base_key,	
            CASE 
                WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя' 
                    AND COALESCE(document_base_key, '********-0000-0000-0000-************') 
                        <> '********-0000-0000-0000-************' THEN 
                            document_base_type  
                WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                    AND COALESCE(deal_key, '********-0000-0000-0000-************')
                        <> '********-0000-0000-0000-************' THEN 
                            deal_type
            END base_type,
            account_key,
            organization_key
        FROM t_one_doc_tax_appendix_2	
        WHERE doc_date >= '01.01.2022'::date
        ) AS tax_sale
        FULL JOIN 
        (
        SELECT
            doc_date::date AS doc_date,
            doc_number,
            right(doc_number,8)::int::text doc_number_short,
            document_amount::numeric(15,2) AS document_amount,
            ref_key,
            organization_key,
            is_accounting
        FROM t_one_doc_return_of_goods_from_customers
        ) AS sale
        ON tax_sale.base_key = sale.ref_key
        INNER JOIN v_one_manager_counterparty_contracts_segments AS client
            ON tax_sale.account_key = client.customer_key
        INNER JOIN v_one_organization_and_type AS org
            ON org.ref_key = tax_sale.organization_key
    WHERE abs(tax_sale.document_amount + sale.document_amount) > 0.1
    ORDER BY customer, tax_sale.doc_date::text DESC 
    ;
    
    COMMENT ON VIEW {VIEW_NAME_TAX_INVOICES_DIFF} 
        IS 'расхождение сумм между Приложение2КНалоговойНакладной и возвратной';
    GRANT SELECT ON TABLE  {VIEW_NAME_TAX_INVOICES_DIFF} TO user_prestige;

"""

SQL_CREATE_VIEW_TAX_MEDOC_DIFF = f"""
    -- DROP VIEW IF EXISTS {VIEW_NAME_TAX_MEDOC_DIFF} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME_TAX_MEDOC_DIFF} AS
    SELECT
        sell.is_accounting БухУчет,
        sell.edrpou ОКПО,
        sell.customer Контрагент,
        sell.tax_doc_date РК_дата,
        sell.tax_doc_number РК_Номер,
        sell.tax_amount РК_Сумма,
        medoc.docsum AS Medoc_Сумма,
        medoc.vatsum AS Medoc_СуммаНДС,
        sell.tax_amount - medoc.docsum РазницаСумм,
        sell.tax_amount_vat - medoc.vatsum РазницаСуммНДС,
        medoc.doc_date Medoc_Дата,
        medoc.docname Medoc_Наименование,
        medoc.sendsttname Medoc_Статус
    FROM 
        (
        SELECT *
        FROM (
            SELECT
                reestr.docsum::numeric(15,2) docsum,
                reestr.vatsum::numeric(15,2) vatsum,
                reestr.doc_date,
                reestr.doc_num,
                info.sendsttname,
                info.lastupdate = max(info.lastupdate) OVER (PARTITION BY doc_id) AS last_updated,
                info.docname,
                reestr.partner_edrpou
            FROM medoc_reestr AS reestr
                LEFT JOIN medoc_doc_info AS info
                    ON reestr.doc_id = info.docid
            ) AS t
        WHERE t.last_updated
        ) AS medoc
        INNER JOIN 
        (
        SELECT DISTINCT 
            organization_type = COALESCE(sale.is_accounting,FALSE) AS is_accounting,
            client.customer,
            tax_sale.doc_date tax_doc_date,
            tax_sale.doc_number tax_doc_number,
            tax_sale.doc_number_short tax_doc_number_short,
            tax_sale.document_amount tax_amount,
            sale.document_amount sale_amount,
            tax_sale.amount_vat_document tax_amount_vat,
            sale.doc_date sale_doc_date,
            sale.doc_number sale_number,
            sale.doc_number_short sale_doc_number_short,
            client.edrpou
        FROM
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,10)::int::text doc_number_short,
                CASE
                    WHEN posted THEN
                        document_amount
                    ELSE
                        0
                END document_amount,
                CASE
                    WHEN posted THEN
                        amount_vat_document
                    ELSE
                        0
                END amount_vat_document,                
                CASE 
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'  
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        document_base_key  
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        deal_key
                END base_key,	
                CASE 
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        document_base_type  
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        deal_type
                END base_type,
                account_key,
                organization_key
            FROM t_one_doc_tax_appendix_2	
            WHERE doc_date >= '01.01.2022'::date
            ) AS tax_sale
            LEFT JOIN 
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,8)::int::text doc_number_short,
                document_amount::numeric(15,2) AS document_amount,
                ref_key,
                organization_key,
                is_accounting
            FROM t_one_doc_return_of_goods_from_customers
            ) AS sale
            ON tax_sale.base_key = sale.ref_key
            INNER JOIN v_one_manager_counterparty_contracts_segments AS client
                ON tax_sale.account_key = client.customer_key
            INNER JOIN v_one_organization_and_type AS org
                ON org.ref_key = tax_sale.organization_key
        )
        AS sell
        ON 	medoc.doc_num = sell.tax_doc_number_short
            AND medoc.doc_date = sell.tax_doc_date
                AND medoc.partner_edrpou = sell.edrpou
    WHERE  abs(sell.tax_amount - medoc.docsum::numeric(15,2)) > 0.2
    ORDER BY sell.customer, medoc.doc_date DESC        
    ;

    COMMENT ON VIEW {VIEW_NAME_TAX_MEDOC_DIFF} IS 'расхождение сумм между нелоговой накладной и medoc';
    GRANT SELECT ON TABLE  {VIEW_NAME_TAX_MEDOC_DIFF} TO user_prestige;

"""


async def main_tax_sale_return_amount_different_async():
    result = await async_save_pg(SQL_CREATE_VIEW_TAX_INVOICES_DIFF)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_INVOICES_DIFF")

    result = await async_save_pg(SQL_CREATE_VIEW_TAX_MEDOC_DIFF)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_MEDOC_DIFF")


async def load_date():
    # await main_doc_tax_appendix2_async()
    # await main_doc_return_of_goods_from_customers_async()
    await main_tax_sale_return_amount_different_async()


if __name__ == '__main__':
    logger.info("START")
    asyncio.get_event_loop().run_until_complete(load_date())
    # asyncio.get_event_loop().run_until_complete(create_views())
    logger.info("FINISH")
