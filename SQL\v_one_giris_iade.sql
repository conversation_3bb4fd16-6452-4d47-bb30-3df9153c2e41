
    CREATE OR REPLACE VIEW v_one_giris_iade
    AS
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        ns.sell_by,
        nom.inbox,
        - goods.quantity as quantity,
        goods.coefficient,
        - (goods.quantity * goods.coefficient) AS ed,
        - ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        goods.amount,
        goods.amount_vat,
        serv.rate_settlement,
        serv.multiplicity_of_mutual_settlements,
        serv.amount_includes_vat,
        serv.is_accounting,
        serv.is_management,
        serv.posted,
        'поступление возврат'::text AS doc_type,
        org.description AS organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        organization_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        nom.supplier_key,
        serv.currency_key AS currency_key,
        goods.warehouse_key,
        serv.contract_key,
        goods.line_number
    FROM t_one_doc_return_of_goods_to_supplier serv
        JOIN t_one_doc_return_of_goods_to_supplier_details goods USING (ref_key)
        JOIN v_one_nomenclature_inbox_supplier nom 
            ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations org 
            ON org.ref_key::text = serv.organization_key::TEXT
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = goods.warehouse_key
        LEFT JOIN t_one_cat_nomenclature_series AS ns ON ns.ref_key = goods.nomenclature_series_key
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_giris_iade IS 'поступление';
    COMMENT ON COLUMN v_one_giris_iade.id IS '№';
    COMMENT ON COLUMN v_one_giris_iade.doc_date  IS 'дата';
    COMMENT ON COLUMN v_one_giris_iade.tarih  IS 'дата_время';
    COMMENT ON COLUMN v_one_giris_iade.ed IS 'ед по накл';
    COMMENT ON COLUMN v_one_giris_iade.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_giris_iade.sell_by IS 'годен до';
    COMMENT ON COLUMN v_one_giris_iade.tobox IS 'в перечт на крб';
    COMMENT ON COLUMN v_one_giris_iade.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_giris_iade.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_giris_iade.sku IS 'sku';
    COMMENT ON COLUMN v_one_giris_iade.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_giris_iade.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_giris_iade.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_giris_iade.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_giris_iade.supplier IS 'поставщик';

    GRANT SELECT ON TABLE v_one_giris_iade TO user_prestige;       

