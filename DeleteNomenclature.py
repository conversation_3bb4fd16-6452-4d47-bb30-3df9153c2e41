# Description: Модуль для удаления номенклатуры, созданной ботом.
# Помечает на удаление номенклатуру, единицы измерения и серии товаров.

import asyncio

from async_request import get_data_1c, patch_data_1c

URL_CONST = r"http://***************/utp_prestige/odata/standard.odata/"
wrong_urls = []


# получим список nomenclature_key, созданных ботом. В комментарии указано 'bot'
async def get_nomenclature_key_bot():
    url = (f"{URL_CONST}Catalog_Номенклатура?$format=json"
           "&$inlinecount=allpages"
           "&$filter=substringof('bot', Комментарий) "
           "and DeletionMark eq true"
           "&$select=Description,Ref_Key")
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')
    return []


# в Catalog_ЕдиницыИзмерения ищем все записи, где Owner = nomenclature_key
async def get_units_of_measurement(nomenclature_key):
    url = (f"{URL_CONST}Catalog_ЕдиницыИзмерения?$format=json"
           f"&$filter=Owner eq cast(guid'{nomenclature_key}', 'Catalog_Номенклатура')")
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')
    return []


# помечаем ед.измерения на удаление
async def units_mark_delete(nomenclature_key):
    global wrong_urls
    units_mark_del = await get_units_of_measurement(nomenclature_key)
    for unit in units_mark_del:
        url = f"{URL_CONST}Catalog_ЕдиницыИзмерения(guid'{unit.get('Ref_Key')}')?$format=json"
        data = {"DeletionMark": True}
        result = patch_data_1c(url, data)
        if result:
            print(f"Помечена на удаление единица измерения {unit.get('Description')}")
        else:
            wrong_urls.append(url)
            print(f"!!! Не Помечена на удаление единица измерения {unit.get('Description')}")
    return wrong_urls


# помечаем на удаление созданные ботом серии товаров.
async def mark_delete_series(nomenclature_key):
    global wrong_urls
    url = (f"{URL_CONST}Catalog_СерииНоменклатуры?$format=json"
           f"&$filter=Owner_Key eq guid'{nomenclature_key}'")
    result = get_data_1c(url)
    if result and result.get('value'):
        for item in result.get('value'):
            url = f"{URL_CONST}Catalog_СерииНоменклатуры(guid'{item.get('Ref_Key')}')?$format=json"
            data = {"DeletionMark": True}
            result = patch_data_1c(url, data)
            if result:
                print(f"Помечена на удаление серия {item.get('Description')}")
            else:
                wrong_urls.append(url)
                print(f"!!! Не Помечена на удаление серия {item.get('Description')}")
    return wrong_urls


# помечаем на удаление созданные ботом карточки товаров. в комментарии указано 'bot'
# перед удалением проверяем чтобы товар не был в продажах
async def delete_nomenclature():
    global wrong_urls
    result = await get_nomenclature_key_bot()
    for item in result:
        nomenclature_key = item.get('Ref_Key')
        sku = item.get('Description')
        print(f"Ищем номенклатуру в продаже {sku}, {nomenclature_key}")
        url_sale = (f"{URL_CONST}Document_РеализацияТоваровУслуг_Товары?$format=json"
                    f"&$filter=Номенклатура_Key eq guid'{nomenclature_key}'")
        result_sale = get_data_1c(url_sale)
        if result_sale and not result_sale.get('value'):
            url = f"{URL_CONST}Catalog_Номенклатура(guid'{nomenclature_key}')?$format=json"
            data = {"DeletionMark": True}
            result = patch_data_1c(url, data)
            if result:
                print(f"Помечена на удаление номенклатура: '{sku}', {nomenclature_key}")
                await units_mark_delete(nomenclature_key)
                await mark_delete_series(nomenclature_key)
            else:
                wrong_urls.append(url)
                print(f"!!! Не Помечена на удаление номенклатура {nomenclature_key}")

    return wrong_urls


if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    loop.run_until_complete(delete_nomenclature())
    print(wrong_urls)
    print('Done')
