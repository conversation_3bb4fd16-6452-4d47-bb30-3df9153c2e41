import os
import sys
import pandas as pd
import asyncio
if os.environ['COMPUTERNAME'] == 'PRESTIGEPRODUCT':
    CONFIG_PATH = r"d:\Prestige\Python\Config"
else:
    CONFIG_PATH = r"C:\Rasim\Python\Config"

sys.path.append(os.path.abspath(CONFIG_PATH))
from multiThread import get_json_from_url

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Catalog_Контрагенты"
SELECT_COLUMNS = "Ref_Key, Description"


async def get_response(ref_key: str):
    url = f"http://*************/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"/?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    filter_ref_key = f"&$filter=Ref_Key eq guid'{ref_key}'"
    url += filter_ref_key
    response = await get_json_from_url(url)
    return response


async def main_customers_async(ref_key):
    df =pd.DataFrame()
    json_data = await get_response(ref_key)
    if ('value' in json_data) and (json_data['value'] != []):
        df = pd.json_normalize(json_data['value'][0])
        df = df.rename({'Description': 'Контрагент'}, axis='columns')
        df = df.rename({'Ref_Key': 'customer_key'}, axis='columns')
    return df


if __name__ == "__main__":
    asyncio.run(main_customers_async("b27d0b0a-c4fb-11eb-810a-001dd8b72b55"))
