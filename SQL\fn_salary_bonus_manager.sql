-- Content: Функция для расчета премии менеджера по продажам
DROP FUNCTION IF EXISTS fn_salary_bonus_manager(date, date, text) CASCADE;

CREATE OR REPLACE FUNCTION fn_salary_bonus_manager(date_first date, date_last date, manager_key text)
    RETURNS TABLE(
        менеджер TEXT,
        клиент TEXT,
        тип_документа TEXT,
        датаДок date,
        номерДок TEXT,
        месяц TEXT,
        сумма NUMERIC)
    LANGUAGE plpgsql
    AS $function$
    DECLARE
        query text;
    BEGIN
        query := format(
        $$
            SELECT
                manager::TEXT as менеджер,
                customer::TEXT as клиент,
                recorder_type::TEXT as тип_документа,
                doc_date::date датаДок,
                doc_number::TEXT номерДок,
                TO_CHAR(doc_date, 'YYYY-MM')::TEXT AS месяц,
                -SUM(doc_sum)::numeric AS сумма
            FROM v_one_balance_details
            WHERE recorder_type IN
                (
                'ПлатежноеПоручениеВходящее',
                'ПлатежноеПоручениеИсходящее',
                'ПриходныйКассовыйОрдер',
                'РасходныйКассовыйОрдер',
                'КорректировкаДолга',
                'КорректировкаЗаписейРегистров'
                )
                AND contract_type = 'СПокупателем'
                AND doc_date >= %L
                AND doc_date <= %L
                AND manager_key = %L
                AND recorder NOT IN
                    (
                    SELECT recorder
                    FROM v_one_balance_details AS req
                    WHERE recorder_type = 'КорректировкаДолга'
                        AND contract_type = 'СПокупателем'
                        AND doc_date >= %L
                        AND doc_date <= %L
                        AND manager_key = %L
                    GROUP BY recorder
                    HAVING count(recorder) > 1 AND sum(doc_sum) = 0
                    )
            GROUP BY
                manager,
                customer,
                recorder_type,
                doc_date::date,
                doc_number,
                TO_CHAR(doc_date, 'YYYY-MM')
        $$, date_first, date_last, manager_key,
            date_first, date_last, manager_key
        );

    -- Выполнение динамического запроса и возврат результата
    RETURN QUERY EXECUTE query;
END;
$function$;

-- Вызов функции
--SELECT *
--FROM fn_salary_bonus_manager((current_date - INTERVAL '1month' * 2)::date,
--        current_date,
--        '5002b988-9831-11e6-80c4-c936aa9c817c');