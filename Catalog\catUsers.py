import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_users"
DOCUMENT = "Catalog_Пользователи"
SELECT_COLUMNS = '''Parent_Key,ФизЛицо_Key,Ref_Key,Code,DataVersion,DeletionMark,Description,IsFolder,Predefined'''

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        description varchar(50) NULL,  -- Description
        code varchar(50) NULL,  -- Code
        dataversion varchar(50) NULL,  -- DataVersion
        deletionmark bool NULL DEFAULT false,  -- DeletionMark
        isfolder bool NULL DEFAULT false,  -- IsFolder
        predefined bool NULL DEFAULT false,  -- Predefined
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        parent_key varchar(50) NULL,  -- Parent_Key
        individual_key varchar(50) NULL,  -- ФизЛицо_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (individual_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.individual_key IS 'ФизЛицо_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';

'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
            ref_key
            , parent_key
            , individual_key
            , code
            , dataversion
            , deletionmark
            , description
            , isfolder
            , predefined
        )
    VALUES {maket}
    ON CONFLICT (individual_key)
    DO UPDATE SET
        ref_key = EXCLUDED.ref_key,
        parent_key = EXCLUDED.parent_key,
        code = EXCLUDED.code,
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        description = EXCLUDED.description,
        isfolder = EXCLUDED.isfolder,
        predefined = EXCLUDED.predefined
    ;
    '''
    return sql.replace("'", "")


async def main_cat_users_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_cat_users_async())
