# взаимозачеты
import asyncio
import os
import sys

from async_Postgres import async_save_pg
from logger_prestige import get_logger
from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

VIEW_NAME = 'v_one_tax_sale_control_uktved'

SQL_CREATE_VIEW = f'''
    -- DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
    SELECT DISTINCT 
        client.customer контрагент,
        tax2.doc_date::date AS РК_дата,
        tax2.doc_number AS РК_номер,
        sale_tax.doc_date::date AS НН_дата,
        sale_tax.doc_number AS НН_номер,
        sku.description as sku,
        cat_uktved_tax2.code AS РК_код,
        cat_uktved_sale_tax.code AS НН_код
    FROM t_one_doc_tax_appendix_2 AS tax2
        LEFT JOIN t_one_doc_tax_appendix_2_details AS tax2_det
            ON tax2.ref_key =tax2_det.ref_key 
        LEFT JOIN t_one_doc_tax_sale_details AS sale_tax_det
            ON sale_tax_det.ref_key = tax2.tax_invoice_key
                AND sale_tax_det.code_uktvd_key <> tax2_det.code_uktved_key
                AND sale_tax_det.nomenclature_key = tax2_det.nomenclature_key
        LEFT JOIN t_one_doc_tax_sale AS sale_tax
            ON sale_tax.ref_key = sale_tax_det.ref_key
        LEFT JOIN t_one_cat_uktved AS cat_uktved_sale_tax
            ON cat_uktved_sale_tax.ref_key = sale_tax_det.code_uktvd_key
        LEFT JOIN t_one_cat_uktved AS cat_uktved_tax2
            ON cat_uktved_tax2.ref_key = tax2_det.code_uktved_key
        LEFT JOIN v_one_manager_counterparty_contracts_segments AS client
            ON client.customer_key = tax2.account_key
        LEFT JOIN t_one_cat_nomenclature AS sku
            on sku.ref_key = tax2_det.nomenclature_key 
    WHERE tax2.posted 
        AND cat_uktved_sale_tax.ref_key <> cat_uktved_tax2.ref_key
    ORDER BY РК_дата DESC;
    ;

    GRANT SELECT ON TABLE {VIEW_NAME} TO user_prestige;
'''


async def main_control_uktved_async():
    result = await async_save_pg(SQL_CREATE_VIEW)
    logger.info(f"{result}, SQL_CREATE_VIEW")

if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(main_control_uktved_async())
    logger.info(f"FINISH")
