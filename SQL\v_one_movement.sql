
    CREATE OR REPLACE VIEW v_one_movement AS
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        goods.quantity,
        goods.coefficient,
        (goods.quantity * goods.coefficient) AS ed,
        ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        tocw_out.description AS warehouse_out,
        tocw_in.description AS warehouse_in,
        'перемещение' as doc_type,
        nom.supplier,
        org.description AS organization,
        serv.ref_key AS document_key,
        nom.nomenclature_key,
        goods.nomenclature_series_key,
        nom.supplier_key,
        serv.organization_key,
        goods.unit_of_key,
        serv.shipping_warehouse_key AS warehouse_out_key,
        serv.warehouse_consignee_key AS warehouse_in_key,
        goods.line_number
    FROM t_one_doc_movement AS serv
        INNER JOIN t_one_doc_movement_details AS goods USING(ref_key)
        LEFT JOIN v_one_nomenclature_inbox_supplier AS nom
            ON nom.nomenclature_key = goods.nomenclature_key
        LEFT JOIN t_one_cat_warehouses tocw_out
            ON tocw_out.ref_key = serv.shipping_warehouse_key 
        LEFT JOIN t_one_cat_warehouses tocw_in 
            ON tocw_in.ref_key = serv.warehouse_consignee_key
        LEFT JOIN t_one_cat_organizations AS org
            ON org.ref_key = serv.organization_key 
    ;

    COMMENT ON COLUMN v_one_movement.doc_number IS 'номер док';
    COMMENT ON COLUMN v_one_movement.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_movement.inbox IS 'в коробке';
    COMMENT ON COLUMN v_one_movement.quantity IS 'количество';
    COMMENT ON COLUMN v_one_movement.coefficient IS 'коэффициент';
    COMMENT ON COLUMN v_one_movement.ed IS 'количество*коэффициент';
    COMMENT ON COLUMN v_one_movement.tobox IS 'количество*коэффициент/в коробке';
    COMMENT ON COLUMN v_one_movement.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_movement.warehouse_out IS 'склад отправитель';
    COMMENT ON COLUMN v_one_movement.warehouse_in IS 'склад получатель';
    COMMENT ON COLUMN v_one_movement.supplier IS 'поставщик товара';
    COMMENT ON COLUMN v_one_movement.organization IS 'организация';
    COMMENT ON VIEW v_one_movement IS 'перемещение';
