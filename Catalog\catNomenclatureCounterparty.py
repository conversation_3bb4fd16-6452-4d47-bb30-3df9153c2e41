# получение номенклатуры из 1С и добавление в базу pg {TABLE_NAME}
# 09.09.2022
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_nomenclature_counterparty"
DOCUMENT = "InformationRegister_НоменклатураКонтрагентов"

SELECT_COLUMNS = ("АртикулНоменклатурыКонтрагента,КодНоменклатурыКонтрагента,НаименованиеНоменклатурыКонтрагента,"
                  "ШтрихКодНоменклатурыКонтрагента,ЕдиницаНоменклатурыКонтрагента_Key,Контрагент_Key,"
                  "Номенклатура_Key,ХарактеристикаНоменклатуры_Key")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        sku_customer varchar(50) NOT NULL,  -- АртикулНоменклатурыКонтрагента
        code_customer varchar(50) NULL,  -- КодНоменклатурыКонтрагента
        name_customer varchar(300) NULL,  -- НаименованиеНоменклатурыКонтрагента
        barcode_customer varchar(50) NULL,  -- ШтрихКодНоменклатурыКонтрагента
        item_key varchar(50) NULL,  -- ЕдиницаНоменклатурыКонтрагента_Key
        counterparty_key varchar(50) NULL,  -- Контрагент_Key
        nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
        characteristics_of_item_key varchar(50) NULL,  -- ХарактеристикаНоменклатуры_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (counterparty_key,nomenclature_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.sku_customer IS 'АртикулНоменклатурыКонтрагента';
    COMMENT ON COLUMN {TABLE_NAME}.code_customer IS 'КодНоменклатурыКонтрагента';
    COMMENT ON COLUMN {TABLE_NAME}.name_customer IS 'НаименованиеНоменклатурыКонтрагента';
    COMMENT ON COLUMN {TABLE_NAME}.barcode_customer IS 'ШтрихКодНоменклатурыКонтрагента';
    COMMENT ON COLUMN {TABLE_NAME}.item_key IS 'ЕдиницаНоменклатурыКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.characteristics_of_item_key IS 'ХарактеристикаНоменклатуры_Key';
'''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}
        (
            sku_customer,
            code_customer,
            name_customer,
            barcode_customer,
            item_key,
            counterparty_key,
            nomenclature_key,
            characteristics_of_item_key
        )
        VALUES {maket}
        ON CONFLICT (counterparty_key, nomenclature_key)
        DO UPDATE SET
            sku_customer = EXCLUDED.sku_customer,
            code_customer = EXCLUDED.code_customer,
            name_customer = EXCLUDED.name_customer,
            barcode_customer = EXCLUDED.barcode_customer,
            item_key = EXCLUDED.item_key,
            characteristics_of_item_key = EXCLUDED.characteristics_of_item_key
    ;
    '''
    return sql.replace("'", "")


async def main_cat_nomenclature_counterparty_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(8)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_nomenclature_counterparty_async())
