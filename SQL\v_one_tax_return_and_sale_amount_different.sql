    CREATE OR REPLACE VIEW v_one_tax_return_and_sale_amount_different AS
    SELECT DISTINCT
        organization_type = COALESCE(sale.is_accounting,FALSE) AS БухУчет,
        client.customer Контрагент,
        tax_sale.doc_date::text РК_дата,
        tax_sale.doc_number РК_номер,
        tax_sale.document_amount РК_сумма,
        sale.document_amount ВН_сумма,
        abs(tax_sale.document_amount + sale.document_amount) AS РазницаСумм,
        sale.doc_date::text ВН_дата,
        sale.doc_number ВН_номер
    FROM
        (
        SELECT
            doc_date::date AS doc_date,
            doc_number,
            right(doc_number,10)::int::text doc_number_short,
            document_amount,
            CASE
                WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                    AND COALESCE(document_base_key, '********-0000-0000-0000-************')
                        <> '********-0000-0000-0000-************' THEN
                            document_base_key
                WHEN deal_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                    AND COALESCE(deal_key, '********-0000-0000-0000-************')
                        <> '********-0000-0000-0000-************' THEN
                            deal_key
            END base_key,
            CASE
                WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                    AND COALESCE(document_base_key, '********-0000-0000-0000-************')
                        <> '********-0000-0000-0000-************' THEN
                            document_base_type
                WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                    AND COALESCE(deal_key, '********-0000-0000-0000-************')
                        <> '********-0000-0000-0000-************' THEN
                            deal_type
            END base_type,
            account_key,
            organization_key
        FROM t_one_doc_tax_appendix_2
        WHERE doc_date >= '01.01.2022'::date
        ) AS tax_sale
        FULL JOIN
        (
        SELECT
            doc_date::date AS doc_date,
            doc_number,
            right(doc_number,8)::int::text doc_number_short,
            document_amount::numeric(15,2) AS document_amount,
            ref_key,
            organization_key,
            is_accounting
        FROM t_one_doc_return_of_goods_from_customers
        ) AS sale
        ON tax_sale.base_key = sale.ref_key
        INNER JOIN v_one_manager_counterparty_contracts_segments AS client
            ON tax_sale.account_key = client.customer_key
        INNER JOIN v_one_organization_and_type AS org
            ON org.ref_key = tax_sale.organization_key
    WHERE abs(tax_sale.document_amount + sale.document_amount) > 0.1
    ORDER BY customer, tax_sale.doc_date::text DESC
    ;

    COMMENT ON VIEW v_one_tax_return_and_sale_amount_different
        IS 'расхождение сумм между Приложение2КНалоговойНакладной и возвратной';

    GRANT SELECT ON TABLE  v_one_tax_return_and_sale_amount_different TO user_prestige;
