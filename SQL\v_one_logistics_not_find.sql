--
--    CREATE OR REPLACE VIEW v_one_logistics_not_find AS
--    SELECT  x.*
--    FROM t_one_sale_logistics x
--    WHERE ((doc_date::date < current_date - 2)
--        AND (doc_date::date >= '01.01.2023')
--        AND ((carrier IS NULL) OR carrier = ''))
--        AND date_send = '0001-01-01'
--    ORDER BY counterparty, doc_date
--    ;
--    GRANT SELECT ON TABLE v_one_logistics_not_find TO user_prestige;
