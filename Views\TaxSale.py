# расхождение сумм между нелоговой накладной и расходной
import asyncio
import os
import sys
from datetime import datetime

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))
from async_Postgres import async_save_pg

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

VIEW_TAX_INVOICES = "v_one_tax_and_sale_amount"
VIEW_TAX_INVOICES_DIFF = "v_one_tax_and_sale_amount_different"
VIEW_TAX_MEDOC_DIFF = "v_one_tax_and_medoc_amount_different"
VIEW_TAX_SALE = "v_one_tax_sale"

SQL_CREATE_VIEW_TAX_SALE = f"""
    -- DROP VIEW IF EXISTS {VIEW_TAX_SALE} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_TAX_SALE} AS
    SELECT
        doc_date::date AS doc_date,
        doc_number,
        right(doc_number,10)::int::text doc_number_short,
        document_amount,
        is_consolidated_invoice,        
        CASE 
            WHEN (document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг')  
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************') 
                        <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN 
                            document_base_key  
            WHEN (deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг')
                AND (COALESCE(deal_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************') AND (COALESCE(deal_key, '') <> '') THEN 
                        deal_key
            WHEN (document_base_type <> 'StandardODATA.Undefined')
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN 
                        document_base_key
            WHEN (deal_type <> 'StandardODATA.Undefined')
                AND (COALESCE(deal_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************') AND (COALESCE(deal_key, '') <> '') THEN 
                        deal_key
            ELSE Null
        END base_key,
        CASE 
            WHEN (document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг') 
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN 
                        document_base_type  
            WHEN (deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг') 
                AND (COALESCE(deal_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************') AND (COALESCE(deal_key, '') <> '') THEN 
                        deal_type
            WHEN (document_base_type <> 'StandardODATA.Undefined') 
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN 
                        document_base_type
            WHEN deal_type <> 'StandardODATA.Undefined' 
                AND COALESCE(deal_key, '********-0000-0000-0000-************') 
                    <> '********-0000-0000-0000-************' AND COALESCE(deal_key, '') <> '' THEN 
                        deal_type
            ELSE Null
        END base_type,
        ref_key,
        account_key,
        organization_key
    FROM t_one_doc_tax_sale	
    ORDER BY doc_date DESC
    ;
"""

SQL_CREATE_VIEW_TAX_INVOICES = f"""
    -- DROP VIEW IF EXISTS {VIEW_TAX_INVOICES} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_TAX_INVOICES} AS
    SELECT DISTINCT 
        orders.doc_date заказДатф,
        orders.doc_number заказаНомер,
        sale.sale_date РН_дата,
        sale.sale_number РН_номер,
        sale.sale_amount РН_сумма,
        sale_tax.tax_date НН_дата,
        sale_tax.tax_number НН_номер,
        sale_tax.tax_amount НН_Сумма,
        CASE
            WHEN (abs(COALESCE(sale.sale_amount,0) - COALESCE(sale_tax.tax_amount,0)) > 0.5 
                AND COALESCE(sale_tax.tax_number,'') <> '') THEN 'Нет'
            ELSE ''
        END "РН=НН",
        sale_return.return_date ВН_дата,
        sale_return.return_number ВН_номер,
        sale_return.return_amount ВН_сумма,
        return_tax.tax_return_date РК_дата,    
        return_tax.tax_return_number РК_номер,
        return_tax.tax_return_amount РК_сумма,         
        CASE
            WHEN (abs(COALESCE(sale_return.return_amount,0) - COALESCE(return_tax.tax_return_amount,0)) > 0.5 
                OR COALESCE(return_tax.tax_return_number,'') <> '') THEN 'Нет'
            ELSE ''
        END "ВН=РК"
    FROM t_one_doc_buyer_order orders
        FULL JOIN 
            (
                SELECT
                    doc_date sale_date,
                    doc_number sale_number,
                    CASE
                        WHEN posted THEN document_amount
                        ELSE 0
                    END sale_amount,
                    deal base_key,
                    ref_key
                FROM t_one_doc_sale_of_goods_services 
                WHERE COALESCE(deal,'********-0000-0000-0000-************') <> '********-0000-0000-0000-************'
            ) AS sale
            ON orders.ref_key = sale.base_key 
        FULL JOIN 
            (
            SELECT
                CASE
                    WHEN posted THEN - document_amount
                    ELSE 0
                END  return_amount,
                doc_date return_date,
                doc_number return_number,
                deal_key AS base_key,
                ref_key
            FROM t_one_doc_return_of_goods_from_customers 
            WHERE COALESCE(deal_key,'********-0000-0000-0000-************') <> '********-0000-0000-0000-************'
            ) AS sale_return
            ON orders.ref_key = sale_return.base_key 
        FULL JOIN 
            (
            SELECT
                doc_date tax_date,
                doc_number tax_number,
                CASE
                    WHEN posted THEN document_amount
                    ELSE 0
                END tax_amount,
                CASE 
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'  
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                            document_base_key  
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                            deal_key
                END sale_key
            FROM t_one_doc_tax_sale 
            WHERE NOT is_consolidated_invoice
            )  AS sale_tax
            ON sale.ref_key = sale_tax.sale_key
        FULL JOIN 
            (
            SELECT            
                doc_date tax_return_date,
                doc_number tax_return_number,
                CASE
                    WHEN posted THEN document_amount
                    ELSE 0
                END tax_return_amount,
                CASE 
                    WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'  
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                            document_base_key  
                    WHEN deal_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                            deal_key
                END return_key
            FROM t_one_doc_tax_appendix_2 
            )  AS return_tax
            ON sale_return.ref_key = return_tax.return_key
    WHERE orders.isposted  
        AND orders.doc_date >= '01.01.2022'::date
        AND (
                (
                    abs(COALESCE(sale.sale_amount,0) - COALESCE(sale_tax.tax_amount,0)) > 0.5 
                    AND COALESCE(sale_tax.tax_number,'') <> ''
                )
            OR (
                    abs(COALESCE(sale_return.return_amount,0) - COALESCE(return_tax.tax_return_amount,0)) > 0.5 
                    AND COALESCE(return_tax.tax_return_number,'') <> ''
                )
            )
    ORDER BY orders.doc_date DESC 
    ;
    
    COMMENT ON VIEW {VIEW_TAX_INVOICES} IS 'связанные с заказом документы';
    GRANT SELECT ON TABLE  {VIEW_TAX_INVOICES} TO user_prestige;
    
"""

SQL_CREATE_VIEW_TAX_INVOICES_DIFF = f"""
    -- DROP VIEW IF EXISTS {VIEW_TAX_INVOICES_DIFF} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_TAX_INVOICES_DIFF} AS
    SELECT DISTINCT 
        organization_type = COALESCE(sale.is_accounting,FALSE) AS БухУчет,
        organization организация,
        client.customer Контрагент,
        tax_sale.doc_date::text НН_дата,
        tax_sale.doc_number НН_номер,
        tax_sale.document_amount НН_сумма,
        sale.document_amount РН_сумма,
        tax_sale.document_amount - sale.document_amount AS РазницаСумм,
        sale.doc_date::text РН_дата,
        sale.doc_number РН_номер
    FROM
        (
        SELECT
            doc_date::date AS doc_date,
            doc_number,
            right(doc_number,10)::int::text doc_number_short,
            document_amount,
            CASE 
                WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'  
                    AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                    document_base_key  
                WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                    AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                    deal_key
            END base_key,	
            CASE 
                WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                    AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                    document_base_type  
                WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                    AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                    deal_type
            END base_type,
            account_key,
            organization_key,
            is_consolidated_invoice
        FROM t_one_doc_tax_sale	
        WHERE posted AND doc_date >= '01.01.2022'::date
        ) AS tax_sale
        FULL JOIN 
        (
        SELECT
            doc_date::date AS doc_date,
            doc_number,
            right(doc_number,8)::int::text doc_number_short,
            document_amount::numeric(15,2) AS document_amount,
            ref_key,
            organization_key,
            is_accounting
        FROM t_one_doc_sale_of_goods_services
        WHERE posted
        ) AS sale
            ON tax_sale.base_key = sale.ref_key
                AND tax_sale.organization_key = sale.organization_key
        LEFT JOIN v_one_manager_counterparty_contracts_segments AS client
            ON tax_sale.account_key = client.customer_key
        LEFT JOIN v_one_organization_and_type AS org
            ON org.ref_key = tax_sale.organization_key
    WHERE COALESCE(tax_sale.document_amount,0) <> COALESCE(sale.document_amount,0)
        AND NOT tax_sale.is_consolidated_invoice
--        AND org.organization_type AND length(COALESCE(client.edrpou::text,'')) = 8
    ;
    
    COMMENT ON VIEW {VIEW_TAX_INVOICES_DIFF} IS 'расхождение сумм между налоговой накладной и расходной';
    GRANT SELECT ON TABLE  {VIEW_TAX_INVOICES_DIFF} TO user_prestige;
    
"""

SQL_CREATE_VIEW_TAX_MEDOC_DIFF = f"""
    -- DROP VIEW IF EXISTS {VIEW_TAX_MEDOC_DIFF} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_TAX_MEDOC_DIFF} AS
    SELECT
        sell.is_accounting БухУчет,
        sell.edrpou ОКПО,
        sell.customer as Контрагент,
        sell.tax_doc_date НН_дата,
        sell.tax_doc_number НН_Номер,
        sell.tax_amount НН_Сумма,
        medoc.docsum::numeric(15,2) AS Medoc_Сумма,
        sell.tax_amount-medoc.docsum::numeric(15,2) РазницаСумм,
        medoc.doc_date Medoc_Дата,
        medoc.docname Medoc_Наименование,
        medoc.sendsttname Medoc_Статус
    FROM 
        (
        SELECT *
        FROM (
            SELECT
                reestr.docsum::numeric(15,2) docsum,
                reestr.doc_date,
                reestr.doc_num,
                info.sendsttname,
                info.lastupdate = max(info.lastupdate) OVER (PARTITION BY doc_id) AS last_updated,
                info.docname,
                reestr.partner_edrpou
            FROM medoc_reestr AS reestr
                LEFT JOIN medoc_doc_info AS info
                    ON reestr.doc_id = info.docid 
            ) AS t
        WHERE t.last_updated
        ) AS medoc
        INNER JOIN 
        (
        SELECT DISTINCT 
            organization_type = COALESCE(sale.is_accounting,FALSE) AS is_accounting,
            client.customer,
            tax_sale.doc_date tax_doc_date,
            tax_sale.doc_number tax_doc_number,
            tax_sale.doc_number_short tax_doc_number_short,
            tax_sale.document_amount tax_amount,
            sale.document_amount sale_amount,
            sale.doc_date sale_doc_date,
            sale.doc_number sale_number,
            sale.doc_number_short sale_doc_number_short,
            tax_sale.is_consolidated_invoice,
            client.edrpou
        FROM
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,10)::int::text doc_number_short,
                document_amount,
                CASE 
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'  
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        document_base_key  
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        deal_key
                END base_key,	
                CASE 
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        document_base_type  
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг' 
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN 
                        deal_type
                END base_type,
                account_key,
                organization_key,
                is_consolidated_invoice
            FROM t_one_doc_tax_sale	
            WHERE doc_date >= '01.01.2022'::date
            ) AS tax_sale
            LEFT JOIN 
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,8)::int::text doc_number_short,
                document_amount::numeric(15,2) AS document_amount,
                ref_key,
                organization_key,
                is_accounting
            FROM t_one_doc_sale_of_goods_services
            ) AS sale
            ON tax_sale.base_key = sale.ref_key
            LEFT JOIN v_one_manager_counterparty_contracts_segments AS client
                ON tax_sale.account_key = client.customer_key
            LEFT JOIN v_one_organization_and_type AS org
                ON org.ref_key = tax_sale.organization_key
        )
        AS sell
        ON 	medoc.doc_num = sell.tax_doc_number_short
            AND medoc.doc_date= sell.tax_doc_date
                AND sell.edrpou = medoc.partner_edrpou
    WHERE  abs(sell.tax_amount-medoc.docsum::numeric(15,2)) > 0.2
        AND NOT sell.is_consolidated_invoice
    ORDER BY sell.customer, medoc.doc_date DESC        
    ;
    
    COMMENT ON VIEW {VIEW_TAX_MEDOC_DIFF} IS 'расхождение сумм между налоговой накладной и medoc';
    GRANT SELECT ON TABLE  {VIEW_TAX_MEDOC_DIFF} TO user_prestige;

"""


async def main_tax_amount_different_async():
    result = await async_save_pg(SQL_CREATE_VIEW_TAX_SALE)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_SALE")

    result = await async_save_pg(SQL_CREATE_VIEW_TAX_INVOICES)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_INVOICES")

    result = await async_save_pg(SQL_CREATE_VIEW_TAX_INVOICES_DIFF)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_INVOICES_DIFF")

    result = await async_save_pg(SQL_CREATE_VIEW_TAX_MEDOC_DIFF)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_MEDOC_DIFF")


if __name__ == '__main__':
    logger.info("START")

    # asyncio.run(main_doc_tax_sale_async())
    # asyncio.run(main_doc_sale_of_goods_services_async())
    asyncio.run(main_tax_amount_different_async())
    # asyncio.run(create_views())

    finish = datetime.now()
    logger.info("FINISH")
