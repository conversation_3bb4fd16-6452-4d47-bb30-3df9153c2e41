import asyncio
import os
import sys
from pathlib import Path

cur_dir = Path(__file__).resolve().parent
sys.path.append(str(cur_dir))
sys.path.append(str(cur_dir.parent))
from Kasa.cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_cash_bank"
DOCUMENT = "банк access"
SQL_CREATE_TABLE = f"""
    -- НЕ УДАЛЯТЬ!!!
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        doc_date timestamp(0) NOT NULL,  -- ДатаДок
        doc_number varchar(20) NOT NULL,  -- Номер
        amount numeric(15, 2) NOT NULL,  -- сумма
        amount_total numeric(15, 2) DEFAULT 0 NOT NULL,  -- сумма с накоплением
        payment_purpose_id uuid NULL,  -- назначение платежа
        item_id uuid NULL,  -- статья
        organization_key varchar(36) NOT NULL,  -- организация
        currency_key varchar(36) NOT NULL,  -- валюта
        customer_key varchar(36) NULL,  -- клиент
        employee_key uuid NULL,  -- сотрудник
        description varchar NULL,  -- примечание
        document_type varchar(25) NULL,  -- тип документа
        create_user varchar(30) DEFAULT CURRENT_USER NOT NULL,
        update_user varchar(30) NULL,
        recorder_type numeric(3) DEFAULT '-1'::integer NULL,  -- 1- приход; '-1' - расход
        createdat timestamp(0) DEFAULT now() NOT NULL,
        item_period date NULL,  -- период
        CONSTRAINT {TABLE_NAME}_amount_check CHECK ((amount <> (0)::numeric)),
        CONSTRAINT {TABLE_NAME}_check CHECK ((recorder_type = ANY (ARRAY[('-1'::integer)::numeric, (1)::numeric]))),
        CONSTRAINT {TABLE_NAME}_number_unique UNIQUE (doc_number),
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_fk_currency FOREIGN KEY (currency_key) REFERENCES t_one_cat_currencies(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_customer FOREIGN KEY (customer_key) REFERENCES t_one_cat_counterparties(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_employee FOREIGN KEY (employee_key) REFERENCES t_one_cat_employees(ref_key) ON DELETE RESTRICT,  -- don't working with uuid
        CONSTRAINT {TABLE_NAME}_fk_item FOREIGN KEY (item_id) REFERENCES t_cash_item(id) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_organizations FOREIGN KEY (organization_key) REFERENCES t_one_cat_organizations(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_payment_purpose_id FOREIGN KEY (payment_purpose_id) REFERENCES t_cash_payment_purpose(id) ON DELETE RESTRICT
    );
    
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'ДатаДок';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_total IS 'суммаИтоговая';
    COMMENT ON COLUMN {TABLE_NAME}.payment_purpose_id IS 'назначение платежа';
    COMMENT ON COLUMN {TABLE_NAME}.item_id IS 'статья';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'организация';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'валюта';
    COMMENT ON COLUMN {TABLE_NAME}.customer_key IS 'клиент';
    COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'сотрудник';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'примечание';
    COMMENT ON COLUMN {TABLE_NAME}.document_type IS 'тип документа';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS '1- приход; -1 - расход';
    COMMENT ON COLUMN {TABLE_NAME}.item_period IS 'период';

    ALTER TABLE {TABLE_NAME} OWNER TO postgres;
    GRANT ALL ON TABLE {TABLE_NAME} TO postgres;
    GRANT ALL ON TABLE {TABLE_NAME} TO {COMPANY_CASHIER};
"""

SQL_CREATE_TRG_AFTER = f"""
    CREATE TRIGGER trg_{TABLE_NAME}_after AFTER
    INSERT
        OR
    DELETE
        OR
    UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_log_changes()
        ;    
"""

SQL_CREATE_FN_AFTER = f"""
    CREATE OR REPLACE FUNCTION fn_log_changes()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
        BEGIN
            IF TG_OP = 'INSERT' THEN
                INSERT INTO t_change_log (table_name, operation, new_data)
                VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW)::jsonb);
            ELSIF TG_OP = 'UPDATE' AND (row_to_json(OLD)::jsonb <> row_to_json(NEW)::jsonb) THEN
                INSERT INTO t_change_log (table_name, operation, old_data, new_data)
                VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD)::jsonb, row_to_json(NEW)::jsonb);
            ELSIF TG_OP = 'DELETE' THEN
                INSERT INTO t_change_log (table_name, operation, old_data)
                VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD)::jsonb);
            END IF;
            RETURN NULL;
        END;
    $function$
    ;
"""


# ******************************  восстановление из лога  **********************************
# 1. Создать временную таблицу temp_cash_bank
SQL_CREATE_TEMP_TABLE = f"""
    DROP TABLE IF EXISTS temp_cash_bank;
    CREATE TEMP TABLE IF NOT EXISTS temp_cash_bank AS
    WITH extract_data_from_log AS (
        SELECT 
            changed_at = max(changed_at) OVER (PARTITION BY doc_number) is_max,
            *
        FROM (
            SELECT DISTINCT 
                changed_at,
                new_data->>'id' AS id,
                new_data->>'doc_date' AS doc_date,
                new_data->>'doc_number' AS doc_number,
                new_data->>'amount' AS amount,
                new_data->>'amount_total' AS amount_total,
                new_data->>'payment_purpose_id' AS payment_purpose_id,
                new_data->>'item_id' AS item_id,
                new_data->>'description' AS description,
                new_data->>'document_type' AS document_type,
                new_data->>'create_user' AS create_user,
                new_data->>'update_user' AS update_user,
                new_data->>'recorder_type' AS recorder_type,
                new_data->>'createdat' AS createdat,
                new_data->>'item_period' AS item_period,
                new_data->>'organization_key' AS organization_key,
                new_data->>'currency_key' AS currency_key,
                new_data->>'customer_key' AS customer_key,
                new_data->>'employee_key' AS employee_key
            FROM 
                t_change_log
            WHERE table_name = '{TABLE_NAME}'
                AND operation <> 'DELETE'
        ) AS t
    ORDER BY 
        id,
        changed_at
    )
    SELECT DISTINCT 
        *
    FROM extract_data_from_log
    WHERE is_max;
"""


# 2. Вставить данные из временной таблицы в t_cash_bank
SQL_INSERT_FROM_TEMP_TABLE = f"""
    INSERT INTO {TABLE_NAME} (
        id,
        doc_date,
        doc_number,
        amount,
        amount_total,
        payment_purpose_id,
        item_id,
        description,
        document_type,
        create_user,
        update_user,
        recorder_type,
        createdat,
        item_period,
        organization_key,
        currency_key,
        customer_key,
        employee_key
    )
    SELECT 
        id::uuid,
        doc_date::timestamp(0),
        doc_number::varchar(20),
        amount::numeric(15, 2),
        amount_total::numeric(15, 2),
        payment_purpose_id::uuid,
        item_id::uuid,
        description::varchar,
        document_type::varchar(25),
        create_user::varchar(30),
        update_user::varchar(30),
        recorder_type::numeric(3),
        createdat::timestamp(0),
        item_period::date,
        organization_key::varchar(36),
        currency_key::varchar(36),
        customer_key::varchar(36),
        employee_key::uuid
    FROM 
        temp_cash_bank;
"""


async def main_t_bank_async():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_FN_AFTER)  # создавать функцию до создания таблицы
    logger.info(f"{result}, SQL_CREATE_FN_AFTER")
    result = await async_save_pg(SQL_CREATE_TABLE)  # создавать таблицу до триггера
    logger.info(f"{result}, SQL_CREATE_TABLE")
    # result = await async_save_pg(SQL_CREATE_TRG_AFTER)
    # logger.info(f"{result}, SQL_CREATE_TRG_AFTER")
    
    # ******************************  восстановление из лога  **********************************
    # если нужно восстановить данные из лога
    # result = await async_save_pg(SQL_CREATE_TEMP_TABLE)
    # logger.info(f"{result}, SQL_CREATE_TEMP_TABLE")
    # result = await async_save_pg(SQL_INSERT_FROM_TEMP_TABLE)
    # logger.info(f"{result}, SQL_INSERT_FROM_TEMP_TABLE")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_t_bank_async())
