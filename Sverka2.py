import pandas as pd
import re
import os
from datetime import datetime
from openpyxl import load_workbook
import xlrd


def open_excel_file(file_path, data_only=True):
    """
    Универсальная функция для открытия Excel-файлов форматов .xls и .xlsx

    Args:
        file_path: путь к файлу Excel
        data_only: для .xlsx файлов - загружать только значения, а не формулы

    Returns:
        Объект книги Excel и активный лист
    """
    file_ext = os.path.splitext(file_path)[1].lower()

    if file_ext == '.xlsx':
        # Для .xlsx используем openpyxl
        wb = load_workbook(filename=file_path, data_only=data_only)
        ws = wb.active
        return wb, ws
    elif file_ext == '.xls':
        # Для .xls используем xlrd
        wb = xlrd.open_workbook(file_path)
        ws = wb.sheet_by_index(0)  # Берем первый лист
        return wb, ws
    else:
        raise ValueError(f"Неподдерживаемый формат файла: {file_ext}. Поддерживаются только .xls и .xlsx")


def parse_excel(file_path):
    # Чтение файла
    wb, ws = open_excel_file(file_path)
    file_ext = os.path.splitext(file_path)[1].lower()
    is_xlsx = file_ext == '.xlsx'

    # Извлечение наименования клиента
    client_pattern = re.compile(r'між\s+(.*?)\s+і\s+(.*?)[\s"]')
    client_name = None

    # Функция для получения значения ячейки в зависимости от типа файла
    def get_cell_value(row_idx, col_idx):
        if is_xlsx:
            return ws.cell(row=row_idx+1, column=col_idx+1).value
        else:
            if row_idx < ws.nrows and col_idx < ws.ncols:
                cell_value = ws.cell_value(row_idx, col_idx)
                cell_type = ws.cell_type(row_idx, col_idx)
                # Обработка даты в xlrd
                if cell_type == xlrd.XL_CELL_DATE:
                    date_tuple = xlrd.xldate_as_tuple(cell_value, wb.datemode)
                    return datetime(*date_tuple)
                return cell_value
            return None

    # Функция для получения строки в зависимости от типа файла
    def get_row_values(row_idx):
        if is_xlsx:
            return [cell.value for cell in ws[row_idx+1]]
        else:
            if row_idx < ws.nrows:
                return [get_cell_value(row_idx, col_idx) for col_idx in range(ws.ncols)]
            return [None] * 10  # Достаточно большой массив с None

    # Получение количества строк
    row_count = ws.max_row if is_xlsx else ws.nrows

    # Поиск наименования клиента
    for row_idx in range(row_count):
        row = get_row_values(row_idx)
        if row[0] and isinstance(row[0], str) and 'між' in row[0] and 'і' in row[0]:
            match = client_pattern.search(row[0])
            if match:
                client_name = match.group(2).strip().replace('ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ ', '')
            break

    data = []
    current_contract = None
    headers = ['Дата', 'Документ', 'Дебет', 'Кредит']

    # Парсинг таблиц
    for row_idx in range(row_count):
        row = get_row_values(row_idx)

        # Определение договора
        if row[0] and isinstance(row[0], str) and 'Договір про надання послуг' in row[0]:
            current_contract = row[0].split('від')[0].strip()

        # Начало таблицы
        if row[0] == headers[0] and row[1] == headers[1]:
            # Пропуск заголовков
            continue

        # Проверка на строку оборотов
        if row[0] and isinstance(row[0], str) and 'Обороти по договору:' in row[0]:
            # Валидация сумм
            total_debt = float(row[2]) if row[2] else 0.0
            total_credit = float(row[4]) if row[4] else 0.0
            if total_debt != total_credit:
                print(f"Внимание: Суммы не совпадают для договора {current_contract}")
            continue

        # Сбор данных
        if current_contract and row[0] and isinstance(row[0], datetime):
            doc_num_match = re.search(r'\((\d+)\s+від', str(row[1]))
            doc_num = doc_num_match.group(1) if doc_num_match else None

            data.append({
                'filename': os.path.basename(file_path),
                'customer': client_name,
                'contract': current_contract,
                'doc_date': row[0].date() if hasattr(row[0], 'date') else row[0],
                'document': row[1],
                'debt': float(row[2]) if row[2] else 0.0,
                'credit': float(row[4]) if row[4] else 0.0,
                'doc_num': doc_num
            })

    df = pd.DataFrame(data)
    return df


# Пример использования
df = parse_excel(r'C:\Users\<USER>\Desktop\Сверка\Акт звірки.xls')
print(df.head())