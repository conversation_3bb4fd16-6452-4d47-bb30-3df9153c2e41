# считает количество созданных(!) возвратных накладных. Для подсчета количества возвр накладных за период
import asyncio
import os
import sqlite3
import sys

import pandas as pd
import requests

if os.environ['COMPUTERNAME'] == 'PRESTIGEPRODUCT':
    authorize_path = r"d:\Prestige\Python\Prestige"
    CONFIG_PATH = r"D:\Prestige\Python\Config"
else:
    authorize_path = r"C:\Rasim\Python"
    CONFIG_PATH = "C:\Rasim\Python\Config"
sys.path.append(os.path.abspath(CONFIG_PATH))
sys.path.append(os.path.abspath(authorize_path))
from configPrestige import DATA_AUTH

DB_PATH = r'C:\Program Files\1cv8\srvinfo\reg_1541\4774b779-e5c2-41b2-87b7-13ded5da4245\1Cv8Log\1Cv8.lgd'
URL_RETURN_INVOICE = ("http://192.168.1.254/utp_prestige/odata/standard.odata/Document_ВозвратТоваровОтПокупателя?"
                      "$format=json&$select=Date,Number,Ref_Key"
                      "&$filter=Posted eq true and Автор_Key eq guid'091d06f1-e92d-11eb-810e-001dd8b72b55'")


async def get_sql(period_year: int, period_month: int):
    return f'''
        SELECT *,
            CAST(strftime('%Y', formatted_date) AS INTEGER) AS extracted_year,
            CAST(strftime('%m', formatted_date) AS INTEGER) AS extracted_month
        FROM (
            SELECT 
            datetime(date/10000-62135578800,'unixepoch','-3 hours')  AS formatted_date, 
            *
            FROM EventLog
            WHERE userCode = 14 -- Света Лужанская
                AND metadataCodes = 35 -- Документ.ВозвратТоваровОтПокупателя
                AND eventCode = 13	-- "_$Data$_.New"
            ) AS t
        WHERE extracted_year = {period_year} AND extracted_month = {period_month}
        ORDER BY formatted_date DESC 
        ;
    '''


async def get_invoice_head_async(period_year: int, period_month: int):
    df = pd.DataFrame()
    url = URL_RETURN_INVOICE + f" and year(ДатаСоздания) eq {period_year} and month(ДатаСоздания) eq {period_month}"
    response = requests.get(url, auth=DATA_AUTH)
    if response.status_code == 200:
        df = pd.DataFrame(response.json()['value'])
        df['Date'] = pd.to_datetime(df['Date'])
    print(df)
    return df


# extract with column dataPresentation date and number and add new columns
async def add_date_and_number_to_df(df: pd.DataFrame):
    df['Number'] = df['dataPresentation'].str.extract(r'''\b([А-Яа-яЁёЇїІіЄєҐґ']+\d+)\b''')
    df['Date'] = pd.to_datetime(df['dataPresentation'].
                                str.extract(r'от (\d{2}.\d{2}.\d{4} \d{2,4}:\d{2}:\d{2})')[0],
                                format='%d.%m.%Y %H:%M:%S')

    return df
    # return df[['Date','Number']]


async def main_quantity_return_invoice(period_year: int, period_month: int):
    df_invoice_head = await get_invoice_head_async(period_year, period_month)
    print(df_invoice_head)
    # Создание соединения только для чтения
    conn = sqlite3.connect(DB_PATH, check_same_thread=False, isolation_level=None)
    sql = await get_sql(period_year, period_month)
    df_log = pd.read_sql_query(sql, conn)
    df_log = await add_date_and_number_to_df(df_log)

    # Закрытие соединения
    conn.close()
    merged_df = pd.merge(df_log, df_invoice_head, left_on=['Date', 'Number'], right_on=['Date', 'Number'], how='inner')
    print(merged_df)

    excel_file_path = r'D:\Prestige\Python\Prestige\output_file.xlsx'

    # Create an Excel writer object
    with pd.ExcelWriter(excel_file_path, engine='xlsxwriter') as writer:
        # Write each DataFrame to a different sheet
        df_log.to_excel(writer, sheet_name='df_log', index=False)
        df_invoice_head.to_excel(writer, sheet_name='df_invoice_head', index=False)
        merged_df.to_excel(writer, sheet_name='merged_df', index=False)


if __name__ == '__main__':
    asyncio.run(main_quantity_return_invoice(2024, 1))
