# reading data from AccountingRegister_Хозрасчетный_RecordType and add to pg in table {TABLE_NAME}
# *********** импортируем данные для подключения к базам
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_self_financing_details"
DOCUMENT = "AccountingRegister_Хозрасчетный_RecordType"
SELECT_COLUMNS = (
    "Period,Recorder,Recorder_Type,LineNumber,Active,Сумма,ВалютнаяСуммаDr,ВалютнаяСуммаCr,КоличествоDr,"
    "КоличествоCr,Содержание,AccountDr_Key,AccountCr_Key,Организация_Key,НалоговоеНазначениеDr_Key,"
    "НалоговоеНазначениеCr_Key,СчетДополнительный_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        dtperiod timestamp NOT NULL,  -- Period
        recorder varchar(50) NULL,  -- Recorder
        line_number numeric(7) NOT NULL DEFAULT 0,  -- LineNumber
        active boolean NOT NULL DEFAULT FALSE,  -- Active
        total numeric(15,3) NOT NULL DEFAULT 0,  -- Сумма
        currency_amount_dr numeric(10,3) NULL,  -- ВалютнаяСуммаDr
        currency_amount_cr numeric(10,3) NULL,  -- ВалютнаяСуммаCr
        quantity_dr numeric(10) NULL,  -- КоличествоDr
        amount_cr numeric(10,3) NULL,  -- КоличествоCr
        content varchar(50) NULL,  -- Содержание
        account_dr_key varchar(50) NULL,  -- AccountDr_Key
        account_cr_key varchar(50) NULL,  -- AccountCr_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        tax_purpose_dr_key varchar(50) NULL,  -- НалоговоеНазначениеDr_Key
        tax_purpose_cr_key varchar(50) NULL,  -- НалоговоеНазначениеCr_Key
        account_additional_key varchar(50) NULL,  -- СчетДополнительный_Key
        recorder_type varchar(250) NULL,  -- Recorder_Type
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dtperiod IS ' Period';
    COMMENT ON COLUMN {TABLE_NAME}.recorder IS ' Recorder';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS ' LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.active IS ' Active';
    COMMENT ON COLUMN {TABLE_NAME}.total IS ' Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.currency_amount_dr IS ' ВалютнаяСуммаDr';
    COMMENT ON COLUMN {TABLE_NAME}.currency_amount_cr IS ' ВалютнаяСуммаCr';
    COMMENT ON COLUMN {TABLE_NAME}.quantity_dr IS ' КоличествоDr';
    COMMENT ON COLUMN {TABLE_NAME}.amount_cr IS ' КоличествоCr';
    COMMENT ON COLUMN {TABLE_NAME}.content IS ' Содержание';
    COMMENT ON COLUMN {TABLE_NAME}.account_dr_key IS ' AccountDr_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_cr_key IS ' AccountCr_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS ' Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.tax_purpose_dr_key IS ' НалоговоеНазначениеDr_Key';
    COMMENT ON COLUMN {TABLE_NAME}.tax_purpose_cr_key IS ' НалоговоеНазначениеCr_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_additional_key IS ' СчетДополнительный_Key';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS ' Recorder_Type';

    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;

"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        dtperiod,
        recorder,
        recorder_type,
        line_number,
        active,
        total,
        currency_amount_dr,
        currency_amount_cr,
        quantity_dr,
        amount_cr,
        content,
        account_dr_key,
        account_cr_key,
        organization_key,
        tax_purpose_dr_key,
        tax_purpose_cr_key,
        account_additional_key
    )
    VALUES {maket}
    ;
    """
    return sql.replace("'", "")


async def main_doc_self_financing_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(17)
    sql = await sql_insert(maket)
    sql = sql.replace("$1,", "to_timestamp($1, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_self_financing_details_async())
