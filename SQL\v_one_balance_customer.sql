-- Формирует отчет по дебиторской задолженности по клиентам
-- DROP FUNCTION fn_debt(date, date, varchar);


CREATE OR REPLACE FUNCTION fn_debt(date_first date, date_last date, manager_key character varying)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    sql_query TEXT;
    date_temp date;
BEGIN

    IF date_last::date > current_date THEN
        date_last = current_date;
        RAISE NOTICE 'Конечная дата (%) позже текущей. Меняем на текущую', date_last::date;
    END IF;

    IF date_last::date < date_first::date THEN
        date_temp = date_first::date;
        date_last = date_first::date;
        date_first = date_temp;
        RAISE NOTICE 'Дата конечная (%) раньше начальной (%). Меняем местами', date_last::date, date_first::date;
    END IF;

    EXECUTE 'DROP VIEW IF EXISTS v_one_balance_customer CASCADE;';

    sql_query = format('CREATE OR REPLACE VIEW v_one_balance_customer AS
        WITH 
            debt_first AS (
                SELECT 
                    manager,
                    segment,
                    customer,
                    currency_name,
                    contract_days,
                    is_accounting,
                    organization,
                    '''' as recordtype,
                    MAX(doc_date::date) AS doc_date,
                    MAX(last_date::date) AS last_date,
                    sum(doc_sum) AS sum_day,
                    customer_key,
                    currency_key,
                    manager_key
                FROM v_one_balance_details
                WHERE 
                    doc_date::date < %L::date
                    AND manager_key = %L
                GROUP BY 
                    manager,
                    segment,
                    customer,
                    currency_name,
                    contract_days,
                    is_accounting,
                    organization,
                    customer_key,
                    currency_key,
                    manager_key
                HAVING abs(sum(doc_sum)) > 10
                ),
            debt_period AS (
                SELECT 
                    manager,
                    segment,
                    customer,
                    currency_name,
                    contract_days,
                    is_accounting,
                    organization,
                    recordtype,
                    doc_date,
                    last_date,
                    sum(doc_sum) AS sum_day,
                    customer_key,
                    currency_key,
                    manager_key
                FROM v_one_balance_details
                WHERE 
                    doc_date::date >= %L::date
                    AND doc_date::date <= %L::date
                    AND manager_key = %L
                GROUP BY 
                    manager,
                    customer,
                    segment,
                    contract_days,
                    currency_name,
                    is_accounting,
                    organization,
                    recordtype,
                    doc_date,
                    last_date,
                    customer_key,
                    currency_key,
                    manager_key
                ),
            debt_last AS (
                SELECT 
                    manager,
                    segment,
                    customer,
                    currency_name,
                    contract_days,
                    is_accounting,
                    organization,
                    '''' as recordtype,
                    CASE
                        WHEN MAX(doc_date::date) < %L::date THEN %L::date
                        ELSE MAX(doc_date::date)
                    END AS doc_date,
                    CASE
                        WHEN MAX(last_date::date) < %L::date THEN %L::date
                        ELSE MAX(last_date::date)
                    END AS last_date,
                    sum(doc_sum) AS last_debt,
                    customer_key,
                    currency_key,
                    manager_key
                FROM v_one_balance_details
                WHERE 
                    doc_date::date <= %L::date
                    AND manager_key = %L
                GROUP BY 
                    manager,
                    customer,
                    segment,
                    currency_name,
                    contract_days,
                    is_accounting,
                    organization,
                    customer_key,
                    currency_key,
                    manager_key
                )
        -- объединяем данные: остаток на начало периода, обороты за период
        -- через UNION добавим остаток на конец периода
        SELECT
            det.manager AS "менеджер",
            det.segment AS "сегмент",
            det.customer AS "контрагент",
            det.organization AS "организация",
            det.recordtype AS "типДокумента",
            det.doc_date::date AS "датаДок",
            det.last_date::date AS "датаСрока",
            det.sum_day AS "сумма",  -- "ПДЗнаДату",
            sum(det.sum_day) OVER (PARTITION BY det.customer_key, det.is_accounting, det.currency_key, det.organization ORDER BY det.last_date) AS "остаток",
            CASE
                WHEN abs(sum(det.sum_day) OVER (
                    PARTITION BY 
                        det.customer_key, 
                        det.is_accounting, 
                        det.currency_key, 
                        det.organization 
                    ORDER BY det.last_date)) > 10 THEN 
                        (det.last_date::date - %L::date)::int
                ELSE 0
            END AS "прошлоДней",            
            det.currency_name AS "валюта",
            det.is_accounting AS "форма",
            det.contract_days AS "отсрочкаДней"
        FROM (
                SELECT *
                FROM debt_first
                UNION ALL
                SELECT *
                FROM debt_period
            ) AS det
        UNION ALL
        -- добавляем состояние на последнюю дату
        SELECT
            dl.manager AS "менеджер",
            dl.segment AS "сегмент",
            dl.customer AS "контрагент",
            dl.organization AS "организация",
            dl.recordtype AS "типДокумента",
            %L::date AS "датаДок",  -- именно last_date, так как это последняя дата
            %L::date AS "датаСрока",
            0 AS "сумма",  -- "ПДЗнаДату",
            dl.last_debt AS "остаток",
            (dl.last_date::date - %L::date)::int AS "прошлоДней",
            dl.currency_name AS "валюта",
            dl.is_accounting AS "форма",
            dl.contract_days AS "отсрочкаДней"
        FROM debt_last as dl
            INNER JOIN
                ( SELECT * 
                FROM debt_last
                WHERE abs(debt_last.last_debt) > 10
                ) as filt
                ON dl.customer_key = filt.customer_key
                    and dl.currency_key = filt.currency_key
                    and dl.is_accounting = filt.is_accounting
                    and dl.organization = filt.organization
        ORDER BY 
            1 NULLS LAST,
            2 NULLS LAST,
            3 NULLS LAST,
            "валюта",
            "форма",
            "датаСрока"
        ;',  --date_first::date, date_first::date,
            date_first::date, manager_key,  -- debt_first
            date_first::date, date_last::date, manager_key,  -- debt_period
            date_last::date, date_last::date, date_last::date, date_last::date, date_last::date, manager_key,  -- debt_last
            date_last::date,  -- debt_first + debt_period = det
            date_last::date, date_last::date, date_last::date); -- debt_last = dl

    RAISE NOTICE '%', sql_query;
    EXECUTE sql_query;
    
    RETURN;

END;
$function$
;
