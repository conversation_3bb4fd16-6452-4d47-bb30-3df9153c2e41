DROP VIEW IF EXISTS v_one_debt_receipt_receipt_return CASCADE;

CREATE OR REPLACE VIEW v_one_debt_receipt_receipt_return AS
    SELECT
        segment.manager,
    	segment.segment_folder,
        segment.segment,
        segment.customer,
        main.doc_date,
        main.doc_number,
        main.document_amount debt,
        0 credit,
        cur.description currency,
        'поступление возврат' operation_type,
        main.ref_key
    FROM t_one_doc_return_of_goods_to_supplier AS main
        LEFT JOIN
        (
            SELECT DISTINCT
                manager,
    	        segment_folder,
                segment,
                customer,
                customer_key
            FROM v_one_manager_counterparty_contracts_segments
        ) AS segment
            ON segment.customer_key = main.account_key
        LEFT JOIN t_one_cat_currencies AS cur
            ON cur.ref_key = main.currency_key
    WHERE main.posted
--        AND main.doc_date::date >= '01.01.2024'::date
--    	AND main.doc_date::date <= current_date
        AND main.organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
        AND main.account_key <> 'f51a0e88-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
--        AND warehouse_key NOT IN (
--            -- товар, который продавался/возвращался для переоценки не учитываем
--              '21f1d3fd-97d9-11ed-8160-001dd8b740bc'  -- склад ПЕРЕОЦІНКА; вирт перемещения товара для переоценки
--            , 'bc5d980a-5fc2-11ef-81bb-001dd8b72b55' -- ПЕРЕОЦІНКА 2; перемещение товара с юр.лица на юр.лицо
----          , 'cc242dd1-099e-11ec-8117-001dd8b72b55' -- Склад возврата, списания, утилизации по Бух Учету; виртуальный склад для списания товара
--        )
    UNION ALL
    SELECT
        segment.manager,
    	segment.segment_folder,
        segment.segment,
        segment.customer,
        main.doc_date,
        main.doc_number,
        0 debt,
        main.document_amount credit,
        cur.description currency,
        'поступление' operation_type,
        main.ref_key
    FROM t_one_doc_receipt_of_goods_services AS main
        LEFT JOIN
        (
            SELECT DISTINCT
                manager,
    	        segment_folder,
                segment,
                customer,
                customer_key
            FROM v_one_manager_counterparty_contracts_segments
        ) AS segment
            ON segment.customer_key = main.account_key
        LEFT JOIN t_one_cat_currencies AS cur
            ON cur.ref_key = main.currency_key
    WHERE main.posted
--        AND main.doc_date::date >= '01.01.2024'::date
--    	AND main.doc_date::date <= current_date
        AND main.organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
        AND main.account_key <> 'f51a0e88-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
--        AND warehouse_key NOT IN (
--            -- товар, который продавался/возвращался для переоценки не учитываем
--              '21f1d3fd-97d9-11ed-8160-001dd8b740bc'  -- склад ПЕРЕОЦІНКА; вирт перемещения товара для переоценки
--            , 'bc5d980a-5fc2-11ef-81bb-001dd8b72b55' -- ПЕРЕОЦІНКА 2; перемещение товара с юр.лица на юр.лицо
----          , 'cc242dd1-099e-11ec-8117-001dd8b72b55' -- Склад возврата, списания, утилизации по Бух Учету; виртуальный склад для списания товара
--        )
    ORDER BY
        manager,
        segment_folder,
        segment,
        customer,
        doc_date,
        doc_number,
        currency
    ;

COMMENT ON VIEW v_one_debt_receipt_receipt_return IS 'поступления и возвраты поставщикам';