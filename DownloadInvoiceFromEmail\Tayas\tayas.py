# https://github.com/ikvk/imap_tools#search-criteria
# pip install pyexcel pyexcel-xls pyexcel-xlsx
import datetime
import sys
import os
import re
import time
import traceback
import pandas as pd  # pip install pandas --upgrade
import xlrd
from pathlib import Path
from prestige_authorize import CONFIG_PATH
sys.path.append(os.path.abspath(CONFIG_PATH))
from datetime import datetime, timedelta, date
from imap_tools import MailBox, A  # pip install imap-tools
from configPrestige import PRESTIGE_EMAIL, GMAIL_PSW_TAS
from error_log import add_to_log

pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 20)
pd.set_option('display.width', 1000)

with MailBox('imap.gmail.com').login(PRESTIGE_EMAIL, GMAIL_PSW_TAS, 'INBOX') as mailbox:  # default INBOX
    # mailbox.folder.set('INBOX')
    # for msg in mailbox.fetch(A(seen=False)):
    # for i in mailbox.fetch(A(uid='3753')):
    #     print('\n\n####', i.date_str, i.subject, i.attachments[0].filename)
    list_mail = []
    for msg in mailbox.fetch():
        for idx, attachment in enumerate(msg.attachments):
            df = pd.DataFrame()
            try:
                att_fn = attachment.filename
                extended = Path(attachment.filename).suffix
                if extended not in ['.xls', '.xlsx']: continue
                if extended == '.xlsx':
                    df = pd.read_excel(attachment.payload, sheet_name=0, engine='openpyxl')  # xlsx
                else:
                    df = pd.read_excel(attachment.payload, sheet_name=0, engine='xlrd')  # xls
                # print(df)

                # xlsBook = xlrd.open_workbook(file_contents=attachment.payload)
                # sheet_count = len(xlsBook.sheets())
                # sht_names = xlsBook.sheet_names()
                # df.columns = ['item_no', 'description_of_goods', 'gram', 'pc', 'box', 'quantity_cartons ',
                #               'price_cartons', 'total', 'none1', 'none2']
                # df = df.drop(columns=['none1', 'none2'], axis=1)
                # first_row = df.query("item_no=='Item No'")
                # first_row_number = first_row.index.values.item() + 2
                # second_row = df.iloc[first_row_number + 1]
                # last_row_number = df[28:].first_valid_index() + 1
                # # print('\n\n#', idx + 1, msg.date_str, msg.subject, att_fn)
                mail_date = datetime.strftime(msg.date, "%d.%m.%Y %H:%M:%S")
                list_mail.append("Дата: {}; Тема: {}".format(mail_date, msg.subject))
                # # print(df[first_row_number:last_row_number])

            except:
                print(traceback.print_exc())  #

    list_mail = [*set(list_mail)]
    print(list_mail)
    # return list_mail
