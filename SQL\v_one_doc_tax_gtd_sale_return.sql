
CREATE OR REPLACE VIEW v_one_doc_tax_gtd_sale_return
AS SELECT stock.supplier,
    stock.organization,
    stock.customer,
    stock.customer_edrpou,
    stock.sku,
    stock.warehouse,
    tax_main.doc_date AS tax_date,
    tax_main.doc_number AS tax_num,
    "right"(tax_main.doc_number::text, 8)::numeric AS tax_num_short,
    stock.doc_date AS selling_date,
    stock.doc_number AS selling_num,
    "right"(stock.doc_number::text, 5)::numeric AS selling_num_short,
    stock.quantity,
    stock.coefficient,
    stock.quantity * stock.coefficient AS ed,
    sum(stock.quantity * stock.coefficient) OVER (PARTITION BY tax_main.organization_key, tax_det.code_uktved_key 
        ORDER BY tax_main.doc_date, tax_det.line_number) AS stock_ed,
    sum(stock.quantity * stock.coefficient) OVER (PARTITION BY tax_main.organization_key, 
        ("left"(tax_det.code_uktved_key::text, 4)) ORDER BY tax_main.doc_date, tax_det.line_number) AS stock_ed_short,
    stock.tobox,
    stock.doc_type,
    "left"(gtd.code::text, 4)::numeric AS short_uktzt,
    gtd.code AS tax_code,
    tax_det.nomenclature_key,
    tax_det.code_uktved_key,
    tax_main.tax_invoice_key
   FROM t_one_doc_tax_appendix_2  tax_main
     JOIN t_one_doc_tax_appendix_2_details tax_det ON tax_main.ref_key::text = tax_det.ref_key::text
     JOIN t_one_stock stock ON tax_main.document_base_key::text = stock.document_key::text 
        AND tax_det.nomenclature_key::text = stock.nomenclature_key::text
     LEFT JOIN t_one_cat_uktved gtd ON gtd.ref_key::text = tax_det.code_uktved_key::text
   WHERE tax_main.posted = true AND tax_main.doc_date >= '2022-09-01 00:00:00'::timestamp without time zone
   ORDER BY tax_main.doc_date DESC, tax_main.doc_number, tax_det.line_number;

