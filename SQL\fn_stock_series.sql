CREATE OR REPLACE FUNCTION fn_stock_series(date_first character, date_last character)
    RETURNS void
    LANGUAGE plpgsql
AS $function$
        DECLARE 
            sql_query TEXT;
        BEGIN

            IF date_last::date > current_date::date THEN 
                date_last = current_date::date::text;
            END IF;

            RAISE NOTICE '%', (date_last::date > current_date);

            EXECUTE '-- DROP VIEW IF EXISTS v_one_stock_series_first CASCADE';        
            EXECUTE '-- DROP VIEW IF EXISTS v_one_stock_series_last CASCADE';
            EXECUTE '-- DROP VIEW IF EXISTS v_one_stock_series_sale CASCADE';
            EXECUTE '-- DROP VIEW IF EXISTS v_one_stock_series_receipt CASCADE';
            EXECUTE '-- DROP VIEW IF EXISTS v_one_stock_series CASCADE';


            sql_query = '
                CREATE OR REPLACE VIEW v_one_stock_series_first AS
                SELECT DISTINCT supplier, sku, sell_by, (entry_price * quantity) entry_amount, 
                    (quantity * maliyet) amount_maliyet, quantity, doctype
                FROM (
                    SELECT supplier, sku, sell_by, entry_price, maliyet,        
                        sum(tobox) OVER (PARTITION BY sku, sell_by) AS quantity,                            
                        ''начОст'' AS doctype, doc_date,
                        doc_date = max(doc_date) OVER (PARTITION BY sku, sell_by) as is_max_date
                    FROM t_one_stock
                    WHERE warehouse_key IN (''381c5d92-4acb-11ed-8148-001dd8b72b55'',
                            ''4b40b865-6d2f-11ec-8125-001dd8b72b55'')
                        AND doc_date::date < ''' || date_first || '''::date
                    ) as t
                WHERE is_max_date
                ORDER BY supplier, sku, sell_by
                ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = '
                CREATE OR REPLACE VIEW v_one_stock_series_last AS
                SELECT DISTINCT supplier, sku, sell_by, (entry_price * quantity) entry_amount, 
                    (quantity * maliyet) amount_maliyet, quantity, doctype
                FROM (
                    SELECT supplier, sku, sell_by, entry_price, maliyet,        
                        sum(tobox) OVER (PARTITION BY sku, sell_by) AS quantity,                            
                        ''конОст'' AS doctype, doc_date,
                        doc_date = max(doc_date) OVER (PARTITION BY sku, sell_by) as is_max_date
                    FROM t_one_stock
                    WHERE warehouse_key IN (''381c5d92-4acb-11ed-8148-001dd8b72b55'',
                            ''4b40b865-6d2f-11ec-8125-001dd8b72b55'')
                        AND doc_date::date <= ''' || date_last || '''::date
                    ) as t
                WHERE is_max_date
                ORDER BY supplier, sku, sell_by
                ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = '
                CREATE OR REPLACE VIEW v_one_stock_series_sale AS
                SELECT supplier, sku, sell_by,        
                    -sum(entry_amount) AS entry_amount,
                    -sum(amount_maliyet) AS amount_maliyet,        
                    -sum(tobox) AS quantity,
                    ''продано'' AS doctype
                FROM t_one_stock
                WHERE organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55'' -- Альфа-Бест
                    AND doc_date::date >= ''' || date_first || '''::date
                    AND doc_date::date <= ''' || date_last || '''::date
                    AND doc_type IN (''продажа'', ''продажа возврат'')
                GROUP BY supplier, sku, sell_by
                ORDER BY supplier, sku, sell_by
                ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = '
                CREATE OR REPLACE VIEW v_one_stock_series_receipt AS
                SELECT supplier, sku, sell_by,        
                    sum(entry_amount) AS entry_amount,
                    sum(amount_maliyet) AS amount_maliyet,        
                    sum(tobox) AS quantity,
                    ''поступило'' AS doctype
                FROM t_one_stock
                WHERE organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55'' -- Альфа-Бест
                    AND doc_date::date >= ''' || date_first || '''::date
                    AND doc_date::date <= ''' || date_last || '''::date
                    AND doc_type IN (''поступление'', ''поступление возврат'')
                GROUP BY supplier, sku, sell_by
                ORDER BY supplier, sku, sell_by
            ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_first TO user_prestige;';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_last TO user_prestige;';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_sale TO user_prestige;';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_receipt TO user_prestige;';

            sql_query = '
                CREATE OR REPLACE VIEW v_one_stock_series AS
                SELECT supplier поставщик, sku номенклатура, sell_by годенДо, quantity количество,
                    entry_amount сумВх, amount_maliyet сумСсть, doctype тип
                FROM (
                    SELECT * FROM v_one_stock_series_first
                    UNION ALL    
                    SELECT * FROM v_one_stock_series_last
                    UNION ALL    
                    SELECT * FROM v_one_stock_series_sale
                    UNION ALL    
                    SELECT * FROM v_one_stock_series_receipt
                    ) as t
                WHERE abs(quantity) >= 0.0005
            ';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;        

            EXECUTE 'COMMENT ON VIEW v_one_stock_series IS ''остатки по сериям (срокам годности)'';';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series TO user_prestige;';

            RETURN;

        END;
        $function$
;
