CREATE OR REPLACE FUNCTION public.fn_stock_series(date_first date, date_last date)
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER  -- Функция выполняется с правами владельца
AS $function$
        DECLARE
            sql_query TEXT;
        BEGIN
            -- Если конечная дата больше текущей, устанавливаем ее равной текущей
            IF date_last > current_date THEN
                date_last := current_date;
            END IF;
            RAISE NOTICE 'Check date_last > current_date: %', (date_last > current_date);

            -- Удаление существующих представлений с обработкой ошибок
            BEGIN
                EXECUTE 'DROP VIEW IF EXISTS v_one_stock_series_first CASCADE';
            EXCEPTION WHEN insufficient_privilege THEN
                RAISE NOTICE 'Нет прав для удаления v_one_stock_series_first';
            END;

            BEGIN
                EXECUTE 'DROP VIEW IF EXISTS v_one_stock_series_last CASCADE';
            EXCEPTION WHEN insufficient_privilege THEN
                RAISE NOTICE 'Нет прав для удаления v_one_stock_series_last';
            END;

            BEGIN
                EXECUTE 'DROP VIEW IF EXISTS v_one_stock_series_sale CASCADE';
            EXCEPTION WHEN insufficient_privilege THEN
                RAISE NOTICE 'Нет прав для удаления v_one_stock_series_sale';
            END;

            BEGIN
                EXECUTE 'DROP VIEW IF EXISTS v_one_stock_series_receipt CASCADE';
            EXCEPTION WHEN insufficient_privilege THEN
                RAISE NOTICE 'Нет прав для удаления v_one_stock_series_receipt';
            END;

            BEGIN
                EXECUTE 'DROP VIEW IF EXISTS v_one_stock_series CASCADE';
            EXCEPTION WHEN insufficient_privilege THEN
                RAISE NOTICE 'Нет прав для удаления v_one_stock_series';
            END;

            -- Остальной код функции остается без изменений...

            -- Представление для начальных остатков по сериям
            sql_query := format('
                CREATE OR REPLACE VIEW v_one_stock_series_first AS
                SELECT DISTINCT supplier, sku, sell_by, (entry_price * quantity) entry_amount,
                    (quantity * maliyet) amount_maliyet, quantity, doctype
                FROM (
                    SELECT supplier, sku, sell_by, entry_price, maliyet,
                        sum(tobox) OVER (PARTITION BY sku, sell_by) AS quantity,
                        ''начОст'' AS doctype, doc_date,
                        doc_date = max(doc_date) OVER (PARTITION BY sku, sell_by) as is_max_date
                    FROM t_one_stock_new
                    WHERE warehouse_key IN (
                            ''381c5d92-4acb-11ed-8148-001dd8b72b55'',  -- МЕРЕЖІ
                            ''4b40b865-6d2f-11ec-8125-001dd8b72b55''   -- Основной склад товаров 2022
                            )
                        AND doc_date::date < %L::date
                    ) as t
                WHERE is_max_date
                ORDER BY supplier, sku, sell_by;', date_first);
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            -- Представление для конечных остатков по сериям
            sql_query := format('
                CREATE OR REPLACE VIEW v_one_stock_series_last AS
                SELECT DISTINCT supplier, sku, sell_by, (entry_price * quantity) entry_amount,
                    (quantity * maliyet) amount_maliyet, quantity, doctype
                FROM (
                    SELECT supplier, sku, sell_by, entry_price, maliyet,
                        sum(tobox) OVER (PARTITION BY sku, sell_by) AS quantity,
                        ''конОст'' AS doctype, doc_date,
                        doc_date = max(doc_date) OVER (PARTITION BY sku, sell_by) as is_max_date
                    FROM t_one_stock_new
                    WHERE warehouse_key IN (
                            ''381c5d92-4acb-11ed-8148-001dd8b72b55''   -- МЕРЕЖІ
--                          ''4b40b865-6d2f-11ec-8125-001dd8b72b55''  -- Основной склад товаров 2022
                            )
                        AND doc_date::date <= %L::date
                    ) as t
                WHERE is_max_date
                ORDER BY supplier, sku, sell_by;', date_last);
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            -- Представление для продаж по сериям
            sql_query := format('
                CREATE OR REPLACE VIEW v_one_stock_series_sale AS
                SELECT supplier, sku, sell_by,
                    -sum(entry_amount) AS entry_amount,
                    -sum(amount_maliyet) AS amount_maliyet,
                    -sum(tobox) AS quantity,
                    ''продано'' AS doctype
                FROM t_one_stock_new
                WHERE doc_date::date >= %L::date
                    AND doc_date::date <= %L::date
                    AND doc_type IN (''продажа'', ''продажа возврат'',
                        ''РеализацияТоваровУслуг'',''ВозвратТоваровОтПокупателя'')
                    AND NOT (customer = %L
                        AND doc_type IN (''поступление'', ''поступление возврат'',
                        ''ПоступлениеТоваровУслуг'',''ВозвратТоваровПоставщику'')
                        AND (organization = %L))
                    AND NOT (customer = %L
                        AND doc_type IN (''продажа'', ''продажа возврат'',
                        ''РеализацияТоваровУслуг'',''ВозвратТоваровОтПокупателя'')
                        AND (organization IN (%L)))
                GROUP BY supplier, sku, sell_by
                ORDER BY supplier, sku, sell_by;',
                date_first, date_last, 'ТОВ "АЛЬФА БЕСТ"', 'ТОВ "ПРЕСТИЖ ПРОДУКТ.К"', 'ТОВ "ПРЕСТИЖ ПРОДУКТ.К"', 'ТОВ "АЛЬФА БЕСТ"');
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            -- Представление для поступлений по сериям
            sql_query := format('
                CREATE OR REPLACE VIEW v_one_stock_series_receipt AS
                SELECT supplier, sku, sell_by,
                    sum(entry_amount) AS entry_amount,
                    sum(amount_maliyet) AS amount_maliyet,
                    sum(tobox) AS quantity,
                    ''поступило'' AS doctype
                FROM t_one_stock_new
                WHERE doc_date::date >= %L::date
                    AND doc_date::date <= %L::date
                    AND doc_type IN (''поступление'', ''поступление возврат'',
                        ''ПоступлениеТоваровУслуг'',''ВозвратТоваровПоставщику'')
                    AND NOT (customer = %L
                        AND doc_type IN (''поступление'', ''поступление возврат'',
                        ''ПоступлениеТоваровУслуг'',''ВозвратТоваровПоставщику'')
                        AND organization = %L)
                    AND NOT (customer = %L
                        AND doc_type IN (''продажа'', ''продажа возврат'',
                        ''РеализацияТоваровУслуг'',''ВозвратТоваровОтПокупателя'')
                        AND organization IN (%L))
                GROUP BY supplier, sku, sell_by
                ORDER BY supplier, sku, sell_by;',
                date_first, date_last, 'ТОВ "АЛЬФА БЕСТ"', 'ТОВ "ПРЕСТИЖ ПРОДУКТ.К"', 'ТОВ "ПРЕСТИЖ ПРОДУКТ.К"', 'ТОВ "АЛЬФА БЕСТ"');
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            -- Объединяющее итоговое представление
            sql_query := '
                CREATE OR REPLACE VIEW v_one_stock_series AS
                SELECT supplier AS поставщик, sku AS номенклатура, sell_by AS годенДо, quantity AS количество,
                    entry_amount AS сумВх, amount_maliyet AS сумСсть, doctype AS тип
                FROM (
                    SELECT * FROM v_one_stock_series_first
                    UNION ALL
                    SELECT * FROM v_one_stock_series_last
                    UNION ALL
                    SELECT * FROM v_one_stock_series_sale
                    UNION ALL
                    SELECT * FROM v_one_stock_series_receipt
                    ) as t
                WHERE abs(quantity) >= 0.0005';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            -- Назначение прав и комментариев
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_first TO user_prestige;';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_last TO user_prestige;';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_sale TO user_prestige;';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series_receipt TO user_prestige;';
            EXECUTE 'COMMENT ON VIEW v_one_stock_series IS ''остатки по сериям (срокам годности)'';';
            EXECUTE 'GRANT SELECT ON TABLE v_one_stock_series TO user_prestige;';



            RETURN;
        END;
$function$;

-- Назначаем владельца функции (выполнить от имени суперпользователя)
ALTER FUNCTION public.fn_stock_series(date, date) OWNER TO postgres;

-- Даем права на выполнение функции
GRANT EXECUTE ON FUNCTION public.fn_stock_series(date, date) TO user_prestige;