# получение номенклатуры из 1С и добавление в базу pg {TABLE_NAME}}
# 09.09.2022
import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_nomenclature_series"
DOCUMENT = "Catalog_СерииНоменклатуры"

SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Predefined,Owner_Key,Code,Description,СерийныйНомер," \
                 "СрокГодности,Комментарий,КоличествоМесяцевХранения"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15) NOT NULL DEFAULT 0,  -- Code
        dataversion varchar(50) NULL,  -- DataVersion
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        description varchar(350) NOT NULL,  -- Description
        serial_doc_number varchar(50) NULL,  -- СерийныйНомер
        sell_by date NOT NULL,  -- ГоденДо
        a_comment varchar(350) NULL,  -- Комментарий
        count_of_months_of_storage numeric(10) NOT NULL DEFAULT 0,  -- КоличествоМесяцевХранения
        ref_key varchar(50) NULL,  -- Ref_Key
        nomenclature_key varchar(50) NULL,  -- Owner_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Owner_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.serial_doc_number IS 'СерийныйНомер';
    COMMENT ON COLUMN {TABLE_NAME}.sell_by IS 'СрокГодности';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.count_of_months_of_storage IS 'КоличествоМесяцевХранения';
    '''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}
        (
            ref_key,
            dataversion,
            deletion_mark,
            predefined,
            nomenclature_key,
            code,
            description,
            serial_doc_number,
            sell_by,
            a_comment,
            count_of_months_of_storage
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            dataversion = EXCLUDED.dataversion,
            deletion_mark = EXCLUDED.deletion_mark,
            predefined = EXCLUDED.predefined,
            nomenclature_key = EXCLUDED.nomenclature_key,
            code = EXCLUDED.code,
            description = EXCLUDED.description,
            serial_doc_number = EXCLUDED.serial_doc_number,
            sell_by = EXCLUDED.sell_by,
            a_comment = EXCLUDED.a_comment,
            count_of_months_of_storage = EXCLUDED.count_of_months_of_storage
    ;
    '''
    return sql.replace("'", "")


async def main_cat_nomenclature_series_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(11)
    sql = await sql_insert(maket)
    sql = sql.replace("$9", "to_timestamp($9, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "description")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_nomenclature_series_async())
