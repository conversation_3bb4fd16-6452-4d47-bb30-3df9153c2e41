
    CREATE OR REPLACE VIEW v_one_managers_and_counterparts AS 
    SELECT DISTINCT 
        managers.direction,
        client_folder.description AS folder_name,
        managers.manager,
        clients_sub.description AS client,
        clients_sub.ref_key AS contract_key,
        cm.manager_key
    FROM t_one_cat_counterparties AS client_folder
        RIGHT JOIN  t_one_cat_counterparties AS clients_sub
            ON client_folder.ref_key = clients_sub.parent_key 
        LEFT JOIN t_one_cat_counterparties_managers AS cm
            ON cm.ref_key = clients_sub.ref_key
        LEFT JOIN 
            (
            SELECT 
                sales_area.description AS direction,
                mngrs.description AS manager,
                mngrs.individual_key 
            FROM t_one_cat_users AS sales_area
                INNER JOIN t_one_cat_users AS mngrs
                    ON mngrs.ref_key = sales_area.individual_key
            WHERE sales_area.isfolder AND sales_area.individual_key <> '2b34889f-e345-11ec-8134-001dd8b740bc' -- Уволенные
                AND NOT mngrs.isfolder 
            ) AS managers
            ON managers.individual_key = cm.manager_key
        INNER JOIN t_one_doc_sale_of_goods_services AS sales
            ON sales.account_key = clients_sub.ref_key 
    WHERE client_folder.isfolder 
        AND sales.is_management AND sales.posted
        AND NOT clients_sub.isfolder
        AND client_folder.parent_key = 'ad421841-905f-11e6-80c4-c936aa9c817c' -- 010 Покупатели
        AND clients_sub.parent_key NOT IN ('c6a0df91-ebe1-11ec-8135-001dd8b740bc') -- МЕНЕДЖЕРЬІ ВЬІБЬІВШИЕ
        AND sales.doc_date::date >= '01.01.2020'::date
        AND manager_key IS NULL 
    ORDER BY folder_name, client
    ;

    GRANT SELECT ON TABLE v_one_managers_and_counterparts TO user_prestige;

