import inspect


def current_function_name():
    return inspect.currentframe().f_code.co_name  # имя текущей функции


def calling_function_name():
    return inspect.stack()[1].function  # имя вызвавшей функции


def example_function():
    print(f"Текущая функция: {current_function_name()}")
    print(f"Вызвана из функции: {calling_function_name()}")


def another_function():
    example_function()


if __name__ == '__main__':
    print(current_function_name())
    print(calling_function_name())
    another_function()
