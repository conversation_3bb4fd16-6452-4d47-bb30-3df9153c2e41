import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_vacation_official_period"
DOCUMENT = "Document_НачислениеОтпускаРаботникамОрганизаций_Отпуска"
SELECT_COLUMNS = "LineN<PERSON>ber,ДатаНачала,ДатаОкончания,НомерСтрокиПервичныйОтпуск,Ref_Key,ВидРасчета_Key"

SQL_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            line_number numeric(10,4) NOT NULL DEFAULT 0,  -- LineNumber
            start_doc_date timestamp NOT NULL,  -- ДатаНачала
            expiration_doc_date timestamp NOT NULL,  -- ДатаОкончания
            line_doc_number_primary_issue varchar(50) NULL,  -- НомерСтрокиПервичныйОтпуск
            ref_key varchar(50) NULL,  -- Ref_Key
            type_of_calculation_key varchar(50) NULL,  -- ВидРасчета_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
        COMMENT ON COLUMN {TABLE_NAME}.start_doc_date IS 'ДатаНачала';
        COMMENT ON COLUMN {TABLE_NAME}.expiration_doc_date IS 'ДатаОкончания';
        COMMENT ON COLUMN {TABLE_NAME}.line_doc_number_primary_issue IS 'НомерСтрокиПервичныйОтпуск';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.type_of_calculation_key IS 'ВидРасчета_Key';
    """


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}(
            line_number,
            start_doc_date,
            expiration_doc_date,
            line_doc_number_primary_issue,
            ref_key,
            type_of_calculation_key
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            line_number = EXCLUDED.line_number,
            start_doc_date = EXCLUDED.start_doc_date,
            expiration_doc_date = EXCLUDED.expiration_doc_date,
            line_doc_number_primary_issue = EXCLUDED.line_doc_number_primary_issue,
            type_of_calculation_key = EXCLUDED.type_of_calculation_key
    """
    return sql.replace("'", "")


async def main_doc_vacation_official_period_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(6)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_vacation_official_period_async())
