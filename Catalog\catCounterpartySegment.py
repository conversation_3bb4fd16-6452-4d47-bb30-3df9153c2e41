import asyncio
import os
import sys

sys.path.append(os.path.dirname(__file__))

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_counterparty_segment"
DOCUMENT = "Catalog_СегментыКонтрагентов"
SELECT_COLUMNS = '''DataVersion,DeletionMark,Predefined,IsFolder,Code,Description,Ref_Key,Parent_Key'''

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(40) NOT NULL,
        description varchar(100) NOT NULL,
        data_version varchar(40) NOT NULL,
        deletion_mark boolean DEFAULT false,
        predefined boolean DEFAULT false,
        isfolder boolean DEFAULT false,
        ref_key varchar(40) NOT NULL,
        parent_key varchar(40) NULL,
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';

    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.data_version IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';    
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'predefined';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'parent_key';
    '''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}
        (
            data_version,
            deletion_mark,
            predefined,
            isfolder,
            code,
            description,
            ref_key,
            parent_key
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            data_version = EXCLUDED.data_version,
            deletion_mark = EXCLUDED.deletion_mark,
            predefined = EXCLUDED.predefined,
            isfolder = EXCLUDED.isfolder,
            code = EXCLUDED.code,
            description = EXCLUDED.description,
            parent_key = EXCLUDED.parent_key
    ;
    '''
    return sql.replace("'", "")


async def main_cat_counterparty_segment_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(8)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "description")
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_cat_counterparty_segment_async())
