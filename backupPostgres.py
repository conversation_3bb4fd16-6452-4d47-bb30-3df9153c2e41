# Description: Проверка наличия pg_dump в системе.

import datetime
import gzip
import shutil
import subprocess
from async_Postgres import username, psw, basename, hostname_public, port


def get_pg_dump_path(host):
    """Определяет путь к pg_dump. Ищет локально или на удаленном сервере."""
    print(f"Ищем pg_dump на сервере {host}...")

    # Сначала проверяем локальный путь
    pg_dump_path = shutil.which("pg_dump")
    if pg_dump_path:
        print(f"pg_dump найден локально: {pg_dump_path}")
        return pg_dump_path

    # Проверяем стандартные пути на сервере PostgreSQL
    possible_paths = [
        f"/usr/lib/postgresql/bin/pg_dump",
        f"/usr/pgsql/bin/pg_dump",
        f"/usr/local/bin/pg_dump",
        f"/opt/PostgreSQL/bin/pg_dump",
    ]

    for path in possible_paths:
        ssh_command = ["ssh", host, f"test -x {path} && echo {path}"]
        try:
            result = subprocess.run(
                ssh_command, capture_output=True, text=True, check=True
            )
            remote_path = result.stdout.strip()
            if remote_path:
                print(f"pg_dump найден на сервере: {remote_path}")
                return remote_path
        except subprocess.CalledProcessError:
            continue  # Если не найден, пробуем следующий путь

    print("pg_dump не найден!")
    return None


def backup_postgres(db_name, user, password, pg_dump_path, host, port):
    """Создает сжатую резервную копию базы данных PostgreSQL."""
    backup_file = None
    try:
        date_str = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"backup_{db_name}_{date_str}.sql.gz"

        command = [
            # "ssh",
            host,  # Запускаем команду на удаленном сервере
            f"PGPASSWORD={password} {pg_dump_path} -h {host} -p {port} -U {user} -F c -Z9 -w {db_name}",
        ]

        print("Запуск команды:", " ".join(command))

        with subprocess.Popen(
            command, stdout=subprocess.PIPE, stderr=subprocess.PIPE
        ) as process, gzip.open(backup_file, "wb") as f:
            for line in process.stdout:
                f.write(line)

            stdout, stderr = process.communicate()
            exit_code = process.poll()

            if exit_code != 0:
                error_message = stderr.decode("utf-8", errors="replace")
                print(f"Ошибка pg_dump (код {exit_code}): {error_message}")
                raise Exception(f"Ошибка pg_dump: {error_message}")

        print(f"Сжатая резервная копия создана: {backup_file}")
        return backup_file

    except Exception as e:
        if backup_file and os.path.exists(backup_file):
            os.remove(backup_file)
            print(f"Удален частичный бэкап: {backup_file}")
        print(f"Ошибка: {str(e)}")
        raise


import subprocess
import gzip
import os
import shutil
from async_Postgres import username, psw, basename, hostname_public, port


def restore_postgres(
    backup_path: str,
    db_name: str,
    user: str,
    password: str,
    host: str = "localhost",
    port: int = 5432,
    drop_if_exists: bool = False,
) -> bool:
    """
    Восстанавливает базу данных PostgreSQL из резервной копии (gzip или custom format).

    Аргументы:
        backup_path: Путь к файлу бэкапа (.sql.gz или .dump)
        db_name: Имя целевой БД
        user: Пользователь PostgreSQL
        password: Пароль
        host: Хост
        port: Порт
        drop_if_exists: Удалить и пересоздать БД перед восстановлением

    Возвращает:
        True если успешно, False при ошибке
    """
    try:
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Файл бэкапа {backup_path} не найден")

        # Определяем нужный инструмент восстановления
        if backup_path.endswith(".gz") or backup_path.endswith(".sql"):
            restore_tool = shutil.which("psql")
            restore_command = [
                restore_tool,
                "-h",
                host,
                "-p",
                str(port),
                "-U",
                user,
                "-d",
                db_name,
            ]
            use_gzip = backup_path.endswith(".gz")
        else:
            restore_tool = shutil.which("pg_restore")
            restore_command = [
                restore_tool,
                "-h",
                host,
                "-p",
                str(port),
                "-U",
                user,
                "-d",
                db_name,
                "--clean",
                "--verbose",
                backup_path,
            ]
            use_gzip = False

        if not restore_tool:
            raise Exception("Не найден `psql` или `pg_restore` в системе!")

        # Удаление и пересоздание базы перед восстановлением
        if drop_if_exists:
            print(f"Удаление и пересоздание базы {db_name}...")
            subprocess.run(
                ["dropdb", "-h", host, "-p", str(port), "-U", user, db_name],
                env={"PGPASSWORD": password},
                check=False,
            )
            subprocess.run(
                ["createdb", "-h", host, "-p", str(port), "-U", user, db_name],
                env={"PGPASSWORD": password},
                check=True,
            )

        print(f"Запуск восстановления с помощью {restore_tool}...")

        # Запуск команды восстановления
        env = os.environ.copy()
        env["PGPASSWORD"] = password

        if use_gzip:
            with gzip.open(backup_path, "rb") as f:
                process = subprocess.Popen(
                    restore_command,
                    stdin=f,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    env=env,
                )
        else:
            process = subprocess.Popen(
                restore_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, env=env
            )

        stdout, stderr = process.communicate()

        if process.returncode != 0:
            error_msg = stderr.decode("utf-8", errors="replace")
            raise Exception(
                f"Ошибка восстановления (код {process.returncode}): {error_msg}"
            )

        print("База данных успешно восстановлена!")
        return True

    except Exception as e:
        print(f"Ошибка восстановления: {str(e)}")
        return False


# Пример использования
if __name__ == "__main__":
    pg_dump_path = get_pg_dump_path(hostname_public)  # Путь к pg_dump на сервере
    if pg_dump_path:
        backup_puth = backup_postgres(
            basename, username, psw, pg_dump_path, host=hostname_public, port=port
        )
    else:
        print("pg_dump не найден в системе")

    # backup_puth = "d:\backup_prestige_20250303_114517.sql.gz"
    # success = restore_postgres(
    #     backup_path=backup_puth,
    #     db_name="my_database",
    #     user=username,
    #     password=psw,
    #     host=hostname_public,
    #     port=port,
    #     drop_if_exists=True,
    # )
    #
    # if success:
    #     print("✅ Восстановление завершено успешно!")
    # else:
    #     print("❌ Восстановление не удалось!")
