
    CREATE OR REPLACE VIEW v_service_expense
    AS 
    SELECT concat(t.supplier,' от ' ,t.tarih::date, ' № ' ,t.doc_number) AS src, 
        t.supplier,
        t.tarih,
        t.doc_number,
        t.dop_doc_date,
        t.dop_doc_number,
        t.client,
        round(t.amount_ue, 3) AS amount_ue
    FROM ( SELECT DISTINCT gs.client AS supplier,
                gs.tarih,
                gs.doc_number,
                dop.doc_date AS dop_doc_date,
                dop.doc_number AS dop_doc_number,
                dop.client,
                dop.dop_sum_ue AS amount_ue
               FROM v_service_tc_group gs
                 LEFT JOIN v_service_tc_dop dop ON gs.batch_document::text = dop.batch_document::text
            UNION ALL
             SELECT DISTINCT gs.client AS supplier,
                gs.tarih AS gs_doc_date,
                gs.doc_number AS gs_doc_number,
                dop.tarih AS dop_doc_date,
                dop.gtd_number AS dop_doc_number,
                'НДС'::text AS client,
                dop.vat_usd
               FROM v_service_tc_group gs
                 LEFT JOIN v_gtd dop ON gs.batch_document::text = dop.batch_document::text
            UNION ALL
             SELECT DISTINCT gs.client AS supplier,
                gs.tarih AS gs_doc_date,
                gs.doc_number AS gs_doc_number,
                dop.tarih AS dop_doc_date,
                dop.gtd_number AS dop_doc_number,
                'пошлина'::text AS client,
                dop.duty_usd
               FROM v_service_tc_group gs
                 LEFT JOIN v_gtd dop ON gs.batch_document::text = dop.batch_document::text
    ) t
    WHERE coalesce(t.amount_ue,0) <> 0
    ORDER BY t.tarih DESC, t.doc_number, t.dop_doc_number;

    COMMENT ON VIEW v_service_expense IS 'док по поступлению и связанные с ним расходы';
    GRANT SELECT ON TABLE v_service_expense TO user_prestige;
