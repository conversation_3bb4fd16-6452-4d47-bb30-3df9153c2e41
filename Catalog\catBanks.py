import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_cat_banks'
DOCUMENT = "Catalog_Банки"
SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Predefined,IsFolder,Code,Description,Город,Адрес,КодПоЕДРПОУ"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15) NOT NULL,  -- Code
        description varchar(250) NULL,  -- Description
        town varchar(50) NULL,  -- Город
        the_address varchar(50) NULL,  -- Адрес
        code_by_edrpou numeric(10) NOT NULL DEFAULT 0,  -- КодПоЕДРПОУ
        deletion_mark bool NOT NULL DEFAULT false,  -- DeletionMark
        predefined bool NOT NULL DEFAULT false,  -- Predefined
        isfolder bool NOT NULL DEFAULT false,  -- IsFolder
        dataversion varchar(50) NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.town IS 'Город';
    COMMENT ON COLUMN {TABLE_NAME}.the_address IS 'Адрес';
    COMMENT ON COLUMN {TABLE_NAME}.code_by_edrpou IS 'КодПоЕДРПОУ';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        ref_key, 
        dataversion, 
        deletion_mark, 
        predefined, 
        isfolder, 
        code, 
        description, 
        town, 
        the_address, 
        code_by_edrpou
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        predefined = EXCLUDED.predefined,
        isfolder = EXCLUDED.isfolder,
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        town = EXCLUDED.town,
        the_address = EXCLUDED.the_address,
        code_by_edrpou = EXCLUDED.code_by_edrpou;    
    '''
    return sql.replace("'", "")


async def main_cat_banks_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_banks_async())
