# акт сверки по бухгалтерии
# 20250427 1312
import datetime
import os
import pandas as pd
from datetime import datetime

if os.name == 'nt':
    os.system('cls')
else:
    os.system('clear')

total_merged_both = []


def get_sql():
    return f"""
        WITH
        sources AS (
            SELECT
                client.segment,
                client.manager,
                client.customer,
                client.contract_name,
                reg.dtperiod::date AS дата,
                rtrn.customer_date,
        --	    rtrn.customer_number AS номерКлиента,
        --        client.contract_name  AS договор,
                substring(client.contract_name FROM '\d+-.') AS договор,
        --	    substring(client.contract_name FROM '\d+') AS договор,
        --	    rtrn.doc_number номер,
                CASE
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ВозвратТоваровОтПокупателя' AND COALESCE(rtrn.customer_number,'') <> '' THEN
                        rtrn.customer_number
                    ELSE
                        reg.doc_number
                END AS номер,
                CASE
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ПоступлениеТоваровУслуг' THEN
                        SUM(reg.amount)
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'РеализацияТоваровУслуг' THEN
                        -SUM(reg.amount)
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ВозвратТоваровОтПокупателя' THEN
                        -SUM(reg.amount)
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ПлатежноеПоручениеВходящее' THEN
                        SUM(reg.amount)
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ПлатежноеПоручениеИсходящее' THEN
                        SUM(reg.amount)
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'КорректировкаДолга' THEN
                        SUM(reg.amount)
                END AS сумма,
                REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') AS doc_type,
                CASE
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ПоступлениеТоваровУслуг' THEN
                        'ПН'
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ВозвратТоваровОтПокупателя' THEN
                        'ВН'
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ПлатежноеПоручениеВходящее' THEN
                        'ППВ'
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'ПлатежноеПоручениеИсходящее' THEN
                        'ППИ'
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'РеализацияТоваровУслуг' THEN
                        'РН'
                    WHEN REPLACE(reg.recorder_type, 'StandardODATA.Document_', '') = 'КорректировкаДолга' THEN
                        'взаимозачет'
                END AS типДок
        --		, reg.recorder
            FROM t_one_doc_acc_reg_reciprocal_settlements_details AS reg
                LEFT JOIN t_one_doc_return_of_goods_from_customers AS rtrn
                    ON reg.recorder = rtrn.ref_key
                LEFT JOIN
                    (SELECT DISTINCT
                        segment,
                        manager,
                        customer,
                        contract_name,
                        customer_key,
                        contract_key
                    FROM v_one_manager_counterparty_contracts_segments
--                    WHERE customer ILIKE '%сокар%'
--                        AND contract_name ILIKE '%-П%'
--                        AND contract_type = 'СПокупателем'
--                        AND contract_days <> 0
                    ) AS client
                    ON reg.counterparty_key = client.customer_key
                    AND reg.contract_key = client.contract_key
            GROUP BY
                client.segment,
                client.manager,
                client.customer,
                client.contract_name,
                reg.recorder,
                reg.dtperiod::date,
                reg.doc_number,
                rtrn.doc_number,
                rtrn.customer_date,
                rtrn.customer_number,
                REPLACE(reg.recorder_type, 'StandardODATA.Document_', '')
        )
        SELECT
            договор,
            дата,
            номер,
            sum(сумма) сумма,
            типДок,
            'Prestige' источник
            --, recorder
        FROM sources
        WHERE дата::date >= '01.01.2021'::date
            AND customer ILIKE '%сокар%'
            AND договор ILIKE '%-П%'
            AND договор ILIKE ANY (ARRAY['%10%', '%355%', '%356%', '%357%', '%358%', '%359%']) -- договор IN ('10','355','356','357','358','359')
        GROUP BY
            договор,
            дата,
            номер,
            типДок
        ORDER BY
            договор,
            дата,
            номер,
            типДок
        ;
    """


def preprocess_dataframe(df):
    # Приводим строки к чистому виду, преобразуем даты и суммы
    # Преобразуем пустые или NaN значения в None.
    # Сделаем регистронезависимое сравнение и удалим пробелы по краям
    df['Договор'] = df['Договор'].apply(lambda x: x.strip().lower() if pd.notna(x) else None)
    df["ТипДок"] = df["ТипДок"].astype(str).str.strip()
    df["Источник"] = df["Источник"].astype(str).str.strip()
    df["Дата"] = pd.to_datetime(df["Дата"], dayfirst=True, errors='coerce')
    df["Сумма"] = pd.to_numeric(df["Сумма"], errors='coerce')

    # Преобразуем номера в числа чз regex, справа, конца строки.
    # чз преобразования в строку работает некорректно, т.к. номер 7 находит в 357
    df["ПолныйНомер"] = df["Номер"].astype(str)
    df["Номер"] = pd.to_numeric(df["Номер"].astype(str).str.extract(r'(\d+$)')[0],
                           errors='coerce').astype('Int64')
    return df


def get_valid_date(df):
    # Отделяем валидные и невалидные строки
    # валидные данные, те, где дата и сумма не пустые
    df_valid = df[df["Дата"].notna() & df["Сумма"].notna()].copy()

    # невалидные данные, те, где дата или сумма пустые
    df_invalid = df[df["Дата"].isna() | df["Сумма"].isna()].copy()

    # Добавим период, год и форматируем дату
    df_valid["ГодМесяц"] = df_valid["Дата"].dt.to_period("M")
    df_valid["Год"] = df_valid["Дата"].dt.year
    df_valid["Дата"] = df_valid["Дата"].dt.date

    # Убираем пустоты в колонке "Источник"
    df_valid["Источник"] = df_valid["Источник"].str.strip()

    sources = df_valid["Источник"].unique()
    if len(sources) != 2:
        raise ValueError("Ожидаются ровно два значения в колонке 'Источник', например, 'сторона1' и 'сторона2'")

    if not 'Prestige' in sources.tolist():
        raise ValueError("В колонке 'Источник' Нет строк с 'Prestige'")

    client_name = sources[sources != 'ppk'][0]
    return df_valid, df_invalid, sources, client_name


def split_df(df_valid, client_name):
    # dвалидные данные делим на 2 df: ppk и клиента

    df_client = df_valid[df_valid["Источник"] != 'Prestige'].copy()
    df_ppk = df_valid[df_valid["Источник"] == 'Prestige'].copy()
    df_client.rename(columns={'Источник': client_name}, inplace=True)
    df_ppk.rename(columns={'Источник': 'ppk'}, inplace=True)  # ppk - Prestige

    # Сохраняем исходные индексы в отдельные колонки
    df_client['индекс_клиента'] = df_client.index
    df_ppk['индекс_ppk'] = df_ppk.index

    # в эти колонки будем записывать результаты сверки (ppk)
    exists_columns = ["Договор_ppk", "Дата_ppk", "Номер_ppk", "Сумма_ppk", "Отклонение"]
    for col in exists_columns:
        df_client[col] = None

    return df_ppk, df_client


def columns_without_cotract(columns_to_match):
    """если columns_to_match содержит "Договор", алгоритм меняется.
    после группировки Договор может включать в себя несколько номеров договоров.
    нам надо будет искать совпадения номера договора клиента в списке номеров наших договоров или наоборот.
    Н: df_client["Договор"] = "355-П", df_ppk["Договор"] = "355-П, 356-П".
    Сначало будем объединять без договоров, потом искать совпадения по договорам.
    """
    if "договор" in [i.lower() for i in columns_to_match]:
        columns_to_match.remove("Договор")
        return True, columns_to_match
    return False, columns_to_match


def check_contract_match(row):
    # Функция проверки совпадений
    # Проверяем, есть ли строка из "Договор_x" в "Договор_y" или наоборот
    # if 'ZO3000001166' in [row.get('ПолныйНомер_x'), row.get('ПолныйНомер_y'), row.get('ПолныйНомер')]:
    #     breakpoint()
    #     print(row)

    if pd.isna(row['Договор_x']) or pd.isna(row['Договор_y']):
        return False
    try:
        str_x = str(row['Договор_x'])
        str_y = str(row['Договор_y'])
        return (str_x in str_y) or (str_y in str_x)
    except:
        return False


def find_matches(df_client, df_ppk, columns_to_match):
    contract, columns_to_match_without_dogovor = columns_without_cotract(columns_to_match)
    # Merge по другим колонкам (кроме "Договор")
    merged = pd.merge(df_client, df_ppk, on=columns_to_match_without_dogovor, how="left", indicator=True)


    if contract and columns_to_match:
        # Создание маски
        mask = (
            merged['Договор_y'].isna() |
            (
                merged['Договор_x'].notna() &
                merged['Договор_y'].notna() &
                merged.apply(check_contract_match, axis=1)
            )
        )

        # Фильтрация
        merged_both = merged[mask].copy()

    # Выбираем только строки, которые есть в обеих таблицах
    merged_both = merged[merged['_merge'] == 'both'].copy()
    return merged_both


def update_df_client(df_client, merged_both):
    # обновим данные в df_client данные из merged_both, если номер содержится в номере соответствия

    columns_to_match = ["Договор", "ТипДок", "ГодМесяц", "Номер", "Дата", "Сумма"]
    for index, row in merged_both.iterrows():
        client_index = row['индекс_клиента']
        for col in columns_to_match:
            # Проверяем наличие колонки "Полный{col}_y" в row
            if f"Полный{col}_y" in row and row[f"Полный{col}_y"] is not None:
                df_client.at[client_index, f"{col}_ppk"] = row[f"Полный{col}_y"]
            elif f"Полный{col}" in row and row[f"Полный{col}"] is not None:
                df_client.at[client_index, f"{col}_ppk"] = row[f"Полный{col}"]
            # Проверяем наличие колонки "{col}_y" в row
            elif f"{col}_y" in row and row[f"{col}_y"] is not None:
                df_client.at[client_index, f"{col}_ppk"] = row[f"{col}_y"]
            # Проверяем наличие колонки "{col}" в row
            elif col in row and row[col] is not None:
                df_client.at[client_index, f"{col}_ppk"] = row[col]

        diff = ''
        if 'Договор_x' in row and 'Договор_y' in row and row['Договор_x'] != row['Договор_y']:
            diff += f"Договор: {row['Договор_x']} / {row['Договор_y']}; "
            df_client.at[client_index, "Отклонение"] = diff

        if 'Номер_x' in row and 'Номер_y' in row and row['Номер_x'] != row['Номер_y']:
            diff += f"Номер: {row['Номер_x']} / {row['ПолныйНомер_y']}; "
            df_client.at[client_index, "Отклонение"] = diff

        if 'Дата_x' in row and 'Дата_y' in row and row['Дата_x'] != row['Дата_y']:
            diff += f"Дата: {row['Дата_x']} / {row['Дата_y']}; "
            df_client.at[client_index, "Отклонение"] = diff

        if 'Сумма_x' in row and 'Сумма_y' in row and row['Сумма_x'] != row['Сумма_y']:
            diff += f"Сумма: {row['Сумма_x']} / {row['Сумма_y']}; "
            df_client.at[client_index, "Отклонение"] = diff


    return df_client


def drop_indexes(df_ppk, merged_both):
    # удалим из df_ppk данные, которые перенесли в df_client

    # Проверяем, есть ли колонка 'индекс_ppk' или 'индекс_ppk_y' в merged_both
    if 'индекс_ppk' in merged_both.columns:
        indexes_to_drop = merged_both['индекс_ppk'].tolist()
    elif 'индекс_ppk_y' in merged_both.columns:
        indexes_to_drop = merged_both['индекс_ppk_y'].tolist()
    else:
        print("Предупреждение: Не найдена колонка 'индекс_ppk' или 'индекс_ppk_y' в merged_both")
        return df_ppk

    df_ppk = df_ppk[~df_ppk.index.isin(indexes_to_drop)]
    return df_ppk


def delete_columns(df_client, df_ppk):
    # удаляем ненужные колонки

    drop_columns=["индекс_ppk", "ГодМесяц", "Год"]
    df_ppk.drop(columns=drop_columns, inplace=True)

    drop_columns=["индекс_клиента", "ТипДок_ppk", "ГодМесяц_ppk", "ГодМесяц", "Год"]
    df_client.drop(columns=drop_columns, inplace=True)

    return df_client, df_ppk


def save_to_excel(df_client, df_ppk, df_other, sources):
    # Сохраняем результаты в Excel

    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"результат_сверки_{sources[0]}_{sources[1]}_{current_time}.xlsx"

    with pd.ExcelWriter(file_name, engine="openpyxl") as writer:
        df_client.to_excel(writer, sheet_name=f"Сверка_{sources[0]}", index=False)
        df_ppk.to_excel(writer, sheet_name=f"Сверка_{sources[1]}", index=False)
        df_other.to_excel(writer, sheet_name="Совпадения_Ошибки", index=False)


def sync_columns(df_client, df_ppk, columns_to_match, count_merged_both=0):
    # синхронизируем колонки в df_client и df_ppk

    global total_merged_both
    from collections import Counter

    merged_both = find_matches(df_client, df_ppk, columns_to_match)

    # Не меняем имена колонок, чтобы сохранить суффиксы _x и _y
    # Это позволит корректно обращаться к колонкам в функции update_df_client

    # к merged_both добавляем предидущие совпадения
    # нужно для того, чтобы определить какие строки-совпадения не занесутся в df_client
    # по этим документам в 1С возможно неправильно занесли номера клиента
    # Н: 1005-144791 и 1055-144791

    # Проверяем, есть ли колонка 'индекс_клиента' или 'индекс_клиента_x' в merged_both
    if 'индекс_клиента' in merged_both.columns:
        total_merged_both.extend(merged_both["индекс_клиента"].tolist())
    elif 'индекс_клиента_x' in merged_both.columns:
        total_merged_both.extend(merged_both["индекс_клиента_x"].tolist())
    else:
        print("Предупреждение: Не найдена колонка 'индекс_клиента' или 'индекс_клиента_x' в merged_both")

    df_client = update_df_client(df_client, merged_both)

    # найдено совпадений записей
    print(f"Найдено совпадений записей: {len(merged_both)}")

    # занесено совпадений в df_client
    count_merged_both += len(merged_both)
    print(f"Занесено совпадений в df_client: {count_merged_both}")
    count_df_client = df_client["Договор_ppk"].count()
    print(f"Всего занесено совпадений в df_client: {count_df_client}")

    # если count_merged_both != count_df_client, то есть не все совпадения занесены в df_client, то выдаем ошибку
    if count_merged_both != count_df_client:
        print(f"count_merged_both: {count_merged_both}, count_df_client: {count_df_client}")
        # проверяем, если в total_merged_both есть дубликаты индекс_клиента и выводим
        duplicates = [item for item, count in Counter(total_merged_both).items() if count > 1]
        if not duplicates:
            print(f"Всего совпадений: {len(total_merged_both)}")
            print(f"Дубликаты в совпадениях: {len(duplicates)}")
            print(duplicates)
        # save_to_excel(df_client, df_ppk, merged_both, sources)
        # raise ValueError("Не все совпадения занесены в df_client")

    # удалим из df_ppk данные, которые перенесли в df_client
    print(f"Записей в df_ppk до удаления совпадений: {len(df_ppk)}")
    df_ppk = drop_indexes(df_ppk, merged_both)
    print(f"Записей в df_ppk после удаления совпадений: {len(df_ppk)}")

    return df_client, df_ppk, count_merged_both


def group_and_sum(df):
    # сгруппируем и просуммируем данные во избежания дублирования

    # columns_to_group = ['Договор', 'Дата', 'ПолныйНомер', 'Номер', 'ТипДок', 'Источник']
    # df_group = df.groupby(columns_to_group)["Сумма"].sum().reset_index()
    df_group = df.groupby(['Дата', 'ПолныйНомер', 'Номер', 'ТипДок', 'Источник'], as_index=False).agg({
        'Договор': ';'.join,  # Объединяет договоры через точку с запятой
        'Сумма': 'sum'  # Суммирует значения в колонке "сумма"
    })
    return df_group


def control(path_to_file):
    # Загружаем файл и обрабатываем данные

    count_matches = 0
    df = pd.read_excel(path_to_file, sheet_name="Sheet1")
    df = preprocess_dataframe(df)
    df = group_and_sum(df)

    df_valid, df_invalid, sources, client_name = get_valid_date(df)
    df_ppk, df_client = split_df(df_valid, client_name)

    # синхронизируем по колонкам
    find_by_columns = [
        ["Договор", "ТипДок", "ГодМесяц", "Номер", "Дата", "Сумма"],
        ["Договор", "ТипДок", "ГодМесяц", "Номер", "Дата"],
        ["Договор", "ТипДок", "ГодМесяц", "Номер", "Сумма"],
        ["Договор", "ТипДок", "ГодМесяц", "Номер"],
        ["Договор", "ТипДок", "ГодМесяц", "Сумма"],
        ["ТипДок", "ГодМесяц", "Номер"],
        ["ТипДок", "ГодМесяц", "Сумма"],
        ["Договор", "ТипДок", "Год", "Номер"],
        ["Договор", "ТипДок", "Год", "Сумма"],
     ]
    for columns in find_by_columns:
        df_client, df_ppk, count_matches = sync_columns(df_client, df_ppk, columns, count_matches)

    delete_columns(df_client, df_ppk)

    save_to_excel(df_client, df_ppk, df_invalid, sources)


if __name__ == "__main__":
    print(f"Start: {datetime.now()}")
    path_to_file = r"C:\Users\<USER>\Desktop\Сверка\Socar\LiteVba.xlsx"
    control(path_to_file)
    print(f"End: {datetime.now()}")
