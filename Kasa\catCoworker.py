import asyncio
import os
import sys
cur_dir = os.path.dirname(__file__)
sys.path.append(cur_dir)
from cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_coworker"
DOCUMENT = "сотрудники, физлица access"
SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key uuid DEFAULT uuid_generate_v4() NOT NULL,
        coworker_name varchar(50) NOT NULL,
        description varchar(300) NULL,
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (coworker_name)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.coworker_name IS 'имя сотрудника';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'примечание';
    GRANT ALL ON TABLE {TABLE_NAME} TO {COMPANY_CASHIER}; --{DOCUMENT}

"""


async def main_t_coworker_async():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_TABLE)
    logger.info(f"{result}, SQL_CREATE_TABLE")
    result = await async_save_pg("SELECT * FROM insert_into_t_coworker()")
    logger.info(f"{result}, SELECT * FROM insert_into_t_coworker()")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_t_coworker_async())
