pip install -r requirements.txt  # устанавливает все пакеты, перечисленные в файле
pip freeze > requirements.txt  # сохраняет список всех установленных пакетов в файл


Удалите Config.py из индекса Git:
Если файл Config.py уже был добавлен в репозиторий, вам нужно удалить его из индекса Git, чтобы он больше не отслеживался, но остался на диске:
git rm --cached Config.py

Зафиксируйте изменения в .gitignore и удаление файла из индекса:
git add .gitignore
git commit -m "Add Config.py to .gitignore and remove it from the repository"