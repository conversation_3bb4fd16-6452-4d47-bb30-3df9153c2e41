# Document_ОприходованиеТоваров
import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_price"
DOCUMENT = "Document_УстановкаЦенНоменклатуры"
SELECT_COLUMNS = "DataVersion,DeletionMark,Number,Date,Posted,Комментарий,Информация,Ref_Key"

SQL_CREATE_TABLE = f"""        
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NULL,  -- DataVersion
            deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
            doc_number varchar(15) NOT NULL DEFAULT 0,  -- Number
            doc_date timestamp NOT NULL,  -- Date
            posted boolean NOT NULL DEFAULT FALSE,  -- Posted
            a_comment varchar(300) NULL,  -- Комментарий
            information varchar(200) NULL,  -- Информация
            ref_key varchar(50) NULL,  -- Ref_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS ' DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS ' DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS ' Number';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS ' Date';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS ' Posted';
        COMMENT ON COLUMN {TABLE_NAME}.a_comment IS ' Комментарий';
        COMMENT ON COLUMN {TABLE_NAME}.information IS ' Информация';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        dataversion,
        deletion_mark,
        doc_number,
        doc_date,
        posted,
        a_comment,
        information,
        ref_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        doc_number = EXCLUDED.doc_number,
        doc_date = EXCLUDED.doc_date,
        posted = EXCLUDED.posted,
        a_comment = EXCLUDED.a_comment,
        information = EXCLUDED.information
    """
    return sql.replace("'", "")


async def main_doc_price_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, False, True)
    maket = await create_model_async(8)
    sql = await sql_insert(maket)
    sql = sql.replace("$4,", "to_timestamp($4, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_price_async())
