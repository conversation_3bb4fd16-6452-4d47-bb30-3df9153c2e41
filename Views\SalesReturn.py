import asyncio
import os

from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

v_one_sale_return = '''
    CREATE OR REPLACE VIEW v_one_sale_return AS
    SELECT 
        goods.id, 
        sale_clients.description AS papka,
        managers.description AS manager,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        goods.quantity,
        goods.coefficient,
        (goods.quantity * goods.coefficient) AS ed,
        ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        goods.amount,
        goods.amount_vat,
        serv.consider_vat,
        serv.amount_includes_vat,
        'продажа возврат'::text AS doc_type,
        1 as issale,
        serv.settlement_rate,
        serv.multiplicity_of_mutual_settlements,        
        org.description AS organization,
        clients.description AS customer,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        clients.ref_key AS customer_key,
        serv.currency_key,
        serv.organization_key,
        nom.supplier_key,
        goods.warehouse_key,
        clients.parent_key,
        serv.contract_key,
        goods.line_number
    FROM t_one_doc_return_of_goods_from_customers serv
        JOIN t_one_doc_return_of_goods_from_customers_goods goods USING (ref_key)
        LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
        JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = goods.warehouse_key
        LEFT JOIN t_one_cat_counterparties managers ON managers.ref_key::text = clients.parent_key::text
        LEFT JOIN t_one_cat_counterparties sale_clients ON sale_clients.ref_key::text = managers.parent_key::text
    WHERE serv.doc_date::date <= current_date          
        AND is_management = true and posted = true
        -- AND sale_clients.ref_key = 'ad421841-905f-11e6-80c4-c936aa9c817c'
    ORDER BY serv.doc_date, serv.doc_number;

    COMMENT ON VIEW v_one_sale_return IS 'продажа возврат';
    COMMENT ON COLUMN v_one_sale_return.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_sale_return.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_sale_return.sku IS 'sku';
    COMMENT ON COLUMN v_one_sale_return.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_sale_return.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_sale_return.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_sale_return.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_sale_return.issale IS 'doc_type 1-sale: -1 - return';

    GRANT SELECT ON TABLE v_one_sale_return TO user_prestige;

    '''


async def create_v_one_sales_return():
    await async_save_pg(v_one_sale_return)
    logger.info("v_one_sale_return")


if __name__ == '__main__':
    asyncio.run(create_v_one_sales_return())
