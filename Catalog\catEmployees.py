import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_employees"
DOCUMENT = "Catalog_СотрудникиОрганизаций"
SELECT_COLUMNS = ("Ref_Key,DataVersion,DeletionMark,Predefined,Parent_Key,IsFolder,Code,Description,Физлицо_Key,"
                  "Актуальность")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NULL,  -- DataVersion
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        parent_key varchar(50) NULL,  -- Parent_Key
        isfolder boolean NOT NULL DEFAULT FALSE,  -- IsFolder
        code numeric(10,4) NOT NULL DEFAULT 0,  -- Code
        description varchar(50) NULL,  -- Description
        ref_key uuid NULL,  -- Ref_Key
        individual_key varchar(50) NULL,  -- Физлицо_Key
        relevance boolean NULL DEFAULT FALSE,  -- Актуальность
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS ' DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS ' DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS ' Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS ' Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS ' IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.code IS ' Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS ' Description';
    COMMENT ON COLUMN {TABLE_NAME}.individual_key IS ' Физлицо_Key';
    COMMENT ON COLUMN {TABLE_NAME}.relevance IS ' Актуальность';
    
    GRANT ALL ON TABLE {TABLE_NAME} TO user_prestige;
'''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}
        (
            ref_key,
            dataversion,
            deletion_mark,
            predefined,
            parent_key,
            isfolder,
            code,
            description,
            individual_key,
            relevance
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            dataversion = EXCLUDED.dataversion,
            deletion_mark = EXCLUDED.deletion_mark,
            predefined = EXCLUDED.predefined,
            parent_key = EXCLUDED.parent_key,
            isfolder = EXCLUDED.isfolder,
            code = EXCLUDED.code,
            description = EXCLUDED.description,
            individual_key = EXCLUDED.individual_key,
            relevance = EXCLUDED.relevance
    ;
    '''
    return sql.replace("'", "")


async def main_cat_employee_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "description")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_employee_async())
