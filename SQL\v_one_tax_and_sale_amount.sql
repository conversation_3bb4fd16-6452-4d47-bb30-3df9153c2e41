    CREATE OR REPLACE VIEW v_one_tax_and_sale_amount AS
    SELECT DISTINCT
        orders.doc_date заказДатф,
        orders.doc_number заказаНомер,
        sale.sale_date РН_дата,
        sale.sale_number РН_номер,
        sale.sale_amount РН_сумма,
        sale_tax.tax_date НН_дата,
        sale_tax.tax_number НН_номер,
        sale_tax.tax_amount НН_Сумма,
        CASE
            WHEN (abs(COALESCE(sale.sale_amount,0) - COALESCE(sale_tax.tax_amount,0)) > 0.5
                AND COALESCE(sale_tax.tax_number,'') <> '') THEN 'Нет'
            ELSE ''
        END "РН=НН",
        sale_return.return_date ВН_дата,
        sale_return.return_number ВН_номер,
        sale_return.return_amount ВН_сумма,
        return_tax.tax_return_date РК_дата,
        return_tax.tax_return_number РК_номер,
        return_tax.tax_return_amount РК_сумма,
        CASE
            WHEN (abs(COALESCE(sale_return.return_amount,0) - COALESCE(return_tax.tax_return_amount,0)) > 0.5
                OR COALESCE(return_tax.tax_return_number,'') <> '') THEN 'Нет'
            ELSE ''
        END "ВН=РК"
    FROM t_one_doc_buyer_order orders
        FULL JOIN
            (
                SELECT
                    doc_date sale_date,
                    doc_number sale_number,
                    CASE
                        WHEN posted THEN document_amount
                        ELSE 0
                    END sale_amount,
                    deal base_key,
                    ref_key
                FROM t_one_doc_sale_of_goods_services
                WHERE COALESCE(deal,'00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000'
            ) AS sale
            ON orders.ref_key = sale.base_key
        FULL JOIN
            (
            SELECT
                CASE
                    WHEN posted THEN - document_amount
                    ELSE 0
                END  return_amount,
                doc_date return_date,
                doc_number return_number,
                deal_key AS base_key,
                ref_key
            FROM t_one_doc_return_of_goods_from_customers
            WHERE COALESCE(deal_key,'00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000'
            ) AS sale_return
            ON orders.ref_key = sale_return.base_key
        FULL JOIN
            (
            SELECT
                doc_date tax_date,
                doc_number tax_number,
                CASE
                    WHEN posted THEN document_amount
                    ELSE 0
                END tax_amount,
                CASE
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(document_base_key, '00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000' THEN
                            document_base_key
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000' THEN
                            deal_key
                END sale_key
            FROM t_one_doc_tax_sale
            WHERE NOT is_consolidated_invoice
            )  AS sale_tax
            ON sale.ref_key = sale_tax.sale_key
        FULL JOIN
            (
            SELECT
                doc_date tax_return_date,
                doc_number tax_return_number,
                CASE
                    WHEN posted THEN document_amount
                    ELSE 0
                END tax_return_amount,
                CASE
                    WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                        AND COALESCE(document_base_key, '00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000' THEN
                            document_base_key
                    WHEN deal_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
                        AND COALESCE(deal_key, '00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000' THEN
                            deal_key
                END return_key
            FROM t_one_doc_tax_appendix_2
            )  AS return_tax
            ON sale_return.ref_key = return_tax.return_key
    WHERE orders.isposted
        AND orders.doc_date >= '01.01.2022'::date
        AND (
                (
                    abs(COALESCE(sale.sale_amount,0) - COALESCE(sale_tax.tax_amount,0)) > 0.5
                    AND COALESCE(sale_tax.tax_number,'') <> ''
                )
            OR (
                    abs(COALESCE(sale_return.return_amount,0) - COALESCE(return_tax.tax_return_amount,0)) > 0.5
                    AND COALESCE(return_tax.tax_return_number,'') <> ''
                )
            )
    ORDER BY orders.doc_date DESC
    ;

    COMMENT ON VIEW v_one_tax_and_sale_amount IS 'связанные с заказом документы';
    GRANT SELECT ON TABLE  v_one_tax_and_sale_amount TO user_prestige;
