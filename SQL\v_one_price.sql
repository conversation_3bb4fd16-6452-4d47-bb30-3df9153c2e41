CREATE OR REPLACE VIEW v_one_price AS
SELECT
    main.doc_date,
    main.doc_number,
    main.a_comment,
    nomenclature.description AS sku,
    price_type.description AS price_type,
    details.price,
    main.ref_key,
    nomenclature.ref_key AS nomenclature_key,
    details.price_type_key
FROM t_one_doc_price AS main
    LEFT JOIN t_one_doc_price_details details
        ON main.ref_key = details.ref_key
    INNER JOIN t_one_cat_nomenclature AS nomenclature
        ON details.nomenclature_key = nomenclature.ref_key
    LEFT JOIN t_one_cat_price_type AS price_type
        ON price_type.ref_key = details.price_type_key
WHERE main.posted
ORDER BY price_type.description, nomenclature.description, main.doc_date, main.doc_number
;
