import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_buyer_order_details"
DOCUMENT = "Document_ЗаказПокупателя_Товары"
SELECT_COLUMNS = (
    "LineNumber,КлючСтроки,Количество,КоличествоМест,Коэффициент,"
    "ПлановаяСебестоимость,Размещение,Сумма,"
    "СуммаНДС,Цена,Ref_Key,ЕдиницаИзмерения_Key,"
    "Номенклатура_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        line_number numeric NOT NULL DEFAULT 0,  -- Line_Number
        quantity numeric(10,4) NOT NULL DEFAULT 0,  -- Количество
        price numeric(10,4) NOT NULL,  -- Цена
        amount numeric(15,4) NOT NULL,  -- Сумма
        amount_vat numeric(10,4) NULL,  -- СуммаНДС
        seats_count numeric(10,4) NOT NULL DEFAULT 0,  -- КоличествоМест
        coefficient numeric(10,4) NOT NULL DEFAULT 0,  -- Коэффициент
        planned_cost numeric(10,4) NOT NULL DEFAULT 0,  -- ПлановаяСебестоимость
        string_key numeric(10,4) NOT NULL DEFAULT 0,  -- КлючСтроки
        ref_key varchar(50) NULL,  -- Ref_Key
        accommodation varchar(50) NULL,  -- Размещение
        unit_of_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
        nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
        
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'Line_Number';
    COMMENT ON COLUMN {TABLE_NAME}.string_key IS 'КлючСтроки';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.seats_count IS 'КоличествоМест';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.planned_cost IS 'ПлановаяСебестоимость';
    COMMENT ON COLUMN {TABLE_NAME}.accommodation IS 'Размещение';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';

"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}
    (
        line_number, 
        string_key, 
        quantity, 
        seats_count, 
        coefficient, 
        planned_cost, 
        accommodation, 
        amount, 
        amount_vat, 
        price, 
        ref_key, 
        unit_of_key, 
        nomenclature_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        string_key = EXCLUDED.string_key,
        quantity = EXCLUDED.quantity,
        seats_count = EXCLUDED.seats_count,
        coefficient = EXCLUDED.coefficient,
        planned_cost = EXCLUDED.planned_cost,
        accommodation = EXCLUDED.accommodation,
        amount = EXCLUDED.amount,
        amount_vat = EXCLUDED.amount_vat,
        price = EXCLUDED.price,
        ref_key= EXCLUDED.ref_key,
        unit_of_key= EXCLUDED.unit_of_key,
        nomenclature_key= EXCLUDED.nomenclature_key
        ;
    """
    return sql.replace("'", "")


async def main_doc_buyer_order_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(13)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_buyer_order_details_async())
