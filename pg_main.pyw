# запуск всех скриптов
# 09.09.2022
import asyncio
import inspect
import os
import sys
import time
import traceback

from Cash.docCashAccRegRecordtype import main_doc_cash_acc_reg_recordtype_async
from Cash.docCashCorrectionOfRegister import main_doc_cash_correction_of_register_async
from Cash.docCashMoneyCheck import main_doc_money_check_async
from Cash.docCashMovement import main_doc_cash_movement_async
from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Cash.docCashPaymentOrderExpenseDetails import main_doc_cash_order_expense_details_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderReceiptDetails import main_doc_cash_order_receipt_details_async
from Cash.docCashPaymentOrderWithdrawalOfFunds import main_doc_cash_payment_order_withdrawal_of_funds_async
from Cash.docCashPaymentOrderWithdrawalOfFundsDetails import \
    main_doc_cash_payment_order_withdrawal_of_funds_details_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashWarrantExpenseDetails import main_doc_cash_warrant_expense_details_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Cash.docCashWarrantReceiptDetails import main_doc_cash_warrant_receipt_details_async
from Catalog.InfRegBarcodes import main_info_reg_barcodes_async
from Catalog.InfRegContactInformation import main_infreg_contact_information_async
from Catalog.InfRegDocumentBarcode import main_info_reg_document_barcode_async
from Catalog.catBanks import main_cat_banks_async
from Catalog.catCash import main_cat_cash_async
from Catalog.catCashAssignment import main_cat_cash_assignment_async
from Catalog.catCashBankAccounts import main_cat_cash_bank_accounts_async
from Catalog.catCashFlowItem import main_cat_cash_clause_async
from Catalog.catContactPersonsOfCounterparties import main_cat_contact_persons_of_counterparties_async
from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catContractsCounterpartiesManagers import main_cat_counterparties_managers_async
from Catalog.catCounterparties import main_cat_counterparties_async
from Catalog.catCounterpartySegment import main_cat_counterparty_segment_async
from Catalog.catCurrency import main_cat_currencies_async
from Catalog.catEmployees import main_cat_employee_async
from Catalog.catIndividuals import main_cat_individuals_async
from Catalog.catNomenclature import main_cat_nomenclature_async
from Catalog.catNomenclatureGTD import main_cat_nomenclature_gtd_async
from Catalog.catNomenclatureSeries import main_cat_nomenclature_series_async
from Catalog.catOrganizationCodes import main_cat_organization_codes_async
from Catalog.catOrganizations import main_cat_organizations_async
from Catalog.catUKTVED import main_cat_uktved_async
from Catalog.catUnits import main_cat_units_async
from Catalog.catUnitsClassifier import main_cat_units_classifier_async
from Catalog.catUsers import main_cat_users_async
from Catalog.catWarehouse import main_cat_warehouse_async
from CreateAndRunSQLScripts import create_views
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async
from Document.docBuyerOrder import main_doc_buyer_order_async
from Document.docCorrectionSeries import main_doc_correction_series_async
from Document.docCorrectionSeriesDetails import main_doc_correction_series_details_async
from Document.docDebtCorrection import main_debt_correction_async
from Document.docDebtCorrectionDebtAmounts import main_debt_correction_debt_amount_async
from Document.docGtdImport import main_gtd_import_async
from Document.docGtdImportGoods import main_gtd_import_goods_async
from Document.docGtdImportPartitions import main_gtd_import_partitions_async
from Document.docIncomingAdditionalExpenses import main_incoming_additional_expenses_async
from Document.docIncomingAdditionalExpensesGoods import main_incoming_additional_expenses_goods_async
from Document.docInventory import main_doc_inventory_async
from Document.docMovement import main_doc_movement_async
from Document.docMovementDetails import main_doc_movement_details_async
from Document.docPayroll import main_doc_payroll_async
from Document.docPayrollEmployees import main_doc_payroll_employees_async
from Document.docPostingGoods import main_doc_posting_goods_async
from Document.docPostingOfGoodsDetails import main_doc_posting_of_goods_details_async
from Document.docReceiptOfGoodsServices import main_receipt_of_goods_services_async
from Document.docReceiptOfGoodsServicesGoods import main_receipt_of_goods_services_goods_async
from Document.docRequirementInvoice import main_doc_requirement_invoice_async
from Document.docRequirementInvoiceMaterials import main_doc_requirement_invoice_materials_async
from Document.docReturnOfGoodsFromCustomers import main_doc_return_of_goods_from_customers_async
from Document.docReturnOfGoodsFromCustomersGoods import main_doc_return_of_goods_from_customers_goods_async
from Document.docReturnOfGoodsToSupplier import main_doc_return_of_goods_to_supplier_async
from Document.docReturnOfGoodsToSupplierDetails import main_doc_return_of_goods_to_supplier_details_async
from Document.docSalary import main_doc_salary_async
from Document.docSalaryPayment import main_doc_salary_payment_async
from Document.docSalaryPaymentOfWages import main_salary_payment_of_wages_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
from Document.docSaleOfGoodsServicesGoods import main_doc_sale_of_goods_services_goods_async
from Document.docTaxAppendix2 import main_doc_tax_appendix2_async
from Document.docTaxAppendix2Details import main_doc_tax_appendix2_details_async
from Document.docTaxReceipt import main_doc_tax_receipt_async
from Document.docTaxReceiptDetails import main_doc_tax_receipt_details_async
from Document.docTaxSale import main_doc_tax_sale_async
from Document.docTaxSaleDetails import main_doc_tax_details_sale_async
from Document.docVacationOfficial import main_doc_vacation_official_async
from Document.docVacationOfficialDetails import main_doc_vacation_official_details_async
from Document.docVacationOfficialPeriod import main_doc_vacation_official_period_async
from Document.docWriteOffOfGoods import main_doc_write_off_of_goods_async
from Document.docWriteOffOfGoodsDetails import main_doc_write_off_of_goods_details_async
from ReloadPostgresql import restart_postgresql_service
# from ClientsContact import main_clients_contact # NP
# from NewPost import main_ttn
# from t_one_sale_logistics import main_one_sale_logistics
from Views.AccountsReceivable import main_accounts_receivable_async
# from prestige_authorize import CONFIG_PATH, CONFIG_PATH_NOVAPOSHTA
from async_Postgres import full_vacuum_pg_async
from createUser import main_create_user_async
from logger_prestige import get_logger
from rate_nbu import main_rate_nbu
from t_one_sale import main_sale_async
from t_one_stock import main_t_one_stock_async
from t_one_stock_short import main_t_one_stock_short_async
from tabAdditionalExpenses import main_tab_additional_expenses_async

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)


async def pg_main():
    # restart_postgresql_service()
    time_start = time.time()
    logger.info("START")
    logger.info(int(time.time() - time_start))

    # курс нбу
    main_rate_nbu()
    logger.info(int(time.time() - time_start))
    
    # # ****************************************************
    # #     Получите список всех функций в текущем модуле
    # all_functions = inspect.getmembers(sys.modules[__name__], inspect.isfunction)
    # # Отфильтруйте функции, чтобы оставить только те, которые начинаются с 'main_cat'
    # main_cat_functions = [f for name, f in all_functions if name.startswith('main_cat')]
    main_cat_functions = []
    # main_cat_functions.append(main_doc_money_check_async)
    # main_cat_functions.append(main_doc_cash_movement_async)
    # main_cat_functions.append(main_doc_cash_order_expense_details_async)
    # main_cat_functions.append(main_doc_cash_order_expense_async)
    # main_cat_functions.append(main_doc_cash_order_receipt_details_async)
    # main_cat_functions.append(main_doc_cash_order_receipt_async)
    # main_cat_functions.append(main_doc_cash_warrant_expense_details_async)
    # main_cat_functions.append(main_doc_cash_warrant_expense_async)
    # main_cat_functions.append(main_doc_cash_warrant_receipt_details_async)
    # main_cat_functions.append(main_doc_cash_warrant_receipt_async)
    #
    # main_cat_functions.append(main_doc_salary_async)
    # main_cat_functions.append(main_doc_salary_payment_async)
    # main_cat_functions.append(main_salary_payment_of_wages_async)
    # main_cat_functions.append(main_doc_tax_appendix2_details_async)
    # main_cat_functions.append(main_doc_tax_appendix2_async)
    # main_cat_functions.append(main_doc_tax_details_sale_async)
    # main_cat_functions.append(main_doc_tax_sale_async)
    # main_cat_functions.append(main_doc_tax_receipt_details_async)
    # main_cat_functions.append(main_doc_tax_receipt_async)
    # main_cat_functions.append(main_doc_buyer_order_async)
    #
    # main_cat_functions.append(main_doc_correction_series_details_async)
    # main_cat_functions.append(main_doc_correction_series_async)
    # main_cat_functions.append(main_doc_posting_of_goods_details_async)
    # main_cat_functions.append(main_doc_posting_goods_async)
    # main_cat_functions.append(main_doc_write_off_of_goods_details_async)
    # main_cat_functions.append(main_doc_write_off_of_goods_async)
    # main_cat_functions.append(main_doc_inventory_async)
    # main_cat_functions.append(main_doc_requirement_invoice_materials_async)
    # main_cat_functions.append(main_doc_requirement_invoice_async)
    # main_cat_functions.append(main_doc_return_of_goods_from_customers_async)

    # main_cat_functions.append(main_doc_movement_details_async)
    # main_cat_functions.append(main_doc_movement_async)
    # main_cat_functions.append(main_doc_return_of_goods_to_supplier_async)
    # main_cat_functions.append(main_receipt_of_goods_services_goods_async)
    # main_cat_functions.append(main_doc_return_of_goods_from_customers_goods_async)
    # main_cat_functions.append(main_doc_sale_of_goods_services_async)

    main_cat_functions.append(main_receipt_of_goods_services_async)
    main_cat_functions.append(main_info_reg_barcodes_async)
    main_cat_functions.append(main_gtd_import_async)
    main_cat_functions.append(main_gtd_import_partitions_async)
    main_cat_functions.append(main_incoming_additional_expenses_async)
    main_cat_functions.append(main_incoming_additional_expenses_goods_async)
    main_cat_functions.append(main_doc_payroll_async())
    main_cat_functions.append(main_doc_payroll_employees_async)
    main_cat_functions.append(main_doc_cash_acc_reg_recordtype_async)
    main_cat_functions.append(main_debt_correction_async)
    main_cat_functions.append(main_debt_correction_debt_amount_async)
    main_cat_functions.append(main_doc_cash_payment_order_withdrawal_of_funds_async)
    main_cat_functions.append(main_doc_cash_payment_order_withdrawal_of_funds_details_async)
    main_cat_functions.append(main_doc_cash_correction_of_register_async)
    main_cat_functions.append(main_doc_vacation_official_async)
    main_cat_functions.append(main_doc_vacation_official_period_async)
    main_cat_functions.append(main_doc_vacation_official_details_async)
    main_cat_functions.append(main_doc_reciprocal_settlements_details_async)
    main_cat_functions.append(main_accounts_receivable_async)
    # # main_cat_functions.append(main_doc_return_of_goods_to_supplier_details_async) # don't need to run
    # main_cat_functions.append(main_doc_sale_of_goods_services_goods_async)  # run single

    # Запустите все функции 'main_cat' параллельно
    await asyncio.gather(*(f() for f in main_cat_functions))

    # # Разделите список функций на группы по 10
    # groups = [main_cat_functions[n:n + 5] for n in range(0, len(main_cat_functions), 5)]
    #
    # # Запустите каждую группу функций
    # for group in groups:
    #     for f in group:
    #         try:
    #             await f()
    #         except Exception as e:
    #             tb = traceback.format_exc()
    #             print(f"Ошибка в функции {f.__name__}: {e}")
    #             print(tb)
    # # ****************************************************

    # # Catalog_Пользователи
    # await main_cat_users_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Номенклатура
    # await main_cat_nomenclature_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_НоменклатураГТД
    # await main_cat_nomenclature_gtd_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_СерииНоменклатуры
    # await main_cat_nomenclature_series_async()
    # logger.info(int(time.time() - time_start))
    #
    # # InformationRegister_Штрихкоды
    # await main_info_reg_barcodes_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Контрагенты_МенеджерыПокупателя
    # await main_cat_counterparties_managers_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Контрагенты
    # await main_cat_counterparties_async()
    # logger.info(int(time.time() - time_start))
    #
    # # InformationRegister_КонтактнаяИнформация
    # await main_infreg_contact_information_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_КонтактныеЛицаКонтрагентов
    # await main_cat_contact_persons_of_counterparties_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_ФизическиеЛица
    # await main_cat_individuals_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Организации
    # await main_cat_organizations_async()
    # logger.info(int(time.time() - time_start))
    #
    # # InformationRegister_КодыОрганизации
    # await main_cat_organization_codes_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Валюты
    # await main_cat_currencies_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_СотрудникиОрганизаций
    # await main_cat_employee_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_ЕдиницыИзмерения
    # await main_cat_units_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_КлассификаторЕдиницИзмерения
    # await main_cat_units_classifier_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Склады
    # await main_cat_warehouse_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_ДоговорыКонтрагентов
    # # main_cat_contracts_counterparties()
    # await main_cat_contracts_counterparties_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_СтатьиДвиженияДенежныхСредств
    # await main_cat_cash_clause_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Кассы
    # await main_cat_cash_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_БанковскиеСчета
    # await main_cat_cash_bank_accounts_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_Банки
    # await main_cat_banks_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_СегментыКонтрагентов
    # await main_cat_counterparty_segment_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Catalog_НазначенияНаличныхДенежныхСредств
    # await main_cat_cash_assignment_async()
    #
    # # Catalog_КлассификаторУКТВЭД
    # await main_cat_uktved_async()

    # # InformationRegister_ШтрихкодыДокументов_RecordType
    # await main_info_reg_document_barcode_async()
    #
    # # Document_ГТДИмпорт
    # await main_gtd_import_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ГТДИмпорт_Разделы
    # await main_gtd_import_partitions_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ПоступлениеДопРасходов
    # await main_incoming_additional_expenses_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПоступлениеДопРасходов_Товары
    # await main_incoming_additional_expenses_goods_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ПоступлениеТоваровУслуг
    # await main_receipt_of_goods_services_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПоступлениеТоваровУслуг_Товары
    # await main_receipt_of_goods_services_goods_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ВозвратТоваровПоставщику
    # await main_doc_return_of_goods_to_supplier_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ВозвратТоваровПоставщику_Товары
    # await main_doc_return_of_goods_to_supplier_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ПеремещениеТоваров
    # await main_doc_movement_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПеремещениеТоваров_Товары
    # await main_doc_movement_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_РеализацияТоваровУслуг.
    # await main_doc_sale_of_goods_services_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ВозвратТоваровОтПокупателя
    # await main_doc_return_of_goods_from_customers_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ВозвратТоваровОтПокупателя_Товары
    # await main_doc_return_of_goods_from_customers_goods_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ТребованиеНакладная
    # await main_doc_requirement_invoice_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ТребованиеНакладная_Материалы
    # await main_doc_requirement_invoice_materials_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ИнвентаризацияТоваровНаСкладе
    # await main_doc_inventory_async()
    # logger.info(int(time.time() - time_start))
    # # Document_СписаниеТоваров
    # await main_doc_write_off_of_goods_async()
    # logger.info(int(time.time() - time_start))
    # # Document_СписаниеТоваров_Товары
    # await main_doc_write_off_of_goods_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ОприходованиеТоваров
    # await main_doc_posting_goods_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ОприходованиеТоваров_Товары
    # await main_doc_posting_of_goods_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_КорректировкаСерийИХарактеристикТоваров
    # await main_doc_correction_series_async()
    # logger.info(int(time.time() - time_start))
    # # Document_КорректировкаСерийИХарактеристикТоваров_Товары
    # await main_doc_correction_series_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ЗаказПокупателя
    # await main_doc_buyer_order_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ЗаказПокупателя_Товары
    # # await main_doc_buyer_order_details_async() # пока нет необходимости запускать
    # # logger.info(int(time.time() - time_start))
    #
    # # Document_РегистрацияВходящегоНалоговогоДокумента
    # await main_doc_tax_receipt_async()
    # # Document_РегистрацияВходящегоНалоговогоДокумента_Товары
    # await main_doc_tax_receipt_details_async()
    #
    # # Document_НалоговаяНакладная
    # await main_doc_tax_sale_async()
    # # Document_НалоговаяНакладная_Товары
    # await main_doc_tax_details_sale_async()
    #
    # # Document_Приложение2КНалоговойНакладной
    # await main_doc_tax_appendix2_async()
    # # Document_Приложение2КНалоговойНакладной_Товары
    # await main_doc_tax_appendix2_details_async()
    #
    # # ***** касса *****
    # # Document_ЗарплатаКВыплатеОрганизаций
    # await main_doc_salary_async()
    # # Document_ЗарплатаКВыплатеОрганизаций_РаботникиОрганизации
    # await main_doc_salary_payment_async()
    # await main_salary_payment_of_wages_async()
    #
    # # Document_ПриходныйКассовыйОрдер
    # await main_doc_cash_warrant_receipt_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
    # await main_doc_cash_warrant_receipt_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_РасходныйКассовыйОрдер
    # await main_doc_cash_warrant_expense_async()
    # logger.info(int(time.time() - time_start))
    # # Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
    # await main_doc_cash_warrant_expense_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ПлатежноеПоручениеВходящее
    # await main_doc_cash_order_receipt_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
    # await main_doc_cash_order_receipt_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ПлатежноеПоручениеИсходящее
    # await main_doc_cash_order_expense_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
    # await main_doc_cash_order_expense_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ВнутреннееПеремещениеНаличныхДенежныхСредств
    # await main_doc_cash_movement_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ДенежныйЧек
    # await main_doc_money_check_async()
    # logger.info(int(time.time() - time_start))
    #
    # # телефоны клиентов (для Новой почты)
    # # main_clients_contact()
    # # logger.info(int(time.time() - time_start))
    #

    # # ****************
    # Document_РеализацияТоваровУслуг_Товары
    await main_doc_sale_of_goods_services_goods_async()  # run single
    logger.info(int(time.time() - time_start))
    #
    # Document_ГТДИмпорт_Товары
    await main_gtd_import_goods_async()
    logger.info(int(time.time() - time_start))
    #
    # # change ref_key and update additional expenses. After ПоступлениеТоваровУслуг
    # await main_tab_additional_expenses_async()
    # logger.info(int(time.time() - time_start))
    # # ******
    #

    # # Document_НачислениеЗарплатыРаботникамОрганизаций
    # await main_doc_payroll_async()
    # logger.info(int(time.time() - time_start))
    # # Document_НачислениеЗарплатыРаботникамОрганизаций_Начисления
    # await main_doc_payroll_employees_async()
    # logger.info(int(time.time() - time_start))
    #
    # # AccumulationRegister_ДенежныеСредства_RecordType
    # await main_doc_cash_acc_reg_recordtype_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_КорректировкаДолга
    # await main_debt_correction_async()
    # logger.info(int(time.time() - time_start))
    # # Document_КорректировкаДолга_СуммыДолга
    # await main_debt_correction_debt_amount_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_ПлатежныйОрдерСписаниеДенежныхСредств
    # await main_doc_cash_payment_order_withdrawal_of_funds_async()
    # logger.info(int(time.time() - time_start))
    # # Document_ПлатежныйОрдерСписаниеДенежныхСредств_РасшифровкаПлатежа
    # await main_doc_cash_payment_order_withdrawal_of_funds_details_async()
    # logger.info(int(time.time() - time_start))
    #
    # # Document_КорректировкаЗаписейРегистров
    # await main_doc_cash_correction_of_register_async()
    # logger.info(int(time.time() - time_start))
    # # ***** касса OFF*****
    #
    # # Document_НачислениеОтпускаРаботникамОрганизаций
    # await main_doc_vacation_official_async()
    #
    # # Document_НачислениеОтпускаРаботникамОрганизаций_Отпуска
    # await main_doc_vacation_official_period_async()
    #
    # # Document_НачислениеОтпускаРаботникамОрганизаций_Начисления
    # await main_doc_vacation_official_details_async()
    #
    # # AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
    # await main_doc_reciprocal_settlements_details_async()
    #
    # # дебиторка
    # await main_accounts_receivable_async()
    # # await main_create_view_return_control_async()
    #
    # # отчет по продажам (после пересоздание views)
    await main_sale_async()
    logger.info(int(time.time() - time_start))
    #
    # # остатки
    # await main_t_one_stock_async()
    # logger.info(int(time.time() - time_start))
    #
    # await main_t_one_stock_short_async()
    # logger.info(int(time.time() - time_start))
    #
    # # создание пользователей postgresql + привелегии
    # await main_create_user_async()
    # logger.info("create_user", int(time.time() - time_start))
    #
    # # ******************** Nova Poshta
    # # await main_ttn()
    # # await main_one_sale_logistics()
    # # ********************
    #
    # await main_balance_async()
    #
    # # пересоздание views
    # # await main_views_pg_async()
    # await create_views()
    # logger.info(int(time.time() - time_start))
    #
    # await full_vacuum_pg_async()

    logger.info('ALL FINISH')


if __name__ == '__main__':
    # asyncio.run(pg_main())
    asyncio.get_event_loop().run_until_complete(pg_main())