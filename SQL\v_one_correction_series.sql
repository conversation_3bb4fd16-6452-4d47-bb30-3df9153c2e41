
    CREATE OR REPLACE VIEW v_one_correction_series AS 
        SELECT 
            serv.dataversion,
            serv.doc_date::date as doc_date,
            serv.doc_date as tarih,
            serv.doc_number,	
            nom.sku,
            nom.inbox,
            dtl.quantity,
            dtl.coefficient,
            (dtl.quantity * dtl.coefficient) AS ed,
            round((dtl.quantity * dtl.coefficient) / nom.inbox,3) AS tobox,
            units.description AS unit,
            warehouse.description AS warehouse,
            org.description AS organization,
            nom.supplier,
            serv.ref_key as document_key,
            dtl.nomenclature_key,
            serv.warehouse_key,
            nom.supplier_key,
            dtl.unit_of_key,
            serv.organization_key
        FROM t_one_doc_correction_series AS serv
            INNER JOIN t_one_doc_correction_series_details AS dtl
                USING (ref_key)
            LEFT JOIN t_one_cat_warehouses AS warehouse
                ON warehouse.ref_key = serv.warehouse_key 
            LEFT JOIN v_one_nomenclature_inbox_supplier AS nom
                ON nom.nomenclature_key = dtl.nomenclature_key 
            LEFT JOIN
                (SELECT *
                FROM t_one_cat_units
                WHERE NOT deletion_mark
                )
            AS units
                ON units.ref_key = dtl.unit_of_key
            LEFT JOIN t_one_cat_organizations AS org
                ON org.ref_key = serv.organization_key 
        WHERE serv.posted = TRUE AND serv.is_management = TRUE 
        ORDER BY doc_date, doc_number, nom.sku
    ;
