# reading data from Document_ПоступлениеТоваровУслуг and add to pg in table {TABLE_NAME}
# Passes the parameter to gtdImportGoods/main_gtd_import_goods
# главная форма при заполнении документов для расчета себестоимости
#

import asyncio
import os
from datetime import datetime

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_receipt_of_goods_services"
date_start = datetime.strptime("2016-01-01", "%Y-%m-%d")
date_start = datetime.strftime(date_start, "%Y-%m-%dT00:00:01")
DOCUMENT = "Document_ПоступлениеТоваровУслуг"
SELECT_COLUMNS = (
    "DataVersion,Ref_Key,ВалютаДокумента_Key,Контрагент_Key,Date,Number,ОтражатьВБухгалтерскомУчете,"
    "ОтражатьВУправленческомУчете,Posted,КурсВзаиморасчетов,СуммаДокумента,СуммаВключаетНДС,КратностьВзаиморасчетов,"
    "Организация_Key,Сделка,СкладОрдер,Foreign_Key,ДоговорКонтрагента_Key,УчитыватьНДС,ВидОперации"
)

SQL_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NOT NULL,  -- DataVersion
            doc_date timestamp NULL,  -- Date
            doc_number varchar(50) NULL,  -- Number
            document_amount numeric(15, 4) NULL DEFAULT 0,  -- СуммаДокумента
            amount_includes_vat bool NULL DEFAULT false,  -- СуммаВключаетНДС
            rate numeric(10, 4) NULL DEFAULT 0,  -- Курс за 1уе
            rate_settlement numeric(10, 4) NULL DEFAULT 0,  -- КурсВзаиморасчетов
            rate_nbu numeric(10, 4) NULL DEFAULT 0,  -- курс НБУ
            multiplicity_of_mutual_settlements numeric(10,4) NULL DEFAULT 0,  -- КратностьВзаиморасчетов
            is_accounting bool NULL DEFAULT false,  -- ОтражатьВБухгалтерскомУчете
            is_management bool NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
            posted bool NULL DEFAULT false,  -- Posted
            consider_vat bool NULL,  -- УчитыватьНДС
            operation_types varchar(20) NULL,  -- ВидОперации
            ref_key varchar(50) NULL,  -- Ref_Key
            currency_key varchar(50) NULL,  -- ВалютаДокумента_Key
            account_key varchar(50) NULL,  -- Контрагент_Key
            organization_key varchar(50) NULL,  -- Организация_Key
            order_key varchar(50) NULL,  -- Заказ
            foreign_key varchar(50) NULL,  -- Дополнительный док
            warehouse_key varchar(50) NOT NULL,  -- Склад_Key
            contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key            
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        -- Table comments
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';        
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
        COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
        COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
        COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements IS 'КратностьВзаиморасчетов';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
        COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
        COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
        COMMENT ON COLUMN {TABLE_NAME}.rate IS 'Курс за 1 уе';
        COMMENT ON COLUMN {TABLE_NAME}.rate_settlement IS 'КурсВзаиморасчетов';
        COMMENT ON COLUMN {TABLE_NAME}.rate_nbu IS 'курс НБУ';
        COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
        COMMENT ON COLUMN {TABLE_NAME}.order_key IS 'Заказ';
        COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
        COMMENT ON COLUMN {TABLE_NAME}.foreign_key IS 'Дополнительный док';
        COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
        COMMENT ON COLUMN {TABLE_NAME}.consider_vat IS 'УчитыватьНДС';
        COMMENT ON COLUMN {TABLE_NAME}.operation_types IS 'ВидОперации';
"""

sql_create_fn_rate = f"""
    DROP FUNCTION IF EXISTS fn_rate CASCADE;
    CREATE OR REPLACE FUNCTION fn_rate()
        RETURNS TRIGGER
        LANGUAGE plpgsql
    AS $function$
        BEGIN
            -- ************** all courses - for 1 c.u.
            NEW.rate = COALESCE(NEW.rate_settlement,0) / COALESCE(NEW.multiplicity_of_mutual_settlements,1);
            
            -- ************** rate NBU
            if new.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' then -- USD
                new.rate_nbu = (SELECT COALESCE(
                                        (SELECT rate_usd_nbu 
                                        FROM t_rate_nbu 
                                        WHERE rate_date::date = new.doc_date::date
                                        ),1));
            elseif new.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' then -- EURO
                new.rate_nbu = (SELECT COALESCE(
                                        (SELECT rate_euro_nbu 
                                        FROM t_rate_nbu 
                                        WHERE rate_date::date = new.doc_date::date
                                        ),1));
            else 
                new.rate_nbu = 1;
            end if;                
              
            RETURN NEW;
                  
        END;
    $function$;
"""

sql_create_trg_receipt_service_change_rate_bfr = f"""
        DROP TRIGGER IF EXISTS trg_receipt_service_change_rate_bfr 
            ON {TABLE_NAME} CASCADE;
            
        CREATE TRIGGER trg_receipt_service_change_rate_bfr BEFORE
        INSERT
            OR
        UPDATE
            ON
            {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_rate();    
    """

sql_fn_add_to_additional_expenses = f"""
        CREATE OR REPLACE FUNCTION fn_add_to_additional_expenses()
         RETURNS trigger
         LANGUAGE plpgsql
        AS $function$
            BEGIN
            
                IF COALESCE(NEW.foreign_key,'00000000-0000-0000-0000-000000000000') 
                    != '00000000-0000-0000-0000-000000000000' THEN
                     
                    INSERT INTO t_additional_expenses(
                            ref_key_first,
                            ref_key_second
                        )
                    VALUES
                        (NEW.ref_key, NEW.foreign_key)
                    ON CONFLICT ON CONSTRAINT t_additional_expenses_unq
                    DO NOTHING;
                END IF;
                
                RETURN NEW;
            
            END;
        $function$
        ;

    """

sql_create_trg_receipt_service_add_additional_expenses_bfr = f"""
        DROP TRIGGER IF EXISTS trg_receipt_service_add_additional_expenses_bfr 
            ON {TABLE_NAME} CASCADE;
            
        CREATE TRIGGER trg_receipt_service_add_additional_expenses_bfr BEFORE
        INSERT
            OR
        UPDATE
            ON
            {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_add_to_additional_expenses();    
    """


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}(
            dataversion,
            ref_key,
            currency_key,
            account_key,
            doc_date,
            doc_number,
            is_accounting,
            is_management,
            posted,
            rate_settlement,
            document_amount,
            amount_includes_vat,
            multiplicity_of_mutual_settlements,
            organization_key,
            order_key,
            warehouse_key,
            foreign_key,
            contract_key,
            consider_vat,
            operation_types
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            dataversion = EXCLUDED.dataversion,
            currency_key = EXCLUDED.currency_key,
            account_key = EXCLUDED.account_key,
            doc_date = EXCLUDED.doc_date,
            doc_number = EXCLUDED.doc_number,
            is_accounting = EXCLUDED.is_accounting,
            is_management = EXCLUDED.is_management,
            posted = EXCLUDED.posted,
            rate_settlement = EXCLUDED.rate_settlement,
            document_amount = EXCLUDED.document_amount,
            amount_includes_vat = EXCLUDED.amount_includes_vat,
            multiplicity_of_mutual_settlements = EXCLUDED.multiplicity_of_mutual_settlements,
            organization_key = EXCLUDED.organization_key,
            order_key = EXCLUDED.order_key,
            warehouse_key = EXCLUDED.warehouse_key,
            foreign_key = EXCLUDED.foreign_key,
            contract_key = EXCLUDED.contract_key,
            consider_vat = EXCLUDED.consider_vat,
            operation_types = EXCLUDED.operation_types
        ;
        """
    return sql.replace("'", "")


async def main_receipt_of_goods_services_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    await async_save_pg(sql_create_fn_rate)
    await async_save_pg(sql_fn_add_to_additional_expenses)
    await async_save_pg(sql_create_trg_receipt_service_change_rate_bfr)
    await async_save_pg(sql_create_trg_receipt_service_add_additional_expenses_bfr)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(20)
    sql = await sql_insert(maket)
    sql = sql.replace("$5", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    await async_sql_create_index(TABLE_NAME, "account_key")
        
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_receipt_of_goods_services_async())
