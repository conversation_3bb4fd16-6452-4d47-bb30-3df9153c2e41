import asyncio
import logging
import os

from CreateAndRunSQLScripts import create_views
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

sql_fn_grant_all_views = '''
    CREATE OR REPLACE FUNCTION fn_grant_all_views(schema_name TEXT, role_name TEXT)
    RETURNS VOID AS $func$
    
    DECLARE view_name TEXT;
    
    BEGIN
    
      FOR view_name IN
        SELECT viewname FROM pg_views WHERE schemaname = schema_name
      LOOP
        -- EXECUTE 'GRANT ALL PRIVILEGES ON ' || schema_name || '.' || view_name || ' TO ' || role_name || ';';
        EXECUTE 'GRANT ALL PRIVILEGES ON ' || schema_name || '.' || view_name || ' TO ' || role_name || ';';
      END LOOP;
    
    END; $func$ LANGUAGE PLPGSQL
    '''

# revoke all privileges, before deleting a user
sql_delete_user = '''
    REVOKE ALL PRIVILEGES ON DATABASE prestige FROM user_prestige;
    REASSIGN OWNED BY user_prestige TO postgres;
    DROP OWNED BY user_prestige;
    DROP USER IF EXISTS user_prestige;
    '''

sql_create_user = '''
    DO $$
    BEGIN
        CREATE USER user_prestige WITH ENCRYPTED PASSWORD 'user_prestige';
        EXCEPTION WHEN duplicate_object THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
    END
    $$;    
    '''

GRANTS = [
    'GRANT SELECT ON TABLE t_one_sale TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_cash TO user_prestige;',
    # 'GRANT SELECT ON TABLE v_one_logistic_kiev_in_order_carrier_isempty TO user_prestige;',
    # 'GRANT SELECT ON TABLE v_one_logistic_kiev_in_order_datesend_isempty TO user_prestige;',
    # 'GRANT SELECT ON TABLE v_one_logistics_not_find TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_sale_analize TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_sale_and_salereturn TO user_prestige;',
    'GRANT SELECT ON TABLE v_service_expense TO user_prestige;',
    'GRANT SELECT ON TABLE v_service_tc_group TO user_prestige;',
    'GRANT SELECT ON TABLE v_service_tc_maliyet TO user_prestige;',
    'GRANT SELECT ON TABLE v_stickers TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_giris TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_doc_tax_gtd_sale TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_managers_and_counterparts TO user_prestige;',
    'GRANT SELECT ON TABLE t_one_stock TO user_prestige;',
    'GRANT SELECT ON TABLE t_one_accreg_cash_recordtype TO user_prestige;',
    'GRANT SELECT ON TABLE t_one_cat_nomenclature TO user_prestige;',
    'GRANT SELECT ON TABLE t_one_cat_cash_flow_item TO user_prestige;',
    'GRANT SELECT ON TABLE t_one_cat_organizations TO user_prestige;',
    'GRANT SELECT ON TABLE t_cash_assignment_group TO user_prestige;',
    'GRANT SELECT ON TABLE t_one_cat_counterparties TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_giris TO user_prestige;',
    'GRANT SELECT ON TABLE v_one_salary TO user_prestige;',
    'GRANT ALL ON TABLE t_one_stock_short TO user_prestige;',
#    'GRANT ALL ON TABLE v_one_stock_days TO user_prestige;',
#    'ALTER TABLE v_one_stock_days OWNER TO user_prestige;',
    'ALTER TABLE v_stock_days_source OWNER TO user_prestige;',
    'ALTER TABLE v_stock_days OWNER TO user_prestige;',
    'ALTER TABLE v_stock_receipt OWNER TO user_prestige;',
    'ALTER TABLE v_stock_sale OWNER TO user_prestige;',
    'ALTER TABLE v_stock_last OWNER TO user_prestige;',
    'ALTER TABLE v_stock_first OWNER TO user_prestige;',
]


async def create_user():
    await async_save_pg(sql_create_user)


async def delete_user():
    await async_save_pg(sql_delete_user)


async def grant_user():
    # await async_save_pg(sql_grant_view)
    for grant in GRANTS:
        result = await async_save_pg(grant)
        logger.info(f"{result}, {grant}")


async def main_create_user_async():
    await create_views()
    await create_user()
    await grant_user()


if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(main_create_user_async())
    logging.info('FINIS')
