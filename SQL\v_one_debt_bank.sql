DROP VIEW IF EXISTS v_one_debt_bank CASCADE;

CREATE OR REPLACE VIEW v_one_debt_bank AS
SELECT
	segment.manager,
	segment.segment_folder,
	segment.segment,
	segment.customer,
	doc_date,
	doc_number,
	amount debt,
	0 credit,
	cur.description currency,
	clause.description operation_type,
	main.is_management
FROM t_one_doc_cash_order_expense main
	LEFT JOIN
	(
		SELECT DISTINCT
			ref_key,
			cash_flow_item
		FROM t_one_doc_cash_order_expense_details
	) AS det
		ON main.ref_key = det.ref_key
	LEFT JOIN
	(
		SELECT DISTINCT
			manager,
			segment_folder,
			segment,
			customer,
			customer_key
		FROM v_one_manager_counterparty_contracts_segments
	) AS segment
		ON segment.customer_key = main.account_key
	LEFT JOIN t_one_cat_cash_flow_item AS clause
		ON det.cash_flow_item = clause.ref_key
	LEFT JOIN t_one_cat_currencies as cur
        on cur.ref_key = main.currency_key
WHERE main.isposted
--	AND main.doc_date::date >= '01.01.2024'::date
--    AND main.doc_date::date <= current_date
	AND operation_type <> 'ПереводНаДругойСчет'  -- между своими счетами
UNION ALL
SELECT
	segment.manager,
	segment.segment_folder,
	segment.segment,
	segment.customer,
	doc_date,
	doc_number,
	0 debt,
	amount credit,
	cur.description currency,
	clause.description operation_type,
	main.is_management
FROM t_one_doc_cash_order_receipt main
	INNER JOIN
	(
		SELECT DISTINCT
			ref_key,
			cash_flow_item
		FROM t_one_doc_cash_order_receipt_details
	) AS det
		ON main.ref_key = det.ref_key
	LEFT JOIN
	(
		SELECT DISTINCT
			manager,
			segment_folder,
			segment,
			customer,
			customer_key
		FROM v_one_manager_counterparty_contracts_segments
	) AS segment
		ON segment.customer_key = main.account_key
	LEFT JOIN t_one_cat_cash_flow_item AS clause
		ON det.cash_flow_item = clause.ref_key
	LEFT JOIN t_one_cat_currencies as cur
        on cur.ref_key = main.currency_key
WHERE main.posted
--	AND main.doc_date::date >= '01.01.2024'::date
--	AND main.doc_date::date <= current_date
	AND operation_type <> 'ПереводНаДругойСчет'  -- между своими счетами
ORDER BY
	manager,
	segment_folder,
	segment,
	customer,
	doc_date,
	doc_number
;

COMMENT ON VIEW v_one_debt_bank IS  'Банк: выдача и прием денег. Период с 01.01.2024 года';