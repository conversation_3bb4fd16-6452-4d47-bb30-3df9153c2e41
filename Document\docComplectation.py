import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_completion"
DOCUMENT = "Document_КомплектацияНоменклатуры"
SELECT_COLUMNS = """Ref_Key,DataVersion,Number,Date,Posted,Организация_Key,Склад_Key,
    ОтражатьВУправленческомУчете,ОтражатьВБухгалтерскомУчете,Комментарий,ВидОперации,Номенклатура_Key,Количество,
    ЕдиницаИзмерения_Key,Коэффициент,ХарактеристикаНоменклатуры_Key,СерияНоменклатуры_Key
"""

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(15) NULL,  -- DataVersion
        doc_number varchar(20) NULL,  -- Number
        doc_date timestamp NOT NULL,  -- Date
        quantity numeric(10,4) NOT NULL DEFAULT 0,  -- Количество
        posted boolean NOT NULL DEFAULT FALSE,  -- Posted
        is_management boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВУправленческомУчете
        is_accounting boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВБухгалтерскомУчете
        coefficient numeric(10,4) NOT NULL DEFAULT 0,  -- Коэффициент
        operation_type varchar(50) NULL,  -- ВидОперации
        ref_key varchar(50) NULL,  -- Ref_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        warehouse_key varchar(50) NULL,  -- Склад_Key
        description varchar(200) NULL,  -- Комментарий
        nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
        unit_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
        characteristic_key varchar(50) NULL,  -- ХарактеристикаНоменклатуры_Key
        series_key varchar(50) NULL,  -- СерияНоменклатуры_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';       
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.unit_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.characteristic_key IS 'ХарактеристикаНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.series_key IS 'СерияНоменклатуры_Key';

"""


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME} (
            ref_key, 
            dataversion, 
            doc_number, 
            doc_date, 
            posted, 
            organization_key, 
            warehouse_key, 
            is_management,
            is_accounting, 
            description, 
            operation_type, 
            nomenclature_key, 
            quantity, 
            unit_key, 
            coefficient, 
            characteristic_key, 
            series_key

        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            dataversion = EXCLUDED.dataversion,
            doc_number = EXCLUDED.doc_number,
            doc_date = EXCLUDED.doc_date,
            posted = EXCLUDED.posted,
            organization_key = EXCLUDED.organization_key,
            warehouse_key = EXCLUDED.warehouse_key,
            is_management = EXCLUDED.is_management,
            is_accounting = EXCLUDED.is_accounting,
            description = EXCLUDED.description,
            operation_type = EXCLUDED.operation_type,
            nomenclature_key = EXCLUDED.nomenclature_key,
            quantity = EXCLUDED.quantity,
            unit_key = EXCLUDED.unit_key,
            coefficient = EXCLUDED.coefficient,
            characteristic_key = EXCLUDED.characteristic_key,
            series_key = EXCLUDED.series_key
    """
    return sql.replace("'", "")


async def main_doc_complectation_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(17)
    sql = await sql_insert(maket)
    sql = sql.replace("$4,", "to_timestamp($4, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_complectation_async())
