# обходит все файлы в папке SQL и выводит зависимости между таблицами и представлениями
import os
import re
import asyncio
import aiofiles


async def get_dependencies():
    # Путь к папке SQL
    FOLDER_PATH = 'SQL'

    # Регулярное выражение для поиска таблиц и представлений
    pattern = re.compile(r'\b(FROM|JOIN|UPDATE|INTO|TABLE)\s+((t_|v_)[a-zA-Z0-9_]+)', re.IGNORECASE)

    # Получить список всех файлов .sql в папке
    sql_files = [f for f in os.listdir(FOLDER_PATH) if f.endswith('.sql')]

    dependencies = {}

    for sql_file in sql_files:
        # Сформировать полный путь к файлу
        file_path = os.path.join(FOLDER_PATH, sql_file)

        # Открыть файл и прочитать его содержимое
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as file:
            content = await file.read()

        # Найти все упоминания таблиц и представлений
        matches = pattern.findall(content)

        # Добавить найденные таблицы и представления в список зависимостей
        dependencies[sql_file] = set([match[1] for match in matches])

    return dependencies

# Запустить асинхронную функцию и вывести результат
dependencies = asyncio.run(get_dependencies())
dependencies = {k: sorted(v) for k, v in dependencies.items()}
for file, deps in dependencies.items():
    print(f"{file}: {', '.join(deps)}")