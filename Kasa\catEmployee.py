import asyncio
import os
import sys

sys.path.append(os.path.dirname(__file__))
from cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
VIEW_NAME = "v_one_employee"
DOCUMENT = "сотрудники access"
CREATE_VIEW_EMPLOYEE = f"""
    DROP VIEW IF EXISTS {VIEW_NAME};
    
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
    SELECT
        ref_key,
        description
    FROM (
        SELECT DISTINCT 
            ref_key,
            trim(description) as description,
            code::int = max(code::int) OVER(PARTITION BY trim(description)) AS is_max_code
        FROM t_one_cat_employees
        WHERE 
            NOT isfolder AND 
            NOT deletion_mark
        ) AS sub
    WHERE is_max_code
    ORDER BY description
    ;
    
    COMMENT ON VIEW {VIEW_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {VIEW_NAME}.description IS 'сотрудник';
    
    GRANT ALL ON TABLE {VIEW_NAME} TO {COMPANY_CASHIER}; --{DOCUMENT}
"""


async def main_v_cat_employee_async():
    logger.info(f"START")
    result = await async_save_pg(CREATE_VIEW_EMPLOYEE)
    logger.info(f"{result}, CREATE_VIEW_EMPLOYEE")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_v_cat_employee_async())
