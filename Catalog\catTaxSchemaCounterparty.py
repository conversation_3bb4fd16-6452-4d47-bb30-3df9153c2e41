import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_tax_schema_counterparty"
DOCUMENT = "InformationRegister_СхемыНалогообложенияКонтрагентов"
SELECT_COLUMNS = "Period,Контрагент_Key,СхемаНалогообложения_Key,ГруппаПлательщикаЕдиногоНалога"
SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id serial NOT NULL,  -- ID
        dtperiod timestamp NOT NULL,  -- Period
        counterparty_key varchar(50) NULL,  -- Контрагент_Key
        taxation_scheme_key varchar(50) NULL,  -- СхемаНалогообложения_Key
        group_of_single_tax_payer varchar(50) NULL,  -- ГруппаПлательщикаЕдиногоНалога
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (counterparty_key, taxation_scheme_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.dtperiod IS 'Period';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.taxation_scheme_key IS 'СхемаНалогообложения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.group_of_single_tax_payer IS 'ГруппаПлательщикаЕдиногоНалога';

'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME} ( 
        dtperiod, 
        counterparty_key, 
        taxation_scheme_key, 
        group_of_single_tax_payer
    )
    VALUES {maket}
    ON CONFLICT (counterparty_key, taxation_scheme_key)
    DO UPDATE SET
        dtperiod = EXCLUDED.dtperiod,
        group_of_single_tax_payer = EXCLUDED.group_of_single_tax_payer;
    ;
    '''
    return sql.replace("'", "")


async def main_cat_tax_schema_counterparty_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(4)
    sql = await sql_insert(maket)
    sql = sql.replace("$1,", "to_timestamp($1, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_tax_schema_counterparty_async())
