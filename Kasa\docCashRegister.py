import asyncio
import os
import sys

sys.path.append(os.path.dirname(__file__))
from Kasa.cashConfig import COMPANY_CASHIER, CREATE_UUID
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_cash_register"
DOCUMENT = "касса access"
SQL_CREATE_TABLE = f"""
    -- НЕ УДАЛЯТЬ!!!
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        doc_date timestamp(0) NOT NULL,  -- ДатаДок
        doc_number int4 NOT NULL,  -- Номер
        amount numeric(15, 2) NOT NULL,  -- сумма
        amount_sub numeric(15, 2) DEFAULT 0 NOT NULL,  -- сумма при обмене куплено
        payment_purpose_id uuid NULL,  -- назначение платежа
        item_id uuid NULL,  -- статья
        organization_key varchar(36) NOT NULL,  -- организация
        organization_key_sub varchar(36) NULL,  -- организация2
        currency_key varchar(36) NOT NULL,  -- валюта
        currency_key_sub varchar(36) NULL,  -- валюта2
        customer_key varchar(36) NULL,  -- клиент
        employee_key uuid NULL,  -- сотрудник
        description varchar(255) NULL,  -- примечание
        document_type varchar(25) NOT NULL,  -- тип документа
        create_user varchar(30) DEFAULT CURRENT_USER NOT NULL,
        update_user varchar(30) NULL,
        item_period date NULL,  -- к какому периоду относится
        recorder_type numeric(3) DEFAULT '-1'::integer NULL,  -- 1- приход; '-1' - расход
        CONSTRAINT {TABLE_NAME}_amount_check CHECK ((amount <> (0)::numeric)),
        CONSTRAINT {TABLE_NAME}_check CHECK ((recorder_type = ANY (ARRAY[('-1'::integer)::numeric, (1)::numeric]))),
        CONSTRAINT {TABLE_NAME}_number_unique UNIQUE (doc_number),
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_fk_currency FOREIGN KEY (currency_key) REFERENCES t_one_cat_currencies(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_currency_sub FOREIGN KEY (currency_key_sub) REFERENCES t_one_cat_currencies(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_customer FOREIGN KEY (customer_key) REFERENCES t_one_cat_counterparties(ref_key) ON DELETE RESTRICT,
--        CONSTRAINT {TABLE_NAME}_fk_employee FOREIGN KEY (employee_key) REFERENCES t_coworker(ref_key) ON DELETE RESTRICT,  -- t_coworker(ref_key) не является сотрудником ключем 
        CONSTRAINT {TABLE_NAME}_fk_item FOREIGN KEY (item_id) REFERENCES t_cash_item(id) ON DELETE RESTRICT,
--        CONSTRAINT {TABLE_NAME}_fk_organizations FOREIGN KEY (organization_key) REFERENCES t_one_cat_organizations(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_organizations_sub FOREIGN KEY (organization_key_sub) REFERENCES t_one_cat_organizations(ref_key) ON DELETE RESTRICT,
        CONSTRAINT {TABLE_NAME}_fk_payment_purpose_id FOREIGN KEY (payment_purpose_id) REFERENCES t_cash_payment_purpose(id) ON DELETE RESTRICT
    );
    
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'ДатаДок';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_sub IS 'сумма при обмене куплено';
    COMMENT ON COLUMN {TABLE_NAME}.payment_purpose_id IS 'назначение платежа';
    COMMENT ON COLUMN {TABLE_NAME}.item_id IS 'статья';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'организация';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key_sub IS 'организация2';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'валюта';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key_sub IS 'валюта2';
    COMMENT ON COLUMN {TABLE_NAME}.customer_key IS 'клиент';
    COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'сотрудник';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'примечание';
    COMMENT ON COLUMN {TABLE_NAME}.document_type IS 'тип документа';
    COMMENT ON COLUMN {TABLE_NAME}.item_period IS 'к какому периоду относится';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS '1- приход; ''-1'' - расход';
"""

SQL_CREATE_USER = f"""
    DO $$
    BEGIN
        CREATE USER {COMPANY_CASHIER} LOGIN ENCRYPTED PASSWORD 'pg_anna' NOSUPERUSER NOCREATEDB NOCREATEROLE NOINHERIT;
        EXCEPTION WHEN duplicate_object THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
    END
    $$;        
"""

SQL_GRANT = f"""
    GRANT SELECT ON TABLE t_one_cat_cash TO {COMPANY_CASHIER}; -- Catalog_Кассы
    GRANT SELECT ON TABLE t_one_cat_organizations TO {COMPANY_CASHIER}; -- Catalog_Организации
    GRANT SELECT ON TABLE t_one_cat_currencies TO {COMPANY_CASHIER}; -- Catalog_Валюты
    GRANT SELECT ON TABLE t_one_cat_counterparties TO {COMPANY_CASHIER}; -- Catalog_Контрагенты
    GRANT SELECT ON TABLE t_one_cat_counterparty_segment TO {COMPANY_CASHIER}; -- Catalog_СегментыКонтрагентов
    GRANT SELECT ON TABLE t_one_cat_contracts_counterparties TO {COMPANY_CASHIER}; -- Catalog_СегментыКонтрагентов
    GRANT SELECT ON TABLE v_one_managers_and_counterparts TO {COMPANY_CASHIER}; 
    GRANT SELECT ON TABLE v_one_customer_manager TO {COMPANY_CASHIER}; 
    GRANT ALL ON TABLE {TABLE_NAME} TO {COMPANY_CASHIER}; --{DOCUMENT}
    ALTER FUNCTION fn_{TABLE_NAME}_bfr() OWNER TO {COMPANY_CASHIER};
    GRANT ALL ON FUNCTION fn_{TABLE_NAME}_bfr() TO {COMPANY_CASHIER};
    
"""

SQL_CREATE_TRIGGER_BEFORE = f"""
    CREATE TRIGGER trg_{TABLE_NAME}_bfr BEFORE
    INSERT
        OR
    UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_{TABLE_NAME}_bfr()
    ;
"""

SQL_CREATE_TRIGGER_AFTER = f"""
    CREATE TRIGGER trg_{TABLE_NAME}_after AFTER
    INSERT
        OR
    DELETE
        OR
    UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_log_changes();

"""

SQL_CREATE_FN_BFR = f"""
    CREATE OR REPLACE FUNCTION fn_{TABLE_NAME}_bfr()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
    DECLARE
        big_amount numeric(15);
        small_amount numeric(15);
        rate varchar(20);
        clear_description varchar;
    BEGIN
        -- если обмен, в коммент ставим курс
        IF COALESCE(NEW.amount_sub,0) <> 0 THEN
            IF COALESCE(NEW.amount,0) = 0 THEN
                RAISE EXCEPTION 'Заполните сумму';
            END IF;
    
            NEW.recorder_type = -1;
            NEW.item_id = '4c61bd07-58cb-11e7-80cf-001dd8b79079'; -- обмен
            NEW.payment_purpose_id = '72327884-78a6-11e9-80ea-001dd8b79079'; -- обмен
            NEw.organization_key = '3d7e2ad1-8ac8-11e6-80c4-c936aa9c817c';
    
            IF abs(NEW.amount) > abs(NEW.amount_sub) THEN
                big_amount = NEW.amount;
                small_amount = NEW.amount_sub;
            ELSE
                big_amount = NEW.amount_sub;
                small_amount = NEW.amount;
            END IF;
    
            rate = concat('; курс: ',round(big_amount/small_amount, 2));
            clear_description = (SELECT TRIM(BOTH ';' 
                FROM REGEXP_REPLACE(REGEXP_REPLACE(description,'[; ]?курс: \d+(\.\d+)?;?','','g'),';*','','g')));
            NEW.description = concat(clear_description, rate);
    
    
        END IF ;
        -- ************ off exchane
    
        -- Проверка на существование и заполнение периода и период не заполнен
        IF EXISTS(SELECT * 
                FROM t_cash_payment_purpose 
                WHERE is_periodic 
                    AND id = NEW.payment_purpose_id) 
                    AND NEW.item_period IS NULL THEN
                    
            RAISE EXCEPTION 'заполните период';
        ELSEIF NOT EXISTS(SELECT * FROM t_cash_payment_purpose WHERE is_periodic AND id = NEW.payment_purpose_id) THEN
            NEW.item_period = current_date;
        END IF;
    
        -- Перемещение
        IF NEW.organization_key_sub IS NOT NULL THEN
            NEW.recorder_type = -1;
            NEW.item_id = 'E204DE7A-F24B-486B-973B-0F1FB73512DE'; -- перемещение
            NEW.payment_purpose_id = '92229A9B-48F1-4090-AE74-6C2A55857B22'; -- перемещение
        END IF;
    
        -- Обновление пользователя при UPDATE
        IF TG_OP = 'UPDATE' THEN
            NEW.update_user := current_user;
        END IF;
    
        -- Корректировка суммы
        NEW.amount = abs(NEW.amount) * NEW.recorder_type;
        RETURN NEW;
    END;
    $function$
    ;

"""

SQL_BACKUP_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME}_backup AS TABLE {TABLE_NAME} WITH NO DATA; -- создание таблицы резервных копий
    GRANT SELECT, INSERT ON TABLE {TABLE_NAME}_backup TO anna;
"""


async def doc_cash_register():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_FN_BFR)  # создавать до создания таблицы
    logger.info(f"{result}, SQL_CREATE_FN_BFR")
    result = await async_save_pg(CREATE_UUID)
    logger.info(f"{result}, CREATE_UUID")
    result = await async_save_pg(SQL_CREATE_TRIGGER_BEFORE)
    logger.info(f"{result}, SQL_CREATE_TRIGGER_BEFORE")
    result = await async_save_pg(SQL_CREATE_TRIGGER_AFTER)
    logger.info(f"{result}, SQL_CREATE_TRIGGER_AFTER")
    result = await async_save_pg(SQL_CREATE_TABLE)
    logger.info(f"{result}, SQL_CREATE_TABLE")
    result = await async_save_pg(SQL_BACKUP_TABLE)
    logger.info(f"{result}, SQL_BACKUP_TABLE")
    result = await async_save_pg(SQL_CREATE_USER)
    logger.info(f"{result}, SQL_CREATE_USER")
    result = await async_save_pg(SQL_GRANT)
    logger.info(f"{result}, SQL_GRANT")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(doc_cash_register())
