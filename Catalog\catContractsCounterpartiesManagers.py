import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_cat_counterparties_managers'
DOCUMENT = "Catalog_Контрагенты_МенеджерыПокупателя"
SELECT_COLUMNS = "Ref_Key,МенеджерПокупателя_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial not null,
        ref_key varchar(50),  -- Ref_Key
        manager_key  varchar(50),  -- МенеджерПокупателя_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.manager_key IS 'МеfнеджерПокупателя_Key';
    '''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        ref_key,
        manager_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        manager_key = EXCLUDED.manager_key
    ;
    '''
    return sql.replace("'", "")


async def main_cat_counterparties_managers_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(2)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_cat_counterparties_managers_async())
