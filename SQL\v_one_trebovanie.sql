
    CREATE OR REPLACE VIEW v_one_trebovanie
    AS 
    SELECT 
        goods.id, 
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        'требование'::text AS customer,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        - goods.quantity as quantity,
        goods.coefficient,
        - (goods.quantity * goods.coefficient) AS ed,
        - ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        'требование'::text AS doc_type,
        org.description as organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        NULL AS customer_key,
        NULL as currency_key ,
        nom.supplier_key,
        serv.organization_key,
        serv.warehouse_key,
        goods.line_number
    FROM t_one_doc_requirement_invoice serv
        JOIN t_one_doc_requirement_invoice_materials goods USING (ref_key)
        LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
        JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations as org ON org.ref_key = serv.organization_key
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = serv.warehouse_key
    WHERE serv.doc_date::date <= current_date
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_trebovanie IS 'требование';
    COMMENT ON COLUMN v_one_trebovanie.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_trebovanie.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_trebovanie.sku IS 'sku';
    COMMENT ON COLUMN v_one_trebovanie.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_trebovanie.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_trebovanie.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_trebovanie.doc_type IS 'тип';

    GRANT SELECT ON TABLE v_one_trebovanie TO user_prestige;       

    