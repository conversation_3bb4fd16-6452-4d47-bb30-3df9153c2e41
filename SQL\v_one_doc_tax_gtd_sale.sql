
    CREATE OR REPLACE VIEW v_one_doc_tax_gtd_sale AS 
    SELECT 
        stock.supplier,
        stock.organization,
        stock.customer,
        stock.customer_edrpou,
        stock.sku,
        stock.warehouse,
        tax_main.doc_date AS tax_date,
        tax_main.doc_number AS tax_num,
        RIGHT(tax_main.doc_number,8)::numeric AS tax_num_short,
        stock.doc_date AS selling_date,
        stock.doc_number AS selling_num,
        RIGHT(stock.doc_number,5)::numeric AS selling_num_short,
        stock.quantity,
        stock.coefficient,
        stock.quantity * stock.coefficient AS ed,
        sum(stock.quantity * stock.coefficient) OVER(PARTITION BY tax_main.organization_key, code_uktvd_key ORDER BY tax_main.doc_date, tax_det.line_number)  AS stock_ed,
        sum(stock.quantity * stock.coefficient) OVER(PARTITION BY tax_main.organization_key, left(code_uktvd_key,4) ORDER BY tax_main.doc_date, tax_det.line_number)  AS stock_ed_short,
        stock.tobox,
        stock.doc_type,
        LEFT(gtd.code,4)::numeric AS short_uktzt,
        gtd.code AS tax_code,
        tax_main.ref_key,
        tax_det.nomenclature_key,
        tax_det.code_uktvd_key
    FROM t_one_doc_tax_sale AS tax_main
        LEFT JOIN  t_one_doc_tax_sale_details AS tax_det
            ON tax_main.ref_key = tax_det.ref_key 
        LEFT JOIN t_one_stock AS stock 
            ON tax_main.document_base_key = stock.document_key
                AND tax_det.nomenclature_key = stock.nomenclature_key 
        LEFT JOIN t_one_cat_uktved AS gtd
            ON gtd.ref_key = tax_det.code_uktvd_key 
    WHERE tax_main.posted = TRUE 
        AND tax_main.doc_date >= '01.09.2022'
    ORDER BY
        tax_main.doc_date DESC,
        tax_main.doc_number,
        tax_det.line_number
    ;

