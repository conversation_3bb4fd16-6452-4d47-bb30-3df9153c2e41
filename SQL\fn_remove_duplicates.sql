CREATE OR REPLACE FUNCTION fn_remove_duplicates(p_table_name TEXT) RETURNS VOID AS $$
DECLARE
    col_list TEXT;   -- Список колонок без уникальных колонок (например, id)
BEGIN
    -- Получаем список всех колонок таблицы, исключая id, если она есть
    SELECT string_agg('"' || column_name || '"', ', ')
    INTO col_list
    FROM information_schema.columns
    WHERE table_name = p_table_name
    AND column_name != 'id';

    -- Динамически выполняем запрос для удаления дубликатов
    EXECUTE format('
        DELETE FROM %I
        WHERE ctid IN (
            SELECT ctid
            FROM (
                SELECT ctid,
                       ROW_NUMBER() OVER (PARTITION BY %s ORDER BY ctid) AS row_num
                FROM %I
            ) AS t
            WHERE t.row_num > 1
        );', p_table_name, col_list, p_table_name);

    RAISE NOTICE 'Duplicates removed from table %', p_table_name;
END;
$$ LANGUAGE plpgsql;
