
    CREATE OR REPLACE VIEW v_one_nomenclature_inbox_supplier AS
    SELECT DISTINCT 
        nom.sku,
        supl.supplier,
        nom.unit,
        nom.inbox,
        nom.count_of_months_of_storage,
        nom.nomenclature_key,
        nom.units_key,
        supl.supplier_key
       FROM (
            SELECT 
                t.nomenclature_key,
                t.supplier,
                t.supplier_key
            FROM ( SELECT serv.nomenclature_key,
                    serv.tarih = max(serv.tarih) OVER (PARTITION BY serv.nomenclature_key) AS max_date,
                    serv.client AS supplier,
                    serv.account_key AS supplier_key
                    FROM v_service_tc_maliyet serv
                ) AS t
            WHERE t.max_date = TRUE        
            ) supl
         RIGHT JOIN (
                    SELECT *
                    FROM (
                        SELECT t.sku,
                            t.unit,
                            t.inbox,
                            t.nomenclature_key,
                            t.units_key,
                            t.count_of_months_of_storage,
                            (t.units_key = FIRST_VALUE(t.units_key) OVER(PARTITION BY t.nomenclature_key)) AS first_unit
                       FROM ( SELECT units.description AS unit,
                                units.coefficient AS inbox,
                                units.coefficient = max(units.coefficient) OVER (PARTITION BY units.nomenclature_key)
                                AS max_units,
                                nom_1.description AS sku,
                                nom_1.ref_key AS nomenclature_key,
                                units.ref_key AS units_key,
                                nom_1.count_of_months_of_storage
                               FROM t_one_cat_nomenclature nom_1
                                 LEFT JOIN
                                 (
                                    SELECT *
                                    FROM t_one_cat_units
                                    WHERE NOT deletion_mark
                                 )
                                  AS units
                                    ON units.nomenclature_key::text = nom_1.ref_key::text
                              WHERE NOT units.deletion_mark AND (nom_1.ref_key::text IN ( SELECT nom_2.ref_key
                                       FROM t_one_cat_nomenclature nom_2
                                      WHERE nom_2.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
                             ) t
                      WHERE t.max_units = TRUE) AS all_first_unit
                      WHERE first_unit = TRUE 
                              ) nom ON supl.nomenclature_key::text = nom.nomenclature_key::text
      ORDER BY nom.sku;

    COMMENT ON VIEW v_one_nomenclature_inbox_supplier IS 'номекл, поставщик, кол-во ед в коробке';
    