
    CREATE TABLE IF NOT EXISTS t_cash_bank (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        doc_date timestamp NOT NULL,  -- ДатаДок
        doc_number varchar(20) NOT NULL,  -- Номер
        amount numeric(15, 2) NOT NULL,  -- сумма
        amount_total numeric(15, 2) DEFAULT 0 NOT NULL,  -- сумма с накоплением
        payment_purpose_id uuid NULL,  -- назначение платежа
        item_id uuid NULL,  -- статья
        organization_key varchar(36) NOT NULL,  -- организация
        currency_key varchar(36) NOT NULL,  -- валюта
        customer_key varchar(36) NULL,  -- клиент
        employee_key uuid NULL,  -- сотрудник
        description varchar NULL,  -- примечание
        document_type varchar(25) NULL,  -- тип документа
        create_user varchar(30) DEFAULT CURRENT_USER NOT NULL,
        update_user varchar(30) NULL,
        recorder_type numeric(3) DEFAULT '-1'::integer NULL,  -- 1- приход; '-1' - расход
        CONSTRAINT t_cash_bank_amount_check CHECK ((amount <> (0)::numeric)),
        CONSTRAINT t_cash_bank_check CHECK ((recorder_type = ANY (ARRAY[('-1'::integer)::numeric, (1)::numeric]))),
        CONSTRAINT t_cash_bank_number_unique UNIQUE (doc_number),
        CONSTRAINT t_cash_bank_pk PRIMARY KEY (id),
        CONSTRAINT t_cash_bank_fk_currency FOREIGN KEY (currency_key) REFERENCES t_one_cat_currencies(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_bank_fk_customer FOREIGN KEY (customer_key) REFERENCES t_one_cat_counterparties(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_bank_fk_employee FOREIGN KEY (employee_key) REFERENCES t_one_cat_employees(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_bank_fk_item FOREIGN KEY (item_id) REFERENCES t_cash_item(id) ON DELETE RESTRICT,
        CONSTRAINT t_cash_bank_fk_organizations FOREIGN KEY (organization_key) REFERENCES t_one_cat_organizations(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_bank_fk_payment_purpose_id FOREIGN KEY (payment_purpose_id) REFERENCES t_cash_payment_purpose(id) ON DELETE RESTRICT
    );
    
    COMMENT ON COLUMN t_cash_bank.doc_date IS 'ДатаДок';
    COMMENT ON COLUMN t_cash_bank.doc_number IS 'Номер';
    COMMENT ON COLUMN t_cash_bank.amount IS 'сумма';
    COMMENT ON COLUMN t_cash_bank.amount_total IS 'суммаИтоговая';
    COMMENT ON COLUMN t_cash_bank.payment_purpose_id IS 'назначение платежа';
    COMMENT ON COLUMN t_cash_bank.item_id IS 'статья';
    COMMENT ON COLUMN t_cash_bank.organization_key IS 'организация';
    COMMENT ON COLUMN t_cash_bank.currency_key IS 'валюта';
    COMMENT ON COLUMN t_cash_bank.customer_key IS 'клиент';
    COMMENT ON COLUMN t_cash_bank.employee_key IS 'сотрудник';
    COMMENT ON COLUMN t_cash_bank.description IS 'примечание';
    COMMENT ON COLUMN t_cash_bank.document_type IS 'тип документа';
    COMMENT ON COLUMN t_cash_bank.recorder_type IS '1- приход; -1 - расход';

-- Table Triggers

CREATE TRIGGER t_cash_bank AFTER
INSERT
    OR
DELETE
    OR
UPDATE
    ON
    t_cash_bank FOR EACH ROW EXECUTE FUNCTION fn_log_changes();
	
--CREATE TRIGGER t_cash_bank_bfr BEFORE
--INSERT
--    OR
--UPDATE
--    ON
--    t_cash_bank FOR EACH ROW EXECUTE FUNCTION fn_t_cash_bank_bfr();

-- Permissions

ALTER TABLE t_cash_bank OWNER TO postgres;
GRANT ALL ON TABLE t_cash_bank TO postgres;
GRANT ALL ON TABLE t_cash_bank TO anna;