import sys
import pandas as pd
from PyQt5 import QtWidgets, QtCore

class DataFrameViewer(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DataFrame Viewer")
        self.setGeometry(100, 100, 800, 600)

        self.central_widget = QtWidgets.QWidget(self)
        self.setCentralWidget(self.central_widget)

        self.table_view = QtWidgets.QTableView(self.central_widget)
        self.table_view.setGeometry(10, 10, 780, 580)

        self.load_data_button = QtWidgets.QPushButton("Load Data", self.central_widget)
        self.load_data_button.setGeometry(10, 600, 100, 30)
        self.load_data_button.clicked.connect(self.load_data)

    def load_data(self):
        file_dialog = QtWidgets.QFileDialog.getOpenFileName(self, "Open CSV File", "", "CSV Files (*.csv)")
        if file_dialog[0]:
            file_path = file_dialog[0]
            df = pd.read_csv(file_path)
            model = PandasModel(df)
            self.table_view.setModel(model)

class PandasModel(QtCore.QAbstractTableModel):
    def __init__(self, data_frame):
        super().__init__()
        self.data_frame = data_frame

    def rowCount(self, parent=None):
        return len(self.data_frame)

    def columnCount(self, parent=None):
        return len(self.data_frame.columns)

    def data(self, index, role=QtCore.Qt.DisplayRole):
        if role == QtCore.Qt.DisplayRole:
            return str(self.data_frame.iloc[index.row(), index.column()])
        return None

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = DataFrameViewer()
    window.show()
    sys.exit(app.exec_())
