import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_infreg_document_barcode"
DOCUMENT = "InformationRegister_ШтрихкодыДокументов_RecordType"
SELECT_COLUMNS = "Recorder,Recorder_Type,Штрихкод"

SQL_CREATE_TABLE = f'''
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        recorder varchar(50) NOT NULL,
        recorder_type varchar NOT NULL,
        barcode varchar(25) NOT NULL,
         CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (recorder)
        );

        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.recorder IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS 'Тип документа';
        COMMENT ON COLUMN {TABLE_NAME}.barcode IS 'штрихкод';
    '''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        recorder,
        recorder_type,
        barcode
        ) VALUES {maket}
    ON CONFLICT (recorder)
    DO UPDATE SET
        recorder_type = EXCLUDED.recorder_type,
        barcode = EXCLUDED.barcode
    ;
    '''
    return sql.replace("'", "")


async def main_info_reg_document_barcode_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(3)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "barcode")
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_info_reg_document_barcode_async())
