
    CREATE OR REPLACE VIEW v_one_clients_contact AS 
    SELECT DISTINCT 
        t.*,
        left(phone,3) as operator_code
    FROM (
        SELECT 
            concat(inf_reg_phone.description,
                cat_cp.description, 
                cat_cpc.description, 
                cat_c.description, 
                cat_org.description,
                cat_user.description,
                cat_ind.description
            ) AS client,
            concat('38',tcc.phone) AS phone, 
            tcc.refkey,
            tcc.catalog_source
        FROM t_clients_contact AS tcc
            LEFT JOIN t_one_cat_contact_person AS cat_cp -- Catalog_КонтактныеЛица
                ON tcc.refkey = cat_cp.ref_key 
            LEFT JOIN t_one_cat_contact_persons_of_counterparties AS cat_cpc -- Catalog_КонтактныеЛицаКонтрагентов
                ON tcc.refkey = cat_cpc.ref_key 
            LEFT JOIN t_one_cat_counterparties AS cat_c -- Catalog_Контрагенты
                ON tcc.refkey = cat_c.ref_key 
            LEFT JOIN t_one_cat_organizations AS cat_org -- Catalog_Организации
                ON tcc.refkey = cat_org.ref_key 
            LEFT JOIN t_one_cat_users AS cat_user -- Catalog_Пользователи
                ON tcc.refkey = cat_user.ref_key
            LEFT JOIN t_one_cat_individuals AS cat_ind -- Catalog_ФизическиеЛица
                ON tcc.refkey = cat_ind.ref_key
            LEFT JOIN v_one_infreg_contact_information_phone AS inf_reg_phone ON inf_reg_phone.ref_key = cat_c.ref_key
        ) AS t
    WHERE client <> '' 
    ORDER BY client;

