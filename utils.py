import shutil
import glob
import patoolib  # pip install patool
from datetime import datetime
import requests
from prestige_authorize import send_file

dt = datetime.strftime(datetime.today(), "%Y.%m.%d %H%M")


def sen_documnet_by_telegram():
    filename = r"d:\Prestige\Престиж Продукт_К_backup\1Cv8_2022.10.14 1024.rar"
    # send_file(filename)
    test_link = "https://www.bbb.org/washington-dc-eastern-pa/business-reviews/online-education" \
                "/k12-inc-in-herndon-va-190911943/#sealclick"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:66.0) Gecko/20100101 Firefox/66.0",
        "Accept-Encoding": "*",
        "Connection": "keep-alive"
    }

    test_link = "https://www.bbb.org/washington-dc-eastern-pa/business-reviews/" \
                "online-education/k12-inc-in-herndon-va-190911943/#sealclick"
    page = requests.get(test_link, headers=headers)
    print(page)


def get_files(folder_path):
    # Используйте модуль glob для поиска файлов с расширением .py в указанной папке
    py_files = glob.glob(f'{folder_path}/*.py')

    # Выведите список найденных файлов
    print("Список файлов с расширением .py:")
    for file in py_files:
        print(file)


def main_urils():
    src = r"c:\1С\1C_Bases\Престиж Продукт_К\1Cv8.1CD"
    new_file_name = r"\1Cv8_%s" % dt
    dst_path = r"d:\Prestige\Престиж Продукт_К_backup"  # 1Cv8_%s.1CD
    dst = dst_path + new_file_name + ".1CD"
    zip_file_name = dst_path + new_file_name + ".rar"
    # 2nd option
    shutil.copy(src, dst)

    patoolib.create_archive(zip_file_name, (dst,))

    send_file(zip_file_name)

    print(dst, "OK")


if __name__ == '__main__':
    get_files(r"D:\Prestige\Python\Prestige\Catalog")
    sen_documnet_by_telegram()
