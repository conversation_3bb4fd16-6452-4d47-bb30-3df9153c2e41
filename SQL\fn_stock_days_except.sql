CREATE OR REPLACE FUNCTION fn_stock_days_except(date_first date, date_last date, min_stock integer)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
    DECLARE
        sql_query TEXT;
    BEGIN

        IF date_last::date > current_date::date THEN
            date_last = current_date::date::text;
        END IF;

        RAISE NOTICE '%', (date_last::date > current_date);
        EXECUTE 'DROP VIEW IF EXISTS v_one_stock_days_except CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_days_source CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_days CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_first CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_last CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_receipt CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_sale_except CASCADE';
        EXECUTE 'DROP VIEW IF EXISTS v_stock_days_series CASCADE';

        PERFORM  fn_stock_days(date_first, date_last, min_stock);

        sql_query = 'CREATE OR REPLACE VIEW v_stock_first AS
            SELECT stock.*
            FROM (
                SELECT DISTINCT
                    supplier, sku, doc_date::date,
                    sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) AS balance_first,
                    sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * entry_price AS entry_amount_first,
                    sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * maliyet AS amount_maliyet_first,
                    doc_date = max(doc_date) OVER (PARTITION BY sku) AS is_max_doc_date
                FROM t_one_stock
                WHERE warehouse_key IN
                    (
                    ''4b40b865-6d2f-11ec-8125-001dd8b72b55'',
                    ''381c5d92-4acb-11ed-8148-001dd8b72b55''
                    )
                    AND (doc_date::Date < ''' || date_first || '''::date)
                )AS stock
            WHERE is_max_doc_date
            ;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = 'CREATE OR REPLACE VIEW v_stock_last AS
            SELECT stock.*
            FROM (
                    SELECT DISTINCT
                        supplier, sku, doc_date::date,
                        sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) AS balance_last,
                        sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * entry_price AS entry_amount_last,
                        sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * maliyet AS amount_maliyet_last,
                        doc_date = max(doc_date) OVER (PARTITION BY sku) AS is_max_doc_date
                    FROM t_one_stock
                    WHERE warehouse_key IN (
                        ''4b40b865-6d2f-11ec-8125-001dd8b72b55'',
                        ''381c5d92-4acb-11ed-8148-001dd8b72b55''
                        )
                        AND (doc_date::Date <= ''' || date_last || '''::date)
                )AS stock
            WHERE is_max_doc_date
            ;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = 'CREATE OR REPLACE VIEW v_stock_sale_except AS
            SELECT	DISTINCT sku, -sum(tobox) AS tobox_sale, -sum(entry_amount) AS entry_amount_sale,
                -sum(amount_maliyet) AS amount_maliyet_sale
            FROM t_one_stock
            WHERE doc_type IN (''продажа'', ''продажа возврат'')
                AND doc_date::date >= ''' || date_first || '''::date
                AND doc_date::date <= ''' || date_last || '''::date
                AND organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55''
                AND customer_key IN (
                    ''53dd1e12-c245-11ed-816c-001dd8b740bc'',  -- ПІІ "АМІК УКРАЇНА"
                    ''68025e4f-0a14-11eb-80fb-001dd8b72b55'',  --  ПП "СТИСАН"
                    ''4a6002d7-3fc1-11ed-8146-001dd8b72b55'',  -- ТОВ "АЛЬЯНС ХОЛДИНГ"
                    ''8ad87ba4-ed71-11ed-8182-001dd8b740bc'',  -- ТОВ "ДАФНАМЕКС"
                    ''c23eea7e-8c42-11ed-815c-001dd8b740bc'',  -- ТОВ "ЕКСОПТ"
                    ''186974d0-d0f4-11ec-8132-001dd8b740bc'',  -- ТОВ "ЕКСПАНСІЯ"
                    ''a07cc46d-12a5-11eb-80fb-001dd8b72b55'',  -- ТОВ "ЄВРО СМАРТ ПАУЕР"
                    ''320cda87-b8b8-11eb-8107-001dd8b72b55'',  -- ТОВ "ІНТЕРФЛО ІНДАСТРІЄЗ УКРАЇНА"
                    ''07303997-1779-11eb-80fb-001dd8b72b55'',  -- ТОВ "КОНВЕРС ЛІНКС"
                    ''b724034e-bb70-11ea-80f8-001dd8b79079'',  -- ТОВ "ЛЕРСЕН ЛТД"
                    ''e3c35262-77d3-11ea-80ef-001dd8b79079'',  -- ТОВ "ЛЮКСВЕН РІТЕЙЛ"
                    ''4ca29579-eaf7-11ec-8135-001dd8b740bc'',  -- ТОВ "МЕТРО Кеш енд Кері Україна"
                    ''c096c783-851a-11ed-815a-001dd8b740bc'',  -- ТОВ "МОТТО РЕНТАЛ"
                    ''c69ee59b-6fce-11ed-8155-001dd8b72b55'',  -- ТОВ "МОТТО РІТЕЙЛ"
                    ''80152341-7b03-11ea-80ef-001dd8b79079'',  -- ТОВ "СІЛЬПО-ФУД"
                    ''eda14a1b-0428-11e9-80e9-001dd8b79079'',  -- ТОВ "СОКАР ПЕТРОЛЕУМ" ЗРУ 358/193
                    ''943a8095-0210-11e9-80e8-001dd8b79079'',  -- ТОВ "СОКАР ПЕТРОЛЕУМ" КРУ 356/194
                    ''eda14a05-0428-11e9-80e9-001dd8b79079'',  -- ТОВ "СОКАР ПЕТРОЛЕУМ" ПРУ 355/192
                    ''eda14a14-0428-11e9-80e9-001dd8b79079'',  -- ТОВ "СОКАР ПЕТРОЛЕУМ" СРУ 359/191
                    ''eda14a21-0428-11e9-80e9-001dd8b79079'',  -- ТОВ "СОКАР ПЕТРОЛЕУМ" ЧРУ 357/190
                    ''25f0dd44-86b8-11ed-815a-001dd8b740bc'',  -- ТОВ "ФАСОЛЬ"
                    ''dee96c2f-8752-11ed-815a-001dd8b740bc'',  -- ТОВ "ФОЗЗІ КОММЕРЦ"
                    ''f3dfb96e-bedc-11ec-8130-001dd8b740bc''  -- ТОВ "ФОРА"
                    )
            GROUP BY sku;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = 'CREATE OR REPLACE VIEW v_stock_receipt AS
            SELECT	DISTINCT sku,
                sum(tobox) AS tobox_receipt, sum(entry_amount) AS entry_amount_receipt,
                sum(amount_maliyet) AS amount_maliyet_receipt
            FROM t_one_stock
            WHERE doc_type IN (''поступление'', ''поступление возврат'')
                AND doc_date::date >= ''' || date_first || '''::date
                AND doc_date::date <= ''' || date_last || '''::date
                AND organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55''
            GROUP BY sku;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = '
            CREATE OR REPLACE VIEW v_stock_days_source AS
            SELECT supplier, sku, doc_date, doc_date_previos, stock, stock_previos,
                CASE
                    WHEN count(*) OVER (PARTITION BY sku) = 1 AND stock >= ''' || min_stock || ''' THEN
                        ''' || date_last || '''::date - doc_date_previos
                    WHEN stock_previos < ''' || min_stock || ''' THEN 0
                    ELSE doc_date-doc_date_previos
                END as days
            FROM (
                SELECT
                    supplier, sku,
                    CASE
                        WHEN  t.doc_date = max(t.doc_date) OVER(partition by sku)
                            AND t.doc_date < ''' || date_last || '''::date AND stock >=''' || min_stock || '''
                             AND stock_previos >= ''' || min_stock || ''' THEN ''' || date_last || '''::date
                        ELSE  t.doc_date
                    END as doc_date,
                    CASE
                        WHEN count(*) OVER (PARTITION BY t.sku) = 1
                            AND t.stock >= ''' || min_stock || '''::numeric
                            AND t.stock_previos < ''' || min_stock || '''::numeric THEN
                                doc_date_last_receipt - 1
                        WHEN count(*) OVER (PARTITION BY t.sku) = 1
                            AND t.stock < ''' || min_stock || '''::numeric
                            AND t.stock_previos >= ''' || min_stock || '''::numeric THEN
                                doc_date_last_sale - 1
                        WHEN  t.doc_date_previos < ''' || date_first || '''::date THEN ''' || date_first || '''::date
                        ELSE  t.doc_date_previos
                    END as doc_date_previos,
                    tobox, stock, stock_previos
                FROM t_one_stock_short AS t WHERE t.doc_date >= ''' || date_first || '''::date
                    AND t.doc_date <= ''' || date_last || '''::date
            ) AS tt
            ORDER BY sku, doc_date;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = 'CREATE OR REPLACE VIEW v_stock_days AS
            SELECT supplier, sku, COALESCE(SUM(days),0) AS total_days
            FROM v_stock_days_source
            GROUP BY supplier, sku;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = 'CREATE OR REPLACE VIEW v_one_stock_days_except AS
            SELECT DISTINCT stock_last.supplier,nomenclature.sku,
            COALESCE(stock_first.balance_first,0) AS начОст,
            COALESCE(stock_first.entry_amount_first,0) AS начОстВх,
            COALESCE(stock_first.amount_maliyet_first,0) AS начОстСсть,
            COALESCE(receipt.tobox_receipt,0) AS прихКрб,
            COALESCE(receipt.entry_amount_receipt,0) AS прихВх,
            COALESCE(receipt.amount_maliyet_receipt,0) AS прихСсть,
            COALESCE(sale.tobox_sale,0) AS продКрб,
            COALESCE(sale.entry_amount_sale,0) AS продВх,
            COALESCE(sale.amount_maliyet_sale,0) AS продСсть,
            COALESCE(stock_last.balance_last,0) AS конОст,
            COALESCE(stock_last.entry_amount_last,0) AS конВх,
            COALESCE(stock_last.amount_maliyet_last,0) AS конСсть,
            CASE
                WHEN COALESCE(total_days,0) = 0 AND COALESCE(receipt.tobox_receipt,0) < ''' || min_stock || '''
                    AND COALESCE(stock_last.balance_last,0) < ''' || min_stock || ''' THEN
                        0
                WHEN COALESCE(total_days,0) = 0 AND COALESCE(sale.tobox_sale,0) >= ''' || min_stock || ''' THEN
                    1
                 WHEN COALESCE(stock_first.balance_first,0) >= ''' || min_stock || '''
                    AND COALESCE(stock_last.balance_last,0) >= ''' || min_stock || '''
                    AND COALESCE(total_days,0) = 0
                    THEN
                        ''' || date_last || '''::date - ''' || date_first || '''::date
                 ELSE COALESCE(total_days,0)
            END AS дней
            FROM (SELECT DISTINCT description AS sku FROM t_one_cat_nomenclature
                    WHERE item_type_key::text = ''e5827eaf-8f86-11e6-80c4-c936aa9c817c'') as nomenclature
            LEFT JOIN v_stock_last as stock_last ON stock_last.sku = nomenclature.sku
            LEFT JOIN v_stock_first AS stock_first ON nomenclature.sku = stock_first.sku
            LEFT JOIN v_stock_sale_except AS sale ON nomenclature.sku = sale.sku
            LEFT JOIN v_stock_receipt AS receipt ON nomenclature.sku = receipt.sku
            LEFT JOIN v_stock_days AS count_days ON nomenclature.sku = count_days.sku
            WHERE (abs(COALESCE(stock_first.balance_first,0)) + abs(COALESCE(receipt.tobox_receipt,0)) +
               abs(COALESCE(sale.tobox_sale,0)) + abs(COALESCE(stock_last.balance_last, 0))) > 0.005
            ORDER BY nomenclature.sku;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        sql_query = 'CREATE OR REPLACE VIEW v_stock_days_series AS
            SELECT days.*, stock.balance_last AS остПоСерии, stock.sell_by as годенДо, entry_amount_last,
                amount_maliyet_last
            FROM v_one_stock_days AS days
                LEFT JOIN
                (SELECT DISTINCT supplier, sku,
                    sum(tobox) OVER (PARTITION BY sku, nomenclature_series_key) AS balance_last,
                    sum(tobox) OVER (PARTITION BY sku, nomenclature_series_key) * entry_price AS entry_amount_last,
                    sum(tobox) OVER (PARTITION BY sku, nomenclature_series_key) * maliyet AS amount_maliyet_last,
                    doc_date = max(doc_date) OVER (PARTITION BY sku, sell_by) AS is_max_doc_date, sell_by
                FROM t_one_stock
                WHERE warehouse_key IN (''4b40b865-6d2f-11ec-8125-001dd8b72b55'',
                        ''381c5d92-4acb-11ed-8148-001dd8b72b55'')
                    AND (doc_date::date <= ''' || date_last || '''::date)
                ) AS stock
                ON stock.sku = days.sku
            WHERE is_max_doc_date AND abs(COALESCE(balance_last,0)) > 0.005
            ORDER BY supplier, stock.sku, sell_by
            ;';
        RAISE NOTICE '%', sql_query;
        EXECUTE sql_query;

        EXECUTE 'ALTER TABLE v_one_stock_days_except OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_one_stock_days OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_days_source OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_days OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_receipt OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_sale_except OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_last OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_first OWNER TO user_prestige;';
        EXECUTE 'ALTER TABLE v_stock_days_series OWNER TO user_prestige;';
        EXECUTE 'GRANT SELECT ON TABLE v_one_stock_days_except TO user_prestige;';

        RETURN;

    END;
    $function$
;

SELECT fn_stock_days_except((current_date - interval '1 month' * 2)::date, current_date::date, 2);
