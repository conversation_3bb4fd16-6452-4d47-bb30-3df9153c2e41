import os
import pandas as pd
import asyncio
from json import loads, dumps

from prestige_authorize import send_request
from configPrestige import URL_CONST
from multiThread import get_json_from_url

pd.set_option('float_format', '{:.2f}'.format)
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Document_РеализацияТоваровУслуг"
SELECT_COLUMNS = "Date,Number,СтруктурнаяЕдиница,СтруктурнаяЕдиница_Type,ВалютаДокумента_Key,ДоговорКонтрагента_Key," \
                 "Контрагент_Key,КратностьВзаиморасчетов,КурсВзаиморасчетов,Организация_Key,Ответственный_Key," \
                 "Склад_Key,СуммаВключаетНДС,СуммаДокумента,УчитыватьНДС,Комментарий,КонтактноеЛицоКонтрагента_Key," \
                 "DeletionMark,Товары"


async def get_response(ref_key: str):
    url = f"http://192.168.1.254/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    filter_ref_key = f"&$filter=Ref_Key eq guid'{ref_key}'"
    url += filter_ref_key
    response = await get_json_from_url(url)
    return response


async def det_child_df(df):
    result = []
    for i, row in df.iterrows():
        result.append({"LineNumber": i + 1,
                       "ЕдиницаИзмерения_Key": "29731aa8-3989-11ed-8145-001dd8b72b55",
                       "ЕдиницаИзмеренияМест_Key": "29731aa8-3989-11ed-8145-001dd8b72b55",
                       "Качество_Key": "d05404a0-6bce-449b-a798-41ebe5e5b977",
                       "Количество": row['Количество'],
                       "КоличествоМест": f"{row['Количество']}",
                       "Коэффициент": row['Коэффициент'],
                       "Номенклатура_Key": row.Номенклатура_Key,
                       "ПроцентСкидкиНаценки": 0,
                       "СерияНоменклатуры_Key": row['СерияНоменклатуры_Key'],
                       "СпособСписанияОстаткаТоваров": "СоСклада",
                       "СтавкаНДС": "НДС20",
                       "Сумма": row['Сумма_с_НДС'],
                       "СуммаНДС": row['НДС'],
                       "Цена": row['с-сть'],
                       "Склад_Key": "4b40b865-6d2f-11ec-8125-001dd8b72b55",
                       "СчетУчетаБУ_Key": "08421d76-5023-4a27-b2c8-ed89b0dc904e",
                       "ПереданныеСчетУчетаБУ_Key": "cf0e025d-91d6-4633-92cb-8f0dbe177b0f",
                       "СхемаРеализации_Key": "9f9155f1-fb07-45ad-a842-b7361abf89db",
                       "НалоговоеНазначение_Key": "0b176d4b-a1b3-48ec-abc4-7e927658eb50",
                       })
    return result


async def get_head_json(head_df):
    return {"DeletionMark": True,
            "Date": f"{head_df['date'].values.item()}T00:00:01",
            "ВидОперации": "ПродажаКомиссия",
            "Организация_Key": "f51a0e76-4821-11ec-811d-001dd8b72b55",
            "БанковскийСчетОрганизации_Key": "f51a0e77-4821-11ec-811d-001dd8b72b55",
            "ОтражатьВУправленческомУчете": True,
            "ОтражатьВБухгалтерскомУчете": True,
            "УдалитьОтражатьВНалоговомУчете": False,
            "Комментарий": f"автозагрузка прихода {head_df['number'].values.item()}",
            "Склад_Key": "4b40b865-6d2f-11ec-8125-001dd8b72b55",
            "ДоговорКонтрагента_Key": "f51a0e82-4821-11ec-811d-001dd8b72b55",
            "Контрагент_Key": "2c04025b-b29a-11e9-80ea-001dd8b79079",
            "ВалютаДокумента_Key": "3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c",
            "УчитыватьНДС": True,
            "СуммаВключаетНДС": True,
            "КурсВзаиморасчетов": 1,
            "АвторасчетНДС": True,
            "СуммаДокумента": head_df['суммадокумента'].values.item(),
            "КратностьВзаиморасчетов": "1",
            "Ответственный_Key": "d0c4c6db-28eb-11e8-80de-001dd8b79079",
            "ВидПередачи": "СоСклада",
            "СчетУчетаРасчетовСКонтрагентом_Key": "9403ee34-c752-42b3-8596-608f1dcf9ae6",
            "СчетУчетаРасчетовПоАвансам_Key": "9403ee34-c752-42b3-8596-608f1dcf9ae6",
            "СчетУчетаНДС_Key": "942f4c03-83a1-4c3d-a9a7-9f65e628f4c2",
            "СчетУчетаНДСПодтвержденный_Key": "fd6c1bb6-9b52-4901-a0dd-830dd99c9bef",
            "Товары": ''}


async def main_create_sale_async(df_source_head: pd.DataFrame(), df_source_details: pd.DataFrame()):
    json_date = await get_head_json(df_source_head)
    json_date['Товары'] = await det_child_df(df_source_details)
    url = URL_CONST + DOCUMENT
    response = send_request(url=url, method="POST", data_json=json_date)
    result = response.json() if response.status_code in [200, 201] else False
    return result


if __name__ == "__main__":
    asyncio.run(main_create_sale_async("5f0cad5b-ad50-11ed-8169-001dd8b72b55"))
