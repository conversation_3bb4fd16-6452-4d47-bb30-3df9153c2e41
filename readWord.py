import os
import re

import unidecode
from docx import DOCUMENT


def read_file(filename):
    doc = DOCUMENT(filename)
    allText = []
    for docpara in doc.paragraphs:
        allText.append(docpara.text.strip())

    return allText


def cycle_in_folders_and_files(path_name):
    global newPapka

    try:
        for root, dirs, files in os.walk(path_name):
            filtered = filter(lambda score: 'doc' in score, files)
            filtered = list(filtered)

    except Exception as e:
        print(str(e))

    finally:
        return filtered


if __name__ == "__main__":
    path = r"C:\Users\<USER>\Desktop\Адреса получателей"
    list_files = cycleInFoldersAndFiles(path)
    for file in list_files:
        FILE_NAME = os.path.join(path, file)
        result = read_file(FILE_NAME)
        ff = '; '.join(result)
        ff = unidecode.unidecode(ff)
        ff = re.sub(r'[\t()-]', '', ff)
        ff = ff.strip().replace("  ", " ")
        pattern = re.compile("\d{10}"
                             "|\(?\d{3})?.\d{7}"
                             "|(?\d{3})?.\d{2}.\d{2}.\d{3}"
                             "|\d{3}.\d{3}.\d{2}.\d{2}"
                             "|\d{3}.\d{2}.\d{3}.\d{2}"
                             "|\d{3}.\d{3}.\d{4}"
                             "|\d{4}.\d{6}"
                             "|\d{6}.\d{2}.\d{2}")
        phone = pattern.findall(ff)
        print(phone, file, ff)

# Validate phone number
