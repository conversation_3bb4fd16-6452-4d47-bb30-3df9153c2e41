-- данные из платформы EDIN, используемые в представлении v_one_edin
-- для загрузки УКТЗЕД на платформу

CREATE TABLE IF NOT EXISTS t_edin_units (
    units_key char(36),
    code int,
    value text,
    CONSTRAINT t_edin_units_pkey PRIMARY KEY (units_key)
);

INSERT INTO t_edin_units (units_key, code, value) VALUES
    ('3d7e2ad2-8ac8-11e6-80c4-c936aa9c817c', 2,'Кілограм'),
    ('82823145-a005-11e6-80c4-c936aa9c817c', 3,'Літр'),
    ('5ff178fc-8eea-11e6-80c4-c936aa9c817c', 4,'Метр'),
    ('5ff178f5-8eea-11e6-80c4-c936aa9c817c', 6,'Квадра<PERSON>ний метр'),
    ('7249987a-ffe2-11e6-80c9-001dd8b79079', 7,'Кубічний метр'),
    ('3d7e2ad3-8ac8-11e6-80c4-c936aa9c817c', 8,'Штука'),
    ('e5827ea3-8f86-11e6-80c4-c936aa9c817c', 9,'Коробка'),
    ('82823144-a005-11e6-80c4-c936aa9c817c', 10,'Пачка'),
    --('e5827ea6-8f86-11e6-80c4-c936aa9c817c', 12,'упаковка'),
    ('e5827ea6-8f86-11e6-80c4-c936aa9c817c', 12,'Коробка'),
    ('e57d8935-910b-11e6-80c4-c936aa9c817c', 14,'Тисяча штук'),
    ('700ea36e-8c8c-11e6-80c4-c936aa9c817c', 16,'Рулон'),
    ('3e2b9902-24d0-11e7-80cc-001dd8b79079', 17,'Послуга'),
    ('e5827ea2-8f86-11e6-80c4-c936aa9c817c', 38,'Блок'),
    ('97ccf981-afbc-11e6-80c4-c936aa9c817c', 39,'Комплект'),
    ('f40ced2d-9119-11e6-80c4-c936aa9c817c', 43,'Банка')
    ON CONFLICT (units_key) DO NOTHING;
    ;
