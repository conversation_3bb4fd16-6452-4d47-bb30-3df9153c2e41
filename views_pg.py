﻿import asyncio
import os
from datetime import date

from Views.AccountsReceivable import main_accounts_receivable_async
from Views.ControlUktved import main_control_uktved_async
from Views.ManagerCustomerContractsSegments import main_manager_customer_contracts_segments_async
from Views.Offsettings import main_offsets_async
from Views.ReturnNoInvoice import main_return_no_invoice_async
from Views.StockDays import main_stock_days_async
from Views.StockDaysExceptCustomers import main_stock_days_except_customers_async
from Views.TaxMedoc import main_tax_medoc_async
from Views.TaxMedocDiff import main_tax_medoc_different_async
from Views.TaxSale import main_tax_amount_different_async
from Views.TaxSaleReturn import main_tax_sale_return_amount_different_async
from Views.Vacation import main_vacation_async
from async_Postgres import async_save_pg

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
from logger_prestige import get_logger

logger = get_logger(FILE_NAME)

v_gtd = '''
    DROP VIEW IF EXISTS v_gtd CASCADE;
    CREATE OR REPLACE VIEW v_gtd
    AS SELECT DISTINCT 
        imp.doc_date::date AS gtd_date,
        imp.doc_date AS tarih,
        imp.doc_number AS gtd_number,
        COALESCE(part.customs_value, 0::numeric)::numeric(10,2) AS part_customs_value,
        COALESCE(part.duty_rate, 0) AS part_duty_rate,
        COALESCE(part.duty_amount, 0::numeric)::numeric(10,2) AS part_duty_amount,
        COALESCE(part.amount_vat, 0::numeric)::numeric(10,2) AS part_amount_vat,
        (COALESCE(serv.rate_settlement, 0::numeric) 
            / COALESCE(serv.multiplicity_of_mutual_settlements, 1::numeric))::numeric(15,4) AS serv_rate,
        CASE
            WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN 
                (COALESCE(part.duty_amount, 0::numeric) 
                    / (COALESCE(serv.rate_settlement, 1::numeric) 
                    / COALESCE(serv.multiplicity_of_mutual_settlements, 1::numeric)))::numeric(10,4)
            ELSE COALESCE(part.duty_amount, 0::numeric)
        END AS duty_usd,
        CASE
            WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN 
                (COALESCE(part.amount_vat, 0::numeric) 
                / (COALESCE(serv.rate_settlement, 1::numeric) 
                / COALESCE(serv.multiplicity_of_mutual_settlements, 1::numeric)))::numeric(10,4)
            ELSE COALESCE(part.amount_vat, 0::numeric)
        END AS vat_usd,
        cur.description AS gtd_cur,
        imp.ref_key,
        goods.batch_document,
        imp.currency_key as currency_key
       FROM t_one_doc_gtd_import imp
         LEFT JOIN t_one_doc_gtd_import_goods goods ON imp.ref_key::text = goods.ref_key::text
         LEFT JOIN t_one_doc_gtd_import_partitions part ON part.ref_key::text = goods.ref_key::text
         JOIN t_one_cat_currencies cur ON cur.ref_key::text = imp.currency_key::text
         LEFT JOIN t_one_doc_receipt_of_goods_services serv ON serv.ref_key::text = goods.batch_document::text
      WHERE imp.posted = true;

    COMMENT ON VIEW v_gtd IS 'таможенные расходы';

    '''

v_service_tc = '''
    CREATE OR REPLACE VIEW v_service_tc AS
    SELECT 
        org.description AS organizations,
        clients.description AS client,
        serv.doc_date::date AS serv_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        COALESCE(goods.tutar_goods, 0) as giris_tutar,
        round(CASE
            WHEN serv.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
                COALESCE(goods.tutar_goods, 0)
            WHEN serv.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
                COALESCE(goods.tutar_goods, 0) / (SELECT rate_usd_nbu
                                                    FROM t_rate_nbu
                                                    WHERE rate_date = serv.doc_date::date)     
            WHEN serv.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
                (COALESCE(goods.tutar_goods, 0) / 
                (SELECT rate_usd_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.doc_date::date)) * 
                (SELECT rate_euro_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.doc_date::date)     

        END,3) AS giris_tutar_usd,
        round(COALESCE(serv.rate_settlement, 0) / COALESCE(serv.multiplicity_of_mutual_settlements, 0),5) 
            AS rate_settlement,
        cur.description AS cur_type,
        serv.ref_key AS batch_document,
        serv.account_key,
        serv.currency_key as currency_key
       FROM t_one_doc_receipt_of_goods_services serv
         JOIN ( SELECT ref_key, sum(quantity * price_tr) AS tutar_goods
               FROM t_one_doc_receipt_of_goods_services_goods
              WHERE price_tr <> 0
              GROUP BY ref_key) goods ON goods.ref_key::text = serv.ref_key::text
         LEFT JOIN t_one_cat_currencies cur ON cur.ref_key::text = serv.currency_key::text
         LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text
         LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
      WHERE serv.posted = true;
      '''

v_service_tc_dop = '''
    CREATE OR REPLACE VIEW v_service_tc_dop
    AS SELECT DISTINCT 
        inc.doc_date::date AS doc_date,
        inc.doc_date AS tarih,
        inc.doc_number,
        org.description AS organization,
        clients.description AS client,
        cur.description AS cur_type,
        serv.rate_settlement,
        inc.document_amount,
        inc.amount_vat,
        round(CASE
            WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- USD
                inc.document_amount 
            WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- UAH
                inc.document_amount / (SELECT rate_usd_nbu
                                    FROM t_rate_nbu
                                    WHERE rate_date = serv.serv_date)  
            WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- EURO
                (inc.document_amount / 
                (SELECT rate_usd_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)) * 
                (SELECT rate_euro_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)                    
        END,3) AS dop_amount_ue,
        round(CASE
            WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- USD
                inc.amount_vat 
            WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- UAH
                inc.amount_vat / (SELECT rate_usd_nbu
                                    FROM t_rate_nbu
                                    WHERE rate_date = serv.serv_date)  
            WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- EURO
                (inc.amount_vat / 
                (SELECT rate_usd_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)) * 
                (SELECT rate_euro_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)            
        END,3) AS dop_vat_ue,
        round(CASE
                WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = TRUE THEN -- USD
                    inc.document_amount
                 WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = FALSE THEN
                    inc.document_amount + inc.amount_vat
                WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = TRUE THEN -- UAH
                    (inc.document_amount) / (SELECT rate_usd_nbu
                                        FROM t_rate_nbu
                                        WHERE rate_date = serv.serv_date)
                WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = FALSE THEN -- UAH
                    (inc.document_amount + inc.amount_vat) / (SELECT rate_usd_nbu
                                                                FROM t_rate_nbu
                                                                WHERE rate_date = serv.serv_date)  
                WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = TRUE THEN -- EURO
                    (inc.document_amount / 
                    (SELECT rate_usd_nbu
                    FROM t_rate_nbu
                    WHERE rate_date = serv.serv_date)) * 
                    (SELECT rate_euro_nbu
                    FROM t_rate_nbu
                    WHERE rate_date = serv.serv_date)
                WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = FALSE THEN -- EURO
                    ((inc.document_amount + inc.amount_vat) / 
                    (SELECT rate_usd_nbu
                    FROM t_rate_nbu
                    WHERE rate_date = serv.serv_date)) * 
                    (SELECT rate_euro_nbu
                    FROM t_rate_nbu
                    WHERE rate_date = serv.serv_date)                  
        END,3) AS dop_sum_ue,
        inc.ref_key,
        goods.batch_document,
        inc.currency_key,
        inc.organization_key
       FROM t_one_doc_incoming_additional_expenses inc
         LEFT JOIN t_one_doc_incoming_additional_expenses_goods goods ON inc.ref_key::text = goods.ref_key::text
         LEFT JOIN t_one_cat_currencies cur ON cur.ref_key::text = inc.currency_key::text
         LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = inc.organization_key::text
         LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = inc.account_key::text
         JOIN v_service_tc serv ON serv.batch_document::text = goods.batch_document::text
       WHERE inc.posted = true AND NOT (clients.ref_key::text IN ( SELECT DISTINCT v_service_tc.account_key
               FROM v_service_tc))
       ORDER BY (inc.doc_date::date) DESC;
        '''

v_service_tc_goods = '''
    CREATE OR REPLACE VIEW v_service_tc_goods
    AS SELECT 
        goods.id,
        goods.sku,
        goods.unit,
        goods.quantity,
        goods.price_tr,
        goods.amount_tr::numeric(10,3) AS amount_tr,
        COALESCE(serv_group.gider_koef, 0)::numeric(10,4) AS gider_koef,
        ((COALESCE(serv_group.gider_koef, 0) + 1::numeric) * COALESCE(goods.amount_tr, 0) 
            / COALESCE(goods.quantity, 0))::numeric(10,4) AS maliyet,
        goods.coef_cart,
        goods.coef_doc,
        goods.coef_cart - goods.coef_doc AS diff,
        goods.cur_type,
        serv.batch_document,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        serv.currency_key       
       FROM v_service_tc serv
         LEFT JOIN ( SELECT
                goods_1.id, 
                nom.description AS sku,
                units.description AS unit,
                COALESCE(units.coefficient, 0) AS coef_cart,
                goods_1.quantity,
                round(CASE
                    WHEN serv_1.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
                        goods_1.price_tr 
                    WHEN serv_1.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
                        goods_1.price_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)     
                    WHEN serv_1.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
                        (goods_1.price_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)) * 
                        (SELECT rate_euro_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)
                END,3) AS price_tr,  
                round(CASE
                    WHEN serv_1.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
                        goods_1.amount_tr 
                    WHEN serv_1.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
                        goods_1.amount_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)     
                    WHEN serv_1.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
                        (goods_1.amount_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)) * 
                        (SELECT rate_euro_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)
                END,3) AS amount_tr,                     
                goods_1.coefficient AS coef_doc,
                goods_1.ref_key,
                goods_1.nomenclature_key,
                goods_1.nomenclature_series_key,
                goods_1.unit_of_key,
                (SELECT description
                FROM t_one_cat_currencies
                WHERE ref_key = serv_1.currency_key
                ) as cur_type                
               FROM t_one_doc_receipt_of_goods_services as serv_1
                 LEFT JOIN t_one_doc_receipt_of_goods_services_goods goods_1
                    ON serv_1.ref_key = goods_1.ref_key
                 LEFT JOIN t_one_cat_nomenclature nom ON nom.ref_key::text = goods_1.nomenclature_key::text
                    LEFT JOIN 
                    (SELECT *
                    FROM t_one_cat_units
                    WHERE NOT deletion_mark
                    ) 
                    AS units
                 units 
                 ON goods_1.unit_of_key::text = units.ref_key::text 
                    AND units.nomenclature_key::text = goods_1.nomenclature_key::text
              WHERE goods_1.price_tr <> 0) goods ON serv.batch_document::text = goods.ref_key::text
         LEFT JOIN v_service_tc_group serv_group ON serv_group.batch_document::text = serv.batch_document::text;
    '''

v_service_tc_gtd = '''
    CREATE OR REPLACE VIEW v_service_tc_gtd
    AS SELECT 
        v_gtd.gtd_date,
        v_gtd.tarih,
        v_gtd.gtd_number,
        COALESCE(v_gtd.duty_usd, 0::numeric) AS duty_usd,
        COALESCE(v_gtd.vat_usd, 0::numeric) AS vat_usd,
        COALESCE(v_gtd.duty_usd, 0::numeric) + COALESCE(v_gtd.vat_usd, 0::numeric) AS gtd_sum_ue,
        (SELECT description
            FROM t_one_cat_currencies
            WHERE ref_key = serv.currency_key
        ) as cur_type,
        serv.batch_document,
        serv.currency_key
       FROM v_service_tc serv
         LEFT JOIN v_gtd ON v_gtd.batch_document::text = serv.batch_document::text;
    '''

v_service_tc_group = '''
    CREATE OR REPLACE VIEW v_service_tc_group
    AS SELECT 
        serv.organizations,
        serv.client,
        serv.serv_date,
        serv.tarih,
        serv.doc_number,
        serv.cur_type AS doviz,
        COALESCE(serv.giris_tutar_usd,0)::numeric(10,4) AS giris_tutar,
        COALESCE(serv.rate_settlement,0)::numeric(10,4) AS kur,
        COALESCE(gtd.gtd_sum_ue,0) AS gtd_sum_ue,
        COALESCE(dop.dop_sum_ue,0)::numeric(10,4) AS dop_sum_ue,
        (COALESCE(gtd.gtd_sum_ue,0) + COALESCE(dop.dop_sum_ue,0))::numeric(10,4) AS toplam_gider,
        ((COALESCE(gtd.gtd_sum_ue,0) + COALESCE(dop.dop_sum_ue,0)) 
            / COALESCE(serv.giris_tutar_usd,0))::numeric(10,4) AS gider_koef,
        serv.batch_document
       FROM v_service_tc serv
         LEFT JOIN ( SELECT batch_document,
                sum(COALESCE(gtd_sum_ue,0)) AS gtd_sum_ue
               FROM v_service_tc_gtd
              GROUP BY batch_document) gtd ON gtd.batch_document::text = serv.batch_document::text
         LEFT JOIN ( SELECT batch_document,
                sum(COALESCE(dop_sum_ue,0)) AS dop_sum_ue
               FROM v_service_tc_dop
              GROUP BY batch_document) dop ON dop.batch_document::text = serv.batch_document::text
      ORDER BY serv.serv_date DESC, serv.doc_number;

    -- Permissions

    ALTER TABLE v_service_tc_group OWNER TO postgres;
    GRANT ALL ON TABLE v_service_tc_group TO postgres;
    GRANT SELECT ON TABLE v_service_tc_group TO user_prestige;
    '''

v_service_tc_dop_second = '''
    CREATE OR REPLACE VIEW v_service_tc_dop_second
    AS SELECT 
        org.description AS organization,
        clients.description AS client,
        cur.description AS doviz,
        dop.doc_date::date AS dop_date_second,
        dop.doc_date as tarih,
        dop.doc_number AS dop_number_second,
        dop.document_amount,
        dop.amount_vat AS dop_vat_second,
        dop.ref_key AS dop_ref_key_second,
        dop.organization_key AS dop_organization_key_second,
        dop.currency_key AS dop_currency_key_second,
        dop_rfk.batch_document
       FROM t_one_doc_incoming_additional_expenses dop
         JOIN ( SELECT DISTINCT dop_goods.ref_key,
                dop_goods.batch_document
               FROM t_one_doc_incoming_additional_expenses_goods dop_goods
                 JOIN ( SELECT DISTINCT dp.ref_key
                       FROM t_one_doc_receipt_of_goods_services_goods dp
                         JOIN ( SELECT DISTINCT v_service_tc_goods.nomenclature_key
                               FROM v_service_tc_goods) nomen_key 
                                ON nomen_key.nomenclature_key::text = dp.nomenclature_key::text
                      WHERE NOT (dp.ref_key::text IN (SELECT DISTINCT v_service_tc.batch_document
                               FROM v_service_tc))) rfk ON rfk.ref_key::text = dop_goods.batch_document::text) dop_rfk 
                                ON dop.ref_key::text = dop_rfk.ref_key::text
         JOIN t_one_cat_currencies cur ON cur.ref_key::text = dop.currency_key::text
         JOIN t_one_cat_organizations org ON org.ref_key::text = dop.organization_key::text
         JOIN t_one_cat_counterparties clients ON clients.ref_key::text = dop.account_key::text
      WHERE NOT (dop.account_key::text IN ( SELECT DISTINCT v_service_tc.account_key
               FROM v_service_tc))
      ORDER BY dop.doc_date DESC;
    '''

v_sale_all = '''
    CREATE OR REPLACE VIEW v_sale_all
    AS
    SELECT sale.id,
        sale.organization,
        sale.customer AS client,
        sale.tarih,
        sale.doc_number AS evrakno,
        sale.sku,
        units.description AS unit,
        sale.inbox,
        COALESCE(sale.quantity, 0) AS adet,
        ((COALESCE(sale.quantity, 0) * COALESCE(sale.coefficient, 0)) / sale.inbox) AS all_box,
        ( SELECT (sum(t.amount) / sum(t.quantity)) AS avg_price_tr
               FROM ( SELECT goods.quantity,
                          goods.price_tr,
                          goods.quantity * goods.price_tr AS amount
                      FROM t_one_doc_receipt_of_goods_services serv
                          LEFT JOIN t_one_doc_receipt_of_goods_services_goods goods 
                            ON serv.ref_key = goods.ref_key
                      WHERE goods.nomenclature_key = sale.nomenclature_key 
                        AND COALESCE(goods.price_tr, 0) <> 0
                        AND serv.doc_date::date <= sale.doc_date::date 
                        AND serv.doc_date::date >= (sale.doc_date::date - '1 year'::interval)
                      ) t
        ) AS giris_ft,
        (sale.tobox * 
                ( SELECT (sum(t.amount) / sum(t.quantity)) AS avg_price_tr
               FROM ( SELECT goods.quantity,
                        goods.price_tr,
                        goods.quantity * goods.price_tr AS amount
                       FROM t_one_doc_receipt_of_goods_services serv
                         LEFT JOIN t_one_doc_receipt_of_goods_services_goods goods 
                            ON serv.ref_key = goods.ref_key
                      WHERE goods.nomenclature_key = sale.nomenclature_key 
                        AND COALESCE(goods.price_tr, 0) <> 0
                        AND serv.doc_date::date <= sale.doc_date::date 
                        AND serv.doc_date::date >= (sale.doc_date::date - '1 year'::interval)
                      ORDER BY serv.doc_date) t))
        AS giris_tutar,
        CASE
            WHEN sale.amount_includes_vat = FALSE AND sale.consider_vat = TRUE THEN 
                ((COALESCE(sale.amount_vat, 0) + COALESCE(sale.amount, 0)) / 
                    COALESCE(sale.quantity, 0))
            ELSE (COALESCE(sale.amount_vat, 0) / COALESCE(sale.quantity, 0))
        END AS price1c_kdv,
        CASE
            WHEN sale.amount_includes_vat = FALSE AND sale.consider_vat = TRUE THEN 
                COALESCE(sale.amount, 0) + 	COALESCE(sale.amount_vat, 0)
            ELSE COALESCE(sale.amount, 0)
        END AS total_kdv,
        (( SELECT sum(v_service_tc_maliyet.maliyet_tutar) / sum(v_service_tc_maliyet.quantity)
          FROM v_service_tc_maliyet
          WHERE v_service_tc_maliyet.nomenclature_key = sale.nomenclature_key 
            AND v_service_tc_maliyet.serv_date <= sale.doc_date::date 
            AND v_service_tc_maliyet.serv_date >= (sale.doc_date::date - '1 year'::interval))) 
        AS maliyet,
        (sale.tobox * (
            ( SELECT sum(v_service_tc_maliyet.maliyet_tutar) / sum(v_service_tc_maliyet.quantity)
              FROM v_service_tc_maliyet
              WHERE v_service_tc_maliyet.nomenclature_key = sale.nomenclature_key 
                AND v_service_tc_maliyet.serv_date <= sale.doc_date::date 
                AND v_service_tc_maliyet.serv_date >= (sale.doc_date::date - '1 year'::interval))))::numeric(15,4) 
        AS maliyet_tutar,
        COALESCE(sale.coefficient::integer, 0) AS coef_doc,
        COALESCE(units.coefficient, 0) AS coef_cart,
        COALESCE(units.coefficient, 0) - COALESCE(sale.coefficient::integer, 0)AS diff,
        cur.description AS doviz,
        sale.doc_type,
        ( SELECT t_rate_nbu.rate_usd_nbu
          FROM t_rate_nbu
          WHERE t_rate_nbu.rate_date = sale.doc_date::date
        ) AS kur_nbu,
        sale.ref_key,
        sale.customer_key,
        sale.organization_key,
        sale.nomenclature_key,
        sale.unit_of_key,
        sale.currency_key
       FROM v_one_sale_and_salereturn AS sale
         JOIN t_one_cat_nomenclature nom ON nom.ref_key = sale.nomenclature_key
         LEFT JOIN t_one_cat_currencies cur ON cur.ref_key = sale.currency_key
         LEFT JOIN (SELECT 
                        ref_key,
                        nomenclature_key, 
                        description,
                        coefficient,
                        max(coefficient) OVER(PARTITION BY nomenclature_key) AS in_box
                    FROM t_one_cat_units
                    WHERE NOT deletion_mark
                    ) AS units
                    ON nom.ref_key = units.nomenclature_key
                        AND units.ref_key = sale.unit_of_key 
      ORDER BY sale.doc_date DESC;    
    '''

v_control_coef = '''
    CREATE OR REPLACE VIEW v_control_coef
    AS SELECT DISTINCT sale.sku,
        mlt.serv_date,
        mlt.tarih,
        mlt.doc_number,
        sale.inbox AS "ед в крб по отчету",
        mlt.coef_doc AS "ед в крб по док.поступл"
       FROM t_one_sale sale
         JOIN v_service_tc_maliyet mlt ON mlt.nomenclature_key::text = sale.nomenclature_key::text
      WHERE sale.inbox <> mlt.coef_doc
      ORDER BY sale.sku;
    '''

v_service_tc_maliyet = '''
    CREATE OR REPLACE VIEW v_service_tc_maliyet AS
    SELECT 
        goods.id,
        serv.client,
        serv.serv_date,
        serv.tarih,
        serv.doc_number,
        goods.sku,
        goods.unit,
        COALESCE(goods.quantity, 0::numeric) AS quantity_orig,
        (COALESCE(goods.quantity, 0::numeric)
        * goods.coef_doc
            / ( SELECT units.coefficient
                   FROM ( SELECT t_one_cat_units.nomenclature_key,
                            max(t_one_cat_units.coefficient) AS coefficient
                           FROM t_one_cat_units
                           WHERE NOT deletion_mark
                          GROUP BY t_one_cat_units.nomenclature_key) units
                  WHERE units.nomenclature_key::text = goods.nomenclature_key::text))::numeric(10,3)
        AS quantity,
--        round(CASE
--            WHEN serv.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
--                COALESCE(goods.price_tr, 0::numeric) 
--            WHEN serv.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
--                COALESCE(goods.price_tr, 0::numeric) / 
--                (SELECT rate_usd_nbu
--                FROM t_rate_nbu
--                WHERE rate_date = serv.serv_date)     
--            WHEN serv.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
--                (COALESCE(goods.price_tr, 0::numeric) / 
--                (SELECT rate_usd_nbu
--                FROM t_rate_nbu
--                WHERE rate_date = serv.serv_date)) * 
--                (SELECT rate_euro_nbu
--                FROM t_rate_nbu
--                WHERE rate_date = serv.serv_date)
--        END,3) AS price_tr_orig,
        (( SELECT units.coefficient
               FROM ( SELECT t_one_cat_units.nomenclature_key,
                        max(t_one_cat_units.coefficient) AS coefficient
                       FROM t_one_cat_units
                       WHERE NOT deletion_mark
                      GROUP BY t_one_cat_units.nomenclature_key) units
              WHERE units.nomenclature_key::text = goods.nomenclature_key::text)
          / goods.coef_doc
          * COALESCE(goods.price_tr, 0::numeric)
        )::numeric(10,3)
        AS price_tr,  -- price for box
        COALESCE(goods.amount_tr, 0::numeric) AS amount_tr,
        COALESCE(goods.gider_koef, 0::numeric)::numeric(10,4) AS gider_koef,
        COALESCE(goods.maliyet, 0::numeric)::numeric(10,4) AS maliyet_orig,
        (( SELECT units.coefficient
               FROM ( SELECT t_one_cat_units.nomenclature_key,
                        max(t_one_cat_units.coefficient) AS coefficient
                       FROM t_one_cat_units
                       WHERE NOT deletion_mark
                      GROUP BY t_one_cat_units.nomenclature_key) units
              WHERE units.nomenclature_key::text = goods.nomenclature_key::text)
          / goods.coef_doc
          * COALESCE(goods.maliyet, 0::numeric))::numeric(10,3)
        AS maliyet,
        (COALESCE(goods.maliyet, 0::numeric) * COALESCE(goods.quantity, 0::numeric))::numeric(10,4) AS maliyet_tutar,
        ( SELECT units.coefficient
               FROM ( SELECT t_one_cat_units.nomenclature_key,
                        max(t_one_cat_units.coefficient) AS coefficient
                       FROM t_one_cat_units
                       WHERE NOT deletion_mark
                      GROUP BY t_one_cat_units.nomenclature_key) units
              WHERE units.nomenclature_key::text = goods.nomenclature_key::text) AS inbox,
        (( SELECT units.coefficient
               FROM ( SELECT t_one_cat_units.nomenclature_key,
                        max(t_one_cat_units.coefficient) AS coefficient
                       FROM t_one_cat_units
                       WHERE NOT deletion_mark
                      GROUP BY t_one_cat_units.nomenclature_key) units
              WHERE units.nomenclature_key::text = goods.nomenclature_key::text)) = goods.coef_cart AS f,
        goods.coef_cart,
        goods.coef_doc,
        goods.coef_cart - goods.coef_doc AS diff,
        serv.cur_type AS doviz,
        goods.batch_document,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        serv.account_key,
        goods.unit_of_key,
        serv.currency_key
    FROM v_service_tc serv
       LEFT JOIN v_service_tc_goods goods ON serv.batch_document::text = goods.batch_document::text
    ORDER BY serv.serv_date DESC;

    GRANT SELECT ON TABLE v_service_tc_maliyet TO user_prestige;


    '''

v_one_nomenclature_inbox_supplier = '''
    CREATE OR REPLACE VIEW v_one_nomenclature_inbox_supplier AS
    SELECT DISTINCT 
        nom.sku,
        supl.supplier,
        nom.unit,
        nom.inbox,
        nom.count_of_months_of_storage,
        nom.nomenclature_key,
        nom.units_key,
        supl.supplier_key
       FROM (
            SELECT 
                t.nomenclature_key,
                t.supplier,
                t.supplier_key
            FROM ( SELECT serv.nomenclature_key,
                    serv.tarih = max(serv.tarih) OVER (PARTITION BY serv.nomenclature_key) AS max_date,
                    serv.client AS supplier,
                    serv.account_key AS supplier_key
                    FROM v_service_tc_maliyet serv
                ) AS t
            WHERE t.max_date = TRUE        
            ) supl
         RIGHT JOIN (
                    SELECT *
                    FROM (
                        SELECT t.sku,
                            t.unit,
                            t.inbox,
                            t.nomenclature_key,
                            t.units_key,
                            t.count_of_months_of_storage,
                            (t.units_key = FIRST_VALUE(t.units_key) OVER(PARTITION BY t.nomenclature_key)) AS first_unit
                       FROM ( SELECT units.description AS unit,
                                units.coefficient AS inbox,
                                units.coefficient = max(units.coefficient) OVER (PARTITION BY units.nomenclature_key) 
                                AS max_units,
                                nom_1.description AS sku,
                                nom_1.ref_key AS nomenclature_key,
                                units.ref_key AS units_key,
                                nom_1.count_of_months_of_storage
                               FROM t_one_cat_nomenclature nom_1
                                 LEFT JOIN 
                                    (SELECT *
                                    FROM t_one_cat_units
                                    WHERE NOT deletion_mark
                                    )AS units 
                                    ON units.nomenclature_key::text = nom_1.ref_key::text
                              WHERE NOT units.deletion_mark AND (nom_1.ref_key::text IN ( SELECT nom_2.ref_key
                                       FROM t_one_cat_nomenclature nom_2
                                      WHERE nom_2.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
                             ) t
                      WHERE t.max_units = TRUE) AS all_first_unit
                      WHERE first_unit = TRUE 
                              ) nom ON supl.nomenclature_key::text = nom.nomenclature_key::text
      ORDER BY nom.sku;

    COMMENT ON VIEW v_one_nomenclature_inbox_supplier IS 'номекл, поставщик, кол-во ед в коробке';
    '''

v_one_stock = '''
    CREATE OR REPLACE VIEW v_one_stock
    AS SELECT t.supplier,
        t.customer,
        t.sku,
        t.warehouse,
        t.doc_date,
        t.tarih,
        t.doc_number,
        t.inbox,
        t.quantity,
        t.coefficient,
        t.ed,
        sum(t.ed) OVER (PARTITION BY t.warehouse_key, t.nomenclature_key, t.nomenclature_series_key ORDER BY t.tarih, 
            t.doc_number, t.id) AS stock_ed,
        t.ed / t.inbox AS tobox,
        sum(t.ed / t.inbox) OVER (PARTITION BY t.warehouse_key, t.nomenclature_key, t.nomenclature_series_key 
            ORDER BY t.tarih, t.doc_number, t.id) AS stock_box,
        coalesce(t.maliyet,0) as maliyet,
        coalesce(t.price_tr,0) AS entry_price,
        t.doc_type,
        t.organization,
        t.organization_key,
        t.nomenclature_key,
        t.nomenclature_series_key,        
        t.document_key,
        t.unit_of_key,
        t.warehouse_key,
        t.supplier_key,
        t.customer_key,
        t.contract_key,
        t.line_number
       FROM ( SELECT
                srv.id, 
                srv.sku,
                srv.inbox,
                srv.tarih::date AS doc_date,
                srv.tarih,
                srv.doc_number,
                srv.quantity,
                srv.coefficient,
                srv.ed,
                mlt.maliyet,
                mlt.price_tr,
                srv.doc_type,
                srv.nomenclature_key,
                srv.nomenclature_series_key,        
                srv.unit_of_key,
                srv.ref_key AS document_key,
                srv.organization,
                srv.organization_key,
                srv.supplier,
                srv.supplier_key,
                NULL::text AS customer,
                NULL::text AS customer_key,
                srv.warehouse,
                srv.warehouse_key,
                srv.contract_key,
                srv.line_number
               FROM v_one_giris_and_girisiade srv
                 LEFT JOIN v_service_tc_maliyet mlt 
                    ON mlt.batch_document::text = srv.ref_key::text AND mlt.id = srv.id
               --WHERE srv.ref_key NOT IN (SELECT ref_key_second FROM t_additional_expenses)
            UNION ALL
             SELECT 
                srv.id, 
                srv.sku,
                srv.inbox,
                srv.doc_date,
                srv.tarih,
                srv.doc_number,
                srv.quantity,
                srv.coefficient,
                srv.ed,
                0 AS maliyet,
                0 AS entry_price,
                srv.doc_type,
                srv.nomenclature_key,
                srv.nomenclature_series_key,
                srv.unit_of_key,
                srv.ref_key AS document_key,
                srv.organization,
                srv.organization_key,
                srv.supplier,
                srv.supplier_key,
                srv.customer,
                srv.customer_key,
                srv.warehouse,
                srv.warehouse_key,
                NULL::text as contract_key,
                srv.line_number
               FROM v_one_trebovanie srv
            UNION ALL
                SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    srv.quantity,
                    srv.coefficient,
                    srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.ref_key AS document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    srv.customer,
                    srv.customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    srv.contract_key,
                    srv.line_number
                FROM v_one_sale_and_salereturn srv
                -- WHERE organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- если поставить, остатки по складам не будут идти
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.ref_key AS document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    srv.customer,
                    srv.customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text as contract_key,
                    srv.line_number
                   FROM v_one_oprihodivanie srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.ref_key AS document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    srv.customer,
                    srv.customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text as contract_key,
                    srv.line_number
                   FROM v_one_spisanie srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    srv.quantity,
                    srv.coefficient,
                    srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse_in AS warehouse,
                    srv.warehouse_in_key AS warehouse_key,
                    NULL::text contract_key,
                    srv.line_number
                   FROM v_one_movement srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text contract_key,
                    srv.line_number
                   FROM v_one_correction srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    srv.quantity,
                    srv.coefficient,
                    srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_new_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text contract_key,
                    srv.line_number
                 FROM v_one_correction srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse_out AS warehouse,
                    srv.warehouse_out_key AS warehouse_key,
                    NULL::text contract_key,
                    srv.line_number
                   FROM v_one_movement srv
        ) t
         LEFT JOIN 
            (
            SELECT *
            FROM t_one_cat_units
            WHERE NOT deletion_mark
            ) AS units 
            ON t.unit_of_key::text = units.ref_key::text 
                AND t.nomenclature_key::text = units.nomenclature_key::text
      ORDER BY t.nomenclature_key, t.tarih
  ;
'''

v_one_stock_maliyet = '''
    CREATE OR REPLACE VIEW v_one_stock_maliyet AS
    SELECT
        t.warehouse,
        t.supplier,   
        t.sku,
        CASE
            WHEN t.tarih = max(t.tarih) OVER (PARTITION BY t.warehouse, t.nomenclature_key, t.tarih::date) THEN 
                t.tarih::date
            ELSE NULL::date
        END AS date_max,
        t.tarih,
        t.doc_number,
        t.maliyet,        
        t.inbox,
        t.tobox,
        sum(t.tobox) OVER (PARTITION BY t.warehouse, t.nomenclature_key ORDER BY t.sku, t.tarih) 
        AS ostatok_krb,
        t.ed,
        sum(t.ed) OVER (PARTITION BY t.warehouse, t.nomenclature_key ORDER BY t.nomenclature_key, t.tarih) 
        AS ostatok_ed,
        sum(t.maliyet * t.tobox) OVER (PARTITION BY t.warehouse, t.nomenclature_key 
                ORDER BY t.nomenclature_key, t.tarih)::numeric(15,3) 
        AS kalan_tutar,
        t.doc_type,
        t.nomenclature_key,
        t.nomenclature_series_key,
        t.warehouse_key
       FROM (
            SELECT warehouse,
                supplier, 
                sku,
                inbox,
                doc_date as tarih,
                doc_number,
                sum(quantity * coefficient) AS ed,
                sum(tobox) AS tobox,
                doc_type,
                nomenclature_key,
                nomenclature_series_key,
                maliyet,
                warehouse_key
            FROM t_one_stock
            GROUP BY supplier, sku, inbox, doc_number, warehouse,
                doc_type, maliyet, nomenclature_key, nomenclature_series_key, doc_date, warehouse_key
       ) t
      ORDER BY t.sku, t.nomenclature_key, t.tarih;
    '''

v_one_giris = '''
    CREATE OR REPLACE VIEW v_one_giris
    AS 
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        ns.sell_by,
        CASE 
            WHEN ns.count_of_months_of_storage <> 0 THEN 
                ns.count_of_months_of_storage
            ELSE 
                nom.count_of_months_of_storage
        END AS count_of_months_of_storage,    
        CASE 
            WHEN ns.count_of_months_of_storage <> 0 THEN 
                (ns.sell_by + '1 mon'::interval * ns.count_of_months_of_storage::double precision)::date - ns.sell_by
            ELSE 
                (ns.sell_by + '1 mon'::interval * nom.count_of_months_of_storage::double precision)::date - ns.sell_by
        END AS count_of_days_of_storage,    
        CASE
            WHEN (ns.count_of_months_of_storage) <> 0 and ((ns.sell_by - serv.doc_date::date)::int) = 0 THEN
                (ns.sell_by - serv.doc_date::date)::numeric(10,3) / -- days_between
                    ((ns.sell_by::date + interval '1 month' 
                        * ns.count_of_months_of_storage)::date - ns.sell_by::date + 1)::numeric(10,3) -- count_of_days_of_storage
            WHEN (nom.count_of_months_of_storage) <> 0 and ((ns.sell_by - serv.doc_date::date)::int) = 0 THEN
                (ns.sell_by - serv.doc_date::date)::numeric(10,3) / -- days_between
                    ((ns.sell_by::date + interval '1 month' 
                        * nom.count_of_months_of_storage)::date - ns.sell_by::date + 1)::numeric(10,3) -- count_of_days_of_storage
            ELSE
                0
        END as percent,
        CASE
            WHEN COALESCE(ns.sell_by,current_date) = current_date THEN
                0
            ELSE
                (ns.sell_by - serv.doc_date::date)::int 
        END as days_between,
        nom.inbox,
        goods.quantity,
        goods.coefficient,
        goods.quantity * goods.coefficient AS ed,
        goods.quantity * goods.coefficient / nom.inbox AS tobox,
        goods.amount,
        goods.amount_vat,
        goods.price_tr,
        (goods.price_tr * goods.quantity) as amount_price_tr,
        serv.rate,
        serv.rate_settlement,
        serv.multiplicity_of_mutual_settlements,
        serv.rate_nbu,
        serv.amount_includes_vat,
        serv.is_accounting,
        serv.is_management,
        serv.posted,
        'поступление'::text AS doc_type,
        org.description AS organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        serv.organization_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        nom.supplier_key,
        serv.currency_key AS currency_key,
        goods.warehouse_key,
        serv.contract_key,
        goods.line_number
       FROM t_one_doc_receipt_of_goods_services serv
         JOIN t_one_doc_receipt_of_goods_services_goods goods USING (ref_key)
         JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
         LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::TEXT
         LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = goods.warehouse_key
         LEFT JOIN t_one_cat_nomenclature_series AS ns ON ns.ref_key = goods.nomenclature_series_key
      ORDER BY serv.doc_date, serv.doc_number, nom.sku;
;

    COMMENT ON VIEW v_one_giris IS 'поступление';
    COMMENT ON COLUMN v_one_giris.id IS '№';
    COMMENT ON COLUMN v_one_giris.doc_date  IS 'дата';
    COMMENT ON COLUMN v_one_giris.tarih  IS 'дата_время';
    COMMENT ON COLUMN v_one_giris.ed IS 'ед по накл';
    COMMENT ON COLUMN v_one_giris.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_giris.sell_by IS 'годен до';
    COMMENT ON COLUMN v_one_giris.tobox IS 'в перечт на крб';
    COMMENT ON COLUMN v_one_giris.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_giris.days_between IS 'годен до - дата поступления';
    COMMENT ON COLUMN v_one_giris.count_of_months_of_storage IS 'кол-во мес хранения';
    COMMENT ON COLUMN v_one_giris.count_of_days_of_storage IS 'кол-во дней хранения';
    COMMENT ON COLUMN v_one_giris.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_giris.sku IS 'sku';
    COMMENT ON COLUMN v_one_giris.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_giris.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_giris.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_giris.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_giris.supplier IS 'поставщик'; 

    GRANT SELECT ON TABLE v_one_giris TO user_prestige;       
    '''

v_one_giris_iade = '''
    CREATE OR REPLACE VIEW v_one_giris_iade
    AS
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        ns.sell_by,
        nom.inbox,
        - goods.quantity as quantity,
        goods.coefficient,
        - (goods.quantity * goods.coefficient) AS ed,
        - ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        goods.amount,
        goods.amount_vat,
        serv.rate_settlement,
        serv.multiplicity_of_mutual_settlements,
        serv.amount_includes_vat,
        serv.is_accounting,
        serv.is_management,
        serv.posted,
        'поступление возврат'::text AS doc_type,
        org.description AS organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        organization_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        nom.supplier_key,
        serv.currency_key AS currency_key,
        goods.warehouse_key,
        serv.contract_key,
        goods.line_number
    FROM t_one_doc_return_of_goods_to_supplier serv
        JOIN t_one_doc_return_of_goods_to_supplier_details goods USING (ref_key)
        JOIN v_one_nomenclature_inbox_supplier nom 
            ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations org 
            ON org.ref_key::text = serv.organization_key::TEXT
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = goods.warehouse_key
        LEFT JOIN t_one_cat_nomenclature_series AS ns ON ns.ref_key = goods.nomenclature_series_key
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_giris_iade IS 'поступление';
    COMMENT ON COLUMN v_one_giris_iade.id IS '№';
    COMMENT ON COLUMN v_one_giris_iade.doc_date  IS 'дата';
    COMMENT ON COLUMN v_one_giris_iade.tarih  IS 'дата_время';
    COMMENT ON COLUMN v_one_giris_iade.ed IS 'ед по накл';
    COMMENT ON COLUMN v_one_giris_iade.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_giris_iade.sell_by IS 'годен до';
    COMMENT ON COLUMN v_one_giris_iade.tobox IS 'в перечт на крб';
    COMMENT ON COLUMN v_one_giris_iade.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_giris_iade.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_giris_iade.sku IS 'sku';
    COMMENT ON COLUMN v_one_giris_iade.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_giris_iade.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_giris_iade.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_giris_iade.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_giris_iade.supplier IS 'поставщик';

    GRANT SELECT ON TABLE v_one_giris_iade TO user_prestige;       

'''

v_one_giris_and_girisiade = '''
    CREATE OR REPLACE VIEW  v_one_giris_and_girisiade
    AS
    SELECT
        id,
        doc_date,
        tarih,
        doc_number,
        sku,
        sell_by,
        CASE
            WHEN sell_by is null THEN
                null 
            ELSE
             --age(sell_by::date, tarih::date)
             sell_by::date - tarih::date
        END as between_sell_by, 
        inbox,
        quantity,
        coefficient,
        ed,
        tobox,
        amount,
        amount_vat,
        rate_settlement,
        multiplicity_of_mutual_settlements,
        amount_includes_vat,
        is_accounting,
        is_management,
        posted,
        doc_type,
        organization,
        supplier,
        warehouse,
        ref_key,
        organization_key,
        nomenclature_key,
        nomenclature_series_key,
        unit_of_key,
        supplier_key,
        currency_key,
        warehouse_key,
        contract_key,
        line_number
    FROM v_one_giris
    UNION ALL
    SELECT
        id,
        doc_date,
        tarih,
        doc_number,
        sku,
        sell_by,
        CASE
            WHEN sell_by is null THEN
                null
            ELSE 
                -- age(sell_by::date, tarih::date)
                sell_by::date - tarih::date
        END as between_sell_by, 
        inbox,
        quantity,
        coefficient,
        ed,
        tobox,
        amount,
        amount_vat,
        rate_settlement,
        multiplicity_of_mutual_settlements,
        amount_includes_vat,
        is_accounting,
        is_management,
        posted,
        doc_type,
        organization,
        supplier,
        warehouse,
        ref_key,
        organization_key,
        nomenclature_key,
        nomenclature_series_key,
        unit_of_key,
        supplier_key,
        currency_key,
        warehouse_key,
        contract_key,
        line_number
    FROM v_one_giris_iade;

    COMMENT ON VIEW v_one_giris_and_girisiade IS 'поступление_возврат';
    COMMENT ON COLUMN v_one_giris_and_girisiade.id IS '№';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_date  IS 'дата';
    COMMENT ON COLUMN v_one_giris_and_girisiade.tarih  IS 'дата_время';
    COMMENT ON COLUMN v_one_giris_and_girisiade.ed IS 'ед по накл';
    COMMENT ON COLUMN v_one_giris_and_girisiade.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_giris_and_girisiade.tobox IS 'в перечт на крб';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_giris_and_girisiade.sku IS 'sku';
    COMMENT ON COLUMN v_one_giris_and_girisiade.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_giris_and_girisiade.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_giris_and_girisiade.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_giris_and_girisiade.supplier IS 'поставщик';

    GRANT SELECT ON TABLE v_one_giris_and_girisiade TO user_prestige;       

'''

v_one_sale = '''
    CREATE OR REPLACE VIEW v_one_sale
    AS    
    SELECT goods.id,
        sale_clients.description AS papka,
        managers.description AS manager,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        - goods.quantity AS quantity,
        goods.coefficient,
        - (goods.quantity * goods.coefficient) AS ed,
        - (goods.quantity * goods.coefficient / nom.inbox) AS tobox,
        - goods.amount AS amount,
        - goods.amount_vat AS amount_vat,
        serv.consider_vat,
        serv.amount_includes_vat,
        'продажа'::text AS doc_type,
        '-1'::integer AS issale,
        serv.rate_settlement AS settlement_rate,
        serv.multiplicity_of_mutual_settlements,
        org.description AS organization,
        clients.description AS customer,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        clients.ref_key AS customer_key,
        serv.currency_key AS currency_key,
        serv.organization_key,
        nom.supplier_key,
        goods.warehouse_key,
        clients.parent_key,
        serv.contract_key,
        serv.price_type_key,
        goods.line_number
       FROM t_one_doc_sale_of_goods_services serv
         JOIN t_one_doc_sale_of_goods_services_goods goods USING (ref_key)
         LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
         JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
         LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text
         LEFT JOIN t_one_cat_warehouses warehouses ON warehouses.ref_key::text = goods.warehouse_key::text
         LEFT JOIN t_one_cat_counterparties managers ON managers.ref_key::text = clients.parent_key::text
         LEFT JOIN t_one_cat_counterparties sale_clients ON sale_clients.ref_key::text = managers.parent_key::text
      WHERE serv.doc_date::date <= CURRENT_DATE
        AND serv.is_management AND serv.posted
        -- AND sale_clients.ref_key = 'ad421841-905f-11e6-80c4-c936aa9c817c'
      ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_sale IS 'продажи';
    COMMENT ON COLUMN v_one_sale.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_sale.manager IS 'менеджер';
    COMMENT ON COLUMN v_one_sale.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_sale.sku IS 'sku';
    COMMENT ON COLUMN v_one_sale.amount_vat IS 'сумма НДС';
    COMMENT ON COLUMN v_one_sale.amount IS 'сумма без НДС';
    COMMENT ON COLUMN v_one_sale.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_sale.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_sale.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_sale.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_sale.issale IS 'doc_type 1-sale: -1 - return';

    GRANT SELECT ON TABLE v_one_sale TO user_prestige;
    '''

v_one_sale_return = '''
    CREATE OR REPLACE VIEW v_one_sale_return AS
    SELECT 
        goods.id, 
        sale_clients.description AS papka,
        managers.description AS manager,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        goods.quantity,
        goods.coefficient,
        (goods.quantity * goods.coefficient) AS ed,
        ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        goods.amount,
        goods.amount_vat,
        serv.consider_vat,
        serv.amount_includes_vat,
        'продажа возврат'::text AS doc_type,
        1 as issale,
        serv.settlement_rate,
        serv.multiplicity_of_mutual_settlements,        
        org.description AS organization,
        clients.description AS customer,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        clients.ref_key AS customer_key,
        serv.currency_key,
        serv.organization_key,
        nom.supplier_key,
        goods.warehouse_key,
        clients.parent_key,
        serv.contract_key,
        goods.line_number
    FROM t_one_doc_return_of_goods_from_customers serv
        JOIN t_one_doc_return_of_goods_from_customers_goods goods USING (ref_key)
        LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
        JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = goods.warehouse_key
        LEFT JOIN t_one_cat_counterparties managers ON managers.ref_key::text = clients.parent_key::text
        LEFT JOIN t_one_cat_counterparties sale_clients ON sale_clients.ref_key::text = managers.parent_key::text
    WHERE serv.doc_date::date <= current_date          
        AND is_management = true and posted = true
        -- AND sale_clients.ref_key = 'ad421841-905f-11e6-80c4-c936aa9c817c'
    ORDER BY serv.doc_date, serv.doc_number;

    COMMENT ON VIEW v_one_sale_return IS 'продажа возврат';
    COMMENT ON COLUMN v_one_sale_return.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_sale_return.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_sale_return.sku IS 'sku';
    COMMENT ON COLUMN v_one_sale_return.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_sale_return.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_sale_return.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_sale_return.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_sale_return.issale IS 'doc_type 1-sale: -1 - return';

    GRANT SELECT ON TABLE v_one_sale_return TO user_prestige;

    '''

v_one_sale_and_salereturn = '''
    CREATE OR REPLACE VIEW v_one_sale_and_salereturn AS 
    SELECT *
    FROM (
        SELECT
            id,
            doc_date,
            tarih,
            doc_number,
            sku,
            inbox,
            quantity,
            coefficient,
            ed,
            tobox,
            amount,
            amount_vat,
            consider_vat,
            amount_includes_vat,
            doc_type,
            issale,
            settlement_rate,
            multiplicity_of_mutual_settlements,
            organization,
            customer,
            supplier,
            warehouse,
            ref_key,
            nomenclature_key,
            nomenclature_series_key,
            unit_of_key,
            customer_key,
            currency_key,
            organization_key,
            supplier_key,
            warehouse_key,
            contract_key,
            line_number
        FROM v_one_sale
        UNION ALL
        SELECT 
            id,
            doc_date,
            tarih,
            doc_number,
            sku,
            inbox,
            quantity,
            coefficient,
            ed,
            tobox,
            amount,
            amount_vat,
            consider_vat,
            amount_includes_vat,
            doc_type,
            issale,
            settlement_rate,
            multiplicity_of_mutual_settlements,
            organization,
            customer,
            supplier,
            warehouse,
            ref_key,
            nomenclature_key,
            nomenclature_series_key,
            unit_of_key,
            customer_key,
            currency_key,
            organization_key,
            supplier_key,
            warehouse_key,
            contract_key,
            line_number
        FROM v_one_sale_return) AS sr
    WHERE sr.doc_date::date <= current_date 
    ORDER BY sr.doc_date;

    COMMENT ON VIEW v_one_sale_and_salereturn IS 'продажи и возвраты';
    COMMENT ON COLUMN v_one_sale_and_salereturn.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_sale_and_salereturn.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_sale_and_salereturn.sku IS 'sku';
    COMMENT ON COLUMN v_one_sale_and_salereturn.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_sale_and_salereturn.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_sale_and_salereturn.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_sale_and_salereturn.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_sale_and_salereturn.warehouse IS 'склад';
    COMMENT ON COLUMN v_one_sale_and_salereturn.warehouse_key IS 'склад_key';

    GRANT SELECT ON TABLE v_one_sale_and_salereturn TO user_prestige;
'''

v_one_sale_analize = '''
    CREATE OR REPLACE VIEW v_one_sale_analize
    AS SELECT 
        t.manager,
        t.customer,
        t.supplier,
        t.sku,
        t.doc_date::date as doc_date,
        t.doc_date as tarih,
        t.doc_number,
        t.tobox_sale,
        t.amount_sale,
        t.tobox_return,
        t.amount_return,
        t.tobox_sale + t.tobox_return AS tobox,
        t.amount_sale + t.amount_return AS amount,
        t.warehouse,
        t.segment,
        t.segment_folder
       FROM ( SELECT
                sku,
                doc_date,
                doc_number,
                customer,
                supplier,
                tobox AS tobox_sale,
                amount AS amount_sale,
                0 AS tobox_return,
                0 AS amount_return,
                distributor AS manager,
                organization_key,
                nomenclature_key,
                warehouse,
                segment,
                segment_folder
               FROM t_one_sale
               WHERE issale = 1
            UNION ALL
             SELECT sku,
                doc_date,
                doc_number,
                customer,
                supplier,
                0,
                0,
                - tobox,
                amount,
                distributor AS manager,
                organization_key,
                nomenclature_key,
                warehouse,
                segment,
                segment_folder
               FROM t_one_sale
               WHERE issale = -1
         ) t
      WHERE t.organization_key::text <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        AND t.doc_date <= CURRENT_DATE 
        AND (t.nomenclature_key::text IN (
              SELECT nom_2.ref_key
              FROM t_one_cat_nomenclature nom_2
              WHERE nom_2.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c')
    );

        COMMENT ON VIEW v_one_sale_analize IS 'анализ продаж и возвратов';

        GRANT SELECT ON TABLE v_one_sale_analize TO user_prestige;

'''

v_one_trebovanie = '''
    CREATE OR REPLACE VIEW v_one_trebovanie
    AS 
    SELECT 
        goods.id, 
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        'требование'::text AS customer,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        - goods.quantity as quantity,
        goods.coefficient,
        - (goods.quantity * goods.coefficient) AS ed,
        - ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        'требование'::text AS doc_type,
        org.description as organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        NULL AS customer_key,
        NULL as currency_key ,
        nom.supplier_key,
        serv.organization_key,
        serv.warehouse_key,
        goods.line_number
    FROM t_one_doc_requirement_invoice serv
        JOIN t_one_doc_requirement_invoice_materials goods USING (ref_key)
        LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
        JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations as org ON org.ref_key = serv.organization_key
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = serv.warehouse_key
    WHERE serv.doc_date::date <= current_date
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_trebovanie IS 'требование';
    COMMENT ON COLUMN v_one_trebovanie.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_trebovanie.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_trebovanie.sku IS 'sku';
    COMMENT ON COLUMN v_one_trebovanie.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_trebovanie.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_trebovanie.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_trebovanie.doc_type IS 'тип';

    GRANT SELECT ON TABLE v_one_trebovanie TO user_prestige;       

    '''

v_one_spisanie = '''
    CREATE OR REPLACE VIEW v_one_spisanie
    AS 
    SELECT 
        goods.id, 
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        'списание'::text AS customer,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        goods.quantity as quantity,
        goods.coefficient,
        (goods.quantity * goods.coefficient) AS ed,
        ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        'списание'::text AS doc_type,
        org.description as organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        NULL::text AS customer_key,
        NULL as currency_key,
        nom.supplier_key,
        serv.organization_key,
        serv.warehouse_key,
        serv.is_accounting,
        goods.line_number
    FROM t_one_doc_write_off_of_goods serv
        JOIN t_one_doc_write_off_of_goods_details goods USING (ref_key)
        JOIN v_one_nomenclature_inbox_supplier nom 
            ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations as org
            ON org.ref_key = serv.organization_key
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = serv.warehouse_key
    WHERE serv.doc_date::date <= current_date
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_spisanie IS 'списание';
    COMMENT ON COLUMN v_one_spisanie.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_spisanie.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_spisanie.sku IS 'sku';
    COMMENT ON COLUMN v_one_spisanie.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_spisanie.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_spisanie.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_spisanie.doc_type IS 'тип';
'''

v_one_oprihodivanie = '''
    CREATE OR REPLACE VIEW v_one_oprihodivanie AS 
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        'оприходывание'::text AS customer,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        - goods.quantity as quantity,
        goods.coefficient,
        - (goods.quantity * goods.coefficient) AS ed,
        - ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        'оприходывание'::text AS doc_type,
        org.description as organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        NULL::text AS customer_key,
        NULL as currency_key,
        serv.organization_key,
        nom.supplier_key,
        serv.warehouse_key,
        serv.is_accounting,
        NULL as contract_key,
        goods.line_number
    FROM t_one_doc_posting_goods serv
        JOIN t_one_doc_posting_goods_details goods USING (ref_key)
        JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations as org	ON org.ref_key = serv.organization_key
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = serv.warehouse_key
    WHERE serv.doc_date::date <= current_date
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_oprihodivanie IS 'оприходывание';
    COMMENT ON COLUMN v_one_oprihodivanie.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_oprihodivanie.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_oprihodivanie.sku IS 'sku';
    COMMENT ON COLUMN v_one_oprihodivanie.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_oprihodivanie.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_oprihodivanie.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_oprihodivanie.doc_type IS 'тип';
    '''

v_one_quantity_rows_in_document = '''
    CREATE OR REPLACE VIEW v_one_quantity_rows_in_document
    AS SELECT 
        t.type_,
        t.type_doc,
        t.rows_count,
        round(sum(t.rows_count) OVER (PARTITION BY t.type_ ORDER BY t.type_doc), 4) AS type_rows,
        round(sum(t.rows_count) OVER (ORDER BY t.type_doc), 4) AS type_doc_rows,
        round(t.ed,4) AS ed,
        round(sum(t.ed) OVER (PARTITION BY t.type_ ORDER BY t.type_doc), 4) AS type_ed,
        round(sum(t.ed) OVER (ORDER BY t.type_doc), 4) AS type_doc_ed,
        round(t.tobox,4) AS tobox,
        round(sum(t.tobox) OVER (PARTITION BY t.type_ ORDER BY t.type_doc), 4) AS type_box,
        round(sum(t.tobox) OVER (ORDER BY t.type_doc), 4) AS type_doc_box
       FROM ( SELECT 
                'оприходывание'::text AS type_doc,
                'пересорт'::text AS type_,
                count(1) AS rows_count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_oprihodivanie
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'списание'::text AS type_doc,
                'пересорт'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_spisanie
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'требование'::text AS type_doc,
                'требование'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_trebovanie
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'поступление'::text AS type_doc,
                'поступление'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_giris
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT
                'поступление возврат'::text AS type_doc,
                'поступление'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_giris_iade
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN (
                    SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'продажа'::text AS type_doc,
                'продажа'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_sale
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'продажа возврат'::text AS type_doc,
                'продажа'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_sale_return
             WHERE v_one_sale_return.doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'перемещение отправитель'::text AS type_doc,
                'перемещение'::text AS type_,
                count(1) AS count,
                - sum(ed) AS ed,
                - sum(tobox) AS tobox
               FROM v_one_movement
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'перемещение получатель'::text AS type_doc,
                'перемещение'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
               FROM v_one_movement
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
        ) t
      ORDER BY t.type_doc;

    COMMENT ON VIEW v_one_quantity_rows_in_document IS 'количество строк по типам документов до текущей даты';
'''

v_one_stock_maliyet_rapor_warehouse = '''
    CREATE OR REPLACE VIEW v_one_stock_maliyet_rapor_warehouse AS
    SELECT 
        warehouse,
        supplier,
        sku,
        last_maliyet AS maliyet,
        t.last_ostatok_ed AS ed,
        round(last_ostatok_krb,2) AS tobox,
        round(last_ostatok_krb * last_maliyet,3) AS amount_maliyet,
        warehouse_key
    FROM (
        SELECT 
            warehouse,
            supplier,
            sku,
            nomenclature_key,
            tarih,
            LAST_VALUE(maliyet) 
            OVER(
            PARTITION BY nomenclature_key
                ORDER BY tarih
                RANGE BETWEEN 
                    UNBOUNDED PRECEDING AND 
                    UNBOUNDED FOLLOWING
            ) last_maliyet,
            LAST_VALUE(ostatok_krb) 
            OVER(
            PARTITION BY warehouse, nomenclature_key
                ORDER BY tarih
                RANGE BETWEEN 
                    UNBOUNDED PRECEDING AND 
                    UNBOUNDED FOLLOWING
            ) last_ostatok_krb, 
            LAST_VALUE(ostatok_ed) 
            OVER(
            PARTITION BY warehouse, nomenclature_key
                ORDER BY tarih
                RANGE BETWEEN 
                    UNBOUNDED PRECEDING AND 
                    UNBOUNDED FOLLOWING
            ) last_ostatok_ed,
            warehouse_key            
        FROM 
            v_one_stock_maliyet
        WHERE date_max < current_date
            AND warehouse_key IN ('a26219b3-8fba-11e6-80c4-c936aa9c817c', '4b40b865-6d2f-11ec-8125-001dd8b72b55')
            AND ostatok_ed <> 0
    ) AS t
    GROUP BY warehouse_key, warehouse, supplier, sku, last_maliyet, nomenclature_key, last_ostatok_krb, last_ostatok_ed
    ORDER BY sku 
    ;

    GRANT SELECT ON TABLE v_one_stock_maliyet_rapor_warehouse TO user_prestige;

'''

v_one_movement = '''
    CREATE OR REPLACE VIEW v_one_movement AS
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        goods.quantity,
        goods.coefficient,
        (goods.quantity * goods.coefficient) AS ed,
        ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        tocw_out.description AS warehouse_out,
        tocw_in.description AS warehouse_in,
        'перемещение' as doc_type,
        nom.supplier,
        org.description AS organization,
        serv.ref_key AS document_key,
        nom.nomenclature_key,
        goods.nomenclature_series_key,
        nom.supplier_key,
        serv.organization_key,
        goods.unit_of_key,
        serv.shipping_warehouse_key AS warehouse_out_key,
        serv.warehouse_consignee_key AS warehouse_in_key,
        goods.line_number
    FROM t_one_doc_movement AS serv
        INNER JOIN t_one_doc_movement_details AS goods USING(ref_key)
        LEFT JOIN v_one_nomenclature_inbox_supplier AS nom
            ON nom.nomenclature_key = goods.nomenclature_key
        LEFT JOIN t_one_cat_warehouses tocw_out
            ON tocw_out.ref_key = serv.shipping_warehouse_key 
        LEFT JOIN t_one_cat_warehouses tocw_in 
            ON tocw_in.ref_key = serv.warehouse_consignee_key
        LEFT JOIN t_one_cat_organizations AS org
            ON org.ref_key = serv.organization_key 
    ;

    COMMENT ON COLUMN v_one_movement.doc_number IS 'номер док';
    COMMENT ON COLUMN v_one_movement.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_movement.inbox IS 'в коробке';
    COMMENT ON COLUMN v_one_movement.quantity IS 'количество';
    COMMENT ON COLUMN v_one_movement.coefficient IS 'коэффициент';
    COMMENT ON COLUMN v_one_movement.ed IS 'количество*коэффициент';
    COMMENT ON COLUMN v_one_movement.tobox IS 'количество*коэффициент/в коробке';
    COMMENT ON COLUMN v_one_movement.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_movement.warehouse_out IS 'склад отправитель';
    COMMENT ON COLUMN v_one_movement.warehouse_in IS 'склад получатель';
    COMMENT ON COLUMN v_one_movement.supplier IS 'поставщик товара';
    COMMENT ON COLUMN v_one_movement.organization IS 'организация';
    COMMENT ON VIEW v_one_movement IS 'перемещение';
'''

v_one_stock_maliyet_rapor = '''
    CREATE OR REPLACE VIEW v_one_stock_maliyet_rapor AS
    SELECT
        sku,
        maliyet,
        ed AS adet,
        tobox AS koli,
        ed * maliyet AS tutar
    FROM (
        SELECT DISTINCT 
            sku,
            nomenclature_key,
            supplier,
            maliyet,
            sum(quantity * coefficient) OVER (PARTITION BY  nomenclature_key) AS ed,
            sum(tobox) OVER (PARTITION BY  nomenclature_key) AS tobox,
            doc_date = max(doc_date) OVER (PARTITION BY nomenclature_key) AS isMaxDate
        FROM t_one_stock	
        WHERE warehouse_key 
            IN ('a26219b3-8fba-11e6-80c4-c936aa9c817c','4b40b865-6d2f-11ec-8125-001dd8b72b55')
            -- склады:
            -- Основной склад товаров	a26219b3-8fba-11e6-80c4-c936aa9c817c
            -- Основной склад товаров 2022	4b40b865-6d2f-11ec-8125-001dd8b72b55
    ) AS t
    WHERE isMaxDate = TRUE
        AND ed <> 0
    ORDER BY sku;

    GRANT SELECT ON TABLE v_one_stock_maliyet_rapor TO user_prestige;

'''

v_service_expense = '''
    CREATE OR REPLACE VIEW v_service_expense
    AS 
    SELECT concat(t.supplier,' от ' ,t.tarih::date, ' № ' ,t.doc_number) AS src, 
        t.supplier,
        t.tarih,
        t.doc_number,
        t.dop_doc_date,
        t.dop_doc_number,
        t.client,
        round(t.amount_ue, 3) AS amount_ue
    FROM ( SELECT DISTINCT gs.client AS supplier,
                gs.tarih,
                gs.doc_number,
                dop.doc_date AS dop_doc_date,
                dop.doc_number AS dop_doc_number,
                dop.client,
                dop.dop_sum_ue AS amount_ue
               FROM v_service_tc_group gs
                 LEFT JOIN v_service_tc_dop dop ON gs.batch_document::text = dop.batch_document::text
            UNION ALL
             SELECT DISTINCT gs.client AS supplier,
                gs.tarih AS gs_doc_date,
                gs.doc_number AS gs_doc_number,
                dop.tarih AS dop_doc_date,
                dop.gtd_number AS dop_doc_number,
                'НДС'::text AS client,
                dop.vat_usd
               FROM v_service_tc_group gs
                 LEFT JOIN v_gtd dop ON gs.batch_document::text = dop.batch_document::text
            UNION ALL
             SELECT DISTINCT gs.client AS supplier,
                gs.tarih AS gs_doc_date,
                gs.doc_number AS gs_doc_number,
                dop.tarih AS dop_doc_date,
                dop.gtd_number AS dop_doc_number,
                'пошлина'::text AS client,
                dop.duty_usd
               FROM v_service_tc_group gs
                 LEFT JOIN v_gtd dop ON gs.batch_document::text = dop.batch_document::text
    ) t
    WHERE coalesce(t.amount_ue,0) <> 0
    ORDER BY t.tarih DESC, t.doc_number, t.dop_doc_number;

    COMMENT ON VIEW v_service_expense IS 'док по поступлению и связанные с ним расходы';
    GRANT SELECT ON TABLE v_service_expense TO user_prestige;
'''

v_one_cash_order_receipt = '''
    CREATE OR REPLACE VIEW v_one_cash_order_receipt AS 
        SELECT 
            r.doc_date::date AS doc_date,
            r.doc_date AS tarih,
            r.doc_number,
            clients.description AS customer,
            contracts.description AS contract,
            coalesce(rd.amount_of_payment,0) as amount_of_payment,
            coalesce(rd.amount_vat,0) as amount_vat,
            sum(rd.amount_of_payment) OVER(PARTITION BY rd.ref_key) AS amount_of_payment_sum,
            sum(rd.amount_vat) OVER(PARTITION BY rd.ref_key) AS amount_of_vat_sum,            
            CASE
                WHEN r.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN --uah
                    round(sum(rd.amount_of_payment) OVER(PARTITION BY rd.ref_key) /rate.rate_usd_nbu,3)
                ELSE 
                    round(sum(rd.amount_of_payment) OVER(PARTITION BY rd.ref_key),3)                
            END AS amount_of_payment_sum_usd,
            CASE
                WHEN r.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN --uah
                    round(sum(rd.amount_vat) OVER(PARTITION BY rd.ref_key) / rate.rate_usd_nbu,3)
                ELSE 
                    round(sum(rd.amount_vat) OVER(PARTITION BY rd.ref_key),3)                
            END AS amount_of_vat_sum_usd,
            coalesce(contracts.limit_days_credit_ua,0) AS contract_days,
            rate.rate_usd_nbu,
            cur.description AS currency,
            item.description AS cash_flow_item,
            r.ref_key,
            r.currency_key,
            r.account_key,
            rd.contract_key,
            rd.cash_flow_item
        FROM t_one_doc_cash_order_receipt AS r
            LEFT JOIN t_one_doc_cash_order_receipt_details AS rd
                USING (ref_key)
            LEFT JOIN t_one_cat_currencies AS cur
                ON r.currency_key = cur.ref_key 
            LEFT JOIN t_one_cat_counterparties AS clients
                ON r.account_key = clients.ref_key
            LEFT JOIN t_one_cat_contracts_counterparties AS contracts
                ON rd.contract_key = contracts.ref_key 
            LEFT JOIN t_rate_nbu AS rate
                ON rate.rate_date::date = r.doc_date::date
            LEFT JOIN t_one_cat_cash_flow_item AS item
                ON rd.cash_flow_item = item.ref_key
        ORDER BY r.doc_date, r.doc_number
    ;

    COMMENT ON VIEW v_one_cash_order_receipt IS 'ПлатежноеПоручениеВходящее';

    COMMENT ON COLUMN v_one_cash_order_receipt.doc_number IS 'номер документа';
    COMMENT ON COLUMN v_one_cash_order_receipt.customer IS 'контрагент';
    COMMENT ON COLUMN v_one_cash_order_receipt.contract IS 'договор';
    COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_payment IS 'сумма оплаты';
    COMMENT ON COLUMN v_one_cash_order_receipt.amount_vat IS 'НДС';
    COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_payment_sum IS 'сумма по документу';
    COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_vat_sum IS 'НДС по документу';
    COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_payment_sum_usd IS 'сумма по документу usd';
    COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_vat_sum_usd IS 'НДС по документу usd';
    COMMENT ON COLUMN v_one_cash_order_receipt.contract_days IS 'лимит дней задолженности';
    COMMENT ON COLUMN v_one_cash_order_receipt.rate_usd_nbu IS 'курс НБУ';
    COMMENT ON COLUMN v_one_cash_order_receipt.currency IS 'валюта';
    COMMENT ON COLUMN v_one_cash_order_receipt.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств';
    COMMENT ON COLUMN v_one_cash_order_receipt.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN v_one_cash_order_receipt.currency_key IS 'Валюта_Key';
    COMMENT ON COLUMN v_one_cash_order_receipt.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN v_one_cash_order_receipt.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN v_one_cash_order_receipt.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';        
'''

v_one_cash_order_expense = '''
    CREATE OR REPLACE VIEW v_one_cash_order_expense
    AS SELECT r.doc_date::date AS doc_date,
        r.doc_date AS tarih,
        r.doc_number,
        clients.description AS customer,
        contracts.description AS contract,
        item.description AS cash_flow_item,
        COALESCE(rd.amount_of_payment, 0::numeric) AS amount_of_payment,
        COALESCE(rd.amount_vat, 0::numeric) AS amount_vat,
        sum(rd.amount_of_payment) OVER (PARTITION BY rd.ref_key) AS amount_of_payment_sum,
        sum(rd.amount_vat) OVER (PARTITION BY rd.ref_key) AS amount_of_vat_sum,
        CASE
            WHEN r.currency_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- UAH
                round(sum(rd.amount_of_payment) OVER (PARTITION BY rd.ref_key) / rate.rate_usd_nbu, 3)
            ELSE 
                round(sum(rd.amount_of_payment) OVER (PARTITION BY rd.ref_key), 3)
        END AS amount_of_payment_sum_usd,
        CASE
            WHEN r.currency_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN 
                round(sum(rd.amount_vat) OVER (PARTITION BY rd.ref_key) / rate.rate_usd_nbu, 3)
            ELSE 
                round(sum(rd.amount_vat) OVER (PARTITION BY rd.ref_key), 3)
        END AS amount_of_vat_sum_usd,
        COALESCE(contracts.limit_days_credit_ua, 0) AS contract_days,
        rate.rate_usd_nbu,
        cur.description AS currency,
        r.ref_key,
        r.currency_key,
        r.account_key,
        rd.contract_key,
        rd.cash_flow_item
       FROM t_one_doc_cash_order_expense r
         LEFT JOIN t_one_doc_cash_order_expense_details rd 
            USING (ref_key)
         LEFT JOIN t_one_cat_currencies cur 
            ON r.currency_key::text = cur.ref_key::text
         LEFT JOIN t_one_cat_counterparties clients 
            ON r.account_key::text = clients.ref_key::text
         LEFT JOIN t_one_cat_contracts_counterparties contracts 
            ON rd.contract_key::text = contracts.ref_key::text
         LEFT JOIN t_rate_nbu rate 
            ON rate.rate_date = r.doc_date::date
         LEFT JOIN t_one_cat_cash_flow_item item 
            ON rd.cash_flow_item::text = item.ref_key::text
       WHERE doc_date::Date >= '01.01.2023' 
       ORDER BY r.doc_date, r.doc_number;

    COMMENT ON VIEW v_one_cash_order_expense IS 'ПлатежноеПоручениеИсходящее';

    COMMENT ON COLUMN v_one_cash_order_expense.doc_number IS 'номер документа';
    COMMENT ON COLUMN v_one_cash_order_expense.customer IS 'контрагент';
    COMMENT ON COLUMN v_one_cash_order_expense.contract IS 'договор';
    COMMENT ON COLUMN v_one_cash_order_expense.amount_of_payment IS 'сумма оплаты';
    COMMENT ON COLUMN v_one_cash_order_expense.amount_vat IS 'НДС';
    COMMENT ON COLUMN v_one_cash_order_expense.amount_of_payment_sum IS 'сумма по документу';
    COMMENT ON COLUMN v_one_cash_order_expense.amount_of_vat_sum IS 'НДС по документу';
    COMMENT ON COLUMN v_one_cash_order_expense.amount_of_payment_sum_usd IS 'сумма по документу usd';
    COMMENT ON COLUMN v_one_cash_order_expense.amount_of_vat_sum_usd IS 'НДС по документу usd';
    COMMENT ON COLUMN v_one_cash_order_expense.contract_days IS 'лимит дней задолженности';
    COMMENT ON COLUMN v_one_cash_order_expense.rate_usd_nbu IS 'курс НБУ';
    COMMENT ON COLUMN v_one_cash_order_expense.currency IS 'валюта';
    COMMENT ON COLUMN v_one_cash_order_expense.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств';
    COMMENT ON COLUMN v_one_cash_order_expense.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN v_one_cash_order_expense.currency_key IS 'Валюта_Key';
    COMMENT ON COLUMN v_one_cash_order_expense.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN v_one_cash_order_expense.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN v_one_cash_order_expense.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
'''

v_one_correction_series = '''
    CREATE OR REPLACE VIEW v_one_correction_series AS 
        SELECT 
            serv.dataversion,
            serv.doc_date::date as doc_date,
            serv.doc_date as tarih,
            serv.doc_number,	
            nom.sku,
            nom.inbox,
            dtl.quantity,
            dtl.coefficient,
            (dtl.quantity * dtl.coefficient) AS ed,
            round((dtl.quantity * dtl.coefficient) / nom.inbox,3) AS tobox,
            units.description AS unit,
            warehouse.description AS warehouse,
            org.description AS organization,
            nom.supplier,
            serv.ref_key as document_key,
            dtl.nomenclature_key,
            serv.warehouse_key,
            nom.supplier_key,
            dtl.unit_of_key,
            serv.organization_key
        FROM t_one_doc_correction_series AS serv
            INNER JOIN t_one_doc_correction_series_details AS dtl
                USING (ref_key)
            LEFT JOIN t_one_cat_warehouses AS warehouse
                ON warehouse.ref_key = serv.warehouse_key 
            LEFT JOIN v_one_nomenclature_inbox_supplier AS nom
                ON nom.nomenclature_key = dtl.nomenclature_key 
            LEFT JOIN 
                (
                    SELECT *
                    FROM t_one_cat_units
                    WHERE NOT deletion_mark
                )
            AS units
                ON units.ref_key = dtl.unit_of_key
            LEFT JOIN t_one_cat_organizations AS org
                ON org.ref_key = serv.organization_key 
        WHERE serv.posted = TRUE AND serv.is_management = TRUE 
        ORDER BY doc_date, doc_number, nom.sku
    ;
'''

v_one_cash_warrant_receipt = '''
    CREATE OR REPLACE VIEW v_one_cash_warrant_receipt AS 
    SELECT 
        main.dataversion,
        main.doc_date,
        main.doc_number,
        main.operation_type,
        main.base,
        clients.description AS client,
        cur2.description AS document_currency,
        main.a_comment,
        main.application,
        main.amount,
        main.vat_rate,
        cur1.description AS worker_settlement_currency,
        cash.description AS checkout,
        assign.description AS assignment_of_money,
        org.description AS organization,
        clause.description AS clause_main,
        contract.description AS contract,
        clause1.description AS clause_detail
    FROM t_one_doc_cash_warrant_receipt AS main
        LEFT JOIN t_one_doc_cash_warrant_receipt_details AS detail
            USING(ref_key)
        LEFT JOIN t_one_cat_counterparties AS clients
            ON clients.ref_key = main.contractor_key 
        LEFT JOIN t_one_cat_currencies AS cur1
            ON cur1.ref_key = main.worker_settlement_currency_key 
        LEFT JOIN t_one_cat_currencies AS cur2
            ON cur2.ref_key = main.currency_key 
        LEFT JOIN t_one_cat_cash AS cash
            ON cash.ref_key = main.checkout_key 
        LEFT JOIN t_one_cat_cash_assignment AS assign
            ON assign.ref_key = main.assignment_key 
        LEFT JOIN t_one_cat_organizations AS org
            ON org.ref_key = main.organization_key 
        LEFT JOIN t_one_cat_cash_flow_item AS clause
            ON clause.ref_key = main.cash_flow_item 
        LEFT JOIN t_one_cat_contracts_counterparties AS contract
            ON contract.ref_key = detail.contract_key
        LEFT JOIN t_one_cat_cash_flow_item AS clause1
            ON clause1.ref_key = detail.cash_flow_item
    WHERE main.posted = TRUE AND is_management = TRUE 
    ORDER BY main.doc_date DESC
    ;

    COMMENT ON VIEW v_one_cash_warrant_receipt IS 'Приходный кассовый ордер';

'''

v_one_cash_warrant_expense = '''
    CREATE OR REPLACE VIEW v_one_cash_warrant_expense AS 
    SELECT 
        main.dataversion,
        main.doc_date,
        main.doc_number,
        main.operation_type,
        main.base,
        clients.description AS client,
        cur2.description AS document_currency,
        main.a_comment,
        main.application,
         - main.amount as document_amount,
        main.vat_rate,
        cur1.description AS worker_settlement_currency,
        cash.description AS checkout,
        assign.description AS assignment_of_money,
        org.description AS organization,
        clause.description AS clause_main,
        contract.description AS contract,
        clause1.description AS clause_detail
    FROM t_one_doc_cash_warrant_expense AS main
        LEFT JOIN t_one_doc_cash_warrant_expense_details AS detail
            USING(ref_key)
        LEFT JOIN t_one_cat_counterparties AS clients
            ON clients.ref_key = main.contractor_key 
        LEFT JOIN t_one_cat_currencies AS cur1
            ON cur1.ref_key = main.worker_settlement_currency_key 
        LEFT JOIN t_one_cat_currencies AS cur2
            ON cur2.ref_key = main.currency_key 
        LEFT JOIN t_one_cat_cash AS cash
            ON cash.ref_key = main.checkout_key 
        LEFT JOIN t_one_cat_cash_assignment AS assign
            ON assign.ref_key = main.assignment_key 
        LEFT JOIN t_one_cat_organizations AS org
            ON org.ref_key = main.organization_key 
        LEFT JOIN t_one_cat_cash_flow_item AS clause
            ON clause.ref_key = main.cash_flow_item 
        LEFT JOIN t_one_cat_contracts_counterparties AS contract
            ON contract.ref_key = detail.contract_key
        LEFT JOIN t_one_cat_cash_flow_item AS clause1
            ON clause1.ref_key = detail.cash_flow_item
    WHERE main.posted = TRUE AND is_management = TRUE 
    ORDER BY main.doc_date DESC
    ;

    COMMENT ON VIEW v_one_cash_warrant_expense IS 'Расходный кассовый ордер';

'''

v_one_cash_warrant_receipt_expense = '''
    CREATE OR REPLACE VIEW v_one_cash_warrant_receipt_expense AS 
    SELECT *
    FROM (
        SELECT *,
            'receipt' AS receipt
        FROM v_one_cash_warrant_receipt
        UNION ALL
        SELECT *,
            'expense' AS expense
        FROM v_one_cash_warrant_expense 
    ) AS t
    ORDER BY t.doc_date, t.doc_number 
    ;

    COMMENT ON VIEW v_one_cash_warrant_receipt_expense IS 'Приходный/Расходный кассовый ордер';
'''

v_one_cash_order_receipt_expense = '''
    CREATE OR REPLACE VIEW v_one_cash_order_receipt_expense
    AS SELECT t.doc_date,
        t.tarih,
        t.doc_number,
        t.customer,
        t.contract,
        t.amount_of_payment,
        t.amount_vat,
        t.amount_of_payment_sum,
        t.amount_of_vat_sum,
        t.amount_of_payment_sum_usd,
        t.amount_of_vat_sum_usd,
        t.contract_days,
        t.rate_usd_nbu,
        t.currency,
        t.cash_flow_item,
        t.ref_key,
        t.currency_key,
        t.account_key,
        t.contract_key,
        t.cash_flow_item,
        t.receipt
       FROM ( SELECT v_one_cash_order_receipt.doc_date,
                v_one_cash_order_receipt.tarih,
                v_one_cash_order_receipt.doc_number,
                v_one_cash_order_receipt.customer,
                v_one_cash_order_receipt.contract,
                v_one_cash_order_receipt.amount_of_payment,
                v_one_cash_order_receipt.amount_vat,
                v_one_cash_order_receipt.amount_of_payment_sum,
                v_one_cash_order_receipt.amount_of_vat_sum,
                v_one_cash_order_receipt.amount_of_payment_sum_usd,
                v_one_cash_order_receipt.amount_of_vat_sum_usd,
                v_one_cash_order_receipt.contract_days,
                v_one_cash_order_receipt.rate_usd_nbu,
                v_one_cash_order_receipt.currency,
                v_one_cash_order_receipt.cash_flow_item,
                v_one_cash_order_receipt.ref_key,
                v_one_cash_order_receipt.currency_key,
                v_one_cash_order_receipt.account_key,
                v_one_cash_order_receipt.contract_key,
                v_one_cash_order_receipt.cash_flow_item,
                'receipt'::text AS receipt
               FROM v_one_cash_order_receipt
            UNION ALL
             SELECT v_one_cash_order_expense.doc_date,
                v_one_cash_order_expense.tarih,
                v_one_cash_order_expense.doc_number,
                v_one_cash_order_expense.customer,
                v_one_cash_order_expense.contract,
                v_one_cash_order_expense.amount_of_payment,
                v_one_cash_order_expense.amount_vat,
                v_one_cash_order_expense.amount_of_payment_sum,
                v_one_cash_order_expense.amount_of_vat_sum,
                v_one_cash_order_expense.amount_of_payment_sum_usd,
                v_one_cash_order_expense.amount_of_vat_sum_usd,
                v_one_cash_order_expense.contract_days,
                v_one_cash_order_expense.rate_usd_nbu,
                v_one_cash_order_expense.currency,
                v_one_cash_order_expense.cash_flow_item,
                v_one_cash_order_expense.ref_key,
                v_one_cash_order_expense.currency_key,
                v_one_cash_order_expense.account_key,
                v_one_cash_order_expense.contract_key,
                v_one_cash_order_expense.cash_flow_item,
                'expense'::text AS expense
               FROM v_one_cash_order_expense) t
      ORDER BY t.doc_date, t.doc_number;
'''

v_one_cash = '''
    CREATE OR REPLACE VIEW v_one_cash AS
    SELECT DISTINCT 
        t.dtperiod::date AS doc_date,
        t.dtperiod AS tarih,
        t.docnumber,
        t.amount_receipt,
        t.amount_expense,
        t.operation_type,
        clause.description AS clause,
        t.client,
        podotchetniy,
        t.a_comment,
        t.kasa,
        t.line_number,
        t.type_of_cash,
        t.active,
        t.record_type,
        t.recorder_type,
        t.bank_account_cashier_type,
        t.recorder_key,
        --t.contract_key,
        t.bank_account_cashier_key,
        t.cash_flow_item,
        t.organization_key
    FROM (
        SELECT
            concat(
                warrant_receipt.operation_type,
                warrant_expense.operation_type
            ) as operation_type, 
            CASE 
                WHEN recorder_type = 'StandardODATA.Document_КорректировкаЗаписейРегистров' THEN 
                    'перемещение между кассами'
                ELSE 
                    concat(
                        cor_reg.description,
                        money_check.a_comment,
                        movement.a_comment,
                        order_expense.a_comment,
                        order_receipt.a_comment,
                        warrant_expense.a_comment,
                        warrant_receipt.a_comment,
                        spisanie.a_comment)
            END	AS a_comment,
            concat(
                cash.description,
                bank.description
            ) AS kasa,
            CASE
                WHEN record_type ILIKE '%Receipt%' THEN amount_control
                ELSE 0
            END AS amount_receipt,
            CASE
                WHEN record_type ILIKE '%Expense%' THEN -amount_control
                ELSE 0
            END AS amount_expense,
            concat(
                warrant_receipt.doc_number, 
                warrant_expense.doc_number, 
                order_receipt.doc_number,
                order_expense.doc_number, 
                movement.doc_number,
                money_check.doc_number, 
                spisanie.doc_number, 
                cor_reg.doc_number 
            ) AS docnumber,
            concat(
                client_spisanie.description,
                client_warrant_receipt.description,
                warrant_expense.give_out,
                client_warrant_expense.description,
                client_order_receipt.description,
                client_order_expense.description
            ) AS client,
            acc.*,
        concat(individuals2.description,individuals.description) AS podotchetniy
        FROM t_one_accreg_cash_recordtype AS acc
            LEFT JOIN t_one_cat_cash AS cash -- Catalog_Кассы
                ON cash.ref_key = acc.bank_account_cashier_key
            LEFT JOIN t_one_cat_cash_bank_accounts AS bank -- Catalog_БанковскиеСчета
                ON bank.ref_key = acc.bank_account_cashier_key
            LEFT JOIN t_one_doc_cash_money_check AS money_check -- Document_ДенежныйЧек
                ON money_check.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_receipt AS warrant_receipt -- Document_ПриходныйКассовыйОрдер
                ON warrant_receipt.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_receipt_details AS warrant_receipt_details -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                ON warrant_receipt_details.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_expense AS warrant_expense -- Document_РасходныйКассовыйОрдер
                ON warrant_expense.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_expense_details AS warrant_expense_details -- Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
                ON warrant_expense_details.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_order_receipt AS order_receipt -- Document_ПлатежноеПоручениеВходящее
                ON order_receipt.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_order_receipt_details AS order_receipt_details -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                ON order_receipt_details.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_order_expense AS order_expense -- Document_ПлатежноеПоручениеИсходящее
                ON order_expense.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_order_expense_details AS order_expense_details -- Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
                ON order_expense_details.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_withdrawal_of_funds AS spisanie -- Document_ПлатежныйОрдерСписаниеДенежныхСредств
                ON spisanie.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_withdrawal_of_funds_details AS spisanie_details -- Document_ПлатежныйОрдерСписаниеДенежныхСредств_РасшифровкаПлатежа
                ON spisanie_details.ref_key = acc.recorder_key		
            LEFT JOIN t_one_doc_cash_movement AS movement -- Document_ВнутреннееПеремещениеНаличныхДенежныхСредств
                ON movement.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_correction_of_register AS cor_reg -- Document_КорректировкаЗаписейРегистров
                ON cor_reg.ref_key = acc.recorder_key
            LEFT JOIN t_one_cat_currencies AS cur_kasa -- currency cash
                ON cur_kasa.ref_key = cash.currency_key
            LEFT JOIN t_one_cat_currencies AS cur_bank -- currency bank
                ON cur_bank.ref_key = bank.currency_key
            LEFT JOIN t_one_cat_counterparties AS client_spisanie
                ON spisanie.account_key = client_spisanie.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_warrant_receipt
                ON warrant_receipt.contractor_key = client_warrant_receipt.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_warrant_expense
                ON warrant_expense.contractor_key = client_warrant_expense.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_order_receipt
                ON order_receipt.account_key = client_order_receipt.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_order_expense
                ON order_expense.account_key = client_order_expense.ref_key
            LEFT JOIN t_one_cat_individuals AS individuals 
                ON warrant_expense.contractor_key = individuals.ref_key  
            LEFT JOIN t_one_cat_individuals AS individuals2 
                ON warrant_receipt.contractor_key = individuals2.ref_key  
        WHERE active = TRUE
    ) AS t
        LEFT JOIN t_one_cat_cash_flow_item AS clause
            ON t.cash_flow_item = clause.ref_key 
    ORDER BY dtperiod DESC, docnumber
    ;

    GRANT SELECT ON TABLE v_one_cash TO user_prestige;

'''

sql_pg_stat_activity = '''
    SELECT *, pg_terminate_backend(pid)
    FROM pg_stat_activity 
    WHERE pid <> pg_backend_pid()
    AND datname = 'prestige';
'''

v_one_infreg_contact_information_phone = '''
    CREATE OR REPLACE VIEW v_one_infreg_contact_information_phone AS
    SELECT 
    regexp_replace (UNNEST(REGEXP_MATCHES(REPLACE(performance,',','; '),
    '.?\d{10}|\(?\d{3}\)?.\d{7}|\(?\d{3}\)?.\d{2}.\d{2}.\d{3}|\d{3}.\d{3}.\d{2}.\d{2}|\d{3}.\d{2}.\d{3}.\d{2}|\d{3}.\d{3}.\d{4}|\d{4}.\d{6}|\d{6}.\d{2}.\d{2}|\d{6}.\d{2}.\d{2}|\d{8}.\d{2}|\(?\d{3}\)?.\d{5}.\d{2}','gm'))
    ,'[-(). ]*','','gm')  AS phone,
    couterpart.*,
    inf.object_type
    FROM t_one_infreg_contact_information AS inf
        LEFT JOIN t_one_cat_counterparties AS couterpart
            ON couterpart.ref_key = inf.an_object
;
    '''

v_one_clients_contact = '''
    CREATE OR REPLACE VIEW v_one_clients_contact AS 
    SELECT DISTINCT 
        t.*,
        left(phone,3) as operator_code
    FROM (
        SELECT 
            concat(inf_reg_phone.description,
                cat_cp.description, 
                cat_cpc.description, 
                cat_c.description, 
                cat_org.description,
                cat_user.description,
                cat_ind.description
            ) AS client,
            concat('38',tcc.phone) AS phone, 
            tcc.refkey,
            tcc.catalog_source
        FROM t_clients_contact AS tcc
            LEFT JOIN t_one_cat_contact_person AS cat_cp -- Catalog_КонтактныеЛица
                ON tcc.refkey = cat_cp.ref_key 
            LEFT JOIN t_one_cat_contact_persons_of_counterparties AS cat_cpc -- Catalog_КонтактныеЛицаКонтрагентов
                ON tcc.refkey = cat_cpc.ref_key 
            LEFT JOIN t_one_cat_counterparties AS cat_c -- Catalog_Контрагенты
                ON tcc.refkey = cat_c.ref_key 
            LEFT JOIN t_one_cat_organizations AS cat_org -- Catalog_Организации
                ON tcc.refkey = cat_org.ref_key 
            LEFT JOIN t_one_cat_users AS cat_user -- Catalog_Пользователи
                ON tcc.refkey = cat_user.ref_key
            LEFT JOIN t_one_cat_individuals AS cat_ind -- Catalog_ФизическиеЛица
                ON tcc.refkey = cat_ind.ref_key
            LEFT JOIN v_one_infreg_contact_information_phone AS inf_reg_phone ON inf_reg_phone.ref_key = cat_c.ref_key
        ) AS t
    WHERE client <> '' 
    ORDER BY client;

'''

v_one_logistics_not_find = '''
    CREATE OR REPLACE VIEW v_one_logistics_not_find AS
    SELECT  x.* 
    FROM t_one_sale_logistics x
    WHERE ((doc_date::date < current_date - 2) 
        AND (doc_date::date >= '01.01.2023') 
        AND ((carrier IS NULL) OR carrier = ''))
        AND date_send = '0001-01-01'
    ORDER BY counterparty, doc_date
    ;
    GRANT SELECT ON TABLE v_one_logistics_not_find TO user_prestige;
'''

v_one_logistic_kiev_in_order_carrier_isempty = '''
    -- the date_send is inserted, but the logistic is empty
    CREATE OR REPLACE VIEW v_one_logistic_kiev_in_order_carrier_isempty AS
    SELECT *
    FROM t_one_sale_logistics
    WHERE (doc_date >= '01.03.23') 
        AND (date_send < current_date) 
        AND (date_send != '0001-01-01' OR date_send IS NULL)
        AND ((add_to_delivery_address = '') 
        OR (add_to_delivery_address IS NULL))
    ;
    GRANT SELECT ON TABLE v_one_logistic_kiev_in_order_carrier_isempty TO user_prestige;    

'''

v_one_logistic_kiev_in_order_datesend_isempty = '''
    -- the date send is empty but logistic is inserted
    CREATE OR REPLACE VIEW v_one_logistic_kiev_in_order_datesend_isempty AS
    SELECT * 
    FROM t_one_sale_logistics
    WHERE (doc_date >= '01.03.2023') 
        AND (date_send = '0001-01-01') 
        AND (add_to_delivery_address IS NOT NULL) 
        AND (add_to_delivery_address <> '') 
        AND (carrier IS NOT NULL)	
        AND (carrier <> '')
    ;
    GRANT SELECT ON TABLE v_one_logistic_kiev_in_order_datesend_isempty TO user_prestige;    
'''

v_stickers = '''
    CREATE OR REPLACE VIEW v_stickers AS 
    SELECT 
        idchat,
        username сотрудник,
        datesticker дата,
        sku_code as sku,
        x.quantity AS количество,
        x.amount AS сумма
    FROM t_stickers x
        LEFT JOIN
        (SELECT DISTINCT chat_id , username
        FROM t_telegram) AS t
        ON chat_id = idchat
--    GROUP BY username, datesticker, idchat
    ORDER BY datesticker, username
    ;

    GRANT SELECT ON TABLE v_stickers TO user_prestige;
'''

v_one_correction = f'''
CREATE OR REPLACE VIEW v_one_correction
AS SELECT
    goods.id, 
    serv.doc_date::date AS doc_date,
    serv.doc_date AS tarih,
    serv.doc_number,
    nom.sku,
    nom.inbox,
    goods.quantity,
    goods.coefficient,
    goods.quantity * goods.coefficient AS ed,
    goods.quantity * goods.coefficient / nom.inbox AS tobox,
    tocw_out.description AS warehouse,
    'корректировка'::text AS doc_type,
    nom.supplier,
    org.description AS organization,
    serv.ref_key AS document_key,
    nom.nomenclature_key,
    goods.nomenclature_series_key,
    goods.nomenclature_series_new_key,
    nom.supplier_key,
    serv.organization_key,
    goods.unit_of_key,
    serv.warehouse_key,
    goods.line_number
   FROM t_one_doc_correction_series serv
     JOIN t_one_doc_correction_series_details goods USING (ref_key)
     LEFT JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
     LEFT JOIN t_one_cat_warehouses tocw_out ON tocw_out.ref_key::text = serv.warehouse_key::text
     LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text;

    COMMENT ON VIEW v_one_correction IS 'корректировка';
    COMMENT ON COLUMN v_one_correction.doc_number IS 'номер док';
    COMMENT ON COLUMN v_one_correction.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_correction.inbox IS 'в коробке';
    COMMENT ON COLUMN v_one_correction.quantity IS 'количество';
    COMMENT ON COLUMN v_one_correction.coefficient IS 'коэффициент';
    COMMENT ON COLUMN v_one_correction.ed IS 'количество*коэффициент';
    COMMENT ON COLUMN v_one_correction.tobox IS 'количество*коэффициент/в коробке';
    COMMENT ON COLUMN v_one_correction.warehouse IS 'склад';
    COMMENT ON COLUMN v_one_correction.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_correction.supplier IS 'поставщик товара';
    COMMENT ON COLUMN v_one_correction.organization IS 'организация';

'''

create_function_array_sort_unique = '''
    CREATE OR REPLACE FUNCTION array_sort_unique (ANYARRAY) RETURNS ANYARRAY
    LANGUAGE SQL
    AS $body$
      SELECT ARRAY(
        SELECT DISTINCT $1[s.i]
        FROM generate_series(array_lower($1,1), array_upper($1,1)) AS s(i)
        ORDER BY 1
      );
    $body$;
'''

v_one_doc_tax_gtd_sale = '''
    CREATE OR REPLACE VIEW v_one_doc_tax_gtd_sale AS 
    SELECT 
        stock.supplier,
        stock.organization,
        stock.customer,
        stock.customer_edrpou,
        stock.sku,
        stock.warehouse,
        tax_main.doc_date AS tax_date,
        tax_main.doc_number AS tax_num,
        RIGHT(tax_main.doc_number,8)::numeric AS tax_num_short,
        stock.doc_date AS selling_date,
        stock.doc_number AS selling_num,
        RIGHT(stock.doc_number,5)::numeric AS selling_num_short,
        stock.quantity,
        stock.coefficient,
        stock.quantity * stock.coefficient AS ed,
        sum(stock.quantity * stock.coefficient) OVER(PARTITION BY tax_main.organization_key, code_uktvd_key ORDER BY tax_main.doc_date, tax_det.line_number)  AS stock_ed,
        sum(stock.quantity * stock.coefficient) OVER(PARTITION BY tax_main.organization_key, left(code_uktvd_key,4) ORDER BY tax_main.doc_date, tax_det.line_number)  AS stock_ed_short,
        stock.tobox,
        stock.doc_type,
        LEFT(gtd.code,4)::numeric AS short_uktzt,
        gtd.code AS tax_code,
        tax_main.ref_key,
        tax_det.nomenclature_key,
        tax_det.code_uktvd_key
    FROM t_one_doc_tax_sale AS tax_main
        LEFT JOIN  t_one_doc_tax_sale_details AS tax_det
            ON tax_main.ref_key = tax_det.ref_key 
        LEFT JOIN t_one_stock AS stock 
            ON tax_main.document_base_key = stock.document_key
                AND tax_det.nomenclature_key = stock.nomenclature_key 
        LEFT JOIN t_one_cat_uktved AS gtd
            ON gtd.ref_key = tax_det.code_uktvd_key 
    WHERE tax_main.posted = TRUE 
        AND tax_main.doc_date >= '01.09.2022'
    ORDER BY
        tax_main.doc_date DESC,
        tax_main.doc_number,
        tax_det.line_number
    ;

'''

v_one_doc_tax_gtd_sale_return = '''
CREATE OR REPLACE VIEW v_one_doc_tax_gtd_sale_return
AS SELECT stock.supplier,
    stock.organization,
    stock.customer,
    stock.customer_edrpou,
    stock.sku,
    stock.warehouse,
    tax_main.doc_date AS tax_date,
    tax_main.doc_number AS tax_num,
    "right"(tax_main.doc_number::text, 8)::numeric AS tax_num_short,
    stock.doc_date AS selling_date,
    stock.doc_number AS selling_num,
    "right"(stock.doc_number::text, 5)::numeric AS selling_num_short,
    stock.quantity,
    stock.coefficient,
    stock.quantity * stock.coefficient AS ed,
    sum(stock.quantity * stock.coefficient) OVER (PARTITION BY tax_main.organization_key, tax_det.code_uktved_key 
        ORDER BY tax_main.doc_date, tax_det.line_number) AS stock_ed,
    sum(stock.quantity * stock.coefficient) OVER (PARTITION BY tax_main.organization_key, 
        ("left"(tax_det.code_uktved_key::text, 4)) ORDER BY tax_main.doc_date, tax_det.line_number) AS stock_ed_short,
    stock.tobox,
    stock.doc_type,
    "left"(gtd.code::text, 4)::numeric AS short_uktzt,
    gtd.code AS tax_code,
    tax_det.nomenclature_key,
    tax_det.code_uktved_key,
    tax_main.tax_invoice_key
   FROM t_one_doc_tax_appendix_2  tax_main
     JOIN t_one_doc_tax_appendix_2_details tax_det ON tax_main.ref_key::text = tax_det.ref_key::text
     JOIN t_one_stock stock ON tax_main.document_base_key::text = stock.document_key::text 
        AND tax_det.nomenclature_key::text = stock.nomenclature_key::text
     LEFT JOIN t_one_cat_uktved gtd ON gtd.ref_key::text = tax_det.code_uktved_key::text
   WHERE tax_main.posted = true AND tax_main.doc_date >= '2022-09-01 00:00:00'::timestamp without time zone
   ORDER BY tax_main.doc_date DESC, tax_main.doc_number, tax_det.line_number;

'''

v_one_managers_and_counterparts = '''
    CREATE OR REPLACE VIEW v_one_managers_and_counterparts AS 
    SELECT DISTINCT 
        managers.direction,
        client_folder.description AS folder_name,
        managers.manager,
        clients_sub.description AS client,
        clients_sub.ref_key AS contract_key,
        cm.manager_key
    FROM t_one_cat_counterparties AS client_folder
        RIGHT JOIN  t_one_cat_counterparties AS clients_sub
            ON client_folder.ref_key = clients_sub.parent_key 
        LEFT JOIN t_one_cat_counterparties_managers AS cm
            ON cm.ref_key = clients_sub.ref_key
        LEFT JOIN 
            (
            SELECT 
                sales_area.description AS direction,
                mngrs.description AS manager,
                mngrs.individual_key 
            FROM t_one_cat_users AS sales_area
                INNER JOIN t_one_cat_users AS mngrs
                    ON mngrs.ref_key = sales_area.individual_key
            WHERE sales_area.isfolder AND sales_area.individual_key <> '2b34889f-e345-11ec-8134-001dd8b740bc' -- Уволенные
                AND NOT mngrs.isfolder 
            ) AS managers
            ON managers.individual_key = cm.manager_key
        INNER JOIN t_one_doc_sale_of_goods_services AS sales
            ON sales.account_key = clients_sub.ref_key 
    WHERE client_folder.isfolder 
        AND sales.is_management AND sales.posted
        AND NOT clients_sub.isfolder
        AND client_folder.parent_key = 'ad421841-905f-11e6-80c4-c936aa9c817c' -- 010 Покупатели
        AND clients_sub.parent_key NOT IN ('c6a0df91-ebe1-11ec-8135-001dd8b740bc') -- МЕНЕДЖЕРЬІ ВЬІБЬІВШИЕ
        AND sales.doc_date::date >= '01.01.2020'::date
        AND manager_key IS NULL 
    ORDER BY folder_name, client
    ;

    GRANT SELECT ON TABLE v_one_managers_and_counterparts TO user_prestige;

'''

v_one_nomenclature_series_double = '''
    CREATE OR REPLACE VIEW v_one_nomenclature_series_double AS 
    SELECT nom.code, nom.description, ser.*
    FROM t_one_cat_nomenclature AS nom
        INNER JOIN 
        (
            SELECT nomenclature_key, sell_by, count(sell_by)
            FROM t_one_cat_nomenclature_series
            GROUP BY nomenclature_key, sell_by
            HAVING count(sell_by) > 1
        ) AS ser
        ON nom.ref_key = ser.nomenclature_key
    ORDER BY description;

'''

v_one_salary_paid_bank = '''
CREATE OR REPLACE VIEW v_one_salary_paid_bank
AS SELECT DISTINCT 
    org.description AS organization,
    bank.doc_date::date AS doc_date,
    bank.doc_number,
    emp.description AS employee,
    sp.amount_management AS amount,
    concat(EXTRACT(year FROM slr.registration_period), '.', 
        TRIM(BOTH FROM to_char(EXTRACT(month FROM slr.registration_period), '00'::text)))::numeric(6,2) AS salary_period,
    clause.description AS clause,
    sp.ref_key,
    sp.employee_key,
    cash_flow_item
   FROM t_one_cat_organizations org
     JOIN t_one_doc_salary slr ON org.ref_key::text = slr.organization_key::text
     JOIN t_one_doc_salary_payment sp ON slr.ref_key::text = sp.ref_key::text
     JOIN t_one_cat_employees emp ON emp.ref_key::text = sp.employee_key::text
     JOIN t_one_doc_salary_payment_of_wages wgs ON wgs.payroll_key::text = slr.ref_key::text
     JOIN t_one_doc_cash_order_expense bank ON bank.ref_key::text = wgs.ref_key::TEXT
     JOIN t_one_cat_cash_flow_item AS clause ON clause.ref_key = cash_flow_item
    ;
'''

v_one_salary_paid_cash = '''
    CREATE OR REPLACE VIEW v_one_salary_paid_cash
    AS 
    SELECT 
        CASE
            WHEN assignment_clause = 'зарплата' and organization_key = '62968b02-f06f-11ec-8136-001dd8b740bc' THEN
                'Мушлі Фатіх'
            ELSE
                counterparty
        END AS employee,
        dtperiod::date as doc_date,
        doc_number,
        CASE 
            WHEN EXTRACT('day' FROM dtperiod::date) < 15 AND salary_period = NULL THEN  
              concat(EXTRACT(YEAR FROM (dtperiod::date - INTERVAL '1 month'))::TEXT, 
                '.', trim(TO_CHAR(EXTRACT(MONTH FROM (dtperiod::date - INTERVAL '1 month')), '00')), 1)::date
            WHEN salary_period = NULL THEN 
              concat(EXTRACT(YEAR FROM dtperiod::date)::TEXT, '.', 
                trim(TO_CHAR(EXTRACT(MONTH FROM dtperiod::date), '00')), 1)::date
            ELSE
                salary_period        
        END salary_period,
        CASE
            WHEN type_of_cash= 'Наличные' THEN 'cash'
            ELSE 'bank'
        END sources,        
        amount_control as amount,
        description,
        recorder_key as ref_key,
        contract_key
    FROM t_one_accreg_cash_recordtype
    WHERE (assignment_clause in ('зарплата', 'Бонус') AND type_of_cash = 'Безналичные' and amount < 0)
        OR (assignment_clause in ('зарплата', 'Бонус') AND type_of_cash = 'Наличные')
        AND dtperiod::date >= '01.01.2023'::date
        AND contract_key <> 'c0347612-b071-11ec-812e-001dd8b740bc' -- Правобережне ВУВД ФССУ у м.Києві
    ;
'''

v_one_salary = '''
    CREATE OR REPLACE VIEW v_one_salary AS 
    SELECT doc_date, doc_number,sources,
        REPLACE(REPLACE(employee, '/РМ/', ''), '/', '') AS employee, 
        salary_period, sum(amount) AS amount
    FROM (
        SELECT doc_date, doc_number,trim(employee) AS employee, amount, salary_period::text, 'bank' AS sources
        FROM v_one_salary_paid_bank AS bank
        WHERE doc_date >= '01.01.2023'::date
        UNION all
        SELECT doc_date, doc_number, trim(employee) AS employee, -amount, salary_period::text, sources
        FROM v_one_salary_paid_cash AS cash
        WHERE doc_date >= '01.01.2023'::date)
    AS t
    GROUP BY salary_period, employee, doc_date, doc_number,sources
    ORDER BY salary_period, employee
    ;

    GRANT SELECT ON TABLE v_one_salary TO user_prestige;

'''

v_one_tax_sale_control_uktved = '''
--    -- DROP VIEW IF EXISTS v_one_tax_sale_control_uktved CASCADE;
    CREATE OR REPLACE VIEW v_one_tax_sale_control_uktved AS
    SELECT DISTINCT 
        client.customer контрагент,
        tax2.doc_date::date AS РК_дата,
        tax2.doc_number AS РК_номер,
        sale_tax.doc_date::date AS НН_дата,
        sale_tax.doc_number AS НН_номер,
        cat_uktved_sale_tax.code AS НН_код,
        cat_uktved_tax2.code AS РК_код
    FROM t_one_doc_tax_appendix_2 AS tax2
        LEFT JOIN t_one_doc_tax_appendix_2_details AS tax2_det
            ON tax2.ref_key =tax2_det.ref_key 
        LEFT JOIN t_one_doc_tax_sale_details AS sale_tax_det
            ON sale_tax_det.ref_key = tax2.tax_invoice_key
                AND sale_tax_det.code_uktvd_key <> tax2_det.code_uktved_key
        LEFT JOIN t_one_doc_tax_sale AS sale_tax
            ON sale_tax.ref_key = sale_tax_det.ref_key
        LEFT JOIN t_one_cat_uktved AS cat_uktved_sale_tax
            ON cat_uktved_sale_tax.ref_key = sale_tax_det.code_uktvd_key
        LEFT JOIN t_one_cat_uktved AS cat_uktved_tax2
            ON cat_uktved_tax2.ref_key = tax2_det.code_uktved_key
        LEFT JOIN v_one_manager_counterparty_contracts_segments AS client
            ON client.customer_key = tax2.account_key 
    WHERE tax2.posted 
        AND cat_uktved_sale_tax.ref_key <> cat_uktved_tax2.ref_key
    ORDER BY РК_дата DESC;
    ;

    GRANT SELECT ON TABLE v_one_tax_sale_control_uktved TO user_prestige;
'''

v_one_manager_counterparty_contracts_segments = """
    -- DROP VIEW IF EXISTS v_one_manager_counterparty_contracts_segments CASCADE;
    CREATE OR REPLACE VIEW v_one_manager_counterparty_contracts_segments AS
    SELECT
        segments_parent.description AS segment_folder,
        segments.description AS segment,
        mngr.description AS manager,
        client.description AS customer,
        client.edrpou,
        client.inn,
        contract.limit_days_credit_ua as contract_days,
        contract.contract_name,
        contract.contract_type,
        client.ref_key AS customer_key,
        contract.ref_key AS contract_key
    FROM t_one_cat_counterparties AS client
        LEFT JOIN t_one_cat_counterparty_segment AS segments
            ON client.segment_key = segments.ref_key
        LEFT JOIN
            (SELECT description, ref_key
            FROM t_one_cat_counterparty_segment
            WHERE isfolder
                AND (parent_key = '00000000-0000-0000-0000-000000000000')
            ) AS segments_parent
            ON segments_parent.ref_key = segments.parent_key
        LEFT JOIN
        (
        SELECT description, ref_key
        FROM t_one_cat_counterparties
        WHERE isfolder
            AND parent_key = 'ad421841-905f-11e6-80c4-c936aa9c817c' -- 010 Покупатели
        ) AS mngr
        ON mngr.ref_key = client.parent_key
        LEFT JOIN
        (
        SELECT limit_days_credit_ua, description as contract_name, owner_key, ref_key, contract_type
        FROM t_one_cat_contracts_counterparties
        ) AS contract
        ON client.ref_key = contract.owner_key
    WHERE NOT client.isfolder
    ORDER BY mngr.description, client.description
    ;

    COMMENT ON VIEW v_one_manager_counterparty_contracts_segments IS 'договора с клиентами';
    GRANT SELECT ON TABLE v_one_manager_counterparty_contracts_segments TO user_prestige;

"""

v_one_organization_and_type = f'''
    CREATE OR REPLACE VIEW v_one_organization_and_type AS
    SELECT 
        org.ref_key,
        org.description AS organization,
        (
        SELECT trim(value)
        FROM regexp_split_to_table(STRING_AGG(codes.number_registration, ', '), ',') AS value
        WHERE value != ''
        LIMIT 1)    
        AS number_registration,
        (
        SELECT trim(value)
        FROM regexp_split_to_table(STRING_AGG(codes.inn_okpo, ', '), ',') AS value
        WHERE value != ''
        LIMIT 1)  AS inn_okpo,
        CASE 
            WHEN (SELECT length(trim(value))
                FROM regexp_split_to_table(STRING_AGG(codes.inn_okpo, ', '), ',') AS value
                WHERE value != ''
                LIMIT 1) = 8 THEN True
            ELSE False
        END::boolean AS organization_type
    FROM t_one_cat_organizations AS org
        LEFT JOIN t_one_infreg_organization_codes AS codes
            ON org.ref_key = codes.organization_key 
    GROUP BY org.ref_key, org.description
    ORDER BY org.description
    ;

    COMMENT ON VIEW v_one_organization_and_type IS 'организации с юр типом';

'''

v_one_price = '''
    -- DROP VIEW IF EXISTS v_one_price;
    CREATE OR REPLACE VIEW v_one_price AS
    SELECT
        main.doc_date,
        main.doc_number,
        main.a_comment,
        nomenclature.description AS sku,
        price_type.description AS price_type,
        details.price,
        main.ref_key,
        nomenclature.ref_key AS nomenclature_key,
        details.price_type_key
    FROM t_one_doc_price AS main
        LEFT JOIN t_one_doc_price_details details
            ON main.ref_key = details.ref_key
        INNER JOIN t_one_cat_nomenclature AS nomenclature
            ON details.nomenclature_key = nomenclature.ref_key 
        LEFT JOIN t_one_cat_price_type AS price_type
            ON price_type.ref_key = details.price_type_key 
    WHERE main.posted
    ORDER BY price_type.description, nomenclature.description, main.doc_date, main.doc_number
    ;
'''


async def main_create_views():
    # result = change_data_in_table_returning(v_gtd, conpg)

    result = await async_save_pg(v_gtd)
    logger.info(f"{result}, v_gtd")
    result = await async_save_pg(create_function_array_sort_unique)
    logger.info(f"{result}, create_function_array_sort_unique")
    result = await async_save_pg(v_one_cash_order_receipt)
    logger.info(f"{result}, v_one_cash_order_receipt")
    result = await async_save_pg(v_one_cash_order_expense)
    logger.info(f"{result}, v_one_cash_order_expense")
    result = await async_save_pg(v_one_cash_warrant_receipt)
    logger.info(f"{result}, v_one_cash_warrant_receipt")
    result = await async_save_pg(v_one_cash_warrant_expense)
    logger.info(f"{result}, v_one_cash_warrant_expense")
    result = await async_save_pg(v_one_cash_warrant_receipt_expense)
    logger.info(f"{result}, v_one_cash_warrant_receipt_expense")
    result = await async_save_pg(v_one_cash_order_receipt_expense)
    logger.info(f"{result}, v_one_cash_warrant_receipt_expense")
    result = await async_save_pg(v_service_tc)
    logger.info(f"{result}, v_service_tc")
    result = await async_save_pg(v_service_tc_dop)
    logger.info(f"{result}, v_service_tc_dop")
    result = await async_save_pg(v_service_tc_gtd)
    logger.info(f"{result}, v_service_tc_gtd")
    result = await async_save_pg(v_service_tc_group)
    logger.info(f"{result}, v_service_tc_group")
    result = await async_save_pg(v_service_tc_goods)
    logger.info(f"{result}, v_service_tc_goods")
    result = await async_save_pg(v_service_tc_dop_second)
    logger.info(f"{result}, v_service_tc_dop_second")
    result = await async_save_pg(v_service_tc_maliyet)
    logger.info(f"{result}, v_service_tc_maliyet")
    result = await async_save_pg(v_one_nomenclature_inbox_supplier)
    logger.info(f"{result}, v_one_nomenclature_inbox_supplier")
    result = await async_save_pg(v_one_correction)
    logger.info(f"{result}, v_one_correction")
    result = await async_save_pg(v_one_giris)
    logger.info(f"{result}, v_one_giris")
    result = await async_save_pg(v_one_giris_iade)
    logger.info(f"{result}, v_one_giris_iade")
    result = await async_save_pg(v_one_giris_and_girisiade)
    logger.info(f"{result}, v_one_giris_and_girisiade")
    result = await async_save_pg(v_service_expense)
    logger.info(f"{result}, v_service_expense")
    result = await async_save_pg(v_one_sale)
    logger.info(f"{result}, v_one_sale")
    result = await async_save_pg(v_one_sale_return)
    logger.info(f"{result}, v_one_sale_return")
    result = await async_save_pg(v_one_sale_and_salereturn)
    logger.info(f"{result}, v_one_sale_and_salereturn")
    result = await async_save_pg(v_one_trebovanie)
    logger.info(f"{result}, v_one_trebovanie")
    result = await async_save_pg(v_one_spisanie)
    logger.info(f"{result}, v_one_spisanie")
    result = await async_save_pg(v_one_oprihodivanie)
    logger.info(f"{result}, v_one_oprihodivanie")
    result = await async_save_pg(v_one_correction_series)
    logger.info(f"{result}, v_one_correction_series")
    result = await async_save_pg(v_one_movement)
    logger.info(f"{result}, v_one_movement")
    result = await async_save_pg(v_control_coef)
    logger.info(f"{result}, v_control_coef")
    result = await async_save_pg(v_one_stock)
    logger.info(f"{result}, v_one_stock")
    result = await async_save_pg(v_one_stock_maliyet)
    logger.info(f"{result}, v_one_stock_maliyet")
    result = await async_save_pg(v_one_quantity_rows_in_document)
    logger.info(f"{result}, v_one_quantity_rows_in_document")
    result = await async_save_pg(v_one_stock_maliyet_rapor_warehouse)
    logger.info(f"{result}, v_one_stock_maliyet_rapor_warehouse")
    result = await async_save_pg(v_one_stock_maliyet_rapor)
    logger.info(f"{result}, v_one_stock_maliyet_rapor")
    result = await async_save_pg(v_one_sale_analize)
    logger.info(f"{result}, v_one_sale_analize")
    result = await async_save_pg(sql_pg_stat_activity)
    logger.info(f"{result}, sql_pg_stat_activity")
    result = await async_save_pg(v_one_cash)
    logger.info(f"{result}, v_one_cash")
    result = await async_save_pg(v_one_infreg_contact_information_phone)
    logger.info(f"{result}, v_one_infreg_contact_information_phone")
    result = await async_save_pg(v_one_clients_contact)
    logger.info(f"{result}, v_one_clients_contact")
    result = await async_save_pg(v_one_logistics_not_find)
    logger.info(f"{result}, v_one_logistics_not_find")
    result = await async_save_pg(v_one_logistic_kiev_in_order_carrier_isempty)
    logger.info(f"{result}, v_one_logistic_kiev_in_order_carrier_isempty")
    result = await async_save_pg(v_one_logistic_kiev_in_order_datesend_isempty)
    logger.info(f"{result}, v_one_logistic_kiev_in_order_datesend_isempty")
    result = await async_save_pg(v_stickers)
    logger.info(f"{result}, v_stickers")
    result = await async_save_pg(v_one_doc_tax_gtd_sale)
    logger.info(f"{result}, v_one_doc_tax_gtd_sale")
    result = await async_save_pg(v_one_doc_tax_gtd_sale_return)
    logger.info(f"{result}, v_one_doc_tax_gtd_sale_return")
    result = await async_save_pg(v_one_managers_and_counterparts)
    logger.info(f"{result}, v_one_managers_and_counterparts")
    result = await async_save_pg(v_one_nomenclature_series_double)
    logger.info(f"{result}, v_one_nomenclature_series_double")
    result = await async_save_pg(v_one_salary_paid_bank)
    logger.info(f"{result}, v_one_salary_paid_bank")
    result = await async_save_pg(v_one_salary_paid_cash)
    logger.info(f"{result}, v_one_salary_paid_cash")
    result = await async_save_pg(v_one_salary)
    logger.info(f"{result}, v_one_salary")
    await main_manager_customer_contracts_segments_async()
    result = await async_save_pg(v_one_tax_sale_control_uktved)
    logger.info(f"{result}, v_one_tax_sale_control_uktved")
    result = await async_save_pg(v_one_organization_and_type)
    logger.info(f"{result}, v_one_organization_and_type")
    result = await async_save_pg(v_one_price)
    logger.info(f"{result}, v_one_price")
    await main_vacation_async()
    await main_accounts_receivable_async()
    await main_offsets_async()
    await main_tax_amount_different_async()
    await main_tax_sale_return_amount_different_async()
    await main_stock_days_async('01.01.2024', date.today().strftime('%d.%m.%Y'), 2)
    await main_stock_days_except_customers_async('01.01.2024', date.today().strftime('%d.%m.%Y'), 2)
    await main_tax_medoc_async()
    await main_tax_medoc_different_async()
    await main_control_uktved_async()
    await main_return_no_invoice_async()


async def main_drop_views():
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_nomenclature_inbox_supplier CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_giris CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_giris_and_girisiade CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_giris_iade CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_sale CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_sale_return CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_sale_and_salereturn CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_trebovanie CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_spisanie CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_movement CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_oprihodivanie CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc_group CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc_goods CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc_dop CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc_gtd CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc_dop_second CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_tc_maliyet CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_gtd CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_sale_all CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_control_coef CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_stock_maliyet CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_stock CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_quantity_rows_in_document CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_stock_maliyet_rapor_warehouse CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_stock_maliyet_rapor CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_service_expense CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_sale_analize CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash_order_receipt CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash_order_expense CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_correction_series CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash_warrant_receipt CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash_warrant_expense CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash_warrant_receipt_expense CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash_order_receipt_expense CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_cash CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_infreg_contact_information_phone CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_clients_contact CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_logistic_kiev_in_order_carrier_isempty CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_logistic_kiev_in_order_datesend_isempty CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_stickers CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_correction CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_doc_tax_gtd_sale CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_managers_and_counterparts CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS sql_fn_stock_days CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS V_ONE_NOMENCLATURE_SERIES_DOUBLE CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS V_ONE_SALARY_PAID_BANK CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS V_ONE_SALARY_PAID_CASH CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS v_one_salary CASCADE")
    await async_save_pg("-- DROP VIEW IF EXISTS V_ONE_TAX_SALE_CONTROL_UKTVED CASCADE")


async def main_views_pg_async():
    await main_drop_views()
    await main_create_views()
    logger.info("main_create_view OK")


if __name__ == '__main__':
    # asyncio.run(main_views_pg_async())
    asyncio.get_event_loop().run_until_complete(main_views_pg_async())
