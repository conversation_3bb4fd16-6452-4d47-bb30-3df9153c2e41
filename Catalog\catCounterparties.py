import asyncio
import os
import sys

sys.path.append(os.path.dirname(__file__))
from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_counterparties"
DOCUMENT = "Catalog_Контрагенты"
SELECT_COLUMNS = """Ref_Key,Code,Description,Покупатель,Поставщик,Parent_Key,ГоловнойКонтрагент_Key,КодПоЕДРПОУ,ИНН,
    IsFolder,DeletionMark,Сегмент_Key,Комментарий"""

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15),  -- Code
        description varchar(150),  -- Description
        a_comment varchar,  -- Комментарий
        buyer bool,  -- Покупатель
        provider bool,  -- Поставщик
        deletion_mark bool NULL DEFAULT false,  -- DeletionMark
        for_report bool default false,  -- отображать в отчете
        edrpou varchar(15),  -- EDRPOU
        inn varchar(15),  -- ИНН
        isfolder bool,  -- IsFolder
        parent_key  varchar(50),  -- Parent_Key
        ref_key varchar(50),  -- Ref_Key
        segment_key varchar(50),  -- Сегмент_Key
        head_contractor_key  varchar(50),  -- ГоловнойКонтрагент_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.edrpou IS 'КодПоЕДРПОУ';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.inn IS 'ИНН';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.buyer IS 'Покупатель';
    COMMENT ON COLUMN {TABLE_NAME}.provider IS 'Поставщик';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.head_contractor_key IS 'ГоловнойКонтрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.for_report IS 'ДляОтчета';
    COMMENT ON COLUMN {TABLE_NAME}.segment_key IS 'Сегмент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    """


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}
        (
            ref_key,
            code,
            description,
            buyer,
            provider,
            parent_key,
            head_contractor_key,
            edrpou,
            inn,
            isfolder,
            deletion_mark,
            segment_key,
            a_comment
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            code = EXCLUDED.code,
            description = EXCLUDED.description,
            buyer = EXCLUDED.buyer,
            provider = EXCLUDED.provider,
            parent_key = EXCLUDED.parent_key,
            head_contractor_key = EXCLUDED.head_contractor_key,
            edrpou = EXCLUDED.edrpou,
            inn = EXCLUDED.inn,
            isfolder = EXCLUDED.isfolder,
            deletion_mark = EXCLUDED.deletion_mark,
            segment_key = EXCLUDED.segment_key,
            a_comment = EXCLUDED.a_comment
    ;
    """
    return sql.replace("'", "")


async def main_cat_counterparties_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(13)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "description")
    await async_sql_create_index(TABLE_NAME, "segment_key")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_counterparties_async())
