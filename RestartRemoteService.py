# pip install pywinrm

import winrm

# Замените на реальные данные для доступа к удаленному компьютеру
remote_computer = "***************:33099"
username = "admin"
password = "psw"

# Создаем объект сессии для WinRM
session = winrm.Session(remote_computer, auth=(username, password))

# Команда для перезапуска службы, например, службы "Spooler"
service_name = "postgresql-14"

# Команда PowerShell для перезапуска службы
command = f"stop-service {service_name}; start-service {service_name}"

# Выполнение команды на удаленном компьютере
result = session.run_ps(command)

# Вывод результата выполнения команды
print(result.std_out)
