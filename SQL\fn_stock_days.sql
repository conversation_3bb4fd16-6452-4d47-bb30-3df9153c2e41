    CREATE OR REPLACE FUNCTION fn_stock_days(date_first date, date_last date, min_stock integer)
     RETURNS void
     LANGUAGE plpgsql
    AS $function$
        DECLARE
            sql_query TEXT;
        BEGIN

            IF date_last::date > current_date::date THEN
                date_last = current_date::date::text;
            END IF;

            RAISE NOTICE '%', (date_last::date > current_date);

            EXECUTE 'DROP VIEW IF EXISTS v_stock_days_source CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_stock_days CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_stock_first CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_stock_last CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_stock_receipt CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_stock_sale CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_stock_days_series CASCADE';
            EXECUTE 'DROP VIEW IF EXISTS v_one_stock_days_quantity CASCADE';


            sql_query = 'CREATE OR REPLACE VIEW v_stock_first AS
                SELECT stock.*
                FROM (
                        SELECT DISTINCT
                            supplier, sku, doc_date::date,
                            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) AS balance_first,
                            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * entry_price AS entry_amount_first,
                            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * maliyet AS amount_maliyet_first,
                            doc_date = max(doc_date) OVER (PARTITION BY sku) AS is_max_doc_date
                        FROM t_one_stock_new
                        WHERE warehouse_key IN (''4b40b865-6d2f-11ec-8125-001dd8b72b55'',''381c5d92-4acb-11ed-8148-001dd8b72b55'')
                            AND (doc_date::Date < ''' || date_first || '''::date)
                    )AS stock
                WHERE is_max_doc_date
                ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = 'CREATE OR REPLACE VIEW v_stock_last AS
                SELECT stock.*
                FROM (
                        SELECT DISTINCT
                            supplier, sku, doc_date::date,
                            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) AS balance_last,
                            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * entry_price AS entry_amount_last,
                            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) * maliyet AS amount_maliyet_last,
                            doc_date = max(doc_date) OVER (PARTITION BY sku) AS is_max_doc_date
                        FROM t_one_stock_new
                        WHERE warehouse_key IN (''4b40b865-6d2f-11ec-8125-001dd8b72b55'',''381c5d92-4acb-11ed-8148-001dd8b72b55'')
                            AND (doc_date::Date <= ''' || date_last || '''::date)
                    )AS stock
                WHERE is_max_doc_date
                ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = 'CREATE OR REPLACE VIEW v_stock_sale AS
                SELECT	DISTINCT sku, -sum(tobox) AS tobox_sale, -sum(entry_amount) AS entry_amount_sale,
                    -sum(amount_maliyet) AS amount_maliyet_sale
                FROM t_one_stock_new
                WHERE doc_type IN (''продажа'', ''продажа возврат'', ''РеализацияТоваровУслуг'', ''ВозвратТоваровОтПокупателя'')
                    AND doc_date::date >= ''' || date_first || '''::date
                    AND doc_date::date <= ''' || date_last || '''::date
                    AND organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55''
                GROUP BY sku;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = 'CREATE OR REPLACE VIEW v_stock_receipt AS
                SELECT	DISTINCT sku,
                    sum(tobox) AS tobox_receipt, sum(entry_amount) AS entry_amount_receipt,
                    sum(amount_maliyet) AS amount_maliyet_receipt
                FROM t_one_stock_new
                WHERE doc_type IN (''поступление'', ''поступление возврат'',''ПоступлениеТоваровУслуг'',''ВозвратТоваровПоставщику'')
                    AND doc_date::date >= ''' || date_first || '''::date
                    AND doc_date::date <= ''' || date_last || '''::date
                    AND organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55''
                GROUP BY sku;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = '
                CREATE OR REPLACE VIEW v_stock_days_source AS
                SELECT supplier, sku, doc_date, doc_date_previos, stock, stock_previos,
                    CASE
                        WHEN count(*) OVER (PARTITION BY sku) = 1 AND stock >= ''' || min_stock || ''' THEN
                            ''' || date_last || '''::date - doc_date_previos
                        WHEN stock_previos < ''' || min_stock || ''' THEN 0
                        ELSE doc_date-doc_date_previos
                    END as days
                FROM (
                    SELECT
                        supplier, sku,
                        CASE
                            WHEN  t.doc_date = max(t.doc_date) OVER(partition by sku)
                                AND t.doc_date < ''' || date_last || '''::date AND stock >=''' || min_stock || '''
                                 AND stock_previos >= ''' || min_stock || ''' THEN ''' || date_last || '''::date
                            ELSE  t.doc_date
                        END as doc_date,
                        CASE
                            WHEN count(*) OVER (PARTITION BY t.sku) = 1 AND t.stock >= ''' || min_stock || '''::numeric
                                AND t.stock_previos < ''' || min_stock || '''::numeric THEN
                                    doc_date_last_receipt - 1
                            WHEN count(*) OVER (PARTITION BY t.sku) = 1 AND t.stock < ''' || min_stock || '''::numeric
                                AND t.stock_previos >= ''' || min_stock || '''::numeric THEN
                                    doc_date_last_sale - 1
                            WHEN  t.doc_date_previos < ''' || date_first || '''::date THEN ''' || date_first || '''::date
                            ELSE  t.doc_date_previos
                        END as doc_date_previos,
                        tobox, stock, stock_previos
                    FROM t_one_stock_short AS t WHERE t.doc_date >= ''' || date_first || '''::date
                        AND t.doc_date <= ''' || date_last || '''::date
                ) AS tt
                ORDER BY sku, doc_date;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;


            sql_query = 'CREATE OR REPLACE VIEW v_stock_days AS
                SELECT supplier, sku, COALESCE(SUM(days),0) AS total_days
                FROM v_stock_days_source
                GROUP BY supplier, sku;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = 'CREATE OR REPLACE VIEW v_one_stock_days AS
                SELECT DISTINCT stock_last.supplier,nomenclature.sku,
                COALESCE(stock_first.balance_first,0) AS начОст,
                COALESCE(stock_first.entry_amount_first,0) AS начОстВх,
                COALESCE(stock_first.amount_maliyet_first,0) AS начОстСсть,
                COALESCE(receipt.tobox_receipt,0) AS прихКрб,
                COALESCE(receipt.entry_amount_receipt,0) AS прихВх,
                COALESCE(receipt.amount_maliyet_receipt,0) AS прихСсть,
                COALESCE(sale.tobox_sale,0) AS продКрб,
                COALESCE(sale.entry_amount_sale,0) AS продВх,
                COALESCE(sale.amount_maliyet_sale,0) AS продСсть,
                COALESCE(stock_last.balance_last,0) AS конОст,
                COALESCE(stock_last.entry_amount_last,0) AS конВх,
                COALESCE(stock_last.amount_maliyet_last,0) AS конСсть,
                CASE
                    WHEN COALESCE(total_days,0) = 0 AND COALESCE(receipt.tobox_receipt,0) < ''' || min_stock || '''
                        AND COALESCE(stock_last.balance_last,0) < ''' || min_stock || ''' THEN
                            0
                    WHEN COALESCE(total_days,0) = 0 AND COALESCE(sale.tobox_sale,0) >= ''' || min_stock || ''' THEN
                        1
                     WHEN COALESCE(stock_first.balance_first,0) >= ''' || min_stock || '''
                        AND COALESCE(stock_last.balance_last,0) >= ''' || min_stock || ''' AND COALESCE(total_days,0) = 0
                        THEN
                            ''' || date_last || '''::date - ''' || date_first || '''::date
                     ELSE COALESCE(total_days,0)
                END AS дней
                FROM (SELECT DISTINCT description AS sku FROM t_one_cat_nomenclature
                        WHERE item_type_key::text = ''e5827eaf-8f86-11e6-80c4-c936aa9c817c'') as nomenclature
                LEFT JOIN v_stock_last as stock_last ON stock_last.sku = nomenclature.sku
                LEFT JOIN v_stock_first AS stock_first ON nomenclature.sku = stock_first.sku
                LEFT JOIN v_stock_sale AS sale ON nomenclature.sku = sale.sku
                LEFT JOIN v_stock_receipt AS receipt ON nomenclature.sku = receipt.sku
                LEFT JOIN v_stock_days AS count_days ON nomenclature.sku = count_days.sku
                WHERE (abs(COALESCE(stock_first.balance_first,0)) + abs(COALESCE(receipt.tobox_receipt,0)) +
                   abs(COALESCE(sale.tobox_sale,0)) + abs(COALESCE(stock_last.balance_last, 0))) > 0.005
                ORDER BY nomenclature.sku;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = 'CREATE OR REPLACE VIEW v_one_stock_days_quantity AS
                SELECT DISTINCT stock_last.supplier,nomenclature.sku,
                COALESCE(stock_first.balance_first,0) AS начОст,
                COALESCE(receipt.tobox_receipt,0) AS прихКрб,
                COALESCE(sale.tobox_sale,0) AS продКрб,
                COALESCE(stock_last.balance_last,0) AS конОст,
                CASE
                    WHEN COALESCE(total_days,0) = 0 AND COALESCE(receipt.tobox_receipt,0) < ''' || min_stock || '''
                        AND COALESCE(stock_last.balance_last,0) < ''' || min_stock || ''' THEN
                            0
                    WHEN COALESCE(total_days,0) = 0 AND COALESCE(sale.tobox_sale,0) >= ''' || min_stock || ''' THEN
                        1
                    WHEN COALESCE(stock_first.balance_first,0) >= ''' || min_stock || '''
                        AND COALESCE(stock_last.balance_last,0) >= ''' || min_stock || ''' AND COALESCE(total_days,0) = 0
                        THEN
                            ''' || date_last || '''::date - ''' || date_first || '''::date
                    ELSE COALESCE(total_days,0)
                END AS дней
                FROM (SELECT DISTINCT description AS sku FROM t_one_cat_nomenclature
                        WHERE item_type_key::text = ''e5827eaf-8f86-11e6-80c4-c936aa9c817c'') as nomenclature
                LEFT JOIN v_stock_last as stock_last ON stock_last.sku = nomenclature.sku
                LEFT JOIN v_stock_first AS stock_first ON nomenclature.sku = stock_first.sku
                LEFT JOIN v_stock_sale AS sale ON nomenclature.sku = sale.sku
                LEFT JOIN v_stock_receipt AS receipt ON nomenclature.sku = receipt.sku
                LEFT JOIN v_stock_days AS count_days ON nomenclature.sku = count_days.sku
                WHERE (abs(COALESCE(stock_first.balance_first,0)) + abs(COALESCE(receipt.tobox_receipt,0)) +
                   abs(COALESCE(sale.tobox_sale,0)) + abs(COALESCE(stock_last.balance_last, 0))) > 0.005
                ORDER BY nomenclature.sku;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            sql_query = 'CREATE OR REPLACE VIEW v_stock_days_series AS
                SELECT days.*, stock.balance_last AS остПоСерии, stock.sell_by as годенДо, entry_amount_last,
                    amount_maliyet_last
                FROM v_one_stock_days AS days
                    LEFT JOIN
                    (SELECT DISTINCT supplier, sku,
                        sum(tobox) OVER (PARTITION BY sku, nomenclature_series_key) AS balance_last,
                        sum(tobox) OVER (PARTITION BY sku, nomenclature_series_key) * entry_price AS entry_amount_last,
                        sum(tobox) OVER (PARTITION BY sku, nomenclature_series_key) * maliyet AS amount_maliyet_last,
                        doc_date = max(doc_date) OVER (PARTITION BY sku, sell_by) AS is_max_doc_date, sell_by
                    FROM t_one_stock_new
                    WHERE warehouse_key IN (''4b40b865-6d2f-11ec-8125-001dd8b72b55'',
                            ''381c5d92-4acb-11ed-8148-001dd8b72b55'')
                        AND (doc_date::date <= ''' || date_last || '''::date)
                    ) AS stock
                    ON stock.sku = days.sku
                WHERE is_max_doc_date AND abs(COALESCE(balance_last,0)) > 0.005
                ORDER BY supplier, stock.sku, sell_by
                ;';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;

            EXECUTE 'ALTER TABLE v_one_stock_days OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_days_source OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_days OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_receipt OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_sale OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_last OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_first OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE v_stock_days_series OWNER TO user_prestige;';

            EXECUTE 'GRANT ALL ON TABLE public.v_stock_last TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_stock_first TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_stock_sale TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_stock_receipt TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_stock_days TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_stock_days_source TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_stock_days_series TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_one_stock_days_quantity TO rm;';
            EXECUTE 'GRANT ALL ON TABLE public.v_one_stock_days TO rm;';



            RETURN;

        END;
        $function$
    ;
