-- Создание функции fn_stock_days с правами владельца
-- Создание или замена функции с расширенной логикой и исправленным расчетом оборачиваемости
CREATE OR REPLACE FUNCTION public.fn_stock_days(
    date_first date,
    date_last date DEFAULT current_date,
    min_stock_quantity integer DEFAULT 1,
    reserve_days integer DEFAULT 60 -- Желаемый запас в днях
)
RETURNS TABLE(
    supplier varchar,
    sku varchar,
    "начОст" numeric,
    "начОстВх" numeric,
    "начОстСсть" numeric,
    "прихКрб" numeric,
    "прихВх" numeric,
    "прихСсть" numeric,
    "продКрб" numeric,
    "продВх" numeric,
    "продСсть" numeric,
    "конОст" numeric,
    "конВх" numeric,
    "конСсть" numeric,
    "дней в наличии" integer,
    "за день (реальн)" numeric,
    "резерв дней (реальн)" numeric,
    "дни отсутствия" integer,
    "упущ_выгода_крб" numeric,
    "заказ крб" numeric,
    "посл_цена_закупки" numeric,
    "сумма заказа" numeric,
    "оборачиваемость" numeric
) AS
$BODY$
DECLARE
    sql_query text;
BEGIN
    sql_query := format(
        $SQL$
        WITH period AS (
          SELECT %L::date AS start_date, %L::date AS end_date
        ),
        opening_balances AS (
          -- ... (код без изменений)
          SELECT
            t.sku,
            t.supplier,
            SUM(t.tobox) AS balance_tobox,
            SUM(t.entry_amount) AS balance_entry_amount,
            SUM(t.amount_maliyet) AS balance_amount_maliyet
          FROM
            t_one_stock_new t
          WHERE
            t.doc_date < %L::date
            AND t.warehouse IN ('Основной склад товаров 2022', 'МЕРЕЖІ')
          GROUP BY
            t.sku,
            t.supplier
        ),
        movements AS (
          -- ... (код без изменений)
          SELECT
            t.sku,
            t.supplier,
            SUM(CASE WHEN t.doc_type = 'ПоступлениеТоваровУслуг' THEN t.tobox WHEN t.doc_type = 'ВозвратТоваровПоставщику' THEN t.tobox ELSE 0 END) AS total_inflow_tobox,
            SUM(CASE WHEN t.doc_type = 'ПоступлениеТоваровУслуг' THEN t.entry_amount WHEN t.doc_type = 'ВозвратТоваровПоставщику' THEN t.entry_amount ELSE 0 END) AS total_inflow_entry_amount,
            SUM(CASE WHEN t.doc_type = 'ПоступлениеТоваровУслуг' THEN t.amount_maliyet WHEN t.doc_type = 'ВозвратТоваровПоставщику' THEN t.amount_maliyet ELSE 0 END) AS total_inflow_amount_maliyet,
            SUM(CASE WHEN t.doc_type = 'РеализацияТоваровУслуг' THEN ABS(t.tobox) WHEN t.doc_type = 'ВозвратТоваровОтПокупателя' THEN -t.tobox ELSE 0 END) AS total_outflow_tobox,
            SUM(CASE WHEN t.doc_type = 'РеализацияТоваровУслуг' THEN ABS(t.entry_amount) WHEN t.doc_type = 'ВозвратТоваровОтПокупателя' THEN -t.entry_amount ELSE 0 END) AS total_outflow_entry_amount,
            SUM(CASE WHEN t.doc_type = 'РеализацияТоваровУслуг' THEN ABS(t.amount_maliyet) WHEN t.doc_type = 'ВозвратТоваровОтПокупателя' THEN -t.amount_maliyet ELSE 0 END) AS total_outflow_amount_maliyet
          FROM
            t_one_stock_new t
          WHERE
            t.doc_date BETWEEN %L::date AND %L::date
            AND NOT (
                (t.organization ILIKE '%%ПРЕСТИЖ%%ПРОДУКТ%%' OR t.customer ILIKE '%%ПРЕСТИЖ%%ПРОДУКТ%%')
                AND (t.customer ILIKE '%%АЛЬФА БЕСТ%%' OR t.organization ILIKE '%%АЛЬФА БЕСТ%%')
            )
            AND t.warehouse NOT ILIKE '%%ПЕРЕОЦІНКА%%'
          GROUP BY
            t.sku,
            t.supplier
        ),
        first_receipt_dates AS (
          -- ... (код без изменений)
          SELECT
                t.sku,
                t.supplier,
                MIN(t.doc_date) as first_receipt_date
            FROM
                t_one_stock_new t
            WHERE
                t.doc_date BETWEEN %L::date AND %L::date
                AND t.doc_type = 'ПоступлениеТоваровУслуг'
                AND NOT (
                    (t.organization ILIKE '%%ПРЕСТИЖ%%ПРОДУКТ%%' OR t.customer ILIKE '%%ПРЕСТИЖ%%ПРОДУКТ%%')
                    AND (t.customer ILIKE '%%АЛЬФА БЕСТ%%' OR t.organization ILIKE '%%АЛЬФА БЕСТ%%')
                )
            AND t.warehouse NOT ILIKE '%%ПЕРЕОЦІНКА%%'
            GROUP BY
                t.sku,
                t.supplier
        ),
        last_prices AS (
            -- ... (код без изменений)
            SELECT
                sku,
                entry_price
            FROM (
                SELECT
                    t.sku,
                    t.entry_price,
                    ROW_NUMBER() OVER(PARTITION BY t.sku ORDER BY t.doc_date DESC, t.id DESC) as rn
                FROM
                    t_one_stock_new t
                WHERE
                    t.doc_type = 'ПоступлениеТоваровУслуг' AND t.entry_price > 0
                    AND t.doc_date <= %L::date
            ) sub
            WHERE rn = 1
        ),
        closing_balances AS (
          -- ... (код без изменений)
          SELECT
            t.sku,
            t.supplier,
            SUM(t.tobox) AS balance_tobox,
            SUM(t.entry_amount) AS balance_entry_amount,
            SUM(t.amount_maliyet) AS balance_amount_maliyet
          FROM
            t_one_stock_new t
          WHERE
            t.doc_date <= %L::date
            AND t.warehouse = 'МЕРЕЖІ'
          GROUP BY
            t.sku,
            t.supplier
        ),
        final_data AS (
            -- ... (код без изменений)
            SELECT
              COALESCE(ob.sku, m.sku, cb.sku, frd.sku) AS sku,
              COALESCE(ob.supplier, m.supplier, cb.supplier, frd.supplier) AS supplier,
              lp.entry_price AS last_entry_price,
              COALESCE(ob.balance_tobox, 0) AS ob_tobox,
              COALESCE(m.total_outflow_tobox, 0) AS outf_tobox,
              COALESCE(cb.balance_tobox, 0) AS cb_tobox,
              CASE
                WHEN COALESCE(cb.balance_tobox, 0) >= 0 AND frd.first_receipt_date IS NOT NULL
                THEN GREATEST(1, (%L::date - frd.first_receipt_date::date) + 1)
                ELSE 0
              END AS storage_days,
              ((%L::date) - (%L::date) + 1) AS period_days,
              COALESCE(ob.balance_entry_amount, 0) AS ob_entry_amount,
              COALESCE(ob.balance_amount_maliyet, 0) AS ob_amount_maliyet,
              COALESCE(m.total_inflow_tobox, 0) AS inf_tobox,
              COALESCE(m.total_inflow_entry_amount, 0) AS inf_entry_amount,
              COALESCE(m.total_inflow_amount_maliyet, 0) AS inf_amount_maliyet,
              COALESCE(m.total_outflow_entry_amount, 0) AS outf_entry_amount,
              COALESCE(m.total_outflow_amount_maliyet, 0) AS outf_amount_maliyet,
              COALESCE(cb.balance_entry_amount, 0) AS cb_entry_amount,
              COALESCE(cb.balance_amount_maliyet, 0) AS cb_amount_maliyet
            FROM
              opening_balances ob
            FULL OUTER JOIN movements m ON ob.sku = m.sku AND ob.supplier = m.supplier
            FULL OUTER JOIN closing_balances cb ON COALESCE(ob.sku, m.sku) = cb.sku AND COALESCE(ob.supplier, m.supplier) = cb.supplier
            FULL OUTER JOIN first_receipt_dates frd ON COALESCE(ob.sku, m.sku, cb.sku) = frd.sku AND COALESCE(ob.supplier, m.supplier, cb.supplier) = frd.supplier
            LEFT JOIN last_prices lp ON COALESCE(ob.sku, m.sku, cb.sku, frd.sku) = lp.sku
        ),
        calculated_metrics AS (
            SELECT
                fd.*,
                (fd.outf_tobox / NULLIF(fd.storage_days, 0))::numeric AS daily_sales_real
            FROM
                final_data fd
        )
        SELECT
            cm.supplier,
            cm.sku,
            ROUND(cm.ob_tobox, 2), ROUND(cm.ob_entry_amount, 2), ROUND(cm.ob_amount_maliyet, 2),
            ROUND(cm.inf_tobox, 2), ROUND(cm.inf_entry_amount, 2), ROUND(cm.inf_amount_maliyet, 2),
            ROUND(cm.outf_tobox, 2), ROUND(cm.outf_entry_amount, 2), ROUND(cm.outf_amount_maliyet, 2),
            ROUND(cm.cb_tobox, 2), ROUND(cm.cb_entry_amount, 2), ROUND(cm.cb_amount_maliyet, 2),
            cm.storage_days,
            ROUND(cm.daily_sales_real, 2) AS "за день (реальн)",
            CASE
                WHEN cm.daily_sales_real > 0 THEN ROUND((cm.cb_tobox / cm.daily_sales_real)::numeric, 2)
                ELSE CASE WHEN cm.cb_tobox > 0 THEN 9999 ELSE 0 END
            END AS "резерв дней (реальн)",
            (cm.period_days - cm.storage_days) AS "дни отсутствия",
            CASE
                WHEN cm.daily_sales_real > 0 AND (cm.period_days - cm.storage_days) > 0 THEN
                    ROUND((cm.daily_sales_real * (cm.period_days - cm.storage_days))::numeric, 2)
                ELSE 0
            END AS "упущ_выгода_крб",
            (CASE
                WHEN cm.daily_sales_real > 0 THEN
                    ROUND(GREATEST(0, (%L::integer - (cm.cb_tobox / cm.daily_sales_real)) * cm.daily_sales_real)::numeric, 0)
                ELSE 0
            END) AS "заказ крб",
            ROUND(COALESCE(cm.last_entry_price, 0), 2) AS "посл_цена_закупки",
            ROUND(
                (CASE
                    WHEN cm.daily_sales_real > 0 THEN
                        ROUND(GREATEST(0, (%L::integer - (cm.cb_tobox / cm.daily_sales_real)) * cm.daily_sales_real)::numeric, 0)
                    ELSE 0
                END) * COALESCE(cm.last_entry_price, 0), 2
            ) AS "сумма заказа",
            -- ИСПРАВЛЕННЫЙ РАСЧЕТ ОБОРАЧИВАЕМОСТИ
            CASE
                -- Проверяем, что средний запас по себестоимости больше порога (0.01)
                WHEN ((cm.ob_amount_maliyet + cm.cb_amount_maliyet) / 2) > 0.01 THEN
                    ROUND((cm.outf_amount_maliyet / ((cm.ob_amount_maliyet + cm.cb_amount_maliyet) / 2))::numeric, 2)
                ELSE 0 -- Если средний запас слишком мал, оборачиваемость считаем равной 0
            END as "оборачиваемость"
        FROM
            calculated_metrics cm
        WHERE (cm.ob_tobox + cm.inf_tobox + cm.outf_tobox + cm.cb_tobox) > %L
        $SQL$,
        date_first, date_last,
        date_first,
        date_first, date_last,
        date_first, date_last,
        date_last, -- для last_prices
        date_last,
        date_last,
        date_last, date_first,
        reserve_days, -- для "заказ крб"
        reserve_days, -- для "сумма заказа"
        min_stock_quantity
    );

    RETURN QUERY EXECUTE sql_query;
END;
$BODY$
LANGUAGE plpgsql STABLE;
