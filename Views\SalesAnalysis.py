import asyncio
import os

from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

v_one_sales_analysis = '''
    CREATE OR REPLACE VIEW v_one_sale_analize
    AS SELECT 
        t.manager,
        t.customer,
        t.supplier,
        t.sku,
        t.doc_date::date as doc_date,
        t.doc_date as tarih,
        t.doc_number,
        t.tobox_sale,
        t.amount_sale,
        t.tobox_return,
        t.amount_return,
        t.tobox_sale + t.tobox_return AS tobox,
        t.amount_sale + t.amount_return AS amount,
        t.warehouse,
        t.segment,
        t.segment_folder
       FROM ( SELECT
                sku,
                doc_date,
                doc_number,
                customer,
                supplier,
                tobox AS tobox_sale,
                amount AS amount_sale,
                0 AS tobox_return,
                0 AS amount_return,
                distributor AS manager,
                organization_key,
                nomenclature_key,
                warehouse,
                segment,
                segment_folder
               FROM t_one_sale
               WHERE issale = 1
            UNION ALL
             SELECT sku,
                doc_date,
                doc_number,
                customer,
                supplier,
                0,
                0,
                - tobox,
                amount,
                distributor AS manager,
                organization_key,
                nomenclature_key,
                warehouse,
                segment,
                segment_folder
               FROM t_one_sale
               WHERE issale = -1
         ) t
      WHERE t.organization_key::text <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        AND t.doc_date <= CURRENT_DATE 
        AND (t.nomenclature_key::text IN (
              SELECT nom_2.ref_key
              FROM t_one_cat_nomenclature nom_2
              WHERE nom_2.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c')
    );

        COMMENT ON VIEW v_one_sale_analize IS 'анализ продаж и возвратов';

        GRANT SELECT ON TABLE v_one_sale_analize TO user_prestige;

'''


async def create_v_one_sales_analysis():
    await async_save_pg(v_one_sales_analysis)
    logger.info("v_one_sale_return")


if __name__ == '__main__':
    asyncio.run(create_v_one_sales_analysis())
