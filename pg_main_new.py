import ast
import importlib
import os
import sys


def get_imports_from_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        tree = ast.parse(file.read())
    imports = [node.names[0].name for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))]
    return imports


file_path = 'pg_main.pyw'
imports = get_imports_from_file(file_path)
print(imports)


def is_system_module(module_name):
    try:
        module = importlib.import_module(module_name)
    except ImportError:
        return False

    module_path = os.path.dirname(os.path.abspath(module.__file__))
    return any(module_path.startswith(path) for path in sys.path)


print(is_system_module('os'))