# взаимозачеты. расхождение между дебитом и кредитом
import asyncio
import os
import sys

from logger_prestige import get_logger
from prestige_authorize import CONFIG_PATH

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

sys.path.append(os.path.abspath(CONFIG_PATH))
from Document.docDebtCorrection import main_debt_correction_async
from Document.docDebtCorrectionDebtAmounts import main_debt_correction_debt_amount_async
from async_Postgres import  async_save_pg

VIEW_NAME = 'v_one_debit_credit'

SQL_CREATE_VIEW = f'''
    -- DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
        SELECT 
            main.doc_date,
            main.doc_number,
            - sum(sub.total) AS total,
            main.a_comment,
            sub.debt_type 
        FROM t_one_doc_debt_correction AS main
            INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                ON main.ref_key = sub.ref_key
        WHERE debt_type = 'Дебиторская' -- контрагенту закрываем долг
            AND main.operation_type NOT IN ('ПереносЗадолженности','СписаниеЗадолженности')
        GROUP BY main.doc_date, main.doc_number, main.a_comment, sub.debt_type
        UNION ALL
        SELECT 
            main.doc_date,
            main.doc_number,
            sum(sub.total),
            main.a_comment,
            sub.debt_type
        FROM t_one_doc_debt_correction AS main
            INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                ON main.ref_key = sub.ref_key
        WHERE debt_type = 'Кредиторская' -- контрагент закрывающий кредит
            AND main.operation_type NOT IN ('ПереносЗадолженности','СписаниеЗадолженности')
        GROUP BY main.doc_date, main.doc_number, main.a_comment, sub.debt_type
        ORDER BY doc_date
    ;
    
    GRANT SELECT ON TABLE v_one_debit_credit TO user_prestige;
    
'''


async def main_debit_credit_async():
    await main_debt_correction_async()
    await main_debt_correction_debt_amount_async()
    result = await async_save_pg(SQL_CREATE_VIEW)
    logger.info(f"{result}, SQL_CREATE_VIEW")



if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(main_debit_credit_async())
    logger.info(f"FINISH")
