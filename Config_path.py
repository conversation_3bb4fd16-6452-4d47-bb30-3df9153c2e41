from operator import ge
import os
import sys
from pathlib import Path
FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

def create_path():
    my_path = os.path.dirname(os.path.abspath(__file__))
    cur_path = Path(my_path)
    drivers = os.path.splitdrive(my_path)[0]
    components = cur_path.parts
    if "Python" in components:
        python_index = components.index("Python")
        if python_index:
            my_path_list = str(my_path).split('\\')[1:python_index + 1]
            authorize_path = os.path.join(drivers, "\\", *my_path_list, "Prestige")
            config_path = os.path.join(drivers, "\\", *my_path_list, "Config")
            image_config_path = os.path.join(drivers, "\\", *my_path_list, "ImageToPDF")
            config_path_vacation = os.path.join(drivers, "\\", *my_path_list, "VacationApplication")

            sys.path.append('C:\\Rasim\\Python\\Prestige')
            sys.path.append("D:\Prestige\Python\Prestige")
            sys.path.append(os.path.abspath(config_path))
            sys.path.append(os.path.abspath(authorize_path))
            sys.path.append(os.path.abspath(image_config_path))
            sys.path.append(os.path.abspath(config_path_vacation))


def find_file_upwards(filename, start_dir):
    current_dir = start_dir
    while True:
        for root, dirs, files in os.walk(current_dir):
            if filename in files:
                return os.path.join(root, filename)
        new_dir = os.path.dirname(current_dir)
        if new_dir == current_dir:
            # Достигнут корень файловой системы, файл не найден
            return None
        current_dir = new_dir


def get_folder_upwards(filename):
    # Использование функции
    start_directory = os.path.dirname(FILE_NAME)
    file_path = find_file_upwards(filename, start_directory)
    if file_path:
        print(f"Файл найден: {file_path}")
        python_path = os.path.join(file_path, ".venv\scripts\python.exe")
        print(f"Python path: {python_path}")
    else:
        print(f"Файл {filename} не найден.")


if __name__ == '__main__':
    create_path()
    # print(sys.path)
    get_folder_upwards("LoadDebetToExcel.py")
