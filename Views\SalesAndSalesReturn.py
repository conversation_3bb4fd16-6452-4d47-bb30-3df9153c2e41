import asyncio
import os

from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

v_one_sale_and_salereturn = '''
    CREATE OR REPLACE VIEW v_one_sale_and_salereturn AS 
    SELECT *
    FROM (
        SELECT
            id,
            doc_date,
            tarih,
            doc_number,
            sku,
            inbox,
            quantity,
            coefficient,
            ed,
            tobox,
            amount,
            amount_vat,
            consider_vat,
            amount_includes_vat,
            doc_type,
            issale,
            settlement_rate,
            multiplicity_of_mutual_settlements,
            organization,
            customer,
            supplier,
            warehouse,
            ref_key,
            nomenclature_key,
            nomenclature_series_key,
            unit_of_key,
            customer_key,
            currency_key,
            organization_key,
            supplier_key,
            warehouse_key,
            contract_key,
            line_number
        FROM v_one_sale
        UNION ALL
        SELECT 
            id,
            doc_date,
            tarih,
            doc_number,
            sku,
            inbox,
            quantity,
            coefficient,
            ed,
            tobox,
            amount,
            amount_vat,
            consider_vat,
            amount_includes_vat,
            doc_type,
            issale,
            settlement_rate,
            multiplicity_of_mutual_settlements,
            organization,
            customer,
            supplier,
            warehouse,
            ref_key,
            nomenclature_key,
            nomenclature_series_key,
            unit_of_key,
            customer_key,
            currency_key,
            organization_key,
            supplier_key,
            warehouse_key,
            contract_key,
            line_number
        FROM v_one_sale_return
    ) AS sr
    WHERE sr.doc_date::date <= current_date 
    ORDER BY sr.doc_date;

    COMMENT ON VIEW v_one_sale_and_salereturn IS 'продажи и возвраты';
    COMMENT ON COLUMN v_one_sale_and_salereturn.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_sale_and_salereturn.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_sale_and_salereturn.sku IS 'sku';
    COMMENT ON COLUMN v_one_sale_and_salereturn.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_sale_and_salereturn.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_sale_and_salereturn.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_sale_and_salereturn.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_sale_and_salereturn.warehouse IS 'склад';
    COMMENT ON COLUMN v_one_sale_and_salereturn.warehouse_key IS 'склад_key';

    GRANT SELECT ON TABLE v_one_sale_and_salereturn TO user_prestige;
'''


async def create_v_one_sales_and_salereturn():
    await async_save_pg(v_one_sale_and_salereturn)
    logger.info("v_one_sale_return")


if __name__ == '__main__':
    asyncio.run(create_v_one_sales_and_salereturn())
