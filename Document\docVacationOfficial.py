import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_vacation_official"
DOCUMENT = "Document_НачислениеОтпускаРаботникамОрганизаций"
SELECT_COLUMNS = (
    "DataVersion,Date,DeletionMark,Number,Posted,Комментарий,ОтражатьВРеглУчете,ОтражатьВУпрУчете,"
    "ПериодРегистрации,Ref_Key,Организация_Key,Сотрудник_Key,Физлицо_Key,"
    "ДнейЧасовКомпенсацииОтпуска,ЧислоМесяцев,ВидДокументаОтпуск"
)

SQL_CREATE_TABLE = f"""        
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NULL,  -- DataVersion
            doc_date timestamp NOT NULL,  -- Date
            deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
            doc_number varchar(15) NULL,  -- Number
            posted boolean NOT NULL DEFAULT FALSE,  -- Posted
            a_comment varchar(300) NULL,  -- Комментарий
            is_regulation boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВРеглУчете
            is_management boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВУпрУчете
            registration_period date NOT NULL,  -- ПериодРегистрации
            compensation_days int NULL DEFAULT 0,  -- ДнейЧасовКомпенсацииОтпуска
            compensation_month int NULL DEFAULT 0,  -- ЧислоМесяцев
            doc_type varchar(100) NULL,  -- ВидДокументаОтпуск
            ref_key varchar(50) NOT NULL,  -- Ref_Key
            organization_key varchar(50) NULL,  -- Организация_Key
            employee_key uuid NULL,  -- Сотрудник_Key
            individual_key varchar(50) NULL,  -- Физлицо_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';        
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
        COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
        COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
        COMMENT ON COLUMN {TABLE_NAME}.is_regulation IS 'ОтражатьВРеглУчете';
        COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУпрУчете';
        COMMENT ON COLUMN {TABLE_NAME}.registration_period IS 'ПериодРегистрации';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
        COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'Сотрудник_Key';
        COMMENT ON COLUMN {TABLE_NAME}.individual_key IS 'Физлицо_Key';
        COMMENT ON COLUMN {TABLE_NAME}.compensation_days IS 'компенсацияЗаДней';
        COMMENT ON COLUMN {TABLE_NAME}.compensation_month IS 'компенсацияЗаМесяцев';
        COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'ВидДокументаОтпуск';
    """


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}(
            dataversion, 
            doc_date, 
            deletion_mark, 
            doc_number, 
            posted, 
            a_comment, 
            is_regulation, 
            is_management, 
            registration_period, 
            ref_key, 
            organization_key, 
            employee_key, 
            individual_key,
            compensation_days,
            compensation_month,
            doc_type
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            dataversion = EXCLUDED.dataversion, 
            doc_date = EXCLUDED.doc_date, 
            deletion_mark = EXCLUDED.deletion_mark, 
            doc_number = EXCLUDED.doc_number, 
            posted = EXCLUDED.posted, 
            a_comment = EXCLUDED.a_comment, 
            is_regulation = EXCLUDED.is_regulation, 
            is_management = EXCLUDED.is_management, 
            registration_period = EXCLUDED.registration_period, 
            organization_key = EXCLUDED.organization_key, 
            employee_key = EXCLUDED.employee_key, 
            individual_key = EXCLUDED.individual_key,
            compensation_days = EXCLUDED.compensation_days,
            compensation_month = EXCLUDED.compensation_month,
            doc_type = EXCLUDED.doc_type
    """
    return sql.replace("'", "")


async def main_doc_vacation_official_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, True, True)
    maket = await create_model_async(16)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$9,", "to_timestamp($9, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
        
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_vacation_official_async())
