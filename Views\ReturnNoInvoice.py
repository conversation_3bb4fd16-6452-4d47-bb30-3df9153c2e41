# отображает возвратные накладные (ВН), в товарах которых нет привязки к расходной накл (РН)
import asyncio
import os
import sys
from pathlib import Path

my_path_list = str(__file__).split('\\')[1:str(__file__).split('\\').index('Python') + 1]
my_config_path = os.path.join(os.path.splitdrive(Path.cwd())[0], '\\', *my_path_list, "Config")

sys.path.append(my_config_path)
from async_Postgres import  async_save_pg

from logger_prestige import get_logger
FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

VIEW_NAME = "v_one_return_not_invoice"
SQL_CREATE_VIEW = f'''
    -- DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
    SELECT DISTINCT 
        users.description AS автор,
        client.customer контрагент,
        main.doc_date ВН_дата,
        main.doc_number ВН_номер,
        nom.description AS номенклатура
    FROM t_one_doc_return_of_goods_from_customers AS main
        INNER JOIN t_one_doc_return_of_goods_from_customers_goods AS det
            ON main.ref_key = det.ref_key 
        INNER JOIN v_one_manager_counterparty_contracts_segments AS client
            ON client.customer_key = main.account_key
        INNER JOIN t_one_cat_users AS users
            ON users.individual_key = main.user_key
        INNER JOIN t_one_cat_nomenclature AS nom
            ON nom.ref_key = det.nomenclature_key 
    WHERE coalesce(base_doc_key,'') = ''
    ORDER BY 
        users.description DESC,
        client.customer,
        main.doc_date DESC,
        main.doc_number,
        nom.description
    ;
    
    GRANT SELECT ON TABLE v_one_return_not_invoice TO user_prestige;
    
'''


async def main_return_no_invoice_async():
    result = await async_save_pg(SQL_CREATE_VIEW)
    logger.info(f"{result}, main_return_no_invoice_async")


if __name__ == '__main__':
    logger.info(f"START")
    from Document.docReturnOfGoodsFromCustomers import main_doc_return_of_goods_from_customers_async
    from Document.docReturnOfGoodsFromCustomersGoods import main_doc_return_of_goods_from_customers_goods_async

    asyncio.run(main_doc_return_of_goods_from_customers_async())
    asyncio.run(main_doc_return_of_goods_from_customers_goods_async())
    asyncio.run(main_return_no_invoice_async())
    logger.info(f"FINISH")