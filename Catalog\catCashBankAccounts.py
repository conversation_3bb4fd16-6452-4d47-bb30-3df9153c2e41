import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = 't_one_cat_cash_bank_accounts'
DOCUMENT = "Catalog_БанковскиеСчета"
SELECT_COLUMNS = "Code,DataVersion,DeletionMark,Description,Owner,Predefined,НомерСчета,Ref_Key,Банк_Key," \
                 "ВалютаДенежныхСредств_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        description varchar NOT NULL,  -- Description
        predefined bool NOT NULL DEFAULT false,  -- Predefined
        account_number varchar(50) NOT NULL,  -- НомерСчета
        code varchar(20) NOT NULL DEFAULT 0,  -- Code
        deletion_mark bool NOT NULL DEFAULT false,  -- DeletionMark
        dataversion varchar(50) NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        organization_key varchar(50) NULL,  -- Owner
        bank_key varchar(50) NULL,  -- Банк_Key
        currency_key varchar(50) NULL,  -- ВалютаДенежныхСредств_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Owner';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.account_number IS 'НомерСчета';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.bank_key IS 'Банк_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДенежныхСредств_Key';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        code, 
        dataversion, 
        deletion_mark, 
        description, 
        organization_key, 
        predefined, 
        account_number, 
        ref_key, 
        bank_key, 
        currency_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        code = EXCLUDED.code,
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        description = EXCLUDED.description,
        organization_key = EXCLUDED.organization_key,
        predefined = EXCLUDED.predefined,
        account_number = EXCLUDED.account_number,
        ref_key = EXCLUDED.ref_key,
        bank_key = EXCLUDED.bank_key,
        currency_key = EXCLUDED.currency_key
    '''
    return sql.replace("'", "")


async def main_cat_cash_bank_accounts_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    # sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_cash_bank_accounts_async())
