CREATE OR REPLACE VIEW v_one_customer_manager AS 
SELECT 
	customer.ref_key,
	customer.description AS customer,
	fld.folder AS folder
FROM t_one_cat_counterparties AS customer
	LEFT JOIN (
	SELECT 
		description AS folder,
		ref_key AS folder_key
	FROM t_one_cat_counterparties
	WHERE isfolder
	) AS fld
	ON fld.folder_key = customer.parent_key
WHERE NOT isfolder
ORDER BY folder, customer
;

GRANT SELECT ON TABLE v_one_customer_manager TO anna;
GRANT SELECT ON TABLE v_one_customer_manager TO user_prestige;

