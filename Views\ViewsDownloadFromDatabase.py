import asyncio

import asyncpg

from async_Postgres import CONN_PARAMS


async def save_view_codes_to_file():
    # SQL-запрос для получения кода всех представлений, созданных пользователем 'postgres' и не начинающихся с 'pg_'
    sql = """
    SELECT
        n.nspname AS schema_name,
        c.relname AS view_name,
        pg_get_viewdef(c.oid) AS view_definition
    FROM
        pg_class c
        LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
        JOIN pg_user u ON u.usesysid = c.relowner
    WHERE
        c.relkind = 'v' AND
        n.nspname = 'public' AND
        u.usename = 'postgres' AND
        c.relname NOT LIKE 'pg_%';
    """

    # Создаем подключение к базе данных
    conn = await asyncpg.connect(**CONN_PARAMS)

    # Выполняем SQL-запрос
    rows = await conn.fetch(sql)

    # Закрываем подключение
    await conn.close()

    # Создаем или открываем файл для записи
    with open('views.sql', 'w') as f:
        for row in rows:
            # Записываем код каждого представления в файл
            f.write(f"CREATE OR REPLACE VIEW {row['schema_name']}.{row['view_name']} AS\n{row['view_definition']}\n\n")

    print("View codes have been saved to 'views.sql'.")


# Запускаем функцию
asyncio.run(save_view_codes_to_file())
