import multiprocessing
import time

from Views.SaleYearsMonth import main_sale_years_month_async


def worker_function(name):
    print(f"Worker {name} started")
    time.sleep(3)  # имитация работы в процессе
    print(f"Worker {name} finished")

if __name__ == "__main__":
    # Создание объекта Process для каждой функции
    process1 = multiprocessing.Process(target=worker_function, args=(1,main_sale_years_month_async))
    process2 = multiprocessing.Process(target=worker_function, args=(2,))

    # Запуск процессов
    process1.start()
    process2.start()

    # Ожидание завершения обоих процессов
    process1.join()
    process2.join()

    print("All processes are finished")
