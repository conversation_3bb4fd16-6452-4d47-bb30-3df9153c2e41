DROP VIEW IF EXISTS v_one_virtual_correction_debt CASCADE;
CREATE OR REPLACE VIEW v_one_virtual_correction_debt AS
    SELECT DISTINCT
        sg.manager мэнеджер,
        sg.customer контрагент,
        main.doc_date дата,
        main.doc_number номер
    FROM t_one_doc_debt_correction AS main
        LEFT JOIN
        (
            SELECT DISTINCT
                manager,
                customer,
                customer_key
            FROM v_one_manager_counterparty_contracts_segments
            WHERE manager_key IS NOT NULL
        ) AS sg
            ON sg.customer_key = main.counterparty_creditor_key
                OR sg.customer_key = main.counterparty_debtor_key
    WHERE main.posted
        AND main.is_management
        AND main.operation_type = 'ПроведениеВзаимозачета'
        AND main.counterparty_creditor_key = main.counterparty_debtor_key
        AND doc_date::date >= '01.01.2024'::date
    ORDER BY
        doc_date DESC,
        doc_number
    ;

COMMENT ON VIEW public.v_one_virtual_correction_debt IS 'корректировка долга виртуальная. для бухУчета';
GRANT SELECT ON TABLE public.v_one_virtual_correction_debt TO user_prestige;
