
    CREATE OR REPLACE VIEW v_one_stock_maliyet_rapor AS
    SELECT
        sku,
        maliyet,
        ed AS adet,
        tobox AS koli,
        ed * maliyet AS tutar
    FROM (
        SELECT DISTINCT 
            sku,
            nomenclature_key,
            supplier,
            maliyet,
            sum(quantity * coefficient) OVER (PARTITION BY  nomenclature_key) AS ed,
            sum(tobox) OVER (PARTITION BY  nomenclature_key) AS tobox,
            doc_date = max(doc_date) OVER (PARTITION BY nomenclature_key) AS isMaxDate
        FROM t_one_stock	
        WHERE warehouse_key 
            IN ('a26219b3-8fba-11e6-80c4-c936aa9c817c','4b40b865-6d2f-11ec-8125-001dd8b72b55')
            -- склады:
            -- Основной склад товаров	a26219b3-8fba-11e6-80c4-c936aa9c817c
            -- Основной склад товаров 2022	4b40b865-6d2f-11ec-8125-001dd8b72b55
    ) AS t
    WHERE isMaxDate = TRUE
        AND ed <> 0
    ORDER BY sku;

    GRANT SELECT ON TABLE v_one_stock_maliyet_rapor TO user_prestige;

