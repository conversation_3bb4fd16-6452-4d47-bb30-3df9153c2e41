import os
import subprocess
import datetime
from async_Postgres import username, psw, basename, hostname_public, port

# Полный путь к pg_dump и psql
pg_dump_path = r'C:\Program Files\PostgreSQL\14\bin\pg_dump.exe'
psql_path = r'C:\Program Files\PostgreSQL\14\bin\psql.exe'

backup_dir = os.path.abspath("Backup")
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
backup_file = os.path.join(backup_dir, f"{basename}_backup_{timestamp}.sql")

if not os.path.exists(backup_dir):
    os.makedirs(backup_dir)

env = {"PGPASSWORD": psw}

def backup_postgresql_db():
    pg_command = [
        pg_dump_path,
        f"--host={hostname_public}",
        f"--port={port}",
        f"--username={username}",
        f"--dbname={basename}",
        "--no-password",
        "--file",
        backup_file,
    ]

    print("Executing command:", ' '.join(pg_command))

    try:
        result = subprocess.run(pg_command, env=env, check=True, text=True, capture_output=True)
        print(f"Backup successful: {backup_file}")
    except subprocess.CalledProcessError as e:
        print(f"Backup failed: {e.stderr}")
    except FileNotFoundError as e:
        print(f"File not found: {e.filename}")

def restore_postgresql_db():
    restore_command = [
        psql_path,
        f"--host={hostname_public}",
        f"--port={port}",
        f"--username={username}",
        f"--dbname={basename}",
        "--no-password",
        "--file",
        backup_file,
    ]

    print("Executing command:", ' '.join(restore_command))

    try:
        result = subprocess.run(restore_command, env=env, check=True, text=True, capture_output=True)
        print(f"База данных успешно восстановлена из резервной копии: {backup_file}")
    except subprocess.CalledProcessError as e:
        print(f"Не удалось восстановить базу данных: {e.stderr}")
    except FileNotFoundError as e:
        print(f"File not found: {e.filename}")

backup_postgresql_db()
# restore_postgresql_db()
