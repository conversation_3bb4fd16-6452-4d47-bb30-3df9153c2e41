
    CREATE OR REPLACE VIEW v_one_cash_warrant_receipt_expense AS 
    SELECT *
    FROM (
        SELECT *,
            'receipt' AS receipt
        FROM v_one_cash_warrant_receipt
        UNION ALL
        SELECT *,
            'expense' AS expense
        FROM v_one_cash_warrant_expense 
    ) AS t
    ORDER BY t.doc_date, t.doc_number 
    ;

    COMMENT ON VIEW v_one_cash_warrant_receipt_expense IS 'Приходный/Расходный кассовый ордер';
