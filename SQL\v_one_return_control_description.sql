    CREATE OR REPLACE VIEW v_one_return_control_description AS
    SELECT doc_date ВН_дата, doc_number ВН_номер, description комментарий,
        customer_number "номер у клиента", number_from_description "номер в коммент",
        customer_date "ВН_датаКлиента", date_from_description "дата указ в коммент",
    concat(
        CASE
            WHEN (customer_date = date_from_description) THEN ''
            ELSE 'дата; '
        END,
        CASE
            WHEN (customer_number = number_from_description) THEN ''
            ELSE 'номер'
        END) нестыковка,
        date_create,
        user_name AS пользователь,
        user_key
    FROM (
        SELECT
            rtrn.description,
            trim(customer_number) customer_number,
            (SELECT (regexp_matches((SELECT regexp_replace(rtrn.description,
                '\d{1,2}(\/|-|\.|,)\d{1,2}(\/|-|\.|,)\d{2,4}|[Оо]т|[Bвв]ід', '', 'g')),
                    '([а-яА-ЯёЁa-zA-ZА-Яа-яёЁЇїІіЄєҐґ0-9\-_/]+)'))[1]) number_from_description,
            customer_date,
            (SELECT (regexp_matches(( SELECT regexp_replace(rtrn.description, '\s', '', 'gi')),
                '((3[01]|[12][0-9]|0?[1-9])(\/|-|\.)(1[0-2]|0?[1-9])(\/|-|\.)2([0-9]{2})?[0-9])'))[1])::date
            AS date_from_description,
            doc_date, doc_number,date_create,
            users.description AS user_name,
            rtrn.user_key
        FROM t_one_doc_return_of_goods_from_customers AS rtrn
        	LEFT JOIN t_one_cat_users AS users
        		ON users.individual_key = rtrn.user_key
        WHERE  posted
            AND COALESCE(rtrn.description,'') <> ''
            AND (rtrn.description NOT IN ('просрочка','переоцінка','ПЕРЕОЦІНКА') OR COALESCE(trim(customer_number),'') = ''
                OR COALESCE(customer_date,'0001-01-01')::date = '0001-01-01'::date)
            AND responsible_key = '091d06f1-e92d-11eb-810e-001dd8b72b55' -- Лужанская Света
            AND doc_date::date >= '01.01.2023'::date
            -- AND responsible_key = '52d9d1db-5cb9-11e7-80d1-001dd8b79079' -- Бебех Сергей
            AND ((SELECT (regexp_matches((SELECT regexp_replace(rtrn.description,
                '\d{1,2}(\/|-|\.|,)\d{1,2}(\/|-|\.|,)\d{2,4}|[Оо]т|[Bвв]ід', '', 'g')),
                    '([а-яА-ЯёЁa-zA-ZА-Яа-яёЁЇїІіЄєҐґ0-9\-_/]+)'))[1]) <> trim(customer_number)
                OR  (SELECT (regexp_matches((SELECT regexp_replace(rtrn.description,
                    '\s', '', 'gi')),
                    '((3[01]|[12][0-9]|0?[1-9])(\/|-|\.)(1[0-2]|0?[1-9])(\/|-|\.)2([0-9]{2})?[0-9])'))[1])::date <> customer_date::date
            )
    ) AS t
    ORDER BY doc_date DESC;

    COMMENT ON VIEW v_one_return_control_description IS 'Возвратные накл.
        Контроль введенных в коммент даты и номер ВН клиента должен совпадать с полями дата и номер ВН клиента';
    GRANT SELECT ON TABLE v_one_return_control_description TO user_prestige;
