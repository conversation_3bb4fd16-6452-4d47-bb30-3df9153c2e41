# pip install pyxlsb
import pandas as pd

fileName = r'C:\Users\<USER>\Desktop\Excel\Поступления_Сроки.xlsb'
df = pd.read_excel(fileName, sheet_name=2)
for row in range(df.shape[0]):
    for col in range(df.shape[1]):
        if df.iat[row, col] == 'MARKING':
            row_start = row
            break
for row in range(df.shape[0]):
    for col in range(df.shape[1]):
        if df.iat[row, col] == 'TOTAL AMOUNT :':
            row_finish = row
            break
df_required = df.loc[row_start + 1:row_finish - 1]
df = pd.read_excel(fileName, 'Sheet(1)', skiprows=row_start, nrows=row_finish,)
# print(df)
print(row_start)
print(df_required)
