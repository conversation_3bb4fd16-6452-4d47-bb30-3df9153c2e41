import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_cat_nomenclature_uktved"
DOCUMENT = "Catalog_НоменклатураГТД"
SELECT_COLUMN = "DataVersion,DeletionMark,Description,Predefined,КодУКТВЭД_Индекс,Комментарий," \
                "НомерГТД_Индекс,Owner_Key,Ref_Key,КодУКТВЭД_Key,НомерГТД_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NULL,  -- DataVersion
        deletionmark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        description varchar(250) NULL,  -- Description
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        code_uktved_index varchar(50) NULL,  -- КодУКТВЭД_Индекс
        a_comment varchar(250) NULL,  -- Комментарий
        doc_number_gtd_index varchar(50) NULL,  -- НомерГТД_Индекс
        owner_key varchar(50) NULL,  -- Owner_Key
        ref_key varchar(50) NULL,  -- Ref_Key
        code_uktved_key varchar(50) NULL,  -- КодУКТВЭД_Key
        doc_number_gtd_key varchar(50) NULL,  -- НомерГТД_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS ' DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS ' DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS ' Description';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS ' Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.code_uktved_index IS ' КодУКТВЭД_Индекс';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS ' Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number_gtd_index IS ' НомерГТД_Индекс';
    COMMENT ON COLUMN {TABLE_NAME}.owner_key IS ' Owner_Key';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code_uktved_key IS ' КодУКТВЭД_Key';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number_gtd_key IS ' НомерГТД_Key';
    '''

SQL_CREATE_TRIGGER = f'''
    CREATE TRIGGER bfr_{TABLE_NAME} BEFORE
    INSERT
        OR
    UPDATE
        ON
    {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_update_{TABLE_NAME}()
    '''

SQL_CREATE_FUNCTION = f'''
    CREATE OR REPLACE FUNCTION fn_update_{TABLE_NAME}()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
        BEGIN
            NEW.code_uktved_index = REPLACE(NEW.code_uktved_index,' ','');
            RETURN NEW;

        END;

      $function$
    ;
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        dataversion,
        deletionmark,
        description,
        predefined,
        code_uktved_index,
        a_comment,
        doc_number_gtd_index,
        owner_key,
        ref_key,
        code_uktved_key,
        doc_number_gtd_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        description = EXCLUDED.description,
        predefined = EXCLUDED.predefined,
        code_uktved_index = EXCLUDED.code_uktved_index,
        a_comment = EXCLUDED.a_comment,
        doc_number_gtd_index = EXCLUDED.doc_number_gtd_index,
        owner_key = EXCLUDED.owner_key,
        code_uktved_key = EXCLUDED.code_uktved_key,
        doc_number_gtd_key = EXCLUDED.doc_number_gtd_key
    ;
    '''
    return sql.replace("'", "")


async def main_cat_nomenclature_gtd_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    await async_save_pg(SQL_CREATE_FUNCTION)
    await async_save_pg(SQL_CREATE_TRIGGER)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMN)
    maket = await create_model_async(11)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_nomenclature_gtd_async())
