import asyncio
from async_Postgres import async_save_pg
from logger_prestige import get_logger

logger = get_logger("explain_only")

async def explain_query():
    # Запрос EXPLAIN (без ANALYZE) для анализа SQL_INSERT
    EXPLAIN_QUERY = """
    EXPLAIN (FORMAT JSON)
    SELECT 
        dtperiod,
        quantity,
        replace(recorder_type,'StandardODATA.Document_',''),
        recorder,
        nomenclature_key,
        item_series_key,
        warehouse_key
    FROM t_one_accreg_good_in_warehauses_recordtype AS reg
    """
    
    result = await async_save_pg(EXPLAIN_QUERY)
    logger.info(f"EXPLAIN результат:\n{result}")

if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(explain_query())
