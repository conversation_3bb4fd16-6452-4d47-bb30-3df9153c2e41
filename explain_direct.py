import asyncio
import asyncpg
import os
import sys
async def explain_query():
    # Получаем параметры подключения из переменных окружения или используем значения по умолчанию
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = os.environ.get('DB_PORT', '5432')
    DB_NAME = os.environ.get('DB_NAME', 'postgres')
    DB_USER = os.environ.get('DB_USER', 'postgres')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', 'postgres')
    
    # Строка подключения
    conn_str = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    
    try:
        # Подключаемся к базе данных
        conn = await asyncpg.connect(conn_str)
        
        # Запрос EXPLAIN для анализа SELECT части SQL_INSERT
        EXPLAIN_QUERY = """
        EXPLAIN (FORMAT TEXT)
        SELECT 
            dtperiod,
            quantity,
            replace(recorder_type,'StandardODATA.Document_',''),
            recorder,
            nomenclature_key,
            item_series_key,
            warehouse_key
        FROM t_one_accreg_good_in_warehauses_recordtype AS reg
        """
        
        # Выполняем запрос
        result = await conn.fetch(EXPLAIN_QUERY)
        
        # Выводим результат
        print("EXPLAIN результат:")
        for row in result:
            print(row[0])
        
        # Закрываем соединение
        await conn.close()
        
    except Exception as e:
        print(f"Ошибка: {e}")

if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(explain_query())
