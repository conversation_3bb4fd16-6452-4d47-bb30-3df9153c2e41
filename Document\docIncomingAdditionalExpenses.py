# Document_ПоступлениеДопРасходов используется для расчета себестоимости
# в таб части содержит
# *********** импортируем данные для подключения к базам
import asyncio
import os
import sys

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_incoming_additional_expenses"
DOCUMENT = "Document_ПоступлениеДопРасходов"
SELECT_COLUMNS = (
    "DataVersion,Ref_Key,Number,Date,Posted,ВалютаДокумента_Key,Сумма<PERSON><PERSON>лючаетНДС,СуммаДокумента,СуммаНДС,"
    "Организация_Key,Контрагент_Key,ОтражатьВБухгалтерскомУчете,ОтражатьВУправленческомУчете,Подразделение_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NOT NULL,  -- DataVersion
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        posted bool NOT NULL,  -- Posted
        amount_includes_vat bool NOT NULL,  -- СуммаВключаетНДС
        document_amount numeric(10, 2) NOT NULL DEFAULT 0,  -- СуммаДокумента
        amount_vat numeric(10, 2) NOT NULL,  -- СуммаНДС
        is_accounting bool NULL DEFAULT false,  -- ОтражатьВБухгалтерскомУчете
        is_management bool NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        account_key varchar(50) NOT NULL,  -- Контрагент_Key
        currency_key varchar(50) NOT NULL,  -- ВалютаДокумента_Key
        organization_key varchar(50) NOT NULL,  -- Организация_Key,
        subdivision_key varchar(50) NULL,  -- Подразделение_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
    COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.subdivision_key IS 'Подразделение_Key';
    
    GRANT SELECT ON TABLE t_one_doc_incoming_additional_expenses TO user_prestige;
    
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        dataversion,
        ref_key,
        doc_number,
        doc_date,
        posted,
        currency_key,
        amount_includes_vat,
        document_amount,
        amount_vat,
        organization_key,
        account_key,
        is_accounting,
        is_management,
        subdivision_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_number = EXCLUDED.doc_number,
        doc_date = EXCLUDED.doc_date,
        posted = EXCLUDED.posted,
        currency_key = EXCLUDED.currency_key,
        amount_includes_vat = EXCLUDED.amount_includes_vat,
        document_amount = EXCLUDED.document_amount,
        amount_vat = EXCLUDED.amount_vat,
        organization_key = EXCLUDED.organization_key,
        account_key = EXCLUDED.account_key,
        is_accounting = EXCLUDED.is_accounting,
        is_management = EXCLUDED.is_management,
        subdivision_key = EXCLUDED.subdivision_key
    ;
    """
    return sql.replace("'", "")


async def main_incoming_additional_expenses_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(14)
    sql = await sql_insert(maket)
    sql = sql.replace("$4", "to_timestamp($4, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main_incoming_additional_expenses_async())
