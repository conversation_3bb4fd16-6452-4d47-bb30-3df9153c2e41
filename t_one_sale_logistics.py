import asyncio
import re
import time
from datetime import datetime, timedelta, date

from Catalog.InfRegDocumentBarcode import main_info_reg_document_barcode_async
from CreateAndRunSQLScripts import create_views
from Document.docBuyerOrder import main_doc_buyer_order_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
from async_Postgres import full_vacuum_pg_async, select_pg, async_save_pg

TABLE_NAME = "t_one_sale_logistics"
PHONE_PATTERN = "0\d{9,12}" \
                "|\(?0\d{2}\)?.\d{7}" \
                "|\(?0\d{2}\)?.\d{2}.\d{2}.\d{3}" \
                "|\(?0\d{2}\)?.\d{3}.\d{2}.\d{2}" \
                "|\(?0\d{2}\)?.\d{2}.\d{3}.\d{2}" \
                "|\(?0\d{2}\)?.\d{3}.\d{4}" \
                "|\(?0\d{2}\)?.\d{5}.\d{2}" \
                "|0\d{3}.\d{6}" \
                "|0\d{5}.\d{2}.\d{2}" \
                "|0\d{7}.\d{2}"

SQL_CREATE_TABLE = f'''
    -- DROP TABLE IF EXISTS  {TABLE_NAME} CASCADE;
    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL,  -- дата РН
        doc_number varchar(15) NOT NULL,  -- номер РН
        barcode  varchar(25) NULL,  -- штрихкод Док
        num_doc numeric NULL,  -- номер РН без префикса
        organization varchar(50) NOT NULL,  -- организация
        counterparty varchar(150) NULL,  -- контрагент
        counterparty_edrpou varchar(15) NULL,  -- ОКПО контрагента
        amount numeric(15, 4) NULL DEFAULT 0,  -- сумма РН
        date_send date NULL,  -- дата отправки
        date_order date NULL,  -- дата заказа
        number_order varchar(15) NULL,  -- номер заказа
        add_to_delivery_address varchar NULL,  -- ДополнениеКАдресуДоставки
        delivery_address varchar NULL,  -- адрес доставки
        is_accounting bool NULL,  -- бух.учет
        doc_prefix bpchar(5) NULL,  -- префика РН
        weight numeric(10, 3) NULL DEFAULT 0,  -- вес
        logistics_costs numeric(15, 4) NULL DEFAULT 0,  -- сумма доставки
        weight_concat varchar NULL,  -- состоит из весов
        logistics_costs_concat varchar NULL,  -- состоит из сумм
        logistics_phones varchar NULL,  -- телефоны получателя
        cargotype varchar NULL,  -- тип карго
        carrier varchar NULL,  -- перевозчик
        ttn varchar NULL,  -- ТТН доставки
        ref_key varchar(50) NULL,  -- id РН
        currency_key varchar(50) NULL,  -- id валюты
        counterparty_key varchar(50) NOT NULL,  -- id контрагента
        deal varchar(50) NULL,  -- Сделка
        organization_key varchar(50) NULL,  -- Ref_Key организации
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS 'РН и сумма доставки';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'дата РН';
    COMMENT ON COLUMN {TABLE_NAME}.add_to_delivery_address IS 'ДополнениеКАдресуДоставки';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'номер РН';
    COMMENT ON COLUMN {TABLE_NAME}.num_doc IS 'номер РН без префикса';
    COMMENT ON COLUMN {TABLE_NAME}.barcode IS 'штрихкод Док';
    COMMENT ON COLUMN {TABLE_NAME}.organization IS 'организация';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty IS 'контрагент';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_edrpou IS 'ОКПО контрагента';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма РН';
    COMMENT ON COLUMN {TABLE_NAME}.logistics_costs IS 'сумма доставки';
    COMMENT ON COLUMN {TABLE_NAME}.carrier IS 'перевозчик';   
    COMMENT ON COLUMN {TABLE_NAME}.date_send IS 'дата отправки';
    COMMENT ON COLUMN {TABLE_NAME}.date_order IS 'дата заказа';
    COMMENT ON COLUMN {TABLE_NAME}.number_order IS 'номер заказа';
    COMMENT ON COLUMN {TABLE_NAME}.cargotype IS 'тип карго';
    COMMENT ON COLUMN {TABLE_NAME}.delivery_address IS 'адрес доставки';
    COMMENT ON COLUMN {TABLE_NAME}.ttn IS 'ТТН доставки';
    COMMENT ON COLUMN {TABLE_NAME}.weight IS 'вес';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'бух.учет';
    COMMENT ON COLUMN {TABLE_NAME}.doc_prefix IS 'префика РН';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'id РН';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'id валюты';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_key IS 'id контрагента';	
    COMMENT ON COLUMN {TABLE_NAME}.deal IS 'id контрагента';	
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Ref_Key организации';
'''

SQL_INSERT_TABLE = f'''
        INSERT INTO {TABLE_NAME}(
            doc_date,
            doc_number,
            num_doc,
            organization,
            counterparty,
            counterparty_edrpou,
            amount,
            delivery_address,
            is_accounting,
            doc_prefix,
            ref_key,
            currency_key,
            counterparty_key,
            deal,
            date_order,
            date_send,
            number_order,
            add_to_delivery_address,
            carrier,
            organization_key,
            barcode,
            logistics_costs
        )
        SELECT
            sale.doc_date,
            sale.doc_number,
            UNNEST(REGEXP_MATCHES(sale.doc_number,'\d+'))::NUMERIC AS num_doc,
            org.description AS organization,
            counterpartys.description AS counterparty,
            CASE 
                WHEN COALESCE(counterpartys.edrpou,'')='' THEN inn
                ELSE counterpartys.edrpou
            END AS counterparty_edrpou,
            sale.document_amount,
            sale.delivery_address,
            sale.reflect_in_accounting_accounting,
            UNNEST(REGEXP_MATCHES(sale.doc_number,'[a-zA-Zа-яА-яёЁ]+')) AS prefix,
            sale.ref_key,
            sale.document_currency_key,
            sale.account_key,
            sale.deal,
            orders.doc_date,
            orders.shipping_doc_date,
            orders.doc_number,
            orders.add_to_delivery_address,
            CASE 
                WHEN sale.logistics_costs <> '' THEN
                    'самовывоз'
                ELSE
                    orders.add_to_delivery_address
            END AS carrier,
            sale.organization_key,
            brc.barcode,
            CASE 
                WHEN (sale.logistics_costs <> '') or (sale.logistics_costs IS NULL) THEN
                    sale.logistics_costs::numeric
            END AS logistics_costs
            FROM t_one_doc_sale_of_goods_services AS sale
                LEFT JOIN t_one_cat_counterparties AS counterpartys
                    ON sale.account_key = counterpartys.ref_key
                LEFT JOIN t_one_cat_organizations AS org
                    ON org.ref_key = sale.organization_key
                LEFT JOIN t_one_doc_buyer_order as orders
                    ON sale.deal = orders.ref_key
                LEFT JOIN t_one_infreg_document_barcode as brc
                    ON brc.recorder = sale.ref_key
            WHERE sale.doc_date::date >= '01.01.2023'
                AND sale.organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
                AND sale.account_key not in ('adacf222-8cdf-11ed-815c-001dd8b740bc',  -- Виртуальный клиент (списание)
                                            'cef2b43d-02ea-11eb-80fa-001dd8b79079',  -- Артур Гаврил
                                            '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
                                            )
            ORDER BY sale.doc_date desc 
        ON CONFLICT ON CONSTRAINT {TABLE_NAME}_pkey 
        DO NOTHING;        
    '''


async def get_logistics_data(date_first, date_last, where):
    sql = f'''
    SELECT
        barcode,
        array_to_string(array_agg(distinct intdocnumber),'; ') AS concat_ttn,
        array_to_string(array_agg(distinct weight),'; ') AS concat_weight,
        array_to_string(array_agg(distinct costonsite),'; ') AS concat_costonsite,
        sum(weight) AS weight,
        sum(costonsite) AS costonsite,
        doc_number,
        array_size,
        recipientdescription,
        recipientedrpou,
        inforegclientbarcodes,
        array_to_string(array_agg(distinct description),'; ') AS concat_description,
        array_to_string(array_agg(distinct recipientcontactphone),'; ') AS concat_recipientcontactphone,
        array_to_string(array_agg(distinct cityrecipientdescription),'; ') AS concat_cityrecipientdescription,
        array_to_string(array_agg(distinct recipientaddressdescription),'; ') AS concat_recipientaddressdescription,
        array_to_string(array_agg(distinct doc_datetime),'; ') AS concat_date,
        array_to_string(array_agg(distinct recipientfullname),'; ') AS concat_recipientfullname,
        array_to_string(array_agg(distinct cargotype),'; ') AS concat_cargotype
    FROM (
        SELECT 
            regexp_split_to_array(regexp_replace(concat(inforegclientbarcodes, additional_information),'[;]','','gmi'),',') AS barcode,
            UNNEST(regexp_split_to_array(regexp_replace(concat(inforegclientbarcodes, additional_information),' ','','gmi'),',')) AS doc_number,
            array_length(ARRAY(SELECT REGEXP_MATCHES(concat(inforegclientbarcodes, additional_information),'S\d6[A-Z]2\d+|\d+','g')),1) AS array_size,
            doc_datetime,
            intdocnumber,
            cargotype,
            recipientdescription,
            recipientedrpou,
            recipientfullname,
            recipientcontactphone,
            recipientaddressdescription,
            cityrecipientdescription,
            description,
            inforegclientbarcodes,
            weight,
            costonsite
        FROM t_np_list_ttn
        WHERE doc_datetime::date>='{date_first}'::date
            and doc_datetime::date <='{date_last}'::date
            and {where}
        ) AS ttn
    GROUP BY 
        barcode,
        doc_number,
        array_size,
        recipientdescription,
        recipientedrpou,
        inforegclientbarcodes
        ;
    '''

    result = await select_pg(sql)
    return result


async def phone_one_c_tuple(row):
    pattern = re.compile(PHONE_PATTERN)
    phone_list_one_c = pattern.findall(row['delivery_address'])
    # add prefix 38 to all phones in list
    tuple_phone = ''
    phone_list_one_c = ['38' + re.sub(r"[^0-9]+", "", x) for x in phone_list_one_c if isinstance(x, str)]
    if len(phone_list_one_c) == 1:
        tuple_phone = "('%s')" % tuple(phone_list_one_c)[0]
    elif len(phone_list_one_c) > 1:
        tuple_phone = tuple(phone_list_one_c)

    return tuple_phone


async def read_sql_result(result):
    for i, row in enumerate(result):
        try:
            if row['doc_date'].date() > (date.today() - timedelta(days=2)):
                continue

            phones = await phone_one_c_tuple(row)

            date_doc = datetime.strptime(datetime.strftime(row['doc_date'], "%Y.%m.%d"), "%Y.%m.%d")

            # logistics date should be between date_first and date_last
            date_first, date_last = await get_dates_for_logistics(date_doc)
            where = f"'{row['barcode']}'=ANY(regexp_split_to_array(regexp_replace(" \
                    f"concat(inforegclientbarcodes, additional_information),' ','','gmi'),','))"
            logistics_data = await get_logistics_data(date_first, date_last, where)
            if logistics_data == []:  # doc_barcode is emptry
                doc_number_int = int(re.findall("\d+", row['doc_number'])[0])

                # 1) edrpou - TRUE; phone - true;find the okpo and then phone number
                # (phone number - because send to our RP)
                edrpou = row['counterparty_edrpou']
                if phones and edrpou:
                    where = f"recipientedrpou = '{edrpou}' and recipientcontactphone in {phones}"
                    logistics_data = await get_logistics_data(date_first, date_last, where)

                if logistics_data == [] and phones and doc_number_int > 0:
                    where = f"inforegclientbarcodes ilike '%{doc_number_int}%' and recipientcontactphone in {phones}"
                    logistics_data = await get_logistics_data(date_first, date_last, where)

                if logistics_data == [] and edrpou and doc_number_int > 0:
                    where = f"recipientedrpou = '{edrpou}' and inforegclientbarcodes ilike '%{doc_number_int}%'"
                    logistics_data = await get_logistics_data(date_first, date_last, where)

                if logistics_data == [] and phones:
                    where = f"(regexp_split_to_array('{row['counterparty']}', '\s+') && " \
                            f"regexp_split_to_array(recipientfullname, '\s+'))" \
                            f" and recipientcontactphone in {phones}" \
                            f" ORDER BY doc_datetime DESC LIMIT 1"
                    logistics_data = await get_logistics_data(date_first, date_last, where)

            if logistics_data:
                result = await edit_date(row['ref_key'], logistics_data[0])

        except Exception as e:
            print(str(e))


async def edit_date(ref_key, logistics_record):
    sql = f'''
        UPDATE {TABLE_NAME}
        SET weight = $2,
            logistics_costs = $3,
            weight_concat = $4,
            logistics_costs_concat = $5,
            logistics_phones = $6,
            ttn = $7,
            cargotype = $8,
            carrier = 'NP'
        WHERE ref_key = $1 
        '''

    data = (
        ref_key,
        logistics_record['weight'],
        logistics_record['costonsite'],
        logistics_record['concat_weight'],
        logistics_record['concat_costonsite'],
        logistics_record['concat_recipientcontactphone'],
        logistics_record['concat_ttn'],
        logistics_record['concat_description']
    )
    return await async_save_pg(sql, [data])


async def get_dates_for_logistics(date_doc):
    """
    row - data (one record) from database
    returned two dates, for filter to logistics cost
    calculation: date of the DOCUMENT minus and plus 7 days
    """
    delta_days = 7
    # return dates for logistics filter
    date_first = date_doc - timedelta(days=delta_days)
    date_last = date_doc + timedelta(days=delta_days)
    return datetime.strftime(date_first, "%Y.%m.%d"), datetime.strftime(date_last, "%Y.%m.%d")


update_repeate_carrier = f'''
    UPDATE  {TABLE_NAME} AS lgs
    SET add_to_delivery_address = t.add_to_delivery_address,
        carrier = t.add_to_delivery_address,
        date_send = t.date_send
    FROM (SELECT DISTINCT 
                    doc_date::date AS doc_date,
                    add_to_delivery_address,
                    date_send,
                    counterparty_key
                FROM {TABLE_NAME} AS logistic
                WHERE logistic.doc_date >= '01.01.2023'
                    AND logistic.counterparty_key IN ('4cd5e36a-9e7d-11eb-8104-001dd8b72b55', 
                                                        'a03b3365-04be-11e7-80ca-001dd8b79079', 
                                                        '09fa4620-19ee-11e7-80cb-001dd8b79079')
                    AND coalesce(add_to_delivery_address,'') <> ''
        ) AS t
    WHERE coalesce(lgs.add_to_delivery_address,'') = ''
        AND lgs.doc_date::Date = t.doc_date::Date
        AND lgs.counterparty_key = t.counterparty_key
    ;
    -- 1. Новус
    -- 2. Епіцентр
    -- 3. Ашан        
'''

update_carrier_virtual = f'''
    UPDATE {TABLE_NAME} AS lgs
    SET carrier = 'virtual'
    WHERE (carrier IS NULL 
        OR carrier = '')
        AND counterparty_key in ('adacf222-8cdf-11ed-815c-001dd8b740bc',
            '2c04025b-b29a-11e9-80ea-001dd8b79079')
'''

update_kyiv_malinska = f'''
    UPDATE {TABLE_NAME}
    SET carrier = 'самовывоз'
    WHERE delivery_address ilike('%київ%') and delivery_address ilike('%малинська%');
'''

update_logistic_costs_kiev = f'''
    UPDATE {TABLE_NAME} AS logs
    SET logistics_costs = ((2000 / t.total) * logs.amount)
    FROM (
        SELECT sum(amount) AS total,date_send, carrier
        FROM {TABLE_NAME}
        WHERE date_send >= '01.03.2023' 
            AND add_to_delivery_address = carrier
            AND carrier IS NOT NULL AND carrier != ''
        GROUP BY date_send, carrier 
        ) AS t
    WHERE logs.date_send::date = t.date_send::date AND  logs.carrier = t.carrier
    ;
'''

update_compensation_logistics = f'''
    UPDATE t_one_sale_logistics AS sale
    SET logistics_costs = expense.amount,
        carrier = 'компенсация логистики'
    FROM t_one_doc_cash_warrant_expense AS expense
    WHERE expense.contractor_key = sale.counterparty_key
        AND  (assignment_key = '3f4bc2ff-6434-11ea-80ef-001dd8b79079')
        AND expense.doc_date::date BETWEEN (sale.doc_date::date - INTERVAL '7 day') 
        AND (sale.doc_date::date + INTERVAL '7 day')    
'''


async def main_one_sale_logistics():
    """ datetime object containing current date and time """
    start = time.time()
    print("start: ", datetime.strftime(datetime.now(), "%Y.%m.%d %H:%M:%S"))
    # await main_doc_buyer_order_async()
    # await main_doc_sale_of_goods_services_async()
    # await main_info_reg_document_barcode_async()

    result = await async_save_pg(SQL_CREATE_TABLE)
    print('sql_create', result, int(time.time() - start))
    result = await async_save_pg(SQL_INSERT_TABLE)
    print('SQL_INSERT_TABLE', result, int(time.time() - start))
    sql = f"SELECT * FROM {TABLE_NAME} WHERE  coalesce(carrier,'') = '';"
    result = await select_pg(sql)
    print("select_pg: ", datetime.strftime(datetime.now(), "%Y.%m.%d %H:%M:%S"))
    await read_sql_result(result)
    print("read_sql_result: ", datetime.strftime(datetime.now(), "%Y.%m.%d %H:%M:%S"))
    await full_vacuum_pg_async()

    await async_save_pg(update_repeate_carrier)
    print('update_repeate_carrier', int(time.time() - start))

    await async_save_pg(update_carrier_virtual)
    print('update_carrier_virtual', int(time.time() - start))

    await async_save_pg(update_kyiv_malinska)
    print('update_kyiv_malinska', int(time.time() - start))

    await async_save_pg(update_logistic_costs_kiev)
    print('update_logistic_costs_kiev', int(time.time() - start))

    await async_save_pg(update_compensation_logistics)
    print('update_compensation_logistics', int(time.time() - start))

    finish = datetime.strftime(datetime.now(), "%Y.%m.%d %H:%M:%S")
    print("finish: ", finish)
    print('main_sale OK: ', time.time() - start)
    await create_views()


if __name__ == '__main__':
    asyncio.run(main_one_sale_logistics())
