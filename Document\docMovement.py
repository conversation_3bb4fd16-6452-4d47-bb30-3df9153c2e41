# док перемещение между складами
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

TABLE_NAME = "t_one_doc_movement"
DOCUMENT = "Document_ПеремещениеТоваров"

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

SELECT_COLUMNS = (
    "Организация_Key,СкладОтправитель_Key,СкладПолучатель_Key,Number,DataVersion,"
    "Posted,Date,ОтражатьВБухгалтерскомУчете,ОтражатьВУправленческомУчете,Комментарий,Ref_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NOT NULL,  -- DataVersion
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        description text NULL,  -- Комментарий
        is_accounting bool NOT NULL DEFAULT false,  -- ОтражатьВБухгалтерскомУчете
        is_management bool NOT NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        posted bool NOT NULL DEFAULT false,  -- Posted
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        organization_key varchar(50) NOT NULL,  -- Организация_Key
        shipping_warehouse_key varchar(50) NOT NULL,  -- СкладОтправитель_Key
        warehouse_consignee_key varchar(50) NOT NULL,  -- СкладПолучатель_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.shipping_warehouse_key IS 'СкладОтправитель_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_consignee_key IS 'СкладПолучатель_Key';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        organization_key,
        shipping_warehouse_key,
        warehouse_consignee_key,
        doc_number,
        dataversion,
        posted,
        doc_date,
        is_accounting,
        is_management,
        description,
        ref_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        organization_key = EXCLUDED.organization_key,
        shipping_warehouse_key = EXCLUDED.shipping_warehouse_key,
        warehouse_consignee_key = EXCLUDED.warehouse_consignee_key,
        doc_number = EXCLUDED.doc_number,
        dataversion = EXCLUDED.dataversion,
        posted = EXCLUDED.posted,
        doc_date = EXCLUDED.doc_date,
        is_accounting = EXCLUDED.is_accounting,
        is_management = EXCLUDED.is_management,
        description = EXCLUDED.description
    ;
    """
    return sql.replace("'", "")


async def main_doc_movement_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(11)
    sql = await sql_insert(maket)
    sql = sql.replace("$7", "to_timestamp($7, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    await async_sql_create_index(TABLE_NAME, "shipping_warehouse_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_consignee_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    # main_doc_movement()
    asyncio.run(main_doc_movement_async())
