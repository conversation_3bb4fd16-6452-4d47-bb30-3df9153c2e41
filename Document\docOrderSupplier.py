import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

TABLE_NAME = "t_one_doc_order_supplier"
DOCUMENT = "Document_ЗаказПоставщику"

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

SELECT_COLUMNS = (
    "Ref_Key,ВалютаДокумента_Key,ДоговорКонтрагента_Key,КонтактноеЛицоКонтрагента_Key,Контрагент_Key,"
    "Организация_Key,Ответственный_Key,Подразделение_Key,Склад_Key,Тип<PERSON>ен_Key,УдалитьКонтактноеЛицо_Key,"
    "УсловиеПродаж_Key,DataVersion,Date,DeletionMark,Number,Posted,ВидОперации,ДатаОплаты,"
    "ДатаПоступления,ДокументОснование,ИспользоватьПлановуюСебестоимость,ИтогПлановаяСебестоимость,"
    "Комментарий,КратностьВзаиморасчетов,КурсВзаиморасчетов,СтруктурнаяЕдиница,СуммаВключаетНДС,"
    "СуммаДокумента,УдалитьВремяНапоминания,УдалитьНапомнитьОСобытии,УчитыватьНДС,"
    "ДокументОснование_Type,СтруктурнаяЕдиница_Type"
)

SQL_CREATE_TABLE = f"""    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50),  -- DataVersion
        doc_date timestamp,  -- Date
        doc_number varchar(50),  -- Number
        deletionmark bool DEFAULT false,  -- DeletionMark
        posted bool DEFAULT false,  -- Posted
        operation_type varchar(50),  -- ВидОперации
        the_doc_date_of_payment timestamp,  -- ДатаОплаты
        receipt_doc_date timestamp,  -- ДатаПоступления
        a_document_base varchar(50),  -- ДокументОснование
        use_planned_cost bool DEFAULT false,  -- ИспользоватьПлановуюСебестоимость
        total_planned_cost numeric(10,4) DEFAULT 0,  -- ИтогПлановаяСебестоимость
        coment varchar(50),  -- Комментарий
        multiplicity_of_mutual_settlements numeric(10,4) DEFAULT 0,  -- КратностьВзаиморасчетов
        settlement_rate numeric(10,4) DEFAULT 0,  -- КурсВзаиморасчетов
        structural_unit varchar(50),  -- СтруктурнаяЕдиница
        amount_includes_vat bool DEFAULT false,  -- СуммаВключаетНДС
        document_amount numeric(10,4) DEFAULT 0,  -- СуммаДокумента
        delete_reminder_time timestamp,  -- УдалитьВремяНапоминания
        delete_remind_of_event bool DEFAULT false,  -- УдалитьНапомнитьОСобытии
        consider_vat bool DEFAULT false,  -- УчитыватьНДС
        document_base_type varchar(50),  -- ДокументОснование_Type
        structural_unit_type varchar(50),  -- СтруктурнаяЕдиница_Type
        ref_key varchar(50),  -- Ref_Key
        document_currency_key varchar(50),  -- ВалютаДокумента_Key
        counterparty_agreement_key varchar(50),  -- ДоговорКонтрагента_Key
        contact_person_of_counterparty_key varchar(50),  -- КонтактноеЛицоКонтрагента_Key
        account_key varchar(50),  -- Контрагент_Key
        organization_key varchar(50),  -- Организация_Key
        responsible_key varchar(50),  -- Ответственный_Key
        department_key varchar(50),  -- Подразделение_Key
        warehouse_key varchar(50),  -- Склад_Key
        price_type_key varchar(50),  -- ТипЦен_Key
        delete_contact_person_key varchar(50),  -- УдалитьКонтактноеЛицо_Key
        sales_condition_key varchar(50),  -- УсловиеПродаж_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.document_currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_agreement_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contact_person_of_counterparty_key IS 'КонтактноеЛицоКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.responsible_key IS 'Ответственный_Key';
    COMMENT ON COLUMN {TABLE_NAME}.department_key IS 'Подразделение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.price_type_key IS 'ТипЦен_Key';
    COMMENT ON COLUMN {TABLE_NAME}.delete_contact_person_key IS 'УдалитьКонтактноеЛицо_Key';
    COMMENT ON COLUMN {TABLE_NAME}.sales_condition_key IS 'УсловиеПродаж_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.the_doc_date_of_payment IS 'ДатаОплаты';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_doc_date IS 'ДатаПоступления';
    COMMENT ON COLUMN {TABLE_NAME}.a_document_base IS 'ДокументОснование';
    COMMENT ON COLUMN {TABLE_NAME}.use_planned_cost IS 'ИспользоватьПлановуюСебестоимость';
    COMMENT ON COLUMN {TABLE_NAME}.total_planned_cost IS 'ИтогПлановаяСебестоимость';
    COMMENT ON COLUMN {TABLE_NAME}.coment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements IS 'КратностьВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_rate IS 'КурсВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.structural_unit IS 'СтруктурнаяЕдиница';
    COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
    COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.delete_reminder_time IS 'УдалитьВремяНапоминания';
    COMMENT ON COLUMN {TABLE_NAME}.delete_remind_of_event IS 'УдалитьНапомнитьОСобытии';
    COMMENT ON COLUMN {TABLE_NAME}.consider_vat IS 'УчитыватьНДС';
    COMMENT ON COLUMN {TABLE_NAME}.document_base_type IS 'ДокументОснование_Type';
    COMMENT ON COLUMN {TABLE_NAME}.structural_unit_type IS 'СтруктурнаяЕдиница_Type';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        document_currency_key,
        counterparty_agreement_key,
        contact_person_of_counterparty_key,
        account_key,
        organization_key,
        responsible_key,
        department_key,
        warehouse_key,
        price_type_key,
        delete_contact_person_key,
        sales_condition_key,
        dataversion,
        doc_date,
        deletionmark,
        doc_number,
        posted,
        operation_type,
        the_doc_date_of_payment,
        receipt_doc_date,
        a_document_base,
        document_base_type,
        use_planned_cost,
        total_planned_cost,
        coment,
        multiplicity_of_mutual_settlements,
        settlement_rate,
        structural_unit,
        structural_unit_type,
        amount_includes_vat,
        document_amount,
        delete_reminder_time,
        delete_remind_of_event,
        consider_vat
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        document_currency_key = EXCLUDED.document_currency_key,
        counterparty_agreement_key = EXCLUDED.counterparty_agreement_key,
        contact_person_of_counterparty_key = EXCLUDED.contact_person_of_counterparty_key,
        account_key = EXCLUDED.account_key,
        organization_key = EXCLUDED.organization_key,
        responsible_key = EXCLUDED.responsible_key,
        department_key = EXCLUDED.department_key,
        warehouse_key = EXCLUDED.warehouse_key,
        price_type_key = EXCLUDED.price_type_key,
        delete_contact_person_key = EXCLUDED.delete_contact_person_key,
        sales_condition_key = EXCLUDED.sales_condition_key,
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        deletionmark = EXCLUDED.deletionmark,
        doc_number = EXCLUDED.doc_number,
        posted = EXCLUDED.posted,
        operation_type = EXCLUDED.operation_type,
        the_doc_date_of_payment = EXCLUDED.the_doc_date_of_payment,
        receipt_doc_date = EXCLUDED.receipt_doc_date,
        a_document_base = EXCLUDED.a_document_base,
        document_base_type = EXCLUDED.document_base_type,
        use_planned_cost = EXCLUDED.use_planned_cost,
        total_planned_cost = EXCLUDED.total_planned_cost,
        coment = EXCLUDED.coment,
        multiplicity_of_mutual_settlements = EXCLUDED.multiplicity_of_mutual_settlements,
        settlement_rate = EXCLUDED.settlement_rate,
        structural_unit = EXCLUDED.structural_unit,
        structural_unit_type = EXCLUDED.structural_unit_type,
        amount_includes_vat = EXCLUDED.amount_includes_vat,
        document_amount = EXCLUDED.document_amount,
        delete_reminder_time = EXCLUDED.delete_reminder_time,
        delete_remind_of_event = EXCLUDED.delete_remind_of_event,
        consider_vat = EXCLUDED.consider_vat
    ;
    """
    return sql.replace("'", "")


async def main_doc_order_to_supplier_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, False, True)
    maket = await create_model_async(34)
    sql = await sql_insert(maket)
    sql = sql.replace("$14", "to_timestamp($14, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$19", "to_timestamp($19, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$20", "to_timestamp($20, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$32", "to_timestamp($32, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    # main_doc_order_to_supplier()
    asyncio.run(main_doc_order_to_supplier_async())
