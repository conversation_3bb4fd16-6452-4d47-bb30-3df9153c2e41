-- v_one_sales_stock_days source

CREATE MATERIALIZED VIEW v_one_sales_stock_days
TABLESPACE pg_default
AS SELECT DISTINCT stock_last.supplier,
    stock_last.sku,
    COALESCE(stock_first.balance_first, 0::numeric) AS "начОст",
    COALESCE(stock_first.entry_amount_first, 0::numeric) AS "начОстВх",
    COALESCE(stock_first.amount_maliyet_first, 0::numeric) AS "начОстСсть",
    COALESCE(receipt.tobox_receipt, 0::numeric) AS "прихКрб",
    COALESCE(receipt.entry_amount_receipt, 0::numeric) AS "прихВх",
    COALESCE(receipt.amount_maliyet_receipt, 0::numeric) AS "прихСсть",
    COALESCE(sale.tobox_sale, 0::numeric) AS "продКрб",
    COALESCE(sale.entry_amount_sale, 0::numeric) AS "продВх",
    COALESCE(sale.amount_maliyet_sale, 0::numeric) AS "продСсть",
    COALESCE(stock_last.balance_last, 0::numeric) AS "конОст",
    COALESCE(stock_last.entry_amount_last, 0::numeric) AS "конВх",
    COALESCE(stock_last.amount_maliyet_last, 0::numeric) AS "конСсть",
    COALESCE(count_days.total_days, 0::numeric) AS "дней"
   FROM ( SELECT DISTINCT s.supplier,
            s.sku,
            s.balance_last,
            s.entry_amount_last,
            s.amount_maliyet_last,
            s.max_date,
            s.ismax
           FROM ( SELECT t_one_stok.supplier,
                    t_one_stok.sku,
                    sum(t_one_stok.tobox) OVER (PARTITION BY t_one_stok.sku) AS balance_last,
                    sum(t_one_stok.tobox) OVER (PARTITION BY t_one_stok.sku) * t_one_stok.entry_price AS entry_amount_last,
                    sum(t_one_stok.tobox) OVER (PARTITION BY t_one_stok.sku) * t_one_stok.maliyet AS amount_maliyet_last,
                    max(t_one_stok.doc_date) OVER (PARTITION BY t_one_stok.sku) AS max_date,
                    t_one_stok.doc_date = max(t_one_stok.doc_date) OVER (PARTITION BY t_one_stok.sku) AS ismax
                   FROM t_one_stok
                  WHERE t_one_stok.doc_date::date <= '2023-11-03'::date AND (t_one_stok.warehouse_key::text = ANY (ARRAY['4b40b865-6d2f-11ec-8125-001dd8b72b55'::character varying, '381c5d92-4acb-11ed-8148-001dd8b72b55'::character varying]::text[]))) s
          WHERE s.ismax = true) stock_last
     LEFT JOIN ( SELECT DISTINCT s.supplier,
            s.sku,
            s.balance_first,
            s.entry_amount_first,
            s.amount_maliyet_first,
            s.max_date,
            s.ismax
           FROM ( SELECT t_one_stok.supplier,
                    t_one_stok.sku,
                    sum(t_one_stok.tobox) OVER (PARTITION BY t_one_stok.sku) AS balance_first,
                    sum(t_one_stok.tobox) OVER (PARTITION BY t_one_stok.sku) * t_one_stok.entry_price AS entry_amount_first,
                    sum(t_one_stok.tobox) OVER (PARTITION BY t_one_stok.sku) * t_one_stok.maliyet AS amount_maliyet_first,
                    max(t_one_stok.doc_date) OVER (PARTITION BY t_one_stok.sku) AS max_date,
                    t_one_stok.doc_date = max(t_one_stok.doc_date) OVER (PARTITION BY t_one_stok.sku) AS ismax
                   FROM t_one_stok
                  WHERE t_one_stok.doc_date < '2023-09-01'::date AND (t_one_stok.warehouse_key::text = ANY (ARRAY['4b40b865-6d2f-11ec-8125-001dd8b72b55'::character varying, '381c5d92-4acb-11ed-8148-001dd8b72b55'::character varying]::text[]))) s
          WHERE s.ismax = true) stock_first ON stock_last.sku::text = stock_first.sku::text
     LEFT JOIN ( SELECT DISTINCT t_one_stok.sku,
            - sum(t_one_stok.tobox) AS tobox_sale,
            - sum(t_one_stok.entry_amount) AS entry_amount_sale,
            - sum(t_one_stok.amount_maliyet) AS amount_maliyet_sale
           FROM t_one_stok
          WHERE t_one_stok.organization_key::text <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'::text AND (t_one_stok.doc_type::text = ANY (ARRAY['продажа'::character varying, 'продажа возврат'::character varying]::text[])) AND t_one_stok.doc_date::date >= '2023-09-01'::date AND t_one_stok.doc_date::date <= '2023-11-03'::date AND (t_one_stok.warehouse_key::text = ANY (ARRAY['4b40b865-6d2f-11ec-8125-001dd8b72b55'::character varying, '381c5d92-4acb-11ed-8148-001dd8b72b55'::character varying]::text[]))
          GROUP BY t_one_stok.sku) sale ON stock_last.sku::text = sale.sku::text
     LEFT JOIN ( SELECT DISTINCT t_one_stok.sku,
            sum(t_one_stok.tobox) AS tobox_receipt,
            sum(t_one_stok.entry_amount) AS entry_amount_receipt,
            sum(t_one_stok.amount_maliyet) AS amount_maliyet_receipt
           FROM t_one_stok
          WHERE t_one_stok.organization_key::text <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'::text AND (t_one_stok.doc_type::text = ANY (ARRAY['поступление'::character varying, 'поступление возврат'::character varying]::text[])) AND t_one_stok.doc_date::date >= '2023-09-01'::date AND t_one_stok.doc_date::date <= '2023-11-03'::date AND (t_one_stok.warehouse_key::text = ANY (ARRAY['4b40b865-6d2f-11ec-8125-001dd8b72b55'::character varying, '381c5d92-4acb-11ed-8148-001dd8b72b55'::character varying]::text[]))
          GROUP BY t_one_stok.sku) receipt ON stock_last.sku::text = receipt.sku::text
     LEFT JOIN ( SELECT DISTINCT subquery.supplier,
            subquery.sku,
            COALESCE(sum(subquery.days), 0::numeric) AS total_days
           FROM ( SELECT tos.supplier,
                    tos.sku,
                    tos.doc_date,
                    tos.doc_number,
                    COALESCE(lag(tos.stok) OVER (PARTITION BY tos.sku ORDER BY tos.doc_date, tos.doc_number), 0::numeric) AS old_stock,
                    COALESCE(tos.stok, 0::numeric) AS stok,
                    COALESCE(
                        CASE
                            WHEN row_number() OVER (PARTITION BY tos.sku ORDER BY tos.doc_date, tos.doc_number) <> count(*) OVER (PARTITION BY tos.sku) AND tos.doc_date::date = max(tos.doc_date::date) OVER (PARTITION BY tos.sku) THEN 0::numeric
                            WHEN row_number() OVER (PARTITION BY tos.sku ORDER BY (tos.doc_date::date), tos.doc_number) = count(*) OVER (PARTITION BY tos.sku) AND '2023-11-03'::date > max(tos.doc_date::date) OVER (PARTITION BY tos.sku) THEN EXTRACT(day FROM '2023-11-03'::date::timestamp without time zone - lag(tos.doc_date) OVER (PARTITION BY tos.sku ORDER BY (tos.doc_date::date)))
                            WHEN tos.doc_date = max(tos.doc_date) OVER (PARTITION BY tos.sku) AND COALESCE(lag(tos.stok) OVER (PARTITION BY tos.sku ORDER BY (tos.doc_date::date), tos.doc_number), 0::numeric) > 2::numeric THEN ('2023-11-03'::date - lag(tos.doc_date::date) OVER (PARTITION BY tos.sku ORDER BY (tos.doc_date::date), tos.doc_number))::numeric
                            WHEN COALESCE(lag(tos.stok) OVER (PARTITION BY tos.sku ORDER BY tos.doc_date, tos.doc_number), 0::numeric) <= 2::numeric THEN 0::numeric
                            ELSE (tos.doc_date::date - lag(tos.doc_date::date) OVER (PARTITION BY tos.sku ORDER BY tos.doc_date, tos.doc_number))::numeric
                        END, 0::numeric) AS days
                   FROM t_one_stok tos
                  WHERE tos.doc_date::date >= '2023-09-01'::date AND tos.doc_date::date <= '2023-11-03'::date AND (tos.warehouse_key::text = ANY (ARRAY['4b40b865-6d2f-11ec-8125-001dd8b72b55'::character varying, '381c5d92-4acb-11ed-8148-001dd8b72b55'::character varying]::text[]))) subquery
          GROUP BY subquery.supplier, subquery.sku) count_days ON stock_last.sku::text = count_days.sku::text
  WHERE (abs(COALESCE(stock_first.balance_first, 0::numeric)) + abs(COALESCE(receipt.tobox_receipt, 0::numeric)) + abs(COALESCE(sale.tobox_sale, 0::numeric)) + abs(COALESCE(stock_last.balance_last, 0::numeric))) > 0.005
  ORDER BY stock_last.sku, (COALESCE(count_days.total_days, 0::numeric))
WITH DATA;

-- Permissions

GRANT ALL ON TABLE v_one_sales_stock_days TO user_prestige;