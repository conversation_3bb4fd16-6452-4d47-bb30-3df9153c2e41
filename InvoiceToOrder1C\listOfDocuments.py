import os
import asyncio

from multiThread import get_json_from_url
from datetime import datetime, timedelta, date

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

date_start = date.today() - timedelta(days=180)
date_start = datetime.strftime(date_start, "%Y-%m-%dT00:00:01")
DOCUMENT = "Document_ПоступлениеТоваровУслуг"
SELECT_COLUMNS = "Date,Number,КурсВзаиморасчетов,СуммаДокумента,Ref_Key,Foreign_Key,Контрагент_Key,Сделка,Организация_Key"


# список документов поступления товаров и услуг
async def main_list_doc_async():
    url = f"http://192.168.1.254/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"/?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    url += "&$filter=Товары/any(d: d/fiyat ne 0)"
    response = await get_json_from_url(url)
    return response.get('value')


if __name__ == "__main__":
    asyncio.run(main_list_doc_async())
