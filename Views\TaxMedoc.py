# расхождение сумм между налоговой накладной и medoc
# и расхождение сумм между расчетом корректировка (налоговВозвратная накл) и medoc
import asyncio
import os
import sys

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))
from async_Postgres import  async_save_pg

from logger_prestige import get_logger
FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

VIEW_NAME_TAX_INVOICES = "v_one_tax_and_medoc"

SQL_CREATE_VIEW = f"""
    -- DROP VIEW IF EXISTS {VIEW_NAME_TAX_INVOICES} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME_TAX_INVOICES} AS
    SELECT DISTINCT 
        client.customer контрагент,
        medoc.doc_date AS МедокДата,
        medoc.doc_num AS МедокНомер,
--        nn.doc_date::date AS НН_дата,
--        nn.doc_number AS НН_номер,
--        app2.doc_date::date AS РК_дата,
--        app2.doc_number AS РК_номер,
        round(medoc.docsum::NUMERIC,2) AS МедокСумма,
        nn.document_amount AS НН_сумма,
        app2.document_amount AS РК_Сумма,
        CASE
            WHEN COALESCE(app2.doc_number,'') = '' THEN
                abs(COALESCE(medoc.docsum::numeric,0) - COALESCE(nn.document_amount,0))
            ELSE 
                abs(COALESCE(medoc.docsum::numeric,0) - COALESCE(app2.document_amount,0))
        END AS Отклонение,
        CASE
            WHEN (COALESCE(nn.doc_number,'') <> '' and COALESCE(nn.document_amount,0) < 0) THEN
                'Продажа. Сумма НН меньше 0'
            WHEN (COALESCE(app2.doc_number,'') <> '' and COALESCE(medoc.docsum,0) > 0) THEN
                'Возврат. Сумма в Медке больше 0'
            WHEN (COALESCE(nn.doc_number,'') <> '' and COALESCE(medoc.docsum,0) < 0) THEN
                'Продажа. Сумма в Медке меньше 0'
            WHEN (COALESCE(app2.doc_number,'') <> '' and COALESCE(app2.document_amount,0) > 0) THEN
                'Возврат. Сумма РК больше 0'
            ELSE 'расхождение сумм'
        END Замечание,
        medoc.docname МедокНаимДокумента,
        medoc.sendsttname Статус,
        medoc.sendstt КодСтатуса,
        org.organization организация,
        medoc.partner_edrpou ОКПО,
        medoc.firm_edrpou
    FROM v_medoc_reestr_and_docinfo AS medoc
        LEFT JOIN t_one_doc_tax_sale AS nn
            ON medoc.doc_num = RIGHT(nn.doc_number,8)::int::TEXT
                AND medoc.doc_date::date = nn.doc_date::date
                AND medoc.docname ILIKE '%Податкова накладна%'
        LEFT JOIN t_one_doc_tax_appendix_2 AS app2
            ON medoc.doc_num = RIGHT(app2.doc_number,8)::int::TEXT
                AND medoc.doc_date::date = app2.doc_date::date
                AND medoc.docname ILIKE '%Додаток%'
        INNER JOIN v_one_manager_counterparty_contracts_segments AS client
            ON (nn.account_key = client.customer_key
                OR app2.account_key = client.customer_key)
            AND 
                CASE 
                    WHEN length(trim(client.edrpou)) > 8 AND medoc.partner_edrpou IS NULL THEN NULL 
                    ELSE trim(client.edrpou)
                END = medoc.partner_edrpou
        INNER JOIN v_one_organization_and_type AS org
            ON (org.ref_key = nn.organization_key
                OR org.ref_key = app2.organization_key)
                AND org.inn_okpo = medoc.firm_edrpou
    WHERE (nn.posted OR app2.posted)
        AND org.ref_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
    --	AND medoc.sendstt NOT IN 
    --		(
    --		10,	-- Не прийнято
    --		11,	-- Помилка відправки
    --		16,	-- Відхилено
    --		53,	-- Заблоковано відправником
    --		144,  -- Заявлено до анулювання
    --		110 -- Реєстрація зупинена
    --		)
    ORDER BY client.customer, medoc.doc_date
    ;
    
    COMMENT ON VIEW {VIEW_NAME_TAX_INVOICES} IS 'расхождение между налогНакл/РасчКорректировки и Medoc';
    GRANT SELECT ON TABLE  {VIEW_NAME_TAX_INVOICES} TO user_prestige;
"""


async def main_tax_medoc_async():
    result = await async_save_pg(SQL_CREATE_VIEW)
    logger.info(f"{result}, SQL_CREATE_VIEW_TAX_SALE")

if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(main_tax_medoc_async())
    # asyncio.get_event_loop().run_until_complete(main_tax_medoc_async())
    logger.info(f"FINISH")
