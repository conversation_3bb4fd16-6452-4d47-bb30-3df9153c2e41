-- стыкует данные о еденицах измерения из 1С и платформы EDIN
-- для дальнейшей загрузки УКТЗЕД на платформу EDIN

DO $$
BEGIN
    -- Попытка обновить материализованное представление
    -- REFRESH MATERIALIZED VIEW v_one_edin;
    DROP VIEW v_one_edin;
EXCEPTION
    WHEN undefined_table THEN
        -- Если материализованное представление не существует, создаем его
        EXECUTE '
            -- CREATE MATERIALIZED VIEW v_one_edin AS
            CREATE VIEW v_one_edin AS
            SELECT DISTINCT
                stock.sku Назва,
                barcode Штрихкод,
                0 AS Ціна,
                5 AS "Ставка ПДВ",
                tmp.value "Од. виміру",
                tmp.code,
                stock.uktzt "Код УКТЗЕД",
                stock.sku "Артикул покупця",
                description,
                supplier
            FROM t_edin_units AS tmp
            INNER JOIN
                (SELECT *
                FROM t_one_cat_units
                WHERE NOT deletion_mark)
            AS cu
                ON	tmp.units_key= unit_by_classifier_key
            INNER JOIN t_one_infreg_barcodes AS brc
                ON brc.unit_of_key = ref_key
            INNER JOIN t_one_stock AS stock
                ON stock.nomenclature_key = brc.nomenclature_key
            WHERE coalesce(trim(barcode),'''') <> '''' AND coalesce(trim(uktzt),'''') <> ''''
            ORDER BY supplier, sku;

            GRANT SELECT ON TABLE v_one_edin TO user_prestige;
            ';
END
$$ LANGUAGE plpgsql;