    CREATE OR REPLACE FUNCTION fn_fetch_salary_data(start_date DATE, end_date DATE)
    RETURNS TABLE (manager TEXT, month TEXT, amount NUMERIC) AS $$
    DECLARE
        ref refcursor;
        row record;
    BEGIN
        -- Вызываем функцию
        SELECT * INTO ref FROM fn_salary_bonus_manager(start_date, end_date);

        -- Извлекаем строки из курсора
        LOOP
            FETCH NEXT FROM ref INTO row;
            EXIT WHEN NOT FOUND;
            -- Выводим строки
            manager := row.manager;
            month := row.month;
            amount := row.amount;
            RETURN NEXT;
        END LOOP;
        CLOSE ref;
    END;
    $$ LANGUAGE plpgsql;
