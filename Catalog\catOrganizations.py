import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_organizations"
DOCUMENT = "Catalog_Организации"
SELECT_COLUMNS = "Code,Ref_Key,DataVersion,Description,ОсновнойБанковскийСчет_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(50) NOT NULL,  -- Code
        dataversion varchar(50) NOT NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        description varchar(50) NOT NULL,  -- Description
        primary_bank_account_key varchar(50) NULL,  -- <PERSON>сновнойБанковскийСчет_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.primary_bank_account_key IS 'ОсновнойБанковскийСчет_Key';
    
    GRANT SELECT ON TABLE {TABLE_NAME} TO anna;    
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        code,
        ref_key,
        dataversion,
        description,
        primary_bank_account_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        code = EXCLUDED.code,
        dataversion = EXCLUDED.dataversion,
        description = EXCLUDED.description,
        primary_bank_account_key = EXCLUDED.primary_bank_account_key
    ;
    '''
    return sql.replace("'", "")


async def main_cat_organizations_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(5)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_organizations_async())
