import winreg
import os
import subprocess
import re


def get_postgres_services():
    """Возвращает список служб PostgreSQL из реестра Windows."""
    services = []
    key_path = r"SYSTEM\CurrentControlSet\Services"
    
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as hkey:
            index = 0
            while True:
                try:
                    service_name = winreg.EnumKey(hkey, index)
                    if "postgres" in service_name.lower():
                        services.append(service_name)
                    index += 1
                except OSError:
                    break
    except FileNotFoundError:
        pass
    return services


def get_service_exe_path(service_name):
    """Извлекает путь к исполняемому файлу службы из реестра."""
    key_path = fr"SYSTEM\CurrentControlSet\Services\{service_name}"
    try:
        with winreg.<PERSON><PERSON>ey(winreg.HKEY_LOCAL_MACHINE, key_path) as service_key:
            image_path, _ = winreg.QueryValueEx(service_key, "ImagePath")
            # Удаление кавычек и аргументов командной строки
            path = re.search(r'^"?(.*?\.exe)"?', image_path.strip()).group(1)
            return os.path.expandvars(path)  # Раскрываем переменные среды
    except Exception:
        return None


def get_postgres_version(exe_path):
    """Получает версию PostgreSQL, выполнив команду --version."""
    try:
        result = subprocess.run(
            [exe_path, "--version"],
            capture_output=True,
            text=True,
            check=True,
            encoding="utf-8"
        )
        version_match = re.search(r"(\d+\.\d+)", result.stdout)
        return version_match.group(1) if version_match else "Unknown"
    except Exception:
        return "Unknown"


if __name__ == "__main__":
    # Основной процесс
    services = get_postgres_services()
    if not services:
        print("Службы PostgreSQL не найдены")
        exit()

    print("Найдены службы PostgreSQL:")
    for service in services:
        exe_path = get_service_exe_path(service)
        if exe_path and os.path.exists(exe_path):
            version = get_postgres_version(exe_path)
            print({"Служба": service},
                  {"Путь": exe_path},
                  {"Версия": version})
        else:
            print(f"- Служба: {service} (не удалось определить путь к исполняемому файлу)")