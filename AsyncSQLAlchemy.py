import asyncio
import os
import sys
from prestige_authorize import CONFIG_PATH
sys.path.append(os.path.abspath(CONFIG_PATH))
from sqlalchemy import <PERSON>a<PERSON><PERSON>
from sqlalchemy import select
from sqlalchemy import Table
from sqlalchemy.ext.asyncio import create_async_engine
from configPrestige import username, psw, hostname, port, basename, URL_CONST, chatid_rasim, DATA_AUTH, schema

meta = MetaData()
# t1 = Table("a_test", meta, Column("name", String(50), primary_key=True))
t1 = Table("a_test",meta)
"postgresql+asyncpg://scott:tiger@localhost/test"
engine = create_async_engine('postgresql+asyncpg://%s:%s@%s:%s/%s' % (username, psw, hostname, port, basename), echo=True,)


async def async_main() -> None:

    async with engine.begin() as conn:
        await conn.run_sync(meta.create_all)

        await conn.execute(
            t1.insert(), [{"num": 1}, {"num": 123}]
        )

    async with engine.connect() as conn:
        # select a Result, which will be delivered with buffered
        # results
        result = await conn.execute(select(t1).where(t1.c.name == "some name 1"))

        print(result.fetchall())

    # for AsyncEngine created in function scope, close and
    # clean-up pooled connections
    await engine.dispose()


asyncio.run(async_main())
