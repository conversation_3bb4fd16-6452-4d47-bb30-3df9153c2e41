import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_doc_cash_order_receipt'
DOCUMENT = "Document_ПлатежноеПоручениеВходящее"
SELECT_COLUMNS = ("DataVersion,Date,ДатаВходящегоДокумента,Number,ДатаОплаты,Комментарий,НазначениеПлатежа,"
                  "СуммаДокумента,Posted,Оплачено,ОтражатьВУправленческомУчете,Отражать<PERSON>БухгалтерскомУчете,"
                  "ВидОперации,Ref_Key,ВалютаДокумента_Key,Контрагент_Key,ДоговорКонтрагента_Key,Организация_Key,"
                  "СтатьяДвиженияДенежныхСредств_Key,СтатьяЗатрат_Key,bankDockId")

SQL_CREATE_TABLE = f''' 
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        doc_date_receipt date NULL,  -- ДатаВходящегоДокумента
        date_payment date NULL,  -- ДатаОплаты
        a_comment varchar NULL,  -- Комментарий
        operation_type varchar(250) NOT NULL,  -- ВидОперации
        assignment varchar NULL,  -- НазначениеПлатежа
        amount numeric(20, 4) NOT NULL DEFAULT 0,  -- СуммаДокумента
        posted bool NOT NULL DEFAULT false,  -- Posted
        paid bool NOT NULL DEFAULT false,  -- Оплачено
        is_management bool NOT NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        is_accounting bool NOT NULL DEFAULT false,  -- ОтражатьВБухгалтерскомУчете
        dataversion varchar(15) NOT NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        currency_key varchar(50) NOT NULL,  -- ВалютаДокумента_Key
        account_key varchar(50) NOT NULL,  -- Контрагент_Key
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        cash_flow_item varchar(50) NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        cost_item_key  varchar(50) NULL,  -- СтатьяЗатрат_Key,
        bankDockId varchar(40) NULL,  -- bankDockId
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_receipt IS 'ДатаВходящегоДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.date_payment IS 'ДатаОплаты';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.assignment IS 'НазначениеПлатежа';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.paid IS 'Оплачено';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item_key IS 'СтатьяЗатрат_Key';
    COMMENT ON COLUMN {TABLE_NAME}.bankDockId IS 'bankDockId';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        dataversion,
        doc_date,
        doc_date_receipt,
        doc_number,
        date_payment,
        a_comment,
        assignment,
        amount,
        posted,
        paid,
        is_management,
        is_accounting,
        operation_type,
        ref_key,
        currency_key,
        account_key,
        contract_key,
        organization_key,
        cash_flow_item,
        cost_item_key,
        bankDockId
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        doc_date_receipt = EXCLUDED.doc_date_receipt,
        doc_number = EXCLUDED.doc_number,
        date_payment = EXCLUDED.date_payment,
        a_comment = EXCLUDED.a_comment,
        assignment = EXCLUDED.assignment,
        amount = EXCLUDED.amount,
        posted = EXCLUDED.posted,
        paid = EXCLUDED.paid,
        is_management = EXCLUDED.is_management,
        is_accounting = EXCLUDED.is_accounting,
        operation_type = EXCLUDED.operation_type,
        currency_key = EXCLUDED.currency_key,
        account_key = EXCLUDED.account_key,
        contract_key = EXCLUDED.contract_key,
        organization_key = EXCLUDED.organization_key,
        cash_flow_item = EXCLUDED.cash_flow_item,
        cost_item_key = EXCLUDED.cost_item_key,
        bankDockId = EXCLUDED.bankDockId
    '''
    return sql.replace("'", "")


async def main_doc_cash_order_receipt_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, True, True)
    maket = await create_model_async(21)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$5,", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, 'doc_date')
    await async_sql_create_index(TABLE_NAME, 'doc_number')
    await async_sql_create_index(TABLE_NAME, 'account_key')
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_order_receipt_async())
