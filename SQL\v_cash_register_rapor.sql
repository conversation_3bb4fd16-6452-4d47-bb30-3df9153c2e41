DROP VIEW IF EXISTS v_cash_register_rapor CASCADE;

CREATE OR REPLACE VIEW v_cash_register_rapor
AS SELECT
    t.doc_date::date AS docdate,
    t.doc_date::time AS doctime,
    t.doc_number,
    t.amount,
    sum(t.amount) OVER (PARTITION BY t.cur ORDER BY t.doc_date) AS total,
    t.item_name,
    t.item_name_tr,
    t.purpose_name,
    t.purpose_name_tr,
    t.description,
    t.org,
    t.cur,
    t.customer,
    t.employee,
    t.doc_name,
    t.recorder_type,
    to_char(t.item_period::timestamp with time zone, 'YYYY.MM'::text) AS item_period,
    t.customer_key
   FROM ( SELECT cash.doc_date,
            cash.doc_number,
            cash.amount,
            item.item_name,
            item.item_name_tr,
            purpose.purpose_name,
            purpose.purpose_name_tr,
            cash.description,
            org.description AS org,
            cur.description AS cur,
            cust.customer,
            cash.customer_key,
            empl.coworker_name AS employee,
            doc_types.doc_name,
            cash.recorder_type,
            cash.item_period
           FROM t_cash_register cash
             LEFT JOIN t_cash_payment_purpose purpose ON cash.payment_purpose_id = purpose.id
             LEFT JOIN t_cash_item item ON cash.item_id = item.id
             LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = cash.organization_key::text
             LEFT JOIN t_one_cat_currencies cur ON cur.ref_key::text = cash.currency_key::text
             LEFT JOIN v_one_customer_manager cust ON cust.ref_key::text = cash.customer_key::text
             LEFT JOIN t_coworker empl ON empl.ref_key::uuid = cash.employee_key::uuid
             LEFT JOIN t_cash_doc_types AS doc_types ON doc_types.id::uuid = purpose.id_doc_type::uuid
          WHERE cash.organization_key_sub IS NULL OR cash.currency_key_sub IS NULL
        UNION ALL
         SELECT cash.doc_date,
            cash.doc_number,
            cash.amount_sub,
            item.item_name,
            item.item_name_tr,
            purpose.purpose_name,
            purpose.purpose_name_tr,
            cash.description,
            org.description AS org,
            cur_sub.description AS cur_sub,
            cust.customer,
            cash.customer_key,
            empl.coworker_name AS employee,
            doc_types.doc_name,
            cash.recorder_type,
            cash.item_period
           FROM t_cash_register cash
             LEFT JOIN t_cash_payment_purpose purpose ON cash.payment_purpose_id = purpose.id
             LEFT JOIN t_cash_item item ON cash.item_id = item.id
             LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = cash.organization_key::text
             LEFT JOIN t_one_cat_currencies cur_sub ON cur_sub.ref_key::text = cash.currency_key_sub::text
             LEFT JOIN v_one_customer_manager cust ON cust.ref_key::text = cash.customer_key::text
             LEFT JOIN t_coworker empl ON empl.ref_key::text = cash.employee_key::text
             LEFT JOIN t_cash_doc_types AS doc_types ON doc_types.id::uuid = purpose.id_doc_type::uuid
          WHERE cash.currency_key_sub IS NOT NULL) t
  ORDER BY t.doc_date DESC;

GRANT SELECT ON TABLE v_cash_register_rapor TO anna;
