import asyncio
import os
import sys
from pathlib import Path
cur_path = Path(os.path.dirname(__file__))
sys.path.append(cur_path)
sys.path.append(cur_path.parent)
from cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
VIEW_NAME = "v_bank_fop"
DOCUMENT = "банк ФОПы"
SQL_CREATE_VIEW_BANK = f'''
    DROP VIEW IF EXISTS {VIEW_NAME};
    
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
    SELECT 
        t_pb.idklienta AS "клиент",
        t_pb.aut_my_acc AS "орг_счет",
        t_pb.aut_my_nam AS "орг",
        t_pb.aut_cntr_crf AS "клиент_окпо",
        t_pb.aut_cntr_nam AS "клиент",
        t_pb.date_time_dat_od_tim_p AS "дата",
        t_pb.num_doc AS "номер_док",
        t_pb.osnd AS "назначение",
        t_pb.ccy AS "валюта",
        t_pb.sum "сумма_грн",
        t_pb.total_sum AS "итого",
        t_pb.trantype AS "тип",
        CASE
            WHEN t_pb.trantype::text = 'C'::text THEN 1
            ELSE '-1'::integer
            END AS recorder_type,
        t_pb.unqkey
    FROM t_pb
    WHERE length(t_pb.idklienta::text) in ()
        AND t_pb.date_time_dat_od_tim_p::date >= '01.07.2024'::date
    ORDER BY t_pb.date_time_dat_od_tim_p;

    COMMENT ON VIEW {VIEW_NAME} IS '{DOCUMENT}';    
    GRANT ALL ON TABLE {VIEW_NAME} TO {COMPANY_CASHIER}; --{DOCUMENT}
    
'''


async def main_v_bank_async():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_VIEW_BANK)
    logger.info(f"{result}, SQL_CREATE_VIEW_BANK")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_v_bank_async())