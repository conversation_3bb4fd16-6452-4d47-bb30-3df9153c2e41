
    CREATE TABLE IF NOT EXISTS t_cash_register (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        doc_date timestamp NOT NULL,  -- ДатаДок
        doc_number int4 NOT NULL,  -- Номер
        amount numeric(15, 2) NOT NULL,  -- сумма
        amount_sub numeric(15, 2) DEFAULT 0 NOT NULL,  -- сумма при обмене куплено
        payment_purpose_id uuid NULL,  -- назначение платежа
        item_id uuid NULL,  -- статья
        organization_key varchar(36) NOT NULL,  -- организация
        organization_key_sub varchar(36) NULL,  -- организация2
        currency_key varchar(36) NOT NULL,  -- валюта
        currency_key_sub varchar(36) NULL,  -- валюта2
        customer_key varchar(36) NULL,  -- клиент
        employee_key uuid NULL,  -- сотрудник
        description varchar(255) NULL,  -- примечание
        document_type varchar(25) NOT NULL,  -- тип документа
        create_user varchar(30) DEFAULT CURRENT_USER NOT NULL,
        update_user varchar(30) NULL,
        item_period date NULL,  -- к какому периоду относится
        recorder_type numeric(3) DEFAULT '-1'::integer NULL,  -- 1- приход; '-1' - расход
        CONSTRAINT t_cash_register_amount_check CHECK ((amount <> (0)::numeric)),
        CONSTRAINT t_cash_register_check CHECK ((recorder_type = ANY (ARRAY[('-1'::integer)::numeric, (1)::numeric]))),
        CONSTRAINT t_cash_register_number_unique UNIQUE (doc_number),
        CONSTRAINT t_cash_register_pk PRIMARY KEY (id),
        CONSTRAINT t_cash_register_fk_currency FOREIGN KEY (currency_key) REFERENCES t_one_cat_currencies(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_currency_sub FOREIGN KEY (currency_key_sub) REFERENCES t_one_cat_currencies(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_customer FOREIGN KEY (customer_key) REFERENCES t_one_cat_counterparties(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_employee FOREIGN KEY (employee_key) REFERENCES t_one_cat_employees(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_item FOREIGN KEY (item_id) REFERENCES t_cash_item(id) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_organizations FOREIGN KEY (organization_key) REFERENCES t_one_cat_organizations(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_organizations_sub FOREIGN KEY (organization_key_sub) REFERENCES t_one_cat_organizations(ref_key) ON DELETE RESTRICT,
        CONSTRAINT t_cash_register_fk_payment_purpose_id FOREIGN KEY (payment_purpose_id) REFERENCES t_cash_payment_purpose(id) ON DELETE RESTRICT
    );
    
    COMMENT ON COLUMN t_cash_register.doc_date IS 'ДатаДок';
    COMMENT ON COLUMN t_cash_register.doc_number IS 'Номер';
    COMMENT ON COLUMN t_cash_register.amount IS 'сумма';
    COMMENT ON COLUMN t_cash_register.amount_sub IS 'сумма при обмене куплено';
    COMMENT ON COLUMN t_cash_register.payment_purpose_id IS 'назначение платежа';
    COMMENT ON COLUMN t_cash_register.item_id IS 'статья';
    COMMENT ON COLUMN t_cash_register.organization_key IS 'организация';
    COMMENT ON COLUMN t_cash_register.organization_key_sub IS 'организация2';
    COMMENT ON COLUMN t_cash_register.currency_key IS 'валюта';
    COMMENT ON COLUMN t_cash_register.currency_key_sub IS 'валюта2';
    COMMENT ON COLUMN t_cash_register.customer_key IS 'клиент';
    COMMENT ON COLUMN t_cash_register.employee_key IS 'сотрудник';
    COMMENT ON COLUMN t_cash_register.description IS 'примечание';
    COMMENT ON COLUMN t_cash_register.document_type IS 'тип документа';
    COMMENT ON COLUMN t_cash_register.item_period IS 'к какому периоду относится';
    COMMENT ON COLUMN t_cash_register.recorder_type IS '1- приход; ''-1'' - расход';


-- Permissions

ALTER TABLE t_cash_register OWNER TO postgres;
GRANT ALL ON TABLE t_cash_register TO postgres;
GRANT ALL ON TABLE t_cash_register TO anna;