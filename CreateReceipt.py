# из Excel создает док Поступление товаров и услуг
import asyncio
import os
from datetime import datetime
import pandas as pd
from dateutil.parser import parse

from CreateDocPrice import get_nomenclature_key
from async_request import get_data_1c, post_data_1c
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

# наименование колонки в Excel с наименованием номенклатуры
NOMENCLATURE_COLUMN = '1c'
PRICE_COLUMN = 'price'
URL_CONST = "http://***************/utp_prestige/odata/standard.odata/"


# шапка для создания документа Поступление товаров и услуг
async def create_receipt_header(receipt_info):
    date_doc = datetime.now().date().strftime('%Y-%m-%d')
    supplier_key = receipt_info.get('Контрагент_Key')[0]
    contract_key = receipt_info.get('ДоговорКонтрагента_Key')[0]
    currency_key = receipt_info.get('ВалютаДокумента_Key')[0]
    warehouse_key = receipt_info.get('СкладОрдер')[0]
    data = {
        "ВалютаДокумента_Key": f"{currency_key}",
        "ДоговорКонтрагента_Key": f"{contract_key}",
        "Контрагент_Key": f"{supplier_key}",
        "Организация_Key": "f51a0e76-4821-11ec-811d-001dd8b72b55",
        "СчетУчетаРасчетовСКонтрагентом_Key": "0063cacb-2dd9-4ff1-b155-2d2be17dfd04",
        "СчетУчетаРасчетовПоАвансам_Key": "0063cacb-2dd9-4ff1-b155-2d2be17dfd04",
        "СчетУчетаНДС_Key": "3ebc1b85-18e3-4448-8e29-1a3a9fbd7741",
        "СчетУчетаНДСПодтвержденный_Key": "6951be23-4da9-4e58-aed6-118d4cae8434",
        "Комментарий": "SLIM",
        "СкладОрдер": f"{warehouse_key}",
        "СкладОрдер_Type": "StandardODATA.Catalog_Склады",
        "Date": f"{date_doc}",  # T00:00:01
        "ОтражатьВУправленческомУчете": True,
        "КратностьВзаиморасчетов": "1",
        "УДАЛИТЬОтражатьВНалоговомУчете": False,
        "Posted": False,
        "КурсВзаиморасчетов": 41.2975,
        "DeletionMark": True,
        "РегистрироватьЦеныПоставщика": False,
        "СуммаДокумента": 0,
        "СуммаВключаетНДС": True,
        "УчитыватьНДС": True,
        "ВидОперации": "ПокупкаКомиссия",
        "ВидПоступления": "НаСклад",
        "ОтражатьВБухгалтерскомУчете": True
    }
    return data


# ref_key макс ед.изм
async def get_unit_key(nomenclature_key):
    url = (f"{URL_CONST}Catalog_ЕдиницыИзмерения"
           f"?$format=json"
           f"&$filter=Owner eq cast(guid'{nomenclature_key}', 'Catalog_Номенклатура')"
           f"&$select=Ref_Key,Коэффициент"
           f"&$orderby=Коэффициент desc"
           f"&$top=1")
    result = get_data_1c(url)
    if result and result.get('value'):
        unit_key = result.get('value')[0].get('Ref_Key')
        coefficient = result.get('value')[0].get('Коэффициент')
        return {"unit_key": unit_key, "coefficient": coefficient}
    return {}


# макет табличной части
async def create_receipt_table_layout(row):
    linenumber = row.get('LineNumber')
    nomenclature_key = row.get('nomenclature_key')
    unit_coef = await get_unit_key(nomenclature_key)
    unit_key = unit_coef.get('unit_key')
    coefficient = unit_coef.get('coefficient')
    quantity = row.get('quantity')
    fiyat = row.get(PRICE_COLUMN)
    series_key = row.get('series_key')
    data = {
        "LineNumber": linenumber,
        "Номенклатура_Key": f"{nomenclature_key}",
        "КоличествоМест": "1",
        "ЕдиницаИзмерения_Key": f"{unit_key}",
        "Коэффициент": coefficient,
        "Количество": quantity,
        "Цена": 0,
        "Сумма": 0,
        "СтавкаНДС": "БезНДС",
        "СуммаНДС": 0,
        "СерияНоменклатуры_Key": f"{series_key}",
        "Склад_Key": "4b40b865-6d2f-11ec-8125-001dd8b72b55",
        "СчетУчетаБУ_Key": "08421d76-5023-4a27-b2c8-ed89b0dc904e",
        "НалоговоеНазначение_Key": "0b176d4b-a1b3-48ec-abc4-7e927658eb50",
        "ЕдиницаИзмеренияМест_Key": f"{unit_key}",
        "КоличествоМест": quantity/coefficient,
        # "НоменклатурнаяПозиция_Key": "00000000-0000-0000-0000-000000000000",
        "fiyat": fiyat
    }
    return data


# создаем табличную часть для документа Поступление товаров и услуг
async def create_receipt_table(df):
    data = []
    df['LineNumber'] = df.index + 1
    for index, row in df.iterrows():
        row_data = await create_receipt_table_layout(row)
        data.append(row_data)
    return data


# загружаем данные из Excel в DataFrame
async def load_data(file_path):
    df = pd.read_excel(file_path)

    # оставляем только те строки, где есть номенклатура
    df = df[df[NOMENCLATURE_COLUMN].notnull()].reset_index(drop=True)
    df = df.rename(columns=str.lower)
    return df


# к df добавляем колонку series_key (Ref_Key срока хранения)
async def get_series_key(df):
    # оставляем только те строки, где есть номенклатура
    df = df[df['срок'].notnull()].reset_index(drop=True)
    for index, row in df.iterrows():
        sku = row.get(NOMENCLATURE_COLUMN)
        nomenclature_key = row.get('nomenclature_key')
        date_expiration = row.get('срок')
        if not date_expiration:
            continue
        if isinstance(date_expiration, str):
            date_expiration = parse(row.get('срок')).date().strftime('%Y-%m-%d')
        else:
            date_expiration = date_expiration.strftime('%Y-%m-%d')
        logger.info(f"добавление срока годности {index + 1} из {len(df)}: {sku} {date_expiration}")
        url = (f"{URL_CONST}Catalog_СерииНоменклатуры?$format=json"
               f"&$filter=Owner_Key eq guid'{nomenclature_key}' "
               f"and year(СрокГодности) eq year(datetime'{date_expiration}T00:00:00') "
               f"and month(СрокГодности) eq month(datetime'{date_expiration}T00:00:00')"
               f"and day(СрокГодности) eq day(datetime'{date_expiration}T00:00:00')"
               f"&$orderby=СрокГодности desc"
               f"&$top=1")
        result = get_data_1c(url)
        if result and result.get('value'):
            series_key = result.get('value')[0].get('Ref_Key')
            df.at[index, 'series_key'] = series_key
        else:
            logger.error(f"срок годности не найден: {sku} {date_expiration}")
            df.at[index, 'series_key'] = ''

    return df


# ищем среди документов Поступление товаров и услуг документ с таким же поставщиком
# на основании этого документа будем создавать шапку нового документа
async def find_receipt_with_supplier(supplier):
    url = (f"{URL_CONST}Document_ПоступлениеТоваровУслуг"
           "?$format=json"
           "&$orderby=Date%20desc"
           "&$top=1&"
           "&$select=**"
           "&$filter=Posted eq true and "
           f"Контрагент/Description eq '{supplier}'")
    result = get_data_1c(url)
    if result and result.get('value'):
        return pd.DataFrame(result.get('value'))
    return {}


# загружаем данные в 1С
async def load_data_1c(data):
    url = f"{URL_CONST}Document_ПоступлениеТоваровУслуг?$format=json"
    result = post_data_1c(url, data)
    return result


# точка входа
async def main_create_receipt_async(file_path, supplier):
    # загружаем данные из Excel
    df = await load_data(file_path)

    # если нет колонки nomenclature_key, то добавляем ее
    if 'nomenclature_key' not in df.columns:
        df = await get_nomenclature_key(df)

    # добавляем в df колонку series_key
    df = await get_series_key(df)

    # ищем среди документов Поступление товаров и услуг документ с таким же поставщиком
    receipt_info = await find_receipt_with_supplier(supplier)
    header = await create_receipt_header(receipt_info)  # шапка для создания документа Поступление товаров и услуг
    table = await create_receipt_table(df)  # табличная часть для создания документа Поступление товаров и услуг
    header['Товары'] = table

    # загружаем данные в 1С
    result = await load_data_1c(header)
    print(result)


if __name__ == '__main__':
    file_path = r'C:\Users\<USER>\Desktop\SlimMaket.xlsx'
    supplier = "SLIM DIS TICARET LTD"
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main_create_receipt_async(file_path, supplier))
