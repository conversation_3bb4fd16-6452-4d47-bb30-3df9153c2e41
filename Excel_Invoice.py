# Description: Сохраняет документ РеализацияТоваровУслуг в Excel
# отправляет Excel файл на почту/телеграмм соответствующему менеджеру

import os
import re

import requests
from dateutil.parser import parse
from openpyxl import Workbook
from openpyxl.styles import Alignment
from openpyxl.styles import Font
from num2words import num2words
from async_Postgres import URL_CONST, C_LOGIN, C_PSW

current_dir = os.path.dirname(os.path.abspath(__file__))

# Создаем новый Excel файл
wb = Workbook()
ws = wb.active
ws.title = "Invoice"


# устанавливаем ширину столбцов
def cells_format():
    ws.column_dimensions['A'].width = 5
    ws.column_dimensions['B'].width = 14
    ws.column_dimensions['C'].width = 50
    ws.column_dimensions['D'].width = 9
    ws.column_dimensions['E'].width = 10
    ws.column_dimensions['F'].width = 11
    ws.column_dimensions['G'].width = 6
    ws.column_dimensions['H'].width = 7
    ws.column_dimensions['I'].width = 7
    ws.column_dimensions['J'].width = 13

    # Assuming ws is your worksheet
    ws['A1'].font = Font(bold=True, size=13)
    ws.merge_cells('A1:J1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

    ws.merge_cells('C3:J3')
    ws['C3'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[3].height = 34
    ws['C3'].alignment = Alignment(wrap_text=True)

    for row in ws.iter_rows(min_row=9, max_row=9, min_col=1, max_col=10):
        for cell in row:
            cell.font = Font(bold=True)


# загружаем шапку документа
def get_invoices_head(document_key):
    url = (
        f"{URL_CONST}Document_РеализацияТоваровУслуг"
        "?$format=json"
        "&$orderby=Date desc"
        f"&$filter=Ref_Key eq guid'{document_key}'"
        "&$expand=*"
    )
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    return response.json()


# загружаем таблицу документа
def get_invoices_table(document_key):
    url = (
        f"{URL_CONST}Document_РеализацияТоваровУслуг_Товары/"
        "?$format=json"
        "&$expand=*"
        f"&$filter=Ref_Key eq guid'{document_key}'"
    )
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    return response.json()


def get_supplier_requesites(organization_key):
    url = (f"{URL_CONST}InformationRegister_КодыОрганизации"
           f"?$format=json"
           f"&$filter=Организация_Key eq guid'{organization_key}'")
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    if response.status_code == 200:
        return response.json()
    return None


# загружаем информацию о поставщике
def get_supplier_info(organization_key):
    url = (f"{URL_CONST}Catalog_Организации"
           f"?$format=json"
           f"&$filter=Ref_Key eq guid'{organization_key}'"
           f"&$expand=*")
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    if response.status_code == 200:
        return response.json()
    return None


# получаем юр.адрес и телефон поставщика
def get_supplier_address(organization_key):
    url = (f"{URL_CONST}InformationRegister_КонтактнаяИнформация"
           f"?$format=json"
           f"&$filter=Объект eq cast(guid'{organization_key}','Catalog_Организации') "
           f"and (Вид eq cast(guid'ecafb10a-3da0-4433-802b-acac84dcd704', 'Catalog_ВидыКонтактнойИнформации') "
           f"or Вид eq cast(guid'b6a5d2ff-32b8-4425-bd40-789bc63c7b4f', 'Catalog_ВидыКонтактнойИнформации'))")
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    if response.status_code == 200:
        address = response.json()["value"][0].get("Представление")
        phone = response.json()["value"][1].get("Представление")
        return f"{address}, тел.: {phone}"
    return ''


def create_supplier_info(header):
    organization_key = header.get("Организация").get("Ref_Key")
    supplier_info = get_supplier_info(organization_key)
    if not supplier_info:
        return
    supplier_info = supplier_info.get("value")[0]
    supplier_name = supplier_info.get("Description")
    bank_name = supplier_info.get("ОсновнойБанковскийСчет").get("Description")
    account = supplier_info.get("ОсновнойБанковскийСчет").get("НомерСчета")
    address_phone = get_supplier_address(organization_key)
    okpo_inn = get_supplier_requesites(organization_key)
    okpo_inn = okpo_inn.get("value")[0]
    OKPO = okpo_inn.get("КодПоЕДРПОУ")
    INN = okpo_inn.get("ИНН")
    dop_info = f"п/р {account}, банк: {bank_name}; {address_phone}; ЄДРПОУ: {OKPO}, ІПН: {INN}"
    ws.append(["Постачальник:", "", supplier_name])
    ws.append(["", "", dop_info])
    ws.append([])  # Пустая строка для разделения


# выгружаем в Excel шапку
def create_head(header):
    # Добавляем шапку накладной
    document_number = header.get("Number")
    document_number = int(re.sub(r'\D', '', document_number))
    document_date = parse(header.get("Date")).strftime("%d.%m.%Y")
    row1 = f"Видаткова накладна № {document_number} від {document_date} р."
    contract_date = parse(header.get("ДоговорКонтрагента").get("Дата")).strftime("%d.%m.%Y")
    ws.append([row1])
    create_supplier_info(header)
    ws.append(["Покупець:", "", header.get("Контрагент").get("Description")])
    ws.append(["Договір:", "", f'{header.get("ДоговорКонтрагента").get("Description").strip()} от {contract_date}'])
    ws.append(["Адреса доставки:", "", header.get("АдресДоставки")])
    ws.append([])  # Пустая строка для разделения


# выгружаем в Excel таблицу
def create_table(data):
    # Добавляем таблицу товаров
    ws.append(
        [
            "№",
            "Штрихкод",
            "Номенклатура",
            "Од.изм.",
            "Серія",
            "Кількість",
            "Ціна",
            "Сума",
            "ПДВ",
            "Сума з ПДВ",
        ]
    )

    for item in data:
        ws.append(
            [
                item.get("LineNumber"),
                item.get(""),
                item.get("Номенклатура").get("Description"),
                item.get("ЕдиницаИзмерения").get("Description"),
                parse(item.get("СерияНоменклатуры").get("СрокГодности")).strftime("%d.%m.%Y"),
                item.get("Количество"),
                item.get("Цена"),
                item.get("Сумма"),
                item.get("СуммаНДС"),
                item.get("Сумма") + item.get("СуммаНДС")
            ]
        )


# bottom. подвал документа
def create_bottom(head, quantity_row):
    currency = head.get("ВалютаДокумента").get("Description")
    doc_sum_with_vat = head.get("СуммаДокумента")
    is_vat = head.get("УчитыватьНДС")
    amount_inc_vat = head.get("СуммаВключаетНДС")
    if is_vat and amount_inc_vat:
        sum_vat = doc_sum_with_vat / 5
    elif is_vat and not amount_inc_vat:
        sum_vat = doc_sum_with_vat / 6
    else:
        sum_vat = 0

    doc_sum_without_vat = doc_sum_with_vat - sum_vat

    ws.append(["", "", "", "", "", "", "", "", "Разом:", doc_sum_without_vat])
    quantity_row += 1
    ws[f'I{quantity_row}'].alignment = Alignment(horizontal='right')
    ws[f'I{quantity_row}'].font = Font(bold=True)
    ws[f'J{quantity_row}'].font = Font(bold=True)
    ws.append(["", "", "", "", "", "", "", "", "Сума ПДВ:", sum_vat])
    quantity_row += 1
    ws[f'I{quantity_row}'].alignment = Alignment(horizontal='right')
    ws[f'I{quantity_row}'].font = Font(bold=True)
    ws[f'J{quantity_row}'].font = Font(bold=True)
    ws.append(["", "", "", "", "", "", "", "", "Всього із ПДВ:", doc_sum_with_vat])
    quantity_row += 1
    ws[f'I{quantity_row}'].alignment = Alignment(horizontal='right')
    ws[f'I{quantity_row}'].font = Font(bold=True)
    ws[f'J{quantity_row}'].font = Font(bold=True)
    ws.append([f'Всього найменувань: {quantity_row - 9} на суму {doc_sum_with_vat} {currency}'])

    amount_to_words = num2words(doc_sum_with_vat,lang='uk', to='currency', currency='UAH')
    vat_to_words = num2words(sum_vat,lang='uk', to='currency', currency='UAH')
    # Append the row first
    ws.append([f"{amount_to_words}\nУ т.ч. ПДВ: {vat_to_words}"])
    ws.merge_cells(f'A{ws.max_row}:J{ws.max_row}')
    # Access the cell directly and set the alignment
    cell = ws[f'A{ws.max_row}']
    cell.alignment = Alignment(wrap_text=True)
    ws.row_dimensions[ws.max_row].height = 34
    # делаем жирным
    ws[f'A{quantity_row+2}'].font = Font(bold=True)


# точка входа
def create_excel(document_key):
    # загружаем шапку документа
    head = get_invoices_head(f"{document_key}")
    if not head:
        return
    head = head.get("value")[0]
    # загружаем таблицу документа
    table = get_invoices_table(f"{document_key}")
    if not table:
        return
    table = table.get("value")

    # выгружаем в Excel шапку и табличную часть
    create_head(head)
    create_table(table)

    # устанавливаем ширину столбцов
    cells_format()

    # формируем подвал документа. сумма документа...
    quantity_row = len(table) + 9
    create_bottom(head, quantity_row)

    # формируем имя файла
    document_number = head.get("Number")
    document_number = int(re.sub(r'\D', '', document_number))
    document_date = parse(head.get("Date")).strftime("%Y%m%d")
    file_name = f"{document_date}_{document_number}.xlsx"
    save_as = os.path.join(current_dir, file_name)

    # Сохраняем в Excel
    wb.save(save_as)


if __name__ == "__main__":
    create_excel("0aeeed28-a1cd-11ef-81cb-001dd8b740bc")
