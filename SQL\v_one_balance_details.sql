DROP VIEW IF EXISTS v_one_balance_details CASCADE;
CREATE OR REPLACE VIEW v_one_balance_details AS
SELECT
    *,
    sum(doc_sum)
        FILTER (WHERE last_date <= current_date)
        OVER (PARTITION BY
            COALESCE(trim(segment), ''),
            COALESCE(trim(manager), ''),
            COALESCE(trim(customer), ''),
            COALESCE(trim(organization), ''),
            COALESCE(trim(currency_name), '')
            ORDER BY last_date, t1.doc_number, id
            )
    AS customer_pdz,
    sum(doc_sum)
        OVER (PARTITION BY
            COALESCE(trim(segment), ''),
            COALESCE(trim(manager), ''),
            COALESCE(trim(customer), ''),
            COALESCE(trim(organization), ''),
            COALESCE(trim(currency_name), '')
            ORDER BY last_date, t1.doc_number, id
            )
    AS customer_sum
FROM (
    SELECT
        id,
        COALESCE(trim(segment_folder),'') segment_folder,
        COALESCE(trim(segment),'') segment,
        COALESCE(trim(manager),'') manager,
        COALESCE(trim(customer),'') customer,
        edrpou,
        seg.contract_days,
        COALESCE(trim(recordtype),'') recordtype,
        dtperiod::date doc_date,
        CASE
            WHEN amount > 1 AND recorder_type IN ('РеализацияТоваровУслуг','StandardODATA.Document_РеализацияТоваровУслуг') THEN
                (dtperiod::date + INTERVAL '1 DAYS' * seg.contract_days)::date
            ELSE
                dtperiod::date
        END
        AS last_date,
        req.doc_number,
        COALESCE(CASE
            WHEN recordtype = 'Receipt' THEN
                amount
            ELSE
                -amount
        END,0) doc_sum,
        COALESCE(CASE
            WHEN recordtype = 'Receipt' THEN
                amount_uah
            ELSE
                -amount_uah
        END,0) doc_sum_uah,
        req.is_accounting,
        REPLACE(recorder_type, 'StandardODATA.Document_', '') recorder_type,
        COALESCE(TRIM(organization), '') organization,
        seg.currency_name,
        seg.contract_type,
        seg.contract_key,
        seg.customer_key,
        req.recorder,
        seg.currency_key,
        seg.manager_key
    FROM t_one_doc_acc_reg_reciprocal_settlements_details AS req
        LEFT JOIN v_one_manager_counterparty_contracts_segments AS seg
            ON req.contract_key = seg.contract_key
        LEFT JOIN v_one_organization_and_type as org
            ON org.ref_key = req.organization_key
    WHERE recorder NOT IN
        (
            SELECT ref_key
            FROM t_one_doc_debt_correction
            WHERE (counterparty_debtor_key = counterparty_creditor_key
                AND operation_type = 'ПроведениеВзаимозачета')
        )
) AS t1
ORDER BY
    manager NULLS LAST,
    segment NULLS LAST,
    customer NULLS LAST,
    organization NULLS LAST,
    currency_name NULLS LAST,
    last_date,
    t1.doc_number,
    id
;


GRANT SELECT ON TABLE v_one_balance_details TO user_prestige;
