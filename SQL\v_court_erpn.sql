DROP VIEW IF EXISTS v_court_erpn CASCADE;
CREATE OR REPLACE VIEW v_court_erpn
AS SELECT
    erpn.hnamebuy AS "Покупатель",
    erpn.code AS "код",
    court.court_name AS "суд",
    erpn.prdinn AS "ИНН_Прод",
    erpn.pkpinn AS "ИНН_Покуп",
    erpn.crtdate AS "дата_НН_РК",
    erpn.nmr AS "ном_НН_РК",
    erpn.r4100g11 AS "сумма",
--    sum(erpn.r4100g11)
--        FILTER (
--            WHERE erpn.hsmcstt IN
--                (
--                    1,  -- реєстрацiю зупинено
--                    2,  -- вiдмовлено за рiшенням Комiсiї
--                    13  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"'
--                )
--                AND erpn.ftype = 0  -- НН
--            )
--        OVER (PARTITION BY erpn.cptin ORDER BY erpn.crtdate)
--    AS "суммаБлокНН",
--    sum(erpn.ndssm)
--        FILTER (
--            WHERE erpn.hsmcstt IN
--                (
--                    1,  -- реєстрацiю зупинено
--                    2,  -- вiдмовлено за рiшенням Комiсiї
--                    13  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"'
--                )
--                AND erpn.ftype = 0  -- НН
--            )
--        OVER (PARTITION BY erpn.cptin ORDER BY erpn.crtdate)
--    AS "суммаБлокНДС",
--    sum(erpn.ndssm * 6)
--        FILTER (
--            WHERE erpn.hsmcstt IN
--                (
--                    1,  -- реєстрацiю зупинено
--                    2,  -- вiдмовлено за рiшенням Комiсiї
--                    13  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"'
--                )
--                AND erpn.ftype = 1  -- РК
--            )
--        OVER (PARTITION BY erpn.cptin ORDER BY erpn.crtdate)
--    AS "суммаБлокРК",
--    sum(erpn.ndssm)
--        FILTER (
--            WHERE erpn.hsmcstt IN
--                (
--                    1,  -- реєстрацiю зупинено
--                    2,  -- вiдмовлено за рiшенням Комiсiї
--                    13  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"'
--                )
--                AND erpn.ftype = 1  -- РК
--            )
--        OVER (PARTITION BY erpn.cptin ORDER BY erpn.crtdate)
--    AS "суммаБлокРК/НДС",
    erpn.ndssm AS "НДС",
    erpn.tin AS "ОКПО_прод",
    erpn.cptin AS "ОКПО_Покуп",
    erpn.corrnmr AS "ном_НН_ориг",
    erpn.ftype AS "0-НН;1-РК;",
    erpn.impdate AS "дата_Рег",
    erpn.docrnn AS "номер_Рег",
    erpn.hsmcstt AS "типСтатуса",
    erpn.hsmcsttname AS "наименСтатуса",
    erpn.kvt2,
    erpn.kvt3,
    erpn.kvt4
   FROM t_tax_cabinet_erpn_api erpn
     LEFT JOIN t_court court ON court.doc_date::date = erpn.crtdate::date
        AND court.doc_number::bigint = erpn.nmr::bigint
  ORDER BY erpn.crtdate DESC, erpn.nmr;

COMMENT ON VIEW public.v_court_erpn IS 'НН/РК их статусы. Данные из сайта налоговой';

ALTER TABLE v_court_erpn OWNER TO postgres;
GRANT ALL ON TABLE v_court_erpn TO postgres;
GRANT SELECT ON TABLE v_court_erpn TO user_prestige;