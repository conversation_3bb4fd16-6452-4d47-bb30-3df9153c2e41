-- 1. Настройка базовых прав для пользователя user_prestige
GRANT CREATE ON SCHEMA public TO user_prestige;
GRANT USAGE ON SCHEMA public TO user_prestige;

-- 2. Назначение владельцев функций и прав на выполнение
ALTER FUNCTION public.fn_stock_series(date, date) OWNER TO postgres;
GRANT EXECUTE ON FUNCTION public.fn_stock_series(date, date) TO user_prestige;

ALTER FUNCTION public.fn_stock_days(date, date, integer) OWNER TO postgres;
GRANT EXECUTE ON FUNCTION public.fn_stock_days(date, date, integer) TO user_prestige;

-- 3. Предоставление прав на базовые таблицы
GRANT SELECT ON TABLE t_one_stock_new TO user_prestige;
GRANT SELECT ON TABLE t_one_cat_nomenclature TO user_prestige;
GRANT SELECT ON TABLE t_one_stock_short TO user_prestige;

-- 4. Предоставление прав на создание и изменение представлений
GRANT CREATE ON SCHEMA public TO user_prestige;
GRANT ALL ON ALL TABLES IN SCHEMA public TO user_prestige;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO user_prestige;

-- 5. Если существуют старые представления, передать их владение
DO $$
BEGIN
    -- Для fn_stock_series
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_series_first') THEN
        ALTER VIEW v_one_stock_series_first OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_series_last') THEN
        ALTER VIEW v_one_stock_series_last OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_series_sale') THEN
        ALTER VIEW v_one_stock_series_sale OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_series_receipt') THEN
        ALTER VIEW v_one_stock_series_receipt OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_series') THEN
        ALTER VIEW v_one_stock_series OWNER TO postgres;
    END IF;

    -- Для fn_stock_days
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_first') THEN
        ALTER VIEW v_stock_first OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_last') THEN
        ALTER VIEW v_stock_last OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_sale') THEN
        ALTER VIEW v_stock_sale OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_receipt') THEN
        ALTER VIEW v_stock_receipt OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_days_source') THEN
        ALTER VIEW v_stock_days_source OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_days') THEN
        ALTER VIEW v_stock_days OWNER TO postgres;
    END IF;

--    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_days') THEN
--        ALTER VIEW v_one_stock_days OWNER TO postgres;
--    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_one_stock_days_quantity') THEN
        ALTER VIEW v_one_stock_days_quantity OWNER TO postgres;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_name = 'v_stock_days_series') THEN
        ALTER VIEW v_stock_days_series OWNER TO postgres;
    END IF;
END $$;