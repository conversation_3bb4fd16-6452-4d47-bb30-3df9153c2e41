# https://github.com/ikvk/imap_tools#search-criteria
# pip install pyexcel pyexcel-xls pyexcel-xlsx
import sys
import os
import pandas as pd  # pip install pandas --upgrade
import asyncio

from prestige_authorize import CONFIG_PATH
sys.path.append(os.path.abspath(CONFIG_PATH))
from tkinter import *
from tkinter import ttk
from tkinter.messagebox import showinfo, OK, WARNING

from pathlib import Path
from datetime import datetime, timedelta, date
from imap_tools import MailBox, A  # pip install imap-tools
from configPrestige import PRESTIGE_EMAIL, GMAIL_PSW_TAS
from error_log import add_to_log

root = Tk()
root.title("выгрузка в excel")
root.geometry("300x200")


def selected(event):
    global ref_key, customer_key, combobox_df, head_df
    # получаем выделенный в раскрывающемся списке элемент
    selection = combobox.get().split(";")
    print(selection)


async def main_email_list():
    with MailBox('imap.gmail.com').login(PRESTIGE_EMAIL, GMAIL_PSW_TAS, 'INBOX') as mailbox:  # default INBOX
        df = pd.DataFrame(columns=['дата','тема','uid'])
        date_start = datetime.today().date() - timedelta(days=30)
        for msg in mailbox.fetch(A(date_gte=date_start)):
            for idx, attachment in enumerate(msg.attachments):
                try:
                    extended = Path(attachment.filename).suffix
                    if extended not in ['.xls', '.xlsx']: continue
                    mail_date = datetime.strftime(msg.date, "%Y.%m.%d %H:%M:%S")
                    data = {"дата": msg.date, "тема": msg.subject, "uid": msg.uid}
                    df.loc[len(df)]=data

                except Exception as e:
                    msg = "Error. EmailList %s" % e
                    print(msg)
                    await add_to_log(msg)

        df = df.sort_values('дата', ascending=False)
        df = df.drop_duplicates(subset=['uid']).reset_index(drop=True)
        df['дата']= pd.to_datetime(df['дата'].dt.date.astype(str))
        # df['дата'] = pd.to_datetime(df['дата'].astype(str))
        print(df)
        return df


def get_email_by_uid():
    print("OK")
    pass


if __name__ == '__main__':
    if __name__ == "__main__":
        emails_list = asyncio.run(main_email_list())
        emails_list = emails_list[['дата', 'тема']].values.tolist()
        if len(emails_list) != []:
            combobox = ttk.Combobox(values=emails_list, state="readonly")
            combobox.pack(anchor=NW, fill=X, padx=5, pady=5)
            combobox.bind("<<ComboboxSelected>>", selected)
            btn = ttk.Button(text="сформировать", command=get_email_by_uid)
            btn.pack()
        root.mainloop()
