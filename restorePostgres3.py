import asyncpg
import asyncio
import binascii
from pathlib import Path
from typing import Dict

DB_CONFIG = {
    "host": "localhost",
    "user": "postgres",
    "password": "hfvpfc15",
    "database": "MEDOC",
    "server_settings": {
        "zero_damaged_pages": "on",
        "ignore_checksum_failure": "on"
    }
}

BACKUP_DIR = Path("E:/backup_medoc")

# Соответствие: код записи в таблице -> (значение TOAST, имя файла)
FILES_TO_RESTORE = {
    223724: (1711623, "chunk_1711623.bin"),
    507209: (17364864, "chunk_17364864.bin"),
    507210: (17364865, "chunk_17364865.bin")
}


async def validate_pgcopy_header(file_path: Path) -> bool:
    """Проверка заголовка PGCOPY"""
    try:
        with open(file_path, "rb") as f:
            header = f.read(11)
            return header == b"PGCOPY\n\377\r\n\0"
    except Exception as e:
        print(f"Ошибка чтения файла {file_path.name}: {str(e)}")
        return False


async def restore_file(file_path: Path, record_code: int, toast_value: int) -> bool:
    """Восстановление данных из файла"""
    conn = None
    try:
        if not file_path.exists():
            print(f"Файл {file_path.name} не найден!")
            return False

        if not await validate_pgcopy_header(file_path):
            return False

        conn = await asyncpg.connect(**DB_CONFIG)
        async with conn.transaction():
            # Создание временной таблицы
            await conn.execute("""
                DROP TABLE IF EXISTS temp_restore;
                CREATE TEMP TABLE temp_restore (
                    code INTEGER PRIMARY KEY,
                    signfile BYTEA
                ) ON COMMIT DROP;
            """)

            # Импорт данных
            await conn.execute(f"""
                COPY temp_restore (code, signfile)
                FROM '{file_path}'
                WITH (FORMAT binary);
            """)

            # Логирование данных временной таблицы
            temp_data = await conn.fetch("SELECT * FROM temp_restore")
            print(f"\nДанные в temp_restore ({file_path.name}):")
            for record in temp_data:
                hex_preview = binascii.hexlify(record['signfile'][:8]).decode()
                print(f"Код: {record['code']}, HEX: {hex_preview}...")

            # Проверяем, содержит ли файл ожидаемый код TOAST
            toast_exists = await conn.fetchval("""
                SELECT EXISTS(SELECT 1 FROM temp_restore WHERE code = $1)
            """, toast_value)

            if not toast_exists:
                print(f"Ошибка: В файле {file_path.name} не найдено значение TOAST {toast_value}!")
                return False

            # Проверка существования кода записи
            exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT 1 FROM docsign_original 
                    WHERE code = $1
                )
            """, record_code)
            print(f"Код {record_code} в основной таблице: {exists}")

            if not exists:
                print(f"Ошибка: В основной таблице не найдена запись с кодом {record_code}!")
                return False

            # Обновление данных (копируем содержимое из TOAST-значения в запись с правильным кодом)
            result = await conn.execute("""
                UPDATE docsign_original
                SET signfile = (SELECT signfile FROM temp_restore WHERE code = $1)
                WHERE code = $2;
            """, toast_value, record_code)

            print(f"Обновлено записей: {result.split()[-1]}")
            return True

    except asyncpg.PostgresError as e:
        print(f"Ошибка PostgreSQL: {str(e)}")
        return False
    except Exception as e:
        print(f"Общая ошибка: {str(e)}")
        return False
    finally:
        if conn and not conn.is_closed():
            await conn.close()


async def main():
    print("=== Начало восстановления данных ===")
    tasks = []

    for record_code, (toast_value, filename) in FILES_TO_RESTORE.items():
        file_path = BACKUP_DIR / filename
        tasks.append(restore_file(file_path, record_code, toast_value))

    results = await asyncio.gather(*tasks)
    print(f"\nИтоги: Успешно: {sum(results)}, Неудач: {len(results) - sum(results)}")
    print("=== Восстановление завершено ===")


if __name__ == "__main__":
    asyncio.run(main())