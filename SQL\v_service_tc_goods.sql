
    CREATE OR REPLACE VIEW v_service_tc_goods AS
    SELECT
        goods.id,
        goods.sku,
        goods.unit,
        goods.quantity,
        goods.price_tr,
        goods.amount_tr::numeric(10,3) AS amount_tr,
        COALESCE(serv_group.gider_koef, 0)::numeric(10,4) AS gider_koef,
        ((COALESCE(serv_group.gider_koef, 0) + 1::numeric) * COALESCE(goods.amount_tr, 0) 
            / COALESCE(goods.quantity, 0))::numeric(10,4) AS maliyet,
        goods.coef_cart,
        goods.coef_doc,
        goods.coef_cart - goods.coef_doc AS diff,
        goods.cur_type,
        serv.batch_document,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        serv.currency_key       
       FROM v_service_tc serv
         LEFT JOIN ( SELECT
                goods_1.id, 
                nom.description AS sku,
                units.description AS unit,
                COALESCE(units.coefficient, 0) AS coef_cart,
                goods_1.quantity,
                round(CASE
                    WHEN serv_1.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
                        goods_1.price_tr 
                    WHEN serv_1.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
                        goods_1.price_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)     
                    WHEN serv_1.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
                        (goods_1.price_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)) * 
                        (SELECT rate_euro_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)
                END,3) AS price_tr,  
                round(CASE
                    WHEN serv_1.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
                        goods_1.amount_tr 
                    WHEN serv_1.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
                        goods_1.amount_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)     
                    WHEN serv_1.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
                        (goods_1.amount_tr / 
                        (SELECT rate_usd_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)) * 
                        (SELECT rate_euro_nbu
                        FROM t_rate_nbu
                        WHERE rate_date = serv_1.doc_date::date)
                END,3) AS amount_tr,                     
                goods_1.coefficient AS coef_doc,
                goods_1.ref_key,
                goods_1.nomenclature_key,
                goods_1.nomenclature_series_key,
                goods_1.unit_of_key,
                (SELECT description
                FROM t_one_cat_currencies
                WHERE ref_key = serv_1.currency_key
                ) as cur_type                
               FROM t_one_doc_receipt_of_goods_services as serv_1
                 LEFT JOIN t_one_doc_receipt_of_goods_services_goods goods_1
                    ON serv_1.ref_key = goods_1.ref_key
                 LEFT JOIN t_one_cat_nomenclature nom ON nom.ref_key::text = goods_1.nomenclature_key::text
                 LEFT JOIN 
                    (
                        SELECT *
                        FROM t_one_cat_units
                        WHERE NOT deletion_mark
                    )
                 AS units ON goods_1.unit_of_key::text = units.ref_key::text 
                    AND units.nomenclature_key::text = goods_1.nomenclature_key::text
              WHERE goods_1.price_tr <> 0) goods ON serv.batch_document::text = goods.ref_key::text
         LEFT JOIN v_service_tc_group serv_group ON serv_group.batch_document::text = serv.batch_document::text;
    