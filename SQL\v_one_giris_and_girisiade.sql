
    CREATE OR REPLACE VIEW  v_one_giris_and_girisiade
    AS
    SELECT
        id,
        doc_date,
        tarih,
        doc_number,
        sku,
        sell_by,
        CASE
            WHEN sell_by is null THEN
                null 
            ELSE
             --age(sell_by::date, tarih::date)
             sell_by::date - tarih::date
        END as between_sell_by, 
        inbox,
        quantity,
        coefficient,
        ed,
        tobox,
        amount,
        amount_vat,
        rate_settlement,
        multiplicity_of_mutual_settlements,
        amount_includes_vat,
        is_accounting,
        is_management,
        posted,
        doc_type,
        organization,
        supplier,
        warehouse,
        ref_key,
        organization_key,
        nomenclature_key,
        nomenclature_series_key,
        unit_of_key,
        supplier_key,
        currency_key,
        warehouse_key,
        contract_key,
        line_number
    FROM v_one_giris
    UNION ALL
    SELECT
        id,
        doc_date,
        tarih,
        doc_number,
        sku,
        sell_by,
        CASE
            WHEN sell_by is null THEN
                null
            ELSE 
                -- age(sell_by::date, tarih::date)
                sell_by::date - tarih::date
        END as between_sell_by, 
        inbox,
        quantity,
        coefficient,
        ed,
        tobox,
        amount,
        amount_vat,
        rate_settlement,
        multiplicity_of_mutual_settlements,
        amount_includes_vat,
        is_accounting,
        is_management,
        posted,
        doc_type,
        organization,
        supplier,
        warehouse,
        ref_key,
        organization_key,
        nomenclature_key,
        nomenclature_series_key,
        unit_of_key,
        supplier_key,
        currency_key,
        warehouse_key,
        contract_key,
        line_number
    FROM v_one_giris_iade;

    COMMENT ON VIEW v_one_giris_and_girisiade IS 'поступление_возврат';
    COMMENT ON COLUMN v_one_giris_and_girisiade.id IS '№';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_date  IS 'дата';
    COMMENT ON COLUMN v_one_giris_and_girisiade.tarih  IS 'дата_время';
    COMMENT ON COLUMN v_one_giris_and_girisiade.ed IS 'ед по накл';
    COMMENT ON COLUMN v_one_giris_and_girisiade.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_giris_and_girisiade.tobox IS 'в перечт на крб';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_giris_and_girisiade.sku IS 'sku';
    COMMENT ON COLUMN v_one_giris_and_girisiade.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_giris_and_girisiade.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_giris_and_girisiade.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_giris_and_girisiade.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_giris_and_girisiade.supplier IS 'поставщик';

    GRANT SELECT ON TABLE v_one_giris_and_girisiade TO user_prestige;       

