import asyncio
import os
import sys

sys.path.append(r'D:\Prestige\Python\Prestige')
from cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_cash_item"
DOCUMENT = "статья расходов access"
SQL_CREATE_TABLE = f"""
    -- таб НЕ УДАЛЯТЬ. ИНФ сначало сохранить!!!    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        item_name varchar(35) NULL,  -- статья 
        item_name_tr varchar(35) NULL,  -- статья транслит
        description varchar(250) NULL,  -- примечание
        isperiodic bool DEFAULT false NOT NULL,  -- относится к периоду
        recorder_type numeric(3) DEFAULT '-1'::integer NOT NULL,  -- 1-приходж -1 - расход
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (item_name),
        CONSTRAINT {TABLE_NAME}_check CHECK (recorder_type IN (-1, 1))
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.item_name IS 'статья';
    COMMENT ON COLUMN {TABLE_NAME}.item_name_tr IS 'статья транслит';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'примечание';
    COMMENT ON COLUMN {TABLE_NAME}.isperiodic IS 'относится к периоду';
    COMMENT ON COLUMN  {TABLE_NAME}.recorder_type IS '1-приходж -1 - расход';
    GRANT ALL ON TABLE {TABLE_NAME} TO {COMPANY_CASHIER}; --{DOCUMENT}

"""


async def main_t_cash_expense_item_async():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_TABLE)
    logger.info(f"{result}, SQL_CREATE_TABLE")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_t_cash_expense_item_async())
