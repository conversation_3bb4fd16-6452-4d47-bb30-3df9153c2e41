# reading data from Document_ГТДИмпорт_Товары and add to pg in table {TABLE_NAME}
# Passes the parameter to gtdImportPartitions/main_gtd_import_partitions
# *********** импортируем данные для подключения к базам
import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_gtd_import_goods"
DOCUMENT = "Document_ГТДИмпорт_Товары"
SELECT_COLUMNS = (
    "Ref_Key,ЕдиницаИзмерения_Key,Номенклатура_Key,СерияНоменклатуры_Key,<PERSON><PERSON><PERSON>ber,ДокументПартии,"
    "Количество,Коэффициент,СуммаНДС,СуммаПошлины,ФактурнаяСтоимость"
)

SQL_CREATE_TABLE = f"""    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        idt serial4 NOT NULL,
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        amount numeric NOT NULL DEFAULT 0,  -- Количество
        coefficient numeric NOT NULL DEFAULT 0,  -- Коэффициент
        amount_vat numeric(10, 4) NOT NULL DEFAULT 0,  -- СуммаНДС
        duty_amount numeric(10, 4) NOT NULL DEFAULT 0,  -- СуммаПошлины
        invoice_cost numeric(10, 4) NOT NULL DEFAULT 0,  -- ФактурнаяСтоимость
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        nomenclature_key varchar(50) NOT NULL,  -- Номенклатура_Key
        unit_of_key varchar(50) NOT NULL,  -- ЕдиницаИзмерения_Key
        nomenclature_series_key varchar(50) NOT NULL,  -- СерияНоменклатуры_Key
        batch_document varchar(50) NOT NULL,  -- ДокументПартии
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key,line_number)   
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';   
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.duty_amount IS 'СуммаПошлины';
    COMMENT ON COLUMN {TABLE_NAME}.invoice_cost IS 'ФактурнаяСтоимость';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.batch_document IS 'ДокументПартии';

    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        unit_of_key,
        nomenclature_key,
        nomenclature_series_key,
        line_number,
        batch_document,
        amount,
        coefficient,
        amount_vat,
        duty_amount,
        invoice_cost
        )
    VALUES {maket}            
    ON CONFLICT (ref_key,line_number)
    DO UPDATE SET
        ref_key= EXCLUDED.ref_key,
        unit_of_key= EXCLUDED.unit_of_key,
        nomenclature_key= EXCLUDED.nomenclature_key,
        nomenclature_series_key= EXCLUDED.nomenclature_series_key,
        line_number= EXCLUDED.line_number,
        batch_document= EXCLUDED.batch_document,
        amount= EXCLUDED.amount,
        coefficient= EXCLUDED.coefficient,
        amount_vat= EXCLUDED.amount_vat,
        duty_amount= EXCLUDED.duty_amount,
        invoice_cost= EXCLUDED.invoice_cost
    """
    return sql.replace("'", "")


sql_update_batch_document_gtd = f"""
    UPDATE {TABLE_NAME}
        SET batch_document = serv.ref_key 
    FROM t_one_doc_receipt_of_goods_services AS serv
    WHERE serv.ref_key <> '00000000-0000-0000-0000-000000000000'
        AND {TABLE_NAME}.batch_document  = serv.ref_key;
    """


async def main_gtd_import_goods_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(11)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_save_pg(sql_update_batch_document_gtd)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_gtd_import_goods_async())
