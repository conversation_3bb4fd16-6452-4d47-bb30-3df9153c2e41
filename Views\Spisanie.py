import asyncio
from async_Postgres import create_view_async

sql = '''
    -- DROP VIEW IF EXISTS v_one_spisanie CASCADE;

    CREATE OR REPLACE VIEW v_one_spisanie
    AS 
    SELECT DISTINCT
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        'списание'::text AS customer,
        serv.doc_number,
        nom.sku,
        nom.inbox,
        goods.quantity as quantity,
        goods.coefficient,
        (goods.quantity * goods.coefficient) AS ed,
        ((goods.quantity * goods.coefficient) / nom.inbox) AS tobox,
        'списание'::text AS doc_type,
        org.description as organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        NULL::text AS customer_key,
        NULL as currency_key,
        nom.supplier_key,
        serv.organization_key,
        serv.warehouse_key,
        serv.is_accounting,
        goods.line_number
    FROM t_one_doc_write_off_of_goods serv
        JOIN t_one_doc_write_off_of_goods_details goods USING (ref_key)
        JOIN v_one_nomenclature_inbox_supplier nom 
            ON nom.nomenclature_key::text = goods.nomenclature_key::text
        LEFT JOIN t_one_cat_organizations as org
            ON org.ref_key = serv.organization_key
        LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = serv.warehouse_key
    WHERE serv.doc_date::date <= current_date
    ORDER BY serv.doc_date, serv.doc_number, nom.sku;

    COMMENT ON VIEW v_one_spisanie IS 'списание';
    COMMENT ON COLUMN v_one_spisanie.customer IS 'покупатель';
    COMMENT ON COLUMN v_one_spisanie.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_spisanie.sku IS 'sku';
    COMMENT ON COLUMN v_one_spisanie.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_spisanie.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_spisanie.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_spisanie.doc_type IS 'тип';
'''

if __name__ == '__main__':
    asyncio.run(create_view_async(sql, 'v_one_spisanie'))
