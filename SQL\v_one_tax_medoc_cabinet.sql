-- Сравнение данных по налоговым накладным из 1С, Медок и ЕРПН(кабинет налогоплательщика)

DROP VIEW IF EXISTS v_one_tax_medoc_cabinet CASCADE;
CREATE OR REPLACE VIEW v_one_tax_medoc_cabinet AS
WITH ts AS (
	SELECT
		doc_date,
		doc_number,
		doc_date::date c_date_sell,
		CAST(regexp_replace(trim(doc_number)::text, '.*?(\d+)$', '\1') AS int8) c_number_sell,
		round(document_amount * settlement_rate,2) AS c_sell_amount,
		posted,
		ref_key,
		account_key
	FROM t_one_doc_tax_sale
	WHERE organization_key = '56d4e33a-1f59-11e7-80cc-001dd8b79079'  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
		AND operation_type <> 'СводнаяНаПревышениеБазыНадЦенойПоставки'
		AND posted
),
medoc AS (
	SELECT
		partner_name mdc_name,
		coalesce(partner_edrpou,'0') mdc_edrpou,
		doc_date mdc_date,
		doc_num::int8 mdc_number,
		docsum mdc_amount
	FROM v_medoc_reestr_and_docinfo
	WHERE (doc_move IN (1))
		AND (doc_type IN (10100))  -- 10100 - Налоговая накладная
),
client AS (
	SELECT DISTINCT
		customer,
		tsc.dtperiod schema_date,
		coalesce(edrpou,'0') c_edrpou,
		customer_key,
		vat
	FROM v_one_manager_counterparty_contracts_segments AS client
		LEFT JOIN t_one_cat_tax_schema_counterparty AS tsc
			ON client.customer_key= tsc.counterparty_key
		LEFT JOIN 	t_one_cat_tax_schema AS tschema
			ON tschema.ref_key = tsc.taxation_scheme_key
),
erpn AS (
	SELECT
		doc_date erpn_date,
		doc_number erpn_number,
		amount AS erpn_amount,
		amount_vat,
		customer_name,
		customer_edrpou,
		customer_inn erpn_inn,
		doc_status
	FROM t_tax_cabinet_erpn
	WHERE amount_vat > 0
		AND customer_inn <> '************'  -- отправитель и получатель один и тот же - ПРЕСТИЖ ПРОДУКТ.К
)
SELECT
	CASE
		WHEN NOT COALESCE(vat,FALSE) OR client.schema_date > ts.c_date_sell THEN
			'0'
		ELSE
			coalesce(c_edrpou,'0')
	END c_edrpou,
	ts.c_date_sell,
	ts.c_number_sell,
	ts.c_sell_amount,
	'1c' sources
FROM ts
	INNER JOIN client
		ON ts.account_key = client.customer_key
UNION ALL
SELECT
	customer_edrpou,
	erpn_date,
	erpn_number,
	-erpn_amount,
	'erpn' sources
FROM erpn
UNION ALL
SELECT
	mdc_edrpou,
	mdc_date,
	mdc_number,
	mdc_amount,
	'medoc'
FROM medoc
ORDER BY 3,4
;

GRANT SELECT ON v_one_tax_medoc_cabinet TO user_prestige;
