# взаимозачеты
import asyncio
import os
import sys
import time
from datetime import datetime

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))
from async_Postgres import  async_save_pg

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)
VIEW_NAME = 'v_one_offsetting'

SQL_CREATE_VIEW = f'''
    -- DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
--    SELECT 
--        assignment_clause, 
--        clause, 
--        counterparty AS customer,
--        dtperiod AS doc_date,
--        doc_number,
--        currency,
--        description AS a_comment,
--        amount AS amount,
--        amount_control AS amount_calc
--    FROM t_one_accreg_cash_recordtype
--    UNION ALL 
    SELECT assignment_clause, 
        clause, 
        customer, 
        doc_date, 
        doc_number, 
        cur.description AS currency,
        a_comment,
        round(sum(COALESCE(total,0)),2) AS amount,
        round(sum(COALESCE(total_calc,0)),2) AS amount_calc        
    FROM 
        (
        SELECT 
            'маркетинг/marketing' AS assignment_clause, 
            'док "корректировка долга"(взаимозачет)' AS clause,
            main.doc_date,
            main.doc_number,
            - sub.total AS total,
            - (COALESCE(sub.rate,0) / COALESCE(multiplicity,0) * COALESCE(sub.total,0)) AS total_calc,        
            main.a_comment,
            main.counterparty_creditor_key AS counterparty_key,
            main.currency_key
        FROM t_one_doc_debt_correction AS main
            INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                ON main.ref_key = sub.ref_key
        WHERE debt_type = 'Дебиторская' -- контрагенту закрываем долг
            AND main.operation_type NOT IN ('ПереносЗадолженности','СписаниеЗадолженности')
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        UNION ALL
        SELECT 
            'Оплата покупателя/musteri odemesi' AS assignment_clause,
            'док "корректировка долга"(взаимозачет)' AS clause,
            main.doc_date,
            main.doc_number,
            sub.total,
            COALESCE(sub.rate,0) / COALESCE(multiplicity,0) * COALESCE(sub.total,0) AS total_calc,        
            main.a_comment,
            main.counterparty_creditor_key AS counterparty_key, 
            main.currency_key
        FROM t_one_doc_debt_correction AS main
            INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                ON main.ref_key = sub.ref_key
        WHERE debt_type = 'Кредиторская' -- контрагент закрывающий кредит
            AND main.operation_type NOT IN ('ПереносЗадолженности','СписаниеЗадолженности')
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        UNION ALL
        SELECT 
            'маркетинг/marketing' AS assignment_clause, 
            'док "корректировка долга"(скидка)' AS clause,
            main.doc_date,
            main.doc_number,
            - sub.total,
            - (COALESCE(sub.rate,0) / COALESCE(multiplicity,0) * COALESCE(sub.total,0)) AS total_calc,        
            main.a_comment,
            COALESCE(main.counterparty_debtor_key,main.counterparty_creditor_key) AS counterparty_key,
            main.currency_key
        FROM t_one_doc_debt_correction AS main
            INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                ON main.ref_key = sub.ref_key
        WHERE main.operation_type IN ('СписаниеЗадолженности')
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        ) AS t
        INNER JOIN 
            (SELECT DISTINCT customer, customer_key FROM t_one_sale WHERE doc_type IN ('возврат','продажа')) AS counterparties
                ON t.counterparty_key = counterparties.customer_key 
        LEFT JOIN t_one_cat_currencies AS cur
            ON cur.ref_key = t.currency_key
    GROUP BY assignment_clause, customer, doc_date, doc_number, clause, cur.description, a_comment
    ORDER BY doc_date DESC, doc_number
    ;    
    COMMENT ON VIEW v_one_offsetting IS 'взаимозачеты';
    GRANT SELECT ON TABLE v_one_offsetting TO user_prestige;
'''


async def main_offsets_async():
    result = await async_save_pg(SQL_CREATE_VIEW)
    logger.info(f"{result}, SQL_CREATE_VIEW'")


if __name__ == '__main__':
    from Document.docDebtCorrection import main_debt_correction_async
    from Document.docDebtCorrectionDebtAmounts import main_debt_correction_debt_amount_async
    start = time.time()
    logger.info(f"START")
    asyncio.run(main_debt_correction_async())
    asyncio.run(main_debt_correction_debt_amount_async())
    asyncio.run(main_offsets_async())
    finish = datetime.now()
    logger.info(f"FINISH")
