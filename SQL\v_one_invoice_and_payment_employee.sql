    CREATE OR REPLACE VIEW v_one_invoice_and_payment_employee AS
    SELECT DISTINCT *
    FROM (
        SELECT counterparties.description AS клиент,
            sell.doc_number номерРН, sell.doc_date датаРН,
            (sell.rate_settlement * sell.document_amount)  AS суммаРН,
            sell.description AS комментРН,
            rcp_cash.doc_date AS датаПКО,
            rcp_cash.doc_number AS номерПКО,
            rcp_cash.amount_of_payment AS суммаПКО,
            rcp_bank.doc_date AS датаБанк,
            rcp_bank.doc_number AS номерБанк,
            rcp_bank.amount_of_payment AS суммаБанк,
            orders.a_comment комментарийЗаказа,
            orders.doc_date AS датаЗаказа,
            orders.doc_number AS номерЗаказа
        FROM t_one_doc_sale_of_goods_services AS sell -- Document_РеализацияТоваровУслуг
            LEFT JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                ON orders.ref_key = sell.deal
            LEFT JOIN t_one_cat_counterparties AS counterparties -- Catalog_Контрагенты
                ON counterparties.ref_key = sell.account_key
            LEFT JOIN (
                SELECT DISTINCT receipt.doc_date, receipt.doc_number, receipt_details.base_key,
                    receipt_details.amount_of_payment
                FROM t_one_doc_cash_warrant_receipt AS receipt -- Document_ПриходныйКассовыйОрдер
                    LEFT JOIN t_one_doc_cash_warrant_receipt_details AS receipt_details -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                        ON receipt.ref_key = receipt_details.ref_key
                WHERE receipt.doc_date >= '01.01.2023'::date
                    AND receipt.is_management AND receipt.posted
            ) AS rcp_cash
                ON rcp_cash.base_key = orders.ref_key
            LEFT JOIN (
                SELECT DISTINCT receipt.doc_date, receipt.doc_number,
                    receipt_details.document_of_settlements_with_a_counterparty_key as base_key,
                    receipt_details.amount_of_payment, receipt_details.deal_key
                FROM t_one_doc_cash_order_receipt AS receipt -- Document_ПлатежноеПоручениеВходящее
                    LEFT JOIN t_one_doc_cash_order_receipt_details AS receipt_details -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                        ON receipt.ref_key = receipt_details.ref_key
                WHERE receipt.doc_date >= '01.01.2023'::date
                    AND receipt.is_management AND receipt.posted
            ) AS rcp_bank
                ON rcp_bank.deal_key = orders.ref_key OR rcp_bank.base_key = sell.ref_key
        WHERE sell.doc_date >= '01.01.2023'::date
            AND sell.is_management AND sell.posted
            AND sell.account_key IN ('163e78bd-2b01-11eb-80fc-001dd8b72b55') -- Сотрудники Офис
        ) AS t
    WHERE (abs(COALESCE(t.суммаРН,0) - COALESCE(t.суммаПКО,0) - COALESCE(t.суммаБанк,0))) > 2
    ORDER BY датаРН
    ;

    COMMENT ON VIEW v_one_invoice_and_payment_employee IS 'продажи сотрудникам и оплаты';

    GRANT SELECT ON TABLE v_one_invoice_and_payment_employee TO user_prestige;
