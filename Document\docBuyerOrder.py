import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_buyer_order"
DOCUMENT = "Document_ЗаказПокупателя"
SELECT_COLUMNS = (
    "DataVersion,Date,Number,Posted,АдресДоставки,ВидОперации,"
    "ДатаВходящегоДокументаЭлектронногоОбмена,ДатаОплаты,ДатаОтгрузки,ДокументОснование,"
    "ДополнениеКАдресуДоставки,Комментарий,"
    "НомерВходящегоДокументаЭлектронногоОбмена,СкладГруппа,скНомерЗаказа,СтруктурнаяЕдиница,"
    "СуммаДокумента,Ref_Key,ВалютаДокумента_Key,Грузоотправитель_Key,Грузополучатель_Key,"
    "ДоговорКонтрагента_Key,КонтактноеЛицоКонтрагента_Key,Контрагент_Key,Организация_Key,"
    "Ответственный_Key,ТипЦен_Key,СуммаВключаетНДС,УчитыватьНДС,АвторасчетНДС"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NULL,  -- DataVersion
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NULL,  -- Number
        isposted boolean NOT NULL DEFAULT FALSE,  -- Posted
        include_vat boolean NOT NULL DEFAULT FALSE,  -- СуммаВключаетНДС
        vat_included boolean NOT NULL DEFAULT FALSE,  -- УчитыватьНДС
        auto_vat boolean NOT NULL DEFAULT FALSE,  -- АвторасчетНДС
        amount numeric(15,4) NOT NULL DEFAULT 0,  -- СуммаДокумента
        delivery_address varchar NULL,  -- АдресДоставки
        operation_type varchar(50) NULL,  -- ВидОперации
        doc_date_of_payment timestamp NOT NULL,  -- ДатаОплаты
        shipping_doc_date timestamp NOT NULL,  -- ДатаОтгрузки
        add_to_delivery_address varchar NULL,  -- ДополнениеКАдресуДоставки
        a_comment varchar NULL,  -- Комментарий
        doc_date_of_incoming_electronic_exchange_document timestamp NOT NULL,  -- ДатаВходящегоДокументаЭлектронногоОбмена
        doc_number_of_the_incoming_document_of_electronic_exchange varchar(50) NULL,  -- НомерВходящегоДокументаЭлектронногоОбмена
        warehouse_group varchar(50) NULL,  -- СкладГруппа
        ck_order_doc_number varchar(50) NULL,  -- скНомерЗаказа
        structural_unit varchar(50) NULL,  -- СтруктурнаяЕдиница
        ref_key varchar(50) NULL,  -- Ref_Key
        a_document_base varchar(50) NULL,  -- ДокументОснование
        currency_key varchar(50) NULL,  -- ВалютаДокумента_Key
        shipper_key varchar(50) NULL,  -- Грузоотправитель_Key
        consignee_key varchar(50) NULL,  -- Грузополучатель_Key
        counterparty_agreement_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        contact_person_of_counterparty_key varchar(50) NULL,  -- КонтактноеЛицоКонтрагента_Key
        account_key varchar(50) NULL,  -- Контрагент_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        responsible_key varchar(50) NULL,  -- Ответственный_Key
        price_type_key varchar(50) NULL,  -- ТипЦен_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.isposted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.delivery_address IS 'АдресДоставки';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_of_incoming_electronic_exchange_document IS 'ДатаВходящегоДокументаЭлектронногоОбмена';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_of_payment IS 'ДатаОплаты';
    COMMENT ON COLUMN {TABLE_NAME}.shipping_doc_date IS 'ДатаОтгрузки';
    COMMENT ON COLUMN {TABLE_NAME}.a_document_base IS 'ДокументОснование';
    COMMENT ON COLUMN {TABLE_NAME}.add_to_delivery_address IS 'ДополнениеКАдресуДоставки';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number_of_the_incoming_document_of_electronic_exchange IS 'НомерВходящегоДокументаЭлектронногоОбмена';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_group IS 'СкладГруппа';
    COMMENT ON COLUMN {TABLE_NAME}.ck_order_doc_number IS 'скНомерЗаказа';
    COMMENT ON COLUMN {TABLE_NAME}.structural_unit IS 'СтруктурнаяЕдиница';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.shipper_key IS 'Грузоотправитель_Key';
    COMMENT ON COLUMN {TABLE_NAME}.consignee_key IS 'Грузополучатель_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_agreement_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contact_person_of_counterparty_key IS 'КонтактноеЛицоКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.responsible_key IS 'Ответственный_Key';
    COMMENT ON COLUMN {TABLE_NAME}.price_type_key IS 'ТипЦен_Key';
    COMMENT ON COLUMN {TABLE_NAME}.include_vat IS 'СуммаВключаетНДС';
    COMMENT ON COLUMN {TABLE_NAME}.vat_included IS 'УчитыватьНДС';
    COMMENT ON COLUMN {TABLE_NAME}.auto_vat IS 'АвторасчетНДС';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}
    (
        dataversion, 
        doc_date, 
        doc_number, 
        isposted, 
        delivery_address, 
        operation_type, 
        doc_date_of_incoming_electronic_exchange_document, 
        doc_date_of_payment, 
        shipping_doc_date, 
        a_document_base, 
        add_to_delivery_address, 
        a_comment, 
        doc_number_of_the_incoming_document_of_electronic_exchange, 
        warehouse_group, 
        ck_order_doc_number, 
        structural_unit, 
        amount, 
        ref_key, 
        currency_key, 
        shipper_key, 
        consignee_key, 
        counterparty_agreement_key, 
        contact_person_of_counterparty_key, 
        account_key, 
        organization_key, 
        responsible_key, 
        price_type_key,
        include_vat,
        vat_included,
        auto_vat
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        doc_number = EXCLUDED.doc_number,
        isposted = EXCLUDED.isposted,
        delivery_address = EXCLUDED.delivery_address,
        operation_type = EXCLUDED.operation_type,
        doc_date_of_incoming_electronic_exchange_document = EXCLUDED.doc_date_of_incoming_electronic_exchange_document,
        doc_date_of_payment = EXCLUDED.doc_date_of_payment,
        shipping_doc_date = EXCLUDED.shipping_doc_date,
        a_document_base = EXCLUDED.a_document_base,
        add_to_delivery_address = EXCLUDED.add_to_delivery_address,
        a_comment = EXCLUDED.a_comment,
        doc_number_of_the_incoming_document_of_electronic_exchange = EXCLUDED.doc_number_of_the_incoming_document_of_electronic_exchange,
        warehouse_group = EXCLUDED.warehouse_group,
        ck_order_doc_number = EXCLUDED.ck_order_doc_number,
        structural_unit = EXCLUDED.structural_unit,
        amount = EXCLUDED.amount,
        currency_key = EXCLUDED.currency_key,
        shipper_key = EXCLUDED.shipper_key,
        consignee_key = EXCLUDED.consignee_key,
        counterparty_agreement_key = EXCLUDED.counterparty_agreement_key,
        contact_person_of_counterparty_key = EXCLUDED.contact_person_of_counterparty_key,
        account_key = EXCLUDED.account_key,
        organization_key = EXCLUDED.organization_key,
        responsible_key = EXCLUDED.responsible_key,
        price_type_key = EXCLUDED.price_type_key,
        include_vat = EXCLUDED.include_vat,
        vat_included = EXCLUDED.vat_included,
        auto_vat = EXCLUDED.auto_vat
        ;            
    """
    return sql.replace("'", "")


async def main_doc_buyer_order_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(30)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$7,", "to_timestamp($7, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$8,", "to_timestamp($8, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$9,", "to_timestamp($9, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_buyer_order_async())
