import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_object_property_values"
DOCUMENT = "InformationRegister_ЗначенияСвойствОбъектов"
SELECT_COLUMNS = "Объект,Объект_Type,Свойство_Key,Значение,Значение_Type"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id SERIAL NOT NULL,
        object_key varchar(50) NOT NULL,  -- Объект
        object_type varchar(100) NOT NULL,  -- Объект_Type
        property_key varchar(50) NOT NULL,  -- Свойство_Key
        value varchar(50) NOT NULL,  -- Значение
        value_type varchar(100) NOT NULL,  -- Значение_Type
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_object_key_property_key UNIQUE (object_key, property_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.object_key IS 'Объект';
    COMMENT ON COLUMN {TABLE_NAME}.object_type IS 'Объект_Type';
    COMMENT ON COLUMN {TABLE_NAME}.property_key IS 'Свойство_Key';
    COMMENT ON COLUMN {TABLE_NAME}.value IS 'Значение';
    COMMENT ON COLUMN {TABLE_NAME}.value_type IS 'Значение_Type';
    
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        object_key,
        object_type,
        property_key,
        value,
        value_type
    )
    VALUES {maket}
    ON CONFLICT (object_key, property_key)
    DO UPDATE SET
        object_type = EXCLUDED.object_type,
        value = EXCLUDED.value,
        value_type = EXCLUDED.value_type
    ;
    '''
    return sql.replace("'", "")


async def main_cat_object_property_values_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(5)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_warehouse_async())
