import os
import asyncio
import pandas as pd

from multiThread import get_json_from_url

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Catalog_Номенклатура"
SELECT_COLUMNS = "Ref_Key,Description"


async def get_response(ref_key: str):
    url = f"http://*************/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"/?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    filter_ref_key = f"&$filter=Ref_Key eq guid'{ref_key}'"
    url += filter_ref_key
    response = await get_json_from_url(url)
    return response


async def main_nomenclature_async(ref_key):
    json_data = await get_response(ref_key)
    if ('value' in json_data) and (json_data['value'] != []):
        df = pd.json_normalize(json_data['value'][0])
        df = df.rename({'Description': 'Номенклатура'}, axis='columns')
    return df


if __name__ == "__main__":
    asyncio.run(main_nomenclature_async("b6793cbf-5903-11e8-80e1-001dd8b79079"))
