import asyncio
import os

from async_Postgres import async_save_pg
from logger_prestige import get_logger
from prestige_authorize import con_postgres_psycopg2, pg_sqlalchemy_table_to_df, change_data_in_table_returning

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

conpg = con_postgres_psycopg2()

SQL_CREATE_TABLE = '''
    -- DROP TABLE IF EXISTS t_additional_expenses;
    
    CREATE TABLE IF NOT EXISTS t_additional_expenses (
        idt serial4 NOT NULL,
        ref_key_first bpchar(36) NOT NULL,  -- RefKey поступление оригинал  
        ref_key_second bpchar(36) NOT NULL,  -- RefKey поступление трансфер
        CONSTRAINT t_additional_expenses_unq UNIQUE (ref_key_first,ref_key_second),
        CONSTRAINT t_additional_expenses_pkey PRIMARY KEY (idt)
    );
    
    COMMENT ON COLUMN t_additional_expenses.ref_key_first IS 'RefKey поступление оригинал';
    COMMENT ON COLUMN t_additional_expenses.ref_key_second IS 'RefKey поступление трансфер';
    '''

sql_insert = '''
    INSERT INTO t_additional_expenses(ref_key_first, ref_key_second)
    SELECT ref_key, foreign_key
    FROM t_one_doc_receipt_of_goods_services
    WHERE foreign_key != '00000000-0000-0000-0000-000000000000'
    '''

# менять ненадо
sql_update_services_goods = '''
        UPDATE t_one_doc_receipt_of_goods_services_goods
        set ref_key = %s
        WHERE ref_key = %s
        '''

sql_update_gtd_goods = '''
        UPDATE t_one_doc_gtd_import_goods
        set batch_document = %s
        WHERE batch_document = %s
        '''

sql_update_dop_goods = '''
        UPDATE t_one_doc_incoming_additional_expenses_goods
        set batch_document = %s
        WHERE batch_document = %s
        '''


# changes the ref_key to the supply original ref_key in all  types documents
def list_changes():
    df = pg_sqlalchemy_table_to_df('t_additional_expenses')
    df = df.reset_index()
    for index, row in df.iterrows():
        odata = (row.ref_key_first, row.ref_key_second)
        change_data_in_table_returning(sql_update_gtd_goods, conpg, odata, "update_gtd_goods")
        change_data_in_table_returning(sql_update_dop_goods, conpg, odata, "update_gtd_goods")


async def main_tab_additional_expenses_async():
    result = True
    if not await async_save_pg(SQL_CREATE_TABLE):
        result = await async_save_pg(sql_insert)

    list_changes()

    logger.info(f"{result}")


if __name__ == "__main__":
    # main_tab_additional_expenses_async()
    asyncio.run(main_tab_additional_expenses_async())
