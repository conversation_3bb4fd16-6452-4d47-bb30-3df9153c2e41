# в договоре с клиентом, в закладку "Свойства" добавляет дополнительные поля (бонусы клиенту):
# отПродажи - 3; отОплаты - 3
from datetime import datetime

from async_request import URL_CONST, get_data_1c, post_data_1c


# Получение ключа папки контрагента
def get_folder_key(folder_name):
    url = (f"{URL_CONST}Catalog_Контрагенты/"
           f"?$format=json"
           f"&$filter=substringof('{folder_name}',Description)"
           f"&$select=Ref_Key")
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')[0]['Ref_Key']
    return None


# извлекаем клиентов, у которые относятся к данной папке
def get_clients_info(folder_key):
    url = (f"{URL_CONST}Catalog_Контрагенты/"
           f"?$format=json"
           f"&$filter=Parent_Key eq guid'{folder_key}' and IsFolder eq false"
           f"&$select=Ref_Key,Description")
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')
    return None


# извлекаем ref_key договоров с клиентами
def get_customer_contracts_key(customer_key):
    url = (f"{URL_CONST}Catalog_ДоговорыКонтрагентов"
           f"?$format=json"
           f"&$select=Ref_Key,Description,Code"
           f"&$filter=Owner_Key eq guid'{customer_key}'")
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')
    return None


# перед добавлением параметров, необходимо проверить, есть ли они в договоре
# если нет, то добавить
def check_parameter_in_contract(contract_key, parameter_key):
    url = (f"{URL_CONST}InformationRegister_ЗначенияСвойствОбъектов"
           f"?$format=json"
           f"&$filter=Объект eq cast(guid'{contract_key}','Catalog_ДоговорыКонтрагентов') "
           f"and Свойство_Key eq guid'{parameter_key}'")
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')[0]['Объект']
    return None


# добавляем в договора дополнительные параметры:
# отПродажи 3%, отОплаты 3%
def add_parameter_sale_pay(contract_key):
    data = []
    parameters_key = ["5ea6743b-b580-11ef-81d6-001dd8b740bc",  # отПродажи
                      "98677dc5-b580-11ef-81d6-001dd8b740bc"  # отОплаты
                      ]
    for parameter_key in parameters_key:
        is_empty = check_parameter_in_contract(contract_key, parameter_key)
        if not is_empty:
            result = add_parameter(contract_key, parameter_key)
            if result:
                data.append(result)
    return data


# добавляем в договора дополнительные параметры: отОплаты 3% и отПродажи 3%
def add_parameter(contract_key, parameter_key):
    url = f"{URL_CONST}InformationRegister_ЗначенияСвойствОбъектов?$format=json"
    data = {
        "Объект": f"{contract_key}",
        "Объект_Type": "StandardODATA.Catalog_ДоговорыКонтрагентов",
        "Свойство_Key": f"{parameter_key}",
        "Значение": "3",
        "Значение_Type": "Edm.Double"
    }
    result = post_data_1c(url, data)
    if result:
        return result.get('Объект')
    return None


# точка входа
def main_customer_contract_parameter_async():
    folder = "010 Довголенко Е."
    folder_key = get_folder_key(folder)
    result = get_clients_info(folder_key)

    # цикл по клиентам
    for customer in result:
        print(customer.get('Description'))
        customer_key = customer.get('Ref_Key')
        contracts = get_customer_contracts_key(customer_key)

        # цикл по договорам клиента
        for contract in contracts:
            print(contract.get('Code'), contract.get('Description'))
            contract_key = contract.get('Ref_Key')
            result = add_parameter_sale_pay(contract_key)
            print(result)


if __name__ == '__main__':
    print("Start", datetime.now())
    main_customer_contract_parameter_async()
    print("End", datetime.now())
