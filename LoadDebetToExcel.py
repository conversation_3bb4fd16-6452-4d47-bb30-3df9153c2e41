# pip install tomlkit
# pip install pathvalidate
from locale import currency
import os
import sys

sys.path.append(os.path.dirname(__file__))
import asyncio
import aiohttp
import aiofiles
import json
import shutil
import pandas as pd
from aiohttp import FormData
from datetime import datetime, date
from dateutil.parser import parse
from pathvalidate import sanitize_filename, sanitize_filepath
import xlwings as xw
from Document.docAccRegReciprocalSettlementsDetails import (
    main_doc_reciprocal_settlements_details_async,
)
from LoadManagerBonusToExcel import managers_chatid_name, managers_name_uuid

from CreateAndRunSQLScripts import create_views
from async_Postgres import (
    sql_to_dataframe_async,
    get_result_one_column,
    async_save_pg,
    send_telegram,
    TELEGRAM_TOKEN,
    chatid_rasim,
)
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)


# проверка существования представления
async def check_view_exists(view_name):
    query = f"SELECT to_regclass('{view_name}');"
    result = await get_result_one_column(query)
    return result is not None


async def copy_file(manager):
    """
    Копирует файл "Debet.xlsx", добавляя текущую дату и время в название файла.
    Возвращает путь к новому файлу.
    """
    # убираем недопустимые символы в названии файла
    manager = sanitize_filename(manager)

    try:
        file_source = os.path.abspath(os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "Debet.xlsx")
        )
        logger.info(f"file_source: {file_source}")
        await send_telegram(f"file_source: {file_source}", sys.argv[-1])
        output_file = (
            f"DT_{manager}_{datetime.strftime(datetime.now(), '%Y%m%d_%H%M%S')}.xlsx"
        )
        await send_telegram(f"output_file: {output_file}", sys.argv[-1])
        print(f"manager: {manager}; output_file: {output_file}")
        output_file = os.path.join(os.path.dirname(file_source), output_file)
        shutil.copyfile(file_source, output_file)
        return output_file
    except Exception as e:
        logger.info(f"Error copying file: {e}")
        return None


# Загрузка долгов по клиентам менеджера
async def load_debts_by_manager_clients() -> pd.DataFrame:
    df = pd.DataFrame()
    try:
        sql = "SELECT * FROM v_one_balance_customer"
        df = await sql_to_dataframe_async(sql)
        if not df.empty:
            # df["ПДЗнаДату"] = df["ПДЗнаДату"].astype(float)
            df["сумма"] = df["сумма"].astype(float)
            df.sort_values(by=["менеджер", "сегмент", "контрагент"], inplace=True)
            logger.info(df)
            return df
    except Exception as e:
        logger.info(f"Error loading data to DataFrame: {e}")

    return df


def refresh_all_pivot_tables(wb):
    """
    Обновляет все сводные таблицы в рабочей книге Excel.
    """
    try:
        for ws in wb.sheets:
            pivot_tables_count = ws.api.PivotTables().Count
            for i in range(1, pivot_tables_count + 1):
                pt = ws.api.PivotTables(i)
                pt.RefreshTable()

                # Автонастройка ширины столбцов
                ws.cells.columns.autofit()

    except Exception as e:
        logger.error(f"Error refreshing pivot tables: {e}")


def update_workbook(output_file, df, sht_name):
    """
    Обновляет рабочую книгу Excel данными из DataFrame.
    """
    try:
        app = xw.App(visible=False)
        wb = xw.Book(output_file)
        ws = wb.sheets[sht_name]

        last_row = ws.range("A" + str(ws.cells.last_cell.row)).end("up").row - 1
        ws.range("A" + str(last_row + 1)).options(index=False).value = df.values

        refresh_all_pivot_tables(wb)

        # Переход к закладке pivot
        wb.sheets["pivot"].activate()
        # apply_custom_format_to_column(output_file, "pivot", "остаток", BUILTIN_FORMATS[42])

        wb.save(output_file)
        wb.close()
        app.quit()
    except Exception as e:
        logger.info(f"Error updating workbook: {e}")


async def check_and_create_views(view_name):
    i = 0
    view_exists = False
    while not view_exists or i < 3:
        view_exists = await check_view_exists(view_name)
        if not view_exists:
            # представление не существует - создаем его
            await create_views()
        else:
            view_exists = True

        i += 1

    return view_exists


async def sort_df(df):
    df["сумма"] = df["сумма"].astype(float)
    df.sort_values(
        by=["менеджер", "сегмент", "контрагент"],
        ascending=[True, True, True],
        na_position="first",
        inplace=True,
    )
    return df


async def add_groupdf_to_olddf(df):
    # Группировка df по 'сегмент' и 'валюта' с расчетом суммы по 'сумма'
    df_group = (
        df.groupby(["менеджер", "сегмент", "контрагент", "валюта"])["сумма"]
        .sum()
        .reset_index()
    )
    all_columns = pd.Index.union(df.columns, df_group.columns)

    # Шаг 2: Добавляем в df2 отсутствующие колонки с значениями None
    for column in all_columns:
        if column not in df_group.columns:
            df_group[column] = None

    # Шаг 3: Объединяем df1 и df2
    result_df = pd.concat([df, df_group], ignore_index=True)

    return result_df


# парсинг и контроль аргументов командной строки
async def control_args():
    date_first = await is_date(sys.argv[1])
    if not (date_first):
        return {"ERROR": "Не определана дата"}

    chatid = sys.argv[2]
    manager_name = await is_manager(chatid)
    manager_key = managers_name_uuid.get(manager_name)
    if not manager_key:
        return {"ERROR": "Вы не зарегистрированы"}

    date_first = parse(sys.argv[1], dayfirst=True).date()
    result = {
        "date_first": date_first,
        "manager_key": manager_key,
        "chatid": chatid,
    }
    print(f"result: {result}")
    return result


async def is_manager(chatid):
    managers_name = managers_chatid_name.get(chatid)
    print(f"managers_name: {managers_name}")
    return managers_name if managers_name else None


# contol date_text is date
async def is_date(date_text):
    try:
        parse(date_text, dayfirst=True)
        return True
    except ValueError:
        return False


# отправка файла в телеграм
async def telegram_bot_send_document(path_doc, chat_id):
    chat_id = str(chat_id)
    logger.info(f"файл для отправки: {path_doc}")
    print(f"файл для отправки: {path_doc}")
    async with aiofiles.open(path_doc, "rb") as doc:
        data = FormData()
        data.add_field("chat_id", chat_id)
        data.add_field("document", doc, filename=path_doc)

        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendDocument"
            async with session.post(url, data=data) as response:
                logger.info(response.status)
                content = await response.text()
                result = json.loads(content)
                filename = {result.get("result").get("document").get("file_name")}
                logger.info(f"filename: {filename}")
                await send_telegram(f"Вам отправлен файл: {filename}", chat_id)

    if chat_id != str(chatid_rasim):
        await telegram_bot_send_document(os.path.abspath(path_doc), str(chatid_rasim))

    return response.status


async def load_data(date_firt, manager_key):
    try:
        await send_telegram("Ожидайте. Вам будет выслан файл.", sys.argv[-1])
        # AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
        await main_doc_reciprocal_settlements_details_async()
        await create_views()

        view_name = "v_one_balance_customer"
        view_exist = await check_and_create_views(view_name)
        if not view_exist:
            logger.info(f"Error: Не удалось создать представление - {view_name}")
            return None

        sql = f"SELECT fn_debt('{date_firt}'::date, current_date, '{manager_key}'::varchar(40));"
        resulr = await async_save_pg(sql)
        if resulr is None:
            logger.info("Error: Unable to get data from database.")
            print("Error: Unable to get data from database.")
            return None

        df = await load_debts_by_manager_clients()
        if df.empty:
            logger.info("Error: Unable to load data to DataFrame.")
            return None

        manager_name = df["менеджер"].iloc[0]
        logger.info(manager_name)
        if manager_name is None:
            await send_telegram("Error: Unable to get manager name.", sys.argv[-1])
            return None

        output_file = await copy_file(manager_name)
        if os.path.exists(output_file) is None:
            logger.info(f"Error: Unable to copy file - {output_file}")
            return None

        sht_name = "source"
        logger.info(output_file)
        update_workbook(output_file, df, sht_name)
        return await telegram_bot_send_document(output_file, sys.argv[2])

    except Exception as e:
        logger.info(f"Error in main function: {e}")

    return None


async def main_load_debt_to_excel_async():
    # добавим аргументы для запуска функции
    logger.info("Start LoadDebetToExcel.py")
    cur_dir = os.path.dirname(__file__)
    dir_to_python = os.path.join(cur_dir, ".venv", "Scripts", "python.exe")
    dir_to_script = os.path.join(cur_dir, "loadDebetToExcel.py")
    if len(sys.argv) == 1:
        sys.argv = [f"{dir_to_python} {dir_to_script}", "1.6.24", "490323168"]
    args = await control_args()
    err = args.get("ERROR")
    if err:
        logger.info(err)
        await send_telegram(err, sys.argv[-1])
        sys.exit(0)
    result = await load_data(args.get("date_first"), args.get("manager_key"))
    logger.info(f"End LoadDebtToExcel.py: {result}")


if __name__ == "__main__":
    asyncio.run(main_load_debt_to_excel_async())
