-- Content-Type: SQL
-- Content-Encoding: UTF-8
-- восстановление данных из лога для таблицы t_cash_register

DROP VIEW IF EXISTS v_log_cash CASCADE;
CREATE OR REPLACE VIEW v_log_cash AS
WITH extract_data_from_log AS (
        SELECT DISTINCT
            changed_at::timestamp(0) changed_at,
            id as log_id,
            operation,
            'old' status,
            (old_data->>'id')::uuid as id,
            (old_data->>'doc_date')::timestamp(0) as doc_date,
            (old_data->>'doc_number')::int4 as doc_number,
            (old_data->>'amount')::numeric as amount,
            (old_data->>'amount_sub')::numeric as amount_sub,
            (old_data->>'payment_purpose_id')::uuid as payment_purpose_id,
            (old_data->>'item_id')::uuid as item_id,
            (old_data->>'organization_key')::varchar as organization_key,
            (old_data->>'organization_key_sub')::varchar as organization_key_sub,
            (old_data->>'currency_key')::varchar as currency_key,
            (old_data->>'currency_key_sub')::varchar as currency_key_sub,
            (old_data->>'customer_key')::varchar as customer_key,
            (old_data->>'employee_key')::uuid as employee_key,
            (old_data->>'description')::varchar as description,
            (old_data->>'document_type')::varchar as document_type,
            (old_data->>'create_user')::varchar as create_user,
            (old_data->>'update_user')::varchar as update_user,
            (old_data->>'item_period')::date as item_period,
            (old_data->>'recorder_type')::numeric as recorder_type,
            (old_data->>'is_bank')::bool as is_bank
        FROM
            t_change_log
        WHERE table_name in ('t_cash_register')
            AND id IS NOT NULL
        UNION ALL
        SELECT DISTINCT
            changed_at::timestamp(0),
            id as log_id,
            operation,
            'new' status,
            (new_data->>'id')::uuid as id,
            (new_data->>'doc_date')::timestamp(0) as doc_date,
            (new_data->>'doc_number')::int4 as doc_number,
            (new_data->>'amount')::numeric as amount,
            (new_data->>'amount_sub')::numeric as amount_sub,
            (new_data->>'payment_purpose_id')::uuid as payment_purpose_id,
            (new_data->>'item_id')::uuid as item_id,
            (new_data->>'organization_key')::varchar as organization_key,
            (new_data->>'organization_key_sub')::varchar as organization_key_sub,
            (new_data->>'currency_key')::varchar as currency_key,
            (new_data->>'currency_key_sub')::varchar as currency_key_sub,
            (new_data->>'customer_key')::varchar as customer_key,
            (new_data->>'employee_key')::uuid as employee_key,
            (new_data->>'description')::varchar as description,
            (new_data->>'document_type')::varchar as document_type,
            (new_data->>'create_user')::varchar as create_user,
            (new_data->>'update_user')::varchar as update_user,
            (new_data->>'item_period')::date as item_period,
            (new_data->>'recorder_type')::numeric as recorder_type,
            (new_data->>'is_bank')::bool as is_bank
        FROM
            t_change_log
        WHERE table_name in ('t_cash_register')
)
SELECT DISTINCT
--    log.log_id,
    log.doc_date,
    log.doc_number,
    item.item_name статья,
    purpose.purpose_name назначение,
    org.description организация,
    org2.description организация2,
    cur.description валюта,
    cur2.description валюта2,
    customer.description AS клиент,
    empl.coworker_name сотрудник,
    log.changed_at,
    log.operation,
    log.status,
    log.amount,
    log.amount_sub,
    log.description,
    log.document_type,
    to_char(log.item_period,'YYYY.MM') as период,
    log.payment_purpose_id,
    purpose.item_id,
    log.recorder_type,
    log.create_user,
    log.update_user,
    log.organization_key,
    log.organization_key_sub,
    log.currency_key,
    log.currency_key_sub,
    log.customer_key,
    log.employee_key
FROM extract_data_from_log AS log
    LEFT JOIN public.t_cash_payment_purpose AS purpose
        ON log.payment_purpose_id = purpose.id
    LEFT JOIN t_cash_item AS item
        ON purpose.item_id = item.id
    LEFT JOIN public.t_one_cat_organizations AS org
        ON log.organization_key = org.ref_key
    LEFT JOIN public.t_one_cat_organizations AS org2
        ON log.organization_key_sub = org2.ref_key
    LEFT JOIN t_one_cat_currencies AS cur
        ON log.currency_key = cur.ref_key
    LEFT JOIN t_one_cat_currencies AS cur2
        ON log.currency_key_sub = cur2.ref_key
    LEFT JOIN t_one_cat_counterparties AS customer
        ON log.customer_key = customer.ref_key
    LEFT JOIN t_coworker AS empl
        ON log.employee_key = empl.ref_key
WHERE log.id IS NOT NULL
ORDER BY
    doc_number desc,
    changed_at desc
;

GRANT SELECT ON TABLE public.v_log_cash TO anna;
GRANT SELECT ON TABLE public.v_log_cash TO user_prestige;
