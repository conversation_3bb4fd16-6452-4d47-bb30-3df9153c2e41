-- продажа товара клиентам и оплата по договорам, в т.ч маркетинг
-- даты установлены вручную для теста
DROP VIEW IF EXISTS v_one_customers_sales_payment CASCADE;

CREATE OR REPLACE VIEW v_one_customers_sales_payment AS
SELECT
	contr.manager,
	contr.segment,
	contr.customer,
	contr.contract_name,
	doctype,
	t.tobox,
	t.amount,
	t.amount_maliyet_uah,
	t.diff,
	t.customer_key,
	t.contract_key
FROM (
	SELECT
		'товар' doctype,
		customer_key,
		tos.contract_key,
		sum(coalesce(tos.tobox,0))::int8 tobox,
		sum(coalesce(tos.amount,0))::int8 amount,
		sum(coalesce(tos.amount_maliyet_uah,0))::int8 amount_maliyet_uah,
		sum( coalesce(tos.amount,0) - coalesce(tos.amount_maliyet_uah,0))::int8 AS diff
	FROM t_one_sale tos -- продажа/возврат товара
	WHERE tos.doc_date::date >= '01.07.2024'
		AND tos.doc_date::date <= '31.07.2024'
	GROUP BY
		tos.customer_key,
		tos.contract_key
	UNION ALL
	SELECT
		'платеж' doctype,
		re.account_key AS customer_key,
		re.contract_key,
		0 tobox,
		sum(re.amount)::int8 as amount,
		0 amount_maliyet,
		sum(re.amount)::int8 AS diff
	FROM v_one_cash_order_receipt_expense as re -- банк, оплата по договорам (клиенту, от клиента)
		LEFT JOIN
			(SELECT DISTINCT distributor, contract_key FROM t_one_sale) AS unq
			ON unq.contract_key = re.contract_key
	WHERE re.doc_date::date >= '01.07.2024'
		AND re.doc_date::date <= '31.07.2024'
	GROUP BY
		re.account_key,
		re.contract_key
) AS t
LEFT JOIN v_one_manager_counterparty_contracts_segments AS contr
	ON contr.contract_key = t.contract_key
ORDER BY
	contr.manager,
	contr.segment,
	contr.customer,
	contr.contract_name
;

COMMENT ON VIEW public.v_one_customers_sales_payment IS 'продажа товара клиентам и оплата по договорам, в т.ч маркетинг';
