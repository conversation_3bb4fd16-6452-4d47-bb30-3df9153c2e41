-- клиент и его бонусы на соответствующую дату
--DROP TABLE IF EXISTS t_customer_bonus;

CREATE TABLE IF NOT EXISTS t_customer_bonus(
	id uuid DEFAULT uuid_generate_v4() NOT NULL,
	contract_key varchar(36) NOT NULL,
	bonus_key uuid NOT NULL,
	bonus_date date DEFAULT current_date,
	bonus_value numeric(10,2) DEFAULT 0,
	CONSTRAINT t_customer_bonus_pkey PRIMARY KEY (id),
	CONSTRAINT t_customer_bonus_unq UNIQUE (contract_key, bonus_key, bonus_date),
	CONSTRAINT t_customer_bonus_contract_key_fkey FOREIGN KEY (contract_key) REFERENCES t_one_cat_contracts_counterparties(ref_key) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT t_customer_bonus_bonuskey_fkey FOREIGN KEY (bonus_key) REFERENCES t_bonus_type(id) ON DELETE RESTRICT ON UPDATE CASCADE
);

COMMENT ON TABLE t_customer_bonus IS 'клиент и его бонусы';
COMMENT ON COLUMN t_customer_bonus.contract_key IS 'клиент_key';
COMMENT ON COLUMN t_customer_bonus.bonus_key IS 'бонус_key';
COMMENT ON COLUMN t_customer_bonus.bonus_date IS 'дата начисления бонуса';
COMMENT ON COLUMN t_customer_bonus.bonus_value IS 'значение бонуса';


INSERT INTO t_customer_bonus (contract_key, bonus_key, bonus_date, bonus_value)
SELECT contract_key, '101ca355-da81-4596-8593-f725cad784ea', '01.01.2023', 3 -- от продаж
FROM v_one_manager_counterparty_contracts_segments
WHERE LEFT(manager,3) IN ('010','011','012','013','014')
    AND contract_key IS NOT NULL
	AND customer NOT IN ('ТОВ "ДЕМАРТ"', 'ТОВ "ОМЕГА"', 'ФОП Федоренко О.В.', 'ТОВ "ВАЙН ХОЛЛ"') -- исключаем клиентов по которым не начисляем бонусы
ON CONFLICT (contract_key, bonus_key, bonus_date) DO NOTHING;
;

INSERT INTO t_customer_bonus (contract_key, bonus_key, bonus_date, bonus_value)
SELECT contract_key, 'ca35f1e2-014d-4ffc-9de0-34f04e9319d2', '01.01.2023', 3 -- от возврата денег
FROM v_one_manager_counterparty_contracts_segments
WHERE LEFT(manager,3) IN ('010','011','012','013','014')
    AND contract_key IS NOT NULL
	AND customer NOT IN ('ТОВ "ДЕМАРТ"', 'ТОВ "ОМЕГА"', 'ФОП Федоренко О.В.', 'ТОВ "ВАЙН ХОЛЛ"')  -- исключаем клиентов по которым не начисляем бонусы
ON CONFLICT (contract_key, bonus_key, bonus_date) do nothing;
;