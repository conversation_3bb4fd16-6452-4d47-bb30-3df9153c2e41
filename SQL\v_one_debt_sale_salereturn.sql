DROP VIEW IF EXISTS v_one_debt_sale_salereturn CASCADE;

CREATE OR REPLACE VIEW v_one_debt_sale_salereturn AS
    SELECT
        distributor manager,
        segment_folder,
        segment,
        customer,
        doc_date,
        doc_number,
        sum(coalesce(amount,0)) debt,
        0 credit,
        -- себестоимость считаем только по продажам, т.к. возвраты - это наша потеря, уже никому не продадим
        -- исключения (склад ПЕРЕОЦІНКА), когда себестоимость учитывается при возврате переоценке
        -- или переброски с контрагента на контрагента
        CASE
            WHEN issale = 1 OR (issale = -1 AND warehouse ILIKE '%ПЕРЕОЦІНКА%') THEN
                sum(amount_maliyet_uah)
            ELSE
                0
        END
        AS costs,
        currency,
        doc_type operation_type,
        ref_key
    FROM t_one_sale
    WHERE
--        doc_date::date >= '01.01.2024'::date
--    	AND doc_date::date <= current_date
        organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
        AND customer_key <> 'f51a0e88-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
        AND warehouse_key NOT IN (
            -- товар, который продавался/возвращался для переоценки не учитываем
              '21f1d3fd-97d9-11ed-8160-001dd8b740bc'  -- склад ПЕРЕОЦІНКА; вирт перемещения товара для переоценки
            , 'bc5d980a-5fc2-11ef-81bb-001dd8b72b55' -- ПЕРЕОЦІНКА 2; перемещение товара с юр.лица на юр.лицо
--          , 'cc242dd1-099e-11ec-8117-001dd8b72b55' -- Склад возврата, списания, утилизации по Бух Учету; виртуальный склад для списания товара
        )
    GROUP BY
        distributor,
        segment_folder,
        segment,
        customer,
        doc_date,
        doc_number,
        warehouse,
        currency,
        doc_type,
        issale,
        ref_key
    ORDER BY
        distributor,
        segment_folder,
        segment,
        customer,
        doc_date,
        doc_number,
        currency,
        doc_type
    ;

COMMENT ON VIEW v_one_debt_sale_salereturn IS 'продажи и возвраты';