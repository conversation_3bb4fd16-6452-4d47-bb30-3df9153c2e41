import re
import asyncpg
from async_Postgres import username, psw, basename, hostname, port
from views_pg import *

dsn = f"postgresql://{username}:{psw}@{hostname}:{port}/{basename}"


async def execute_sql_file_table(conn, filename):
    with open(filename, "r", encoding='utf-8') as file:
        sql = file.read()
        # Проверка, содержит ли файл упоминание таблицы, начинающейся с t_,
        # и не содержит ли он JOIN с представлением, начинающимся на v_
        if re.search(r"FROM t_", sql, re.IGNORECASE) and not re.search(r"JOIN v_", sql, re.IGNORECASE) \
                and not re.search(r"FROM v_", sql, re.IGNORECASE):
            try:
                await conn.execute(sql)
                print(f"t: {filename}")
            except Exception as e:
                print(f"Error executing t_file {filename}: {e}")
                raise filename


async def execute_sql_file_views(conn, filename):
    with open(filename, "r", encoding='utf-8') as file:
        sql = file.read()
        # Проверка, содержит ли файл упоминание таблицы, начинающейся с t_,
        # и не содержит ли он JOIN с представлением, начинающимся на v_
        if re.search(r"FROM v_", sql, re.IGNORECASE) or re.search(r"JOIN v_", sql, re.IGNORECASE):
            try:
                await conn.execute(sql)
                print(f"v: {filename}")
            except Exception as e:
                print(f"Error executing v_file {filename}: {e}")
                new_filename = str(e).split('"')[1] + ".sql"
                await execute_sql_file_views(conn, os.path.join("sql", new_filename))
                # await conn.execute(sql)


async def main():
    conn = await asyncpg.connect(dsn)
    try:
        for filename in os.listdir("sql"):
            if filename.endswith(".sql"):
                await execute_sql_file_table(conn, os.path.join("sql", filename))

        for filename in os.listdir("sql"):
            if filename.endswith(".sql"):
                await execute_sql_file_views(conn, os.path.join("sql", filename))
    finally:
        await conn.close()


if __name__ == '__main__':
    # Запуск асинхронной функции
    # asyncio.run(main())
    asyncio.get_event_loop().run_until_complete(main())
