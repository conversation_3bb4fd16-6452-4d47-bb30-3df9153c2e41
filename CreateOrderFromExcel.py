# Description: Скрипт для чтения почты и сохранения заказов в excel из писем
# отбираются письма с темой содержащей слово "заказ" и формой "Ф1" или "Ф2"
# данные письма будем загружать в заказ 1С
# Для работы скрипта необходимо настроить файл Config.py

# https://github.com/ikvk/imap_tools#search-criteria
import asyncio
import math
import os
import re
import smtplib
from datetime import datetime, date
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

import aiohttp
import pandas as pd
from dateutil.relativedelta import relativedelta
from imap_tools import MailBox, A  # pip install imap-tools

from Config import RASIM_MAIL, RASIM_PSW, IMAP_SERVER, SMTP_SERVER
from CreateNomenclatureFromExcel import get_nomenclature_key_from_sale
from async_Postgres import send_telegram, C_LOGIN, C_PSW, USER_KEY
from async_request import get_data_1c, get_1c_async

# Настройка отображения всех колонок
pd.set_option('display.max_columns', None)

URL_CONST = "http://***************/utp_prestige/odata/standard.odata/"
SELECT_COLUMN = """ВалютаДокумента_Key, ДоговорКонтрагента_Key, Контрагент_Key, Организация_Key, КратностьВзаиморасчетов,
    КурсВзаиморасчетов, СкладГруппа, СкладГруппа_Type, СтруктурнаяЕдиница, СтруктурнаяЕдиница_Type, СуммаВключаетНДС, 
    ТипЦен_Key, УчитыватьНДС, АвторасчетНДС, ВидОперации, Грузополучатель_Key, КонтактноеЛицоКонтрагента_Key
"""

# минимальный срок годности товара от текущей даты
MIN_EXPIRATION_DATE = date.today() + relativedelta(months=1)
NOMENCLATURE_COLUMN = '1c'  # наименование колонки в Excel с наименованием номенклатуры
QUANTITY_COLUMN = 'заказ крб'  # наименование колонки в Excel с количеством товара
PRICE = 'цена'  # наименование колонки в Excel с ценой товара
msg = MailBox(IMAP_SERVER).fetch(A(seen=False), mark_seen=False)


# получаем письмо если в теме есть слово "заказ"
async def get_msg_from_email():
    global msg
    try:
        with MailBox(IMAP_SERVER).login(RASIM_MAIL, RASIM_PSW, 'INBOX') as mailbox:  # default INBOX

            # получаем письма с флагом "не прочитано"
            for msg in mailbox.fetch(A(seen=False), mark_seen=False):

                # если тема письма содержит слово "заказ"
                if 'заказ' in msg.subject.lower():
                    mailbox.flag(msg.uid, '\\Seen', False)  # Mark as read
                    return msg
                else:
                    # если письмо не подходит по условиям, то помечаем его как прочитанное
                    mailbox.flag(msg.uid, '\\Seen', False)  # Mark as unread

    except Exception as e:
        response = f"ERROR: get_file_from_email, {e}"
        print(response)
        return None


# сохранение ВСЕХ excel файлов из письма (вложения)
async def get_filelist_from_email() -> list:
    global msg
    file_paths = []
    for att in msg.attachments:
        if att.filename.endswith('.xlsx') or att.filename.endswith('.xls'):
            file_path = os.path.join(os.getcwd(), att.filename)
            with open(file_path, 'wb') as f:
                f.write(att.payload)
            file_paths.append(file_path)

    # set - оставляем только уникальные пути к файлам
    return list(set(file_paths))


# извлекаем наименование заказчика из темы письма
async def get_customer_name_from_email(msg) -> str:
    return msg.subject.lower().replace('заказ', '').replace('от', '').replace('ф1', '').replace('ф2', '').strip()


# извлекаем форму (Ф1, Ф2) из темы письма
async def get_form_type_from_email(msg):
    pattern = re.compile(r'ф[1-2]')
    match = pattern.search(msg.subject.lower())
    if match:
        if match.group(0) == 'ф1':
            return "true"
        elif match.group(0) == 'ф2':
            return "false"
    return None


# получаем шапку заказа из 1С, используя contract_key
async def get_customer_info_from_order_1c(contract_key):
    url = (f"{URL_CONST}/Document_ЗаказПокупателя?"
           "$format=json"
           "&$orderby=Date desc"
           "&$top=1"
           f"&$select={SELECT_COLUMN}"
           f"&$filter=ДоговорКонтрагента_Key eq guid'{contract_key}'")

    answer = await get_1c_async(url)
    if answer:
        return answer[0]
    return None


# создаем шапку нового заказа для загрузки в 1С
# за основу берем шапку старого заказа
# к этим данным будем добавлять табличную часть с наименованиями товаров
async def create_order_header(old_header, date_doc):
    new_header = {
        "DeletionMark": True,
        "Date": f"{date_doc}T10:00:00",
        "Posted": False,
        "Ответственный_Key": USER_KEY,  # "cd51edb5-4a05-11ed-8148-001dd8b72b55",  # auto
        "Комментарий": "загрузки из почты",
    }
    new_header.update(old_header)
    return new_header


# находим коэф у 'ЕдиницаХраненияОстатков_Key' для перевода количества товара в заказе в 'ЕдиницаХраненияОстатков_Key'
async def get_units_key_from_report_units(nomenclature_key, unit_key):
    url = (f"{URL_CONST}Catalog_ЕдиницыИзмерения?$format=json"
           f"&$filter=Owner eq cast(guid'{nomenclature_key}', 'Catalog_Номенклатура') "
           f"and ЕдиницаПоКлассификатору_Key eq guid'{unit_key}'"
           f"&$select=Ref_Key,Коэффициент")
    answer = await get_1c_async(url)
    if answer:
        answer = answer[0]
        coef = answer['Коэффициент']
        unit_key = answer['Ref_Key']
        return {'unit_key': unit_key, 'coef': coef}
    return {}


# если количество товара в заказе больше, чем остаток на складе по определенной серии
# тогда выбираем эту серию и следующую по сроку годности, и так далее
# если остатков нет, то товар не добавляем в заказ
async def get_series_by_expiration_date(stock_series_df, quantity):
    series = []
    sub_series = {}
    for index, row in stock_series_df.iterrows():
        if 0 < quantity <= row['stock_quantity']:
            sub_series = {'nomenclature_key': row['Номенклатура_Key'],
                          'series_key': row['СерияНоменклатуры_Key'],
                          'Размещение': row['Склад_Key'],
                          'quantity_find': quantity}
            quantity = 0
        elif quantity > row['stock_quantity']:
            sub_series = {'nomenclature_key': row['Номенклатура_Key'],
                          'series_key': row['СерияНоменклатуры_Key'],
                          'Размещение': row['Склад_Key'],
                          'quantity_find': row['stock_quantity']}
            quantity -= row['stock_quantity']

        series.append(sub_series)  # добавляем текущую серию в заказ
        if quantity <= 0:
            break
    columns = list(sub_series.keys())
    return pd.DataFrame(series, columns=columns)


# в регистрах накопления 1С количество везде со знаком +
# исправим знаки Receipt-оставим как есть, а Expense-перевернем
# нужно, чтобы сгруппировать по складам, сериям и вывести остатки
async def quantity_sign(df):
    df.loc[df['RecordType'] == 'Expense', 'Количество'] *= -1
    return df


# остатки по складам с указанием серий товаров и сроком годности > MIN_EXPIRATION_DATE
# ед.изм. - ЕдиницаХраненияОстатков_Key. Те. ед.изм заказа надо перевести в ЕдиницаХраненияОстатков_Key.
async def get_stock_balance(nomenclature_key):
    expiration_date = MIN_EXPIRATION_DATE.strftime('%Y-%m-%d')
    url = (f"{URL_CONST}AccumulationRegister_ТоварыНаСкладах_RecordType"
           f"?$format=json"
           f"&$filter=Номенклатура_Key eq guid'{nomenclature_key}' "
           f"and (Склад_Key eq guid'381c5d92-4acb-11ed-8148-001dd8b72b55' "  # МЕРЕЖІ
           f"or Склад_Key eq guid'a26219b3-8fba-11e6-80c4-c936aa9c817c' "  # Основной склад товаров
           f"or Склад_Key eq guid'4b40b865-6d2f-11ec-8125-001dd8b72b55') "  # Основной склад товаров 2022
           f"and СерияНоменклатуры/СрокГодности ge datetime'{expiration_date}T00:00:00'"
           f"&$select=RecordType,Склад_Key,Номенклатура_Key,СерияНоменклатуры_Key,Количество,СерияНоменклатуры_Key,"
           f"СерияНоменклатуры/СрокГодности"
           f"&$expand=СерияНоменклатуры")
    df = pd.DataFrame()
    answer = await get_1c_async(url)
    if answer:
        df = pd.DataFrame(answer)

        df['СрокГодности'] = df['СерияНоменклатуры'].apply(lambda x: x['СрокГодности'])

        # удаляем колонку СерияНоменклатуры
        df = df.drop(columns=['СерияНоменклатуры@navigationLinkUrl', 'СерияНоменклатуры'])

        # преобразуем дату в формат datetime.
        df['СрокГодности'] = pd.to_datetime(df['СрокГодности']).dt.date

        df = await quantity_sign(df)  # исправим знаки у количества в строках Expense

    if not df.empty:
        # переименуем Количество в quantity_find
        df = df.rename(columns={'Количество': 'stock_quantity'})
    return df


# создаем из df табличную часть заказа
# далее, эту табличную часть будем объединять с шапкой заказа
async def create_order_table(df):
    return df.to_dict(orient='records')


# Формируем полный df для табличной части заказа. Добавляем недостающие колонки
async def complete_df(df):
    rename_columns = {
        'nomenclature_key': 'Номенклатура_Key',
        'series_key': 'СерияНоменклатуры_Key',
        'quantity_find': 'Количество',
        'price': 'Цена',
        'comment': 'Комментарий'
    }
    df = df.rename(columns=rename_columns)
    df['Размещение_Type'] = 'StandardODATA.Catalog_Склады'
    df['Коэффициент'] = df['БазоваяЕдиницаИзмеренияКоэф']
    df['СтавкаНДС'] = 'НДС20'
    df['ЕдиницаИзмерения_Key'] = df['БазоваяЕдиницаИзмерения_Key']
    df['КоличествоМест'] = df['Количество'] / df['ЕдиницаИзмеренияМестКоэф']
    # округляем кол-во мест до целого числа в большую сторону
    df['КоличествоМест'] = df['КоличествоМест'].apply(lambda x: math.ceil(x))
    df['Сумма'] = df['Цена'] * df['Количество']
    df['LineNumber'] = df.index + 1
    df['КлючСтроки'] = df.index + 1
    df = df[[
        'LineNumber',
        'ЕдиницаИзмерения_Key',
        'ЕдиницаИзмеренияМест_Key',
        'Количество',
        'КоличествоМест',
        'Коэффициент',
        'Номенклатура_Key',
        'Размещение',
        'Размещение_Type',
        'СтавкаНДС',
        'Сумма',
        'Цена',
        'КлючСтроки',
        'СерияНоменклатуры_Key'
    ]]
    return df


# Загружаем данные в 1С. Используем метод POST запрос
async def upload_order_to_1c(order_header):
    url = f"{URL_CONST}/Document_ЗаказПокупателя?$format=json"
    connector = aiohttp.TCPConnector(limit=50)
    async with aiohttp.ClientSession(connector=connector, auth=aiohttp.BasicAuth(C_LOGIN, C_PSW)) as session:
        try:
            async with session.post(url, json=order_header) as response:
                if response.status == 201:
                    order_info = await response.json()
                    order_info = ("Заказ загружен в 1С\n"
                                  f"Номер заказа: {order_info['Number']}\n"
                                  f"Дата заказа: {order_info['Date']}\n"
                                  f"Сумма заказа: {order_info['СуммаДокумента']}\n"
                                  )
                    return order_info
                else:
                    return None
        except Exception as e:
            print(f"Failed to fetch customer info from 1C: {e}")
        finally:
            await session.close()

    return None


# если заказ загружен успешно, то отвечаем на почту
# адрес получателя = адрес отправителя заказа
async def send_email(msg, text):
    try:
        # Создание письма
        email_msg = MIMEMultipart()
        email_msg['From'] = RASIM_MAIL
        email_msg['To'] = msg.from_
        email_msg['Subject'] = f"Re: {msg.subject}"
        email_msg.attach(MIMEText(text, 'plain'))

        # Логирование деталей письма
        print(f"Отправка письма от: {RASIM_MAIL}")
        print(f"Отправка письма к: {msg.from_}")
        print(f"Тема письма: {email_msg['Subject']}")
        print(f"Тело письма: {text}")

        # Подключение к SMTP серверу
        with smtplib.SMTP(SMTP_SERVER, 587) as server:
            server.starttls()
            server.login(RASIM_MAIL, RASIM_PSW)
            response = server.sendmail(RASIM_MAIL, msg.from_, email_msg.as_string())
            if response:
                print(f"Ошибка при отправке письма: {response}")
            else:
                print("Email отправлен успешно")
    except Exception as e:
        print(f"Не удалось отправить письмо: {e}")


# если ошибка при загрузке заказа в 1С,тогда делаем письмо непрочитанным
# и отправляем сообщение в телеграм
async def mark_email_as_unread_and_send_sms(msg, sms):
    with MailBox(IMAP_SERVER).login(RASIM_MAIL, RASIM_PSW, 'INBOX') as mailbox:
        mailbox.flag(msg.uid, '\\Seen', False)  # Mark as unread
    sms = f"не удалось загрузить заказ в 1С\nТема письма: {msg.subject}\n{sms}"
    await send_telegram(sms)


# В справочнике Контрагентов наименование одного и того же заказчика может дублироваться
# поэтому получаем Ref_Key заказчика из продаж, по его наименованию и типу продаж(ф1,ф2). Берем последнюю продажу.
async def get_customer_key_from_sales(customer_name: str, is_accounting) -> list:
    url = (f"{URL_CONST}Document_РеализацияТоваровУслуг/"
           "?$inlinecount=allpages"
           "&$format=json"
           "&$orderby=Date desc"
           "&$select=Контрагент_Key,ДоговорКонтрагента_Key"
           f"&$filter=Контрагент/Description eq '{customer_name.strip()}' "
           f"and ОтражатьВБухгалтерскомУчете eq {is_accounting}"
           "&$top=1")
    response = get_data_1c(url)
    if response.get('value'):
        customer_key = response['value'][0]['Контрагент_Key']
        contract_key = response['value'][0]['ДоговорКонтрагента_Key']
        return [customer_key, contract_key]
    return []


# Проверяем есть ли в колонке df пустые значения.
# Если есть, то возвращаем False
async def check_empty_values(df, column_name):
    return df[column_name].isnull().values.any()


# из Catalog_Номенклатура ищем ref_key для по sku.
# т.к. в справочнике могут дублироваться наименования sku, берем с макс code
async def get_nomenclature_key_from_catalog_1c(sku) -> str:
    url = (f"{URL_CONST}Catalog_Номенклатура?$format=json"
           f"&$filter=Description eq '{sku}' "
           f"and DeletionMark eq false"
           "&$orderby=Code desc"
           "&$top=1"
           )
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')[0].get('Ref_Key')
    return ''


# nomenclature_key начинаем искать в продажах,
# т.к. наименования в справочнике 1С могут дублироваться. соответственно будут разные ref_key
# напротив каждого sku вставляем nomenclature_key.
async def get_nomenclature_key(df: pd.DataFrame) -> pd.DataFrame:
    for index, row in df.iterrows():
        global msg
        sku = row.get(NOMENCLATURE_COLUMN)
        sku = sku.replace("&", "%26")  # заменяем & на %26, иначе url не работает
        # ищем вначале в продажах. т.к. в каталоге могут дублироваться наименования sku
        nomenclature_key = await get_nomenclature_key_from_sale(sku)
        if not nomenclature_key:
            # если товара нет в продажах, то берем его ref_key из Catalog_Номенклатура
            nomenclature_key = await get_nomenclature_key_from_catalog_1c(sku)

        if nomenclature_key:
            df.at[index, 'nomenclature_key'] = nomenclature_key
            df.at[index, NOMENCLATURE_COLUMN] = sku
            continue
        else:
            sms = f"!!! Не найден: '{sku}'"
            print(sms)
            await mark_email_as_unread_and_send_sms(msg, sms)
    return df


# переименуем колонку в df c количеством товара
async def rename_quantity_column(df):
    df = df.rename(columns={QUANTITY_COLUMN: 'quantity'})
    return df


# проверяем все ли необходимые колонки есть в df.
async def check_columns_in_df(df):
    columns = [NOMENCLATURE_COLUMN, QUANTITY_COLUMN]
    for column in columns:
        if column not in df.columns:
            return False
    return True


# получаем последнюю цену из 1С, согласно типу цены и номенклатуре
async def get_last_price(nomenclature_key, price_type_key):
    url = (f"{URL_CONST}/InformationRegister_ЦеныНоменклатуры_RecordType"
           "?$orderby=Period desc"
           "&$format=json"
           "&$top=1"
           f"&$filter=ТипЦен_Key eq guid'{price_type_key}' "
           f"and Номенклатура_Key eq guid'{nomenclature_key}'"
           "&$expand=*"
           )
    answer = await get_1c_async(url)
    if answer:
        return answer[0]

    return None


# каждому товару добавляем последнюю цену из справочника 1С
async def add_price_to_df(df):
    for index, row in df.iterrows():
        nomenclature_key = row['nomenclature_key']
        price_type_key = row['ТипЦен_Key']
        price_info = await get_last_price(nomenclature_key, price_type_key)
        if price_info:
            price = price_info['Цена']
            price_coef = price_info['ЕдиницаИзмерения']['Коэффициент']
            if price_coef != 1:
                df.at[index, 'комментарий'] = f"{row.get('комментарий')}; price: {price}; price_coef: {price_coef}"
            price = price / price_coef  # цена в базовой единице измерения
            df.at[index, 'price_coef'] = price_coef
            df.at[index, 'Цена'] = price
        else:
            df.at[index, 'price'] = 0
            df.at[index, 'price_coef'] = 0
    return df


#
# # добавим коэффициенты к каждому товару
# async def get_unit_coefficient(nomenclature_key, base_unit_key):
#     url = (f"{URL_CONST}/Catalog_ЕдиницыИзмерения?$format=json"
#            f"&$filter=Owner eq cast(guid'{nomenclature_key}', 'Catalog_Номенклатура') and "
#            f"ЕдиницаПоКлассификатору_Key eq guid'{base_unit_key}'"
#            f"&$orderby=Коэффициент"
#            f"&$top=1")
#     unit = get_data_1c(url)
#     if unit and unit.get('value'):
#         return unit['value'][0]['Коэффициент']
#     return None


# добавляем к каждому товару единицы измерения и их коэффициент
async def add_units_to_df(df):
    for index, row in df.iterrows():
        nomenclature_key = row['nomenclature_key']
        url = (f"{URL_CONST}/Catalog_Номенклатура?$format=json"
               f"&$filter=Ref_Key eq guid'{nomenclature_key}'"
               "&$expand=*")
        units = get_data_1c(url)
        if units and units.get('value'):
            unit = units['value'][0]
            base_unit = await get_units_key_from_report_units(nomenclature_key, unit['БазоваяЕдиницаИзмерения_Key'])
            base_unit_key = base_unit.get('unit_key')
            coefficient = base_unit.get('coef')
            df.at[index, 'БазоваяЕдиницаИзмерения_Key'] = base_unit_key
            df.at[index, 'БазоваяЕдиницаИзмерения'] = unit['БазоваяЕдиницаИзмерения']['Description']
            # coefficient = await get_unit_coefficient(nomenclature_key, base_unit_key)
            df.at[index, 'БазоваяЕдиницаИзмеренияКоэф'] = coefficient

            df.at[index, 'ЕдиницаИзмеренияМест_Key'] = unit['ЕдиницаИзмеренияМест_Key']
            df.at[index, 'ЕдиницаИзмеренияМест'] = unit['ЕдиницаИзмеренияМест']['Description']
            df.at[index, 'ЕдиницаИзмеренияМестКоэф'] = unit['ЕдиницаИзмеренияМест']['Коэффициент']

            df.at[index, 'ЕдиницаХраненияОстатков_Key'] = unit['ЕдиницаХраненияОстатков_Key']
            df.at[index, 'ЕдиницаХраненияОстатков'] = unit['ЕдиницаХраненияОстатков']['Description']
            df.at[index, 'ЕдиницаХраненияОстатковКоэф'] = unit['ЕдиницаХраненияОстатков']['Коэффициент']
    return df


# добавим остатки товара на складах
async def add_stock_balance_to_df(df):
    df_total = pd.DataFrame()
    for index, row in df.iterrows():
        nomenclature_key = row['nomenclature_key']
        quantity = row['quantity_base_unit']

        # движение товара по документам в разрезе складов и серий
        # берем только те серии, у которых дата годности > MIN_EXPIRATION_DATE
        flow_goods_df = await get_stock_balance(nomenclature_key)

        # группируем для получения остатков по складам и сериям
        stock_series_df = flow_goods_df.groupby(['Номенклатура_Key', 'СерияНоменклатуры_Key', 'Склад_Key'])[
            'stock_quantity'].sum().reset_index()

        # оставляем только те товары у которых остаток > 0
        stock_series_df = stock_series_df[stock_series_df['stock_quantity'] > 0]
        if stock_series_df.empty:
            # Остатков по данному товару нет. Товар не добавляем в заказ
            continue

        # ЕдиницаХраненияОстатковКоэф = 144, БазоваяЕдиницаИзмеренияКоэф = 1
        if row['ЕдиницаХраненияОстатковКоэф'] > row['БазоваяЕдиницаИзмеренияКоэф']:
            stock_series_df['stock_quantity'] = stock_series_df['stock_quantity'] / row['ЕдиницаХраненияОстатковКоэф'] * \
                                                row['БазоваяЕдиницаИзмеренияКоэф']
        else:
            stock_series_df['stock_quantity'] = stock_series_df['stock_quantity'] * row['БазоваяЕдиницаИзмеренияКоэф']

        # делим заказ на серии товаров с учетом срока годности
        stock_series_warehouse_df = await get_series_by_expiration_date(stock_series_df, quantity)

        # объединяем данные о товаре и остатки на складах с учетом серии - срока годности
        # в merged_df может содержаться несколько строк одного и того же товара, но с разными сериями
        # помним, что все сроки годности товара отсортированы по возрастанию и > MIN_EXPIRATION_DATE
        merged_df = pd.merge(stock_series_warehouse_df, row.to_frame().T, on='nomenclature_key')
        if merged_df.empty:
            continue
        df_total = pd.concat([df_total, merged_df], ignore_index=True)
    return df_total


# точка входа
async def main_mail_read():
    while True:
        # Проверяем почту пока есть письма. Если нет, то ожидаем 5 минут и проверяем снова
        # await asyncio.sleep(300) # 5 минут

        # 1) получаем письмо с заказом из почты
        msg = await get_msg_from_email()
        if msg is None:
            # заказов нет
            break

        # 2) извлекаем наименование заказчика из темы письма
        customer_name_from_email = await get_customer_name_from_email(msg)
        print(f"Customer: {customer_name_from_email}")
        if not customer_name_from_email:
            sms = f"в теме почты не найден заказчик: {customer_name_from_email}"
            await mark_email_as_unread_and_send_sms(msg, sms)
            break

        # 3) извлекаем форму (Ф1, Ф2) из темы письма
        form_type = await get_form_type_from_email(msg)
        if form_type is None:
            sms = f"в теме почты не найдена форма заказа (ф1/ф2): {customer_name_from_email}"
            await mark_email_as_unread_and_send_sms(msg, sms)
            break

        # 4) находим customer_key, contract_key заказчика из продаж. Берем последнюю продажу.
        customer_info = await get_customer_key_from_sales(customer_name_from_email, form_type)
        if not customer_info:
            # Если заказчик не найден в продажах, то выходим.
            # Возможно заказчик новый и по нему нет еще продаж в 1С
            # или заказчик не указан/неправильно указан, в теме письма
            sms = f"в 1С не найден заказчик: {customer_name_from_email}"
            print(sms)
            await mark_email_as_unread_and_send_sms(msg, sms)
            break

        # 5) получаем из заказа инф о заказчике из 1С, используя contract_key
        # не ищем напрямую из 1С, т.к. в 1С наименование одного и того же заказчика может дублироваться
        # нам надо правильно определить ref_key, contract_key заказчика
        contract_key = customer_info[1]
        customer_info_from_order_1c = await get_customer_info_from_order_1c(contract_key)
        print(customer_info_from_order_1c)
        if not customer_info_from_order_1c:
            sms = f"в 1С не найден заказчик: {customer_name_from_email}"
            await mark_email_as_unread_and_send_sms(msg, sms)
            break

        # 6) создаем шапку нового заказа. Потом будем объединять с табличной частью
        order_header = await create_order_header(customer_info_from_order_1c, date.today())
        if not order_header:
            sms = f"не удалось создать шапку заказа для загрузки в 1С"
            await mark_email_as_unread_and_send_sms(msg, sms)
            break

        # 7) создаем табличную часть заказа
        # excel файлы из письма с заказанным товаром
        file_paths = await get_filelist_from_email()
        for file_path in file_paths:
            print(f"File: {file_path}")

            # 7.1) читаем excel файл с заказом и загружаем в df
            df_order = pd.read_excel(file_path)
            if df_order.empty:
                sms = f"не удалось прочитать файл заказа"
                await mark_email_as_unread_and_send_sms(msg, sms)
                break

            # 7.1.1) проверяем наличие колонок в df
            if not await check_columns_in_df(df_order):
                sms = f"в файле заказа не найдены колонки: {NOMENCLATURE_COLUMN}, {QUANTITY_COLUMN}"
                print(sms)
                await mark_email_as_unread_and_send_sms(msg, sms)
                break

            # Колонки Excel с наименованием товара и количеством заказа - отбираем только заполненные строки
            df_order = df_order[df_order[NOMENCLATURE_COLUMN].notnull()
                                & df_order[QUANTITY_COLUMN].notnull()].reset_index(drop=True)
            df_order = df_order.rename(columns=str.lower)

            df_order['old_sku'] = df_order[NOMENCLATURE_COLUMN].copy()

            # 7.1.2) переименуем колонку в df c количеством товара на 'quantity'
            df_order = await rename_quantity_column(df_order)

            # 7.2) если excel не содержит колонку 'nomenclature_key', то добавляем ее
            if 'nomenclature_key' not in df_order.columns:
                df_order = await get_nomenclature_key(df_order)

            # 7.3) если в df есть незаполненные nomenclature_key, то выходим
            result = await check_empty_values(df_order, 'nomenclature_key')
            if result:
                sms = f"в файле заказа не найден товар:"
                values = df_order[df_order['nomenclature_key'].isnull()]['sku'].values
                for value in values:
                    sms += f"\n{value}"
                await mark_email_as_unread_and_send_sms(msg, sms)
                break

            # 7.3.1) добавляем к каждому товару единицы измерения и их коэффициент
            df_order = await add_units_to_df(df_order)

            # 7.3.2) переводим количество товара в базовую единицу измерения
            df_order['quantity_base_unit'] = df_order['quantity'] * df_order['ЕдиницаИзмеренияМестКоэф']

            df_order['ТипЦен_Key'] = customer_info_from_order_1c['ТипЦен_Key']

            if PRICE not in df_order.columns:
                # 7.4) добавляем к каждому товару последнюю цену из справочника 1С
                df_order = await add_price_to_df(df_order)

            # 7.4.1) проверяем наличие пустых значений в колонке 'Цена'
            result = await check_empty_values(df_order, 'Цена')
            if result:
                sms = f"в файле заказа не найдена цена:"
                # values = df_order[df_order['nomenclature_key'].isnull()]['sku'].values
                for value in result:
                    sms += f"\n{value}"
                await mark_email_as_unread_and_send_sms(msg, sms)
                break

            # 8) комплектуем df_order недостающими колонками
            df_order = await add_stock_balance_to_df(df_order)
            df_order = await complete_df(df_order)
            if order_header.get('УчитыватьНДС') and order_header.get('СуммаВключаетНДС'):
                df_order['СуммаНДС'] = df_order['Сумма'] * 0.2
            elif order_header.get('УчитыватьНДС') and not order_header.get('СуммаВключаетНДС'):
                df_order['СуммаНДС'] = df_order['Сумма'] / 6
                df_order['Цена'] = df_order['Цена'] / 1.2
                df_order['Сумма'] = df_order['Сумма'] / 1.2
            elif not order_header.get('УчитыватьНДС'):
                df_order['СуммаНДС'] = 0
            print(df_order)

            # 9) создаем табличную часть заказа для загрузки в 1С
            order_table = await create_order_table(df_order)

            # 10) объединяем шапку заказа и табличную часть
            order_header['Товары'] = order_table
            print(order_header)

            # 11) загружаем заказ в 1С
            result = await upload_order_to_1c(order_header)
            if result:
                result = (f"{result}\n"
                          f"Тема письма: {msg.subject}")

                # Заказ успешно загружен. Помечаем письмо как прочитанное
                with MailBox(IMAP_SERVER).login(RASIM_MAIL, RASIM_PSW, 'INBOX') as mailbox:
                    mailbox.flag(msg.uid, '\\Seen', True)  # Mark as read

            else:
                result = (f"не удалось загрузить заказ в 1С\n"
                          f"Тема письма: {msg.subject}")
                await mark_email_as_unread_and_send_sms(msg, result)

            # 12) отправляем результат на почту и телеграм
            await send_telegram(result)
            await send_email(msg, result)
        break


def run_main():
    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    loop.run_until_complete(main_mail_read())


if __name__ == '__main__':
    print("Start", datetime.now())
    run_main()
    print("End", datetime.now())
