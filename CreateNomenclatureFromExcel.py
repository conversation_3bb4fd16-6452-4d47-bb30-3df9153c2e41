"""
в 1С создает карточки номенклатуры из файла Excel
данный скрипт заточен под Slim. Ед.изм пока только блок/банка, коробка. Т.е. 2 ед.изм
в файле Excel должны быть следующие колонки:"""

import asyncio
from calendar import c
from datetime import datetime
from urllib.parse import urlencode
import json
import pandas as pd
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta

from GetCorrectNomenclatureName import get_correct_nomenclature_name
from async_Postgres import URL_CONST, send_telegram
from async_request import get_data_1c, patch_data_1c, post_data_1c

# наименование колонки в Excel с наименованием номенклатуры
NOMENCLATURE_COLUMN = "sku_ua"

NEED_COLUMNS_IN_EXCEL = [
    "folder",  # в какую папку справочника загружаем
    "sku_tr",  # sku производителя
    NOMENCLATURE_COLUMN,  # sku получателя
    "units",  # ед.изм
    "quantity",  # количество
    "date_until",  # дата годности
    "weight_net",  # вес нетто
    "weight_gross",  # вес брутто
    "volume_m3",  # объем за 1кор (коробку)
    "price",  # цена
    "amount",  # сумма
    "grm",  # грамм в 1ед
    "pcs_in_block",  # pcs в блоке
    "block_in_box",  # блоков в крб
    "market_box",  # ящик цена сети
    "market_block",  # блок цена сети
    "market_pcs",  # pcs цена сети
    "rm_box",  # ящик цена РМ
    "rm_block",  # блок цена РМ
    "rm_pcs",  # шт цена РМ
]

# Настройка отображения всех колонок
pd.set_option("display.max_columns", None)

# Устанавливаем максимальную ширину колонки на None (без ограничения)
pd.set_option("display.max_colwidth", None)


# Функция для создания URL с закодированными параметрами запроса
# иначе, если в наименовании товара есть знаки типа '&', то запрос не выполнится
def create_url(base_url, params):
    # Убедитесь, что символ $ не закодирован
    query_string = urlencode(params, safe=":$")
    return f"{base_url}?{query_string}"


# получать дату последнего дня месяца
def last_day_of_month(any_date) -> datetime.date:
    any_date = parse(any_date, dayfirst=True)
    year = any_date.year
    month = any_date.month + 1
    new_date = (datetime(year, month, 1) - relativedelta(days=1)).date()
    return new_date


# получать дату первого дня месяца
def first_day_of_month(any_date) -> datetime.date:
    any_date = parse(any_date, dayfirst=True)
    year = any_date.year
    month = any_date.month
    new_date = datetime(year, month, 1).date()
    return new_date


# преобразовать текст типа "02.24-02.26" в даты и получить количество месяцев хранения
def get_dates_from_text(stext: str) -> datetime.date:
    stext = stext.replace(" ", "")
    ldates = stext.split("-")
    if len(ldates) != 2:
        return None
    ldates = [f"1.{date}" for date in ldates]
    first_day = first_day_of_month(ldates[0])
    last_day = last_day_of_month(ldates[1])
    quantity_month_between_dates = get_months_between_dates(first_day, last_day)
    return quantity_month_between_dates, last_day


# Вычисляем количество месяцев между двумя датами. Нужно для месяцев хранения
def get_months_between_dates(date1: datetime.date, date2: datetime.date) -> int:
    months = (date2.year - date1.year) * 12 + date2.month - date1.month
    return int(months)


# проверка существования файла
def check_file_exists(s_full_path_to_file: str) -> bool:
    try:
        with open(s_full_path_to_file):
            return True
    except FileNotFoundError:
        return False


# колонку 'column_name' проведем чз функцию get_correct_nomenclature_name для получения правильного названия
async def get_correct_sku1c_name(df: pd.DataFrame) -> pd.DataFrame:
    for index, row in df.iterrows():
        sku = row.get(NOMENCLATURE_COLUMN).strip()
        nomenclature_key = row.get("nomenclature_key")
        if not nomenclature_key:
            sku = get_correct_nomenclature_name(sku)
        df.at[index, NOMENCLATURE_COLUMN] = sku
    return df


# загружаем в df файл Excel
async def df_load_excel(s_full_path_to_xsl_file: str) -> pd.DataFrame:
    df = pd.DataFrame()
    try:
        df = pd.read_excel(s_full_path_to_xsl_file, engine="openpyxl")
        df.columns = (
            df.columns.str.lower().str.strip()
        )  # приводим названия колонок к нижнему регистру и удаляем пробелы
    except Exception as e:
        print(str(e))

    return df


# Из Catalog_Номенклатура ищем ref_key для по sku.
# Т.к. в справочнике могут дублироваться наименования sku, берем с макс code
async def get_nomenclature_key_from_catalog_1c(sku, folder_key) -> str:
    url = (
        f"{URL_CONST}Catalog_Номенклатура?$format=json"
        f"&$filter=Parent_Key eq guid'{folder_key}' "
        f"and Description eq '{sku}' "
        f"and DeletionMark eq false"
        "&$orderby=Code desc"
        "&$top=1"
    )
    result = get_data_1c(url)
    if result and result.get("value"):
        return result.get("value")[0].get("Ref_Key")
    return ""


# ищем nomenclature_key в продажах
async def get_nomenclature_key_from_sale(sku: str):
    # если отформатированное sku не найден, то берем старое значение
    url = (
        f"{URL_CONST}Document_РеализацияТоваровУслуг_Товары/?$format=json"
        f"&$top=1"
        f"&$select=Номенклатура_Key"
       f"&$filter=substringof('{sku}', Номенклатура/Description)"
    )
        # f"&$filter=Номенклатура/Description eq '{sku}'"
    nomenclature_key = ""
    result = get_data_1c(url)
    if result and result.get("value"):
        # если товар есть в продажах, то берем его ref_key
        nomenclature_key = result.get("value")[0].get("Номенклатура_Key")

    return nomenclature_key


# nomenclature_key начинаем искать в продажах,
# т.к. наименования в справочнике 1С могут дублироваться. соответственно будут разные ref_key
# напротив каждого sku вставляем nomenclature_key.
async def get_nomenclature_key(df: pd.DataFrame) -> pd.DataFrame:
    nomenclature_key = ""
    for index, row in df.iterrows():
        sku_new = row.get(NOMENCLATURE_COLUMN)
        sku_old = row.get("old_sku")
        sku_dict = {"sku_old": sku_old, "sku_new": sku_new}
        key = ""
        sku = ""
        for key, sku in sku_dict.items():
            sku = sku.strip()
            sku = sku.replace("&", "%26")  # заменяем & на %26, иначе url не работает
            # ищем вначале в продажах. т.к. в каталоге могут дублироваться наименования sku
            nomenclature_key = await get_nomenclature_key_from_sale(sku)
            if nomenclature_key:
                df.at[index, "is_sale"] = True  # sku есть в продажах
            else:
                # если товара нет в продажах, то берем его ref_key из Catalog_Номенклатура
                df.at[index, "is_sale"] = False
                folder_key = row["parent_key"]
                nomenclature_key = await get_nomenclature_key_from_catalog_1c(
                    sku, folder_key
                )

            if nomenclature_key:
                df.at[index, "nomenclature_key"] = nomenclature_key
                df.at[index, NOMENCLATURE_COLUMN] = sku
                break

        if not nomenclature_key:
            print(f"!!! Не найден: {key} '{sku}'")
    return df


# добавляем к df конечный срок годности
async def df_insert_end_date(df: pd.DataFrame) -> pd.DataFrame:
    df["months"] = 0
    for index, row in df.iterrows():
        sdate = row["date_until"]
        if pd.isna(sdate):
            continue
        quantity_months, sdate = get_dates_from_text(sdate)
        df.at[index, "months"] = quantity_months  # количество месяцев хранения
        df.at[index, "expiry_date"] = sdate

    # df['months'] = df['months'].astype(int)
    return df


# обновляем данные в Catalog_СерииНоменклатуры данными о периоде хранения (количество месяцев)
async def update_months_in_1c(ref_key: str, months: int):
    data = {"КоличествоМесяцевХранения": int(months)}
    url = f"{URL_CONST}Catalog_СерииНоменклатуры(guid'{ref_key}')?$format=json"
    result = patch_data_1c(url, data)
    return result


# перед загрузкой в Catalog_СерииНоменклатуры проверим, есть ли такой срок годности у товара
# будем брать товар с максимальным датой в данном год/месяце
async def check_sku_in_1c(row: pd.Series) -> str:
    owner_key = row.get("nomenclature_key")
    expiry_date = row.get("expiry_date").strftime("%Y-%m-%dT00:00:01")
    if owner_key is None or expiry_date is None:
        return ""
    url = (
        f"{URL_CONST}Catalog_СерииНоменклатуры?$format=json"
        f"&$filter=Owner_Key eq guid'{owner_key}' "
        f"and year(СрокГодности) eq year(datetime'{expiry_date}') "
        f"and month(СрокГодности) eq month(datetime'{expiry_date}') "
        "&$orderby=СрокГодности desc"
        "&$top=1"
    )
    # f"and day(СрокГодности) eq day(datetime'{expiry_date}')")
    result = get_data_1c(url)
    if result and result.get("value"):
        # товар с таким сроком годности уже есть в 1С
        # значит будем только обновлять период хранения
        return result.get("value")[0].get("Ref_Key")
    return ""


# создаем данные для загрузки в Catalog_СерииНоменклатуры
async def create_data_for_upload_to_1c(row: pd.Series) -> dict:
    # создаем в случаем если такой товар уже есть в 1С, но с другим сроком годности
    print(
        f"Catalog_СерииНоменклатуры. sku: {row.get(NOMENCLATURE_COLUMN)}, "
        f"nomenclature_key: {row.get('nomenclature_key')}"
    )

    # проверяем есть ли такой срок хранения у данного товара
    result = await check_sku_in_1c(row)
    if result:
        # товар с таким сроком годности уже есть в 1С
        # обновляем только период хранения
        result = await update_months_in_1c(result, row.get("months"))
        if result:
            print(
                f"обновлена длительность хранения у {row.get(NOMENCLATURE_COLUMN)} на {row.get('months')}"
            )
        return {}  # None - потому что товар уже есть в 1С и нужная информация обновлена

    data = {
        "Owner_Key": row.get("nomenclature_key"),
        "Description": row.get("expiry_date").strftime("%d.%m.%Y"),
        "СрокГодности": row.get("expiry_date").strftime("%Y-%m-%dT%H:%M:%S"),
        "КоличествоМесяцевХранения": row.get("months"),
    }
    # data = json.dumps(data, ensure_ascii=False)
    return data


# добавляем в 1С срок годности и количество месяцев хранения
async def add_expiry_date_and_months_to_1c(row: pd.Series):
    url = f"{URL_CONST}Catalog_СерииНоменклатуры?$format=json"
    data = await create_data_for_upload_to_1c(row)  # создаем данные для загрузки
    if not data:
        # товар с таким сроком годности уже есть в 1С
        return True

    try:
        result = post_data_1c(url, data)
        if result and result.get("Ref_Key"):
            date = row.get("expiry_date").strftime("%d.%m.%Y")
            print(
                f"Добавлено: {row.get(NOMENCLATURE_COLUMN)}, {date}, мес: {row.get('months')}"
            )
            return True
        else:
            print(
                f"Не добавились сроки годности и мес хранения. {row.get(NOMENCLATURE_COLUMN)}"
            )
            return None
    except Exception as e:
        print(f"Ошибка: при добавлении сроков годности и мес хранения.\n{e}")

    return None


# Ref_Key единицы измерения нужен для Catalog_ЕдиницыИзмерения
async def get_unit_key(unit_name: str) -> dict:
    # удаляем все не буквенно-цифровые символы
    unit_name = "".join(e for e in unit_name if e.isalnum())
    url = (
        f"{URL_CONST}Catalog_КлассификаторЕдиницИзмерения?$format=json"
        f"&$filter=substringof('{unit_name}',Description)"
    )
    result = get_data_1c(url)
    unit_dict = {}
    if result and result.get("value"):
        ref_key = result.get("value")[0].get("Ref_Key")
        description = result.get("value")[0].get("Description")
        unit_dict = {"unit_key": ref_key, "unit_name": description}
    return unit_dict


# # получим все Ref_Key единиц-измерения для данного товара
# async def get_units_key(row: pd.Series) -> list:
#     units = row.get('units')
#     units = units.split(',')  # разделяем по запятой, т.к. в колонке данные хранятся в виде "шт,банк,кор"
#     unit_keys = []
#     for i, unit in enumerate(units):
#         result = await get_unit_key(unit)
#         if result:
#             unit_keys.append(result)
#             if i == 0:
#                 # базовая единица измерения. Она должна быть в начале списка ['шт', 'кор', 'блок'], т.е 'шт'
#                 base_unit = {'base_unit_key': result.get('unit_key'), 'base_unit_name': result.get('unit_name')}
#                 unit_keys.append(base_unit)
#             elif i == len(units) - 1:
#                 # max единица измерения. Она должна быть в конце списка ['шт', 'блок', 'кор'], т.е 'кор'
#                 last_unit = {'last_unit_key': result.get('unit_key'), 'last_unit_name': result.get('unit_name')}
#                 unit_keys.append(last_unit)
#     return unit_keys  # список словарей unit_key, unit_name [{}]


# получим все Ref_Key единиц-измерения для данного товара
async def get_units_key(row: pd.Series) -> list:
    units = row.get("units")
    units = units.split(
        ","
    )  # разделяем по запятой, т.к. в колонке данные хранятся в виде "шт,блок,кор"
    unit_keys = []
    sku = row.get(NOMENCLATURE_COLUMN)
    if len(units) not in [2, 3]:
        sms = f"{sku}\nКоличество ед.изм должно быть in [2, 3]"
        print(sms)
        await send_telegram(sms)
    elif len(units) == 2:
        result = await get_unit_key(units[0])
        # Базовая единица измерения.
        # Она должна быть в начале списка ['блок', 'кор'], т.е 'блок'
        base_unit = {
            "base_unit_key": result.get("unit_key"),
            "base_unit_name": result.get("unit_name"),
            "base_unit_coefficients": 1,
        }
        unit_keys.append(base_unit)

        # max единица измерения.
        # Она должна быть в конце списка ['блок', 'кор'], т.е 'кор'
        result = await get_unit_key(units[-1])
        last_unit = {
            "last_unit_key": result.get("unit_key"),
            "last_unit_name": result.get("unit_name"),
            "last_unit_coefficients": row.get("block_in_box"),
        }
        unit_keys.append(last_unit)
    elif len(units) == 3:
        result = await get_unit_key(units[0])
        # базовая единица измерения.
        # Она должна быть в начале списка ['шт', 'блок', 'кор'], т.е 'шт'
        base_unit = {
            "base_unit_key": result.get("unit_key"),
            "base_unit_name": result.get("unit_name"),
            "base_unit_coefficients": 1,
        }
        unit_keys.append(base_unit)

        result = await get_unit_key(units[1])
        # промежуточная единица измерения.
        # Она должна быть в середине списка ['шт', 'блок', 'кор'], т.е 'блок'
        middle_unit = {
            "unit_key": result.get("unit_key"),
            "unit_name": result.get("unit_name"),
            "unit_coefficients": row.get("pcs_in_block"),
        }
        unit_keys.append(middle_unit)

        # max единица измерения.
        # Она должна быть в конце списка ['шт', 'блок', 'кор'], т.е 'кор'
        result = await get_unit_key(units[-1])
        last_unit = {
            "last_unit_key": result.get("unit_key"),
            "last_unit_name": result.get("unit_name"),
            "last_unit_coefficients": row.get("block_in_box") * row.get("pcs_in_block"),
        }
        unit_keys.append(last_unit)

    return unit_keys  # список словарей unit_key, unit_name [{}]


# проверяем, существует в Catalog_ЕдиницыИзмерения данный товар с данной единицей измерения
# у одна и та же ед.изм (Н:block_in_box) может содержать разное количество. Все зависит от товара
async def check_unit_key(nomenclature_key, unit_key) -> str:
    url = (
        f"{URL_CONST}Catalog_ЕдиницыИзмерения?$format=json"
        f"&$filter=Owner eq cast(guid'{nomenclature_key}', 'Catalog_Номенклатура') "
        f"and ЕдиницаПоКлассификатору_Key eq guid'{unit_key}'"
    )
    result = get_data_1c(url)
    if result and result.get("value"):
        return result.get("value")[0].get("Ref_Key")
    return ""


# получим коэффициент для единицы измерения. количество блоков в кор
async def get_coefficient(row: pd.Series) -> int:
    unit_name = row.get("unit_name")
    unit_count = row.get("units").split(",")
    if unit_name == "кор" and len(unit_count) == 3:
        # если ед.изм ['шт', 'блок', 'кор'], то коэффициент = pcs_in_block * block_in_box
        return row.get("pcs_in_block") * row.get("block_in_box")
    elif unit_name == "кор" and len(unit_count) == 2:
        # если ед.изм ['блок', 'кор'], то коэффициент = block_in_box
        return row.get("block_in_box")
    elif unit_name in ["блок", "банк"] and len(unit_count) == 2:
        # если ед.изм ['блок', 'кор'], то коэффициент = 1
        return 1
    elif unit_name in ["блок", "банк"] and len(unit_count) == 3:
        # если ед.изм ['шт', 'блок', 'кор'], то коэффициент = row.get('pcs_in_block')
        return row.get("pcs_in_block")
    elif unit_name == "шт":
        # если ед.изм ['шт', 'блок', 'кор'], то коэффициент = 1
        return 1
    else:
        raise ValueError(f"!!! Не найден коэффициент для {unit_name}")


# получим вес для единицы измерения
async def get_weight(row: pd.Series) -> float:
    unit_name = row.get("unit_name")
    if unit_name == "кор":
        return row.get("grm") * row.get("pcs_in_block") * row.get("block_in_box")
    elif unit_name in ["блок", "банк"]:
        return row.get("grm") * row.get("pcs_in_block")
    elif unit_name in ["шт"]:
        return row.get("grm")

    return 0


# получим объем для единицы измерения
async def get_volume(row: pd.Series) -> float:
    # в таблице из.изм дается для коробки
    unit_name = row.get("unit_name")
    if unit_name == "кор":
        return row.get("volume_m3")
    elif unit_name in ["блок", "банк"]:
        return row.get("volume_m3") / row.get("block_in_box")
    elif unit_name in ["шт"]:
        return row.get("volume_m3") / (
            row.get("pcs_in_block") * row.get("block_in_box")
        )

    return 0


# создаем данные для загрузки в Catalog_ЕдиницыИзмерения
async def create_data_for_upload_units_to_1c(row: pd.Series) -> dict:
    unit_name = row.get("unit_name")
    coefficient = await get_coefficient(row)
    weight = await get_weight(row) / 1000  # вес в кг
    volume = await get_volume(row)  # объем в м3
    volume = round(volume, 3)  # округляем до 4 знаков после запятой
    data = {
        "Owner": row.get("nomenclature_key"),
        "Owner_Type": "StandardODATA.Catalog_Номенклатура",
        "Description": unit_name,
        "ЕдиницаПоКлассификатору_Key": row.get("unit_key"),
        "Коэффициент": coefficient,
        "Объем": volume,
        "Вес": weight,
    }
    return data


# загружаем данные для в Catalog_ЕдиницыИзмерения (связка номенклатура + ед.изм)
async def post_data_for_upload_nomenclature_units(data) -> dict:
    url = f"{URL_CONST}Catalog_ЕдиницыИзмерения?$format=json"
    result = post_data_1c(url, data)
    return result


# проверим, все ли единицы измерения есть в Catalog_ЕдиницыИзмерения у данного товара
# если нет, то добавим их
async def create_nomenclature_units(row: pd.Series):
    nomenclature_key = row.get("nomenclature_key")
    sku = row.get(NOMENCLATURE_COLUMN).strip()
    units_list = await get_units_key(
        row
    )  # получаем список словарей unit_key, unit_name
    if not units_list:
        raise ValueError(
            f"!!! Не найдены единицы измерения для {sku}"
        )  # !!! так не должно быть

    # base_unit_key(min ед.изм), last_unit_key(макс ед.изм) не берем. Т.к. они уже дублируются под ключем 'unit_key'
    units_list_items = [item for item in units_list]

    # ед.изм у нас несколько. Если нет связки (nomenclature_key, unit_key), то добавляем
    for unit in units_list_items:
        # добавляем в row данные: unit_key, unit_name
        r = dict(row)
        # unit_key = unit.get('unit_key')
        unit_key = list(unit.values())[0]
        unit_name = list(unit.values())[1]
        unit_data = {"unit_key": unit_key, "unit_name": unit_name}
        r.update(unit_data)
        row = pd.Series(r)
        result = await check_unit_key(nomenclature_key, unit_key)
        if result:
            print(f"Такая связка ({sku}, {unit_name}) уже есть. Пропускаем")
            continue  # такая связка (nomenclature_key, unit_key) уже есть. Пропускаем

        # создаем данные для загрузки в Catalog_ЕдиницыИзмерения
        data = await create_data_for_upload_units_to_1c(row)
        if data:
            result = await post_data_for_upload_nomenclature_units(data)
            if result and result.get("Ref_Key"):
                print(f"Добавлена единица измерения {unit_name} для {sku}")
            else:
                raise ValueError(
                    f"!!! Не удалось добавить единицу измерения {unit_name} для {sku}"
                )
    return True


# отберем данные у которых nomenclature_key = None
async def get_data_without_nomenclature_key(df: pd.DataFrame) -> pd.DataFrame:
    df = df[df["nomenclature_key"].isnull()].reset_index(drop=True)  # новый товар
    # df = df[df['nomenclature_key'].notnull()].reset_index(drop=True)  # товар уже есть в 1С
    return df


async def get_data_new(row: pd.Series) -> dict:
    return {
        "Parent_Key": row.get("parent_key"),
        "Description": row.get(NOMENCLATURE_COLUMN),
        "sku": row["sku_tr"],
        "Комментарий": "bot",
        "НаименованиеПолное": row.get(NOMENCLATURE_COLUMN),
        "ВидНоменклатуры_Key": "e5827eaf-8f86-11e6-80c4-c936aa9c817c",  # Товар
        "БазоваяЕдиницаИзмерения_Key": row["base_unit_key"],
    }


async def get_data_update(row: pd.Series) -> dict:
    nomenclature_key = row.get("nomenclature_key")
    base_unit_key = row.get("base_unit_key")
    last_unit_key = row.get("last_unit_key")
    base_unit_key_from_catalog = await check_unit_key(nomenclature_key, base_unit_key)
    last_unit_key_from_catalog = await check_unit_key(nomenclature_key, last_unit_key)

    return {
        "DeletionMark": False,
        "Predefined": False,
        "IsFolder": False,
        "ЕдиницаХраненияОстатков_Key": base_unit_key_from_catalog,
        "ЕдиницаДляОтчетов_Key": last_unit_key_from_catalog,
        "ЕдиницаИзмеренияМест_Key": last_unit_key_from_catalog,
        "КоличествоМесяцевХранения": row.get("months", 0),
        "Весовой": False,
        "ВесовойКоэффициентВхождения": "0",
        "ВестиПартионныйУчетПоСериям": False,
        "ВестиУчетПоСериям": True,  # True - т.к. у нас есть срок годности. иначе не отображается закладка "Серии"
        "ВестиУчетПоХарактеристикам": False,
        "ТранспортнаяУслуга": False,
        "Набор": False,
        "ЛьготаНДС": "",
        "СтавкаНДС": "НДС20",
        "Услуга": False,
        "sku": row["sku_tr"],
        "ИзмеряетсяТолькоВСуммовомВыражении": False,
        "ТекстДляПечатиВКолонкеКоличествоНалоговойНакладной": "",
        "БланкСтрогогоУчета": False,
        "УчитываетсяПоНоминальнойСтоимости": False,
        "ВестиСерийныеНомера": False,
        "Комплект": False,
        "Комментарий": "bot",
        "ПодакцизныйТовар": False,
        "КодЛьготы": "",
        "Действует": False,
        "УказыватьШтрихкодАкцизнойМаркиПриПечатиЧека": False,
    }


# создаем карточки номенклатуры. Минимальные данные
async def create_nomenclature(row: pd.Series, is_new=False) -> str:
    nomenclature_key = row.get("nomenclature_key")
    sku = row.get(NOMENCLATURE_COLUMN).strip()
    if not nomenclature_key or pd.isna(nomenclature_key):
        # создаем новую номенклатуру
        url = f"{URL_CONST}Catalog_Номенклатура?$format=json"
        data = await get_data_new(row)  # минимальные данные для создания
        result = post_data_1c(url, data)
        if result and result.get("Ref_Key"):
            print(f"Добавлена номенклатура {sku}")
            return result.get("Ref_Key")
        else:
            print(f"!!! Не удалось добавить номенклатуру {sku}")
    elif is_new:
        print(f"обновляются данные по nomenclature_key: {nomenclature_key}")
        # если товар новый и создан ботом, то обновляем данные. Т.к. некоторые данные при создании еще отсутствовали
        url = f"{URL_CONST}Catalog_Номенклатура(guid'{nomenclature_key}')?$format=json"
        data = await get_data_update(row)  # формируем данные для обновления
        result = patch_data_1c(url, data)
        if result and result.get("Ref_Key"):
            print(f"Обновлены данные для {sku}")
            return result.get("Ref_Key")
        else:
            print(f"!!! Не удалось обновить данные для '{sku}'")

    return ""


# Добавляем последние цены.
# Тип цены: ДИСТРИБ’ЮТОР 2022 01.11
# Валюта: грн
# Единица измерения: базовая
async def add_last_price(row: pd.Series):
    last_price = 0
    price_type_key = "911e681c-59c0-11ed-8150-001dd8b740bc"  # ДИСТРИБ’ЮТОР 2022 01.11
    currency_key = "3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c"  # грн
    nomenclature_key = row.get("nomenclature_key")
    last_unit_key = row.get("last_unit_key")
    if last_unit_key == "00000000-0000-0000-0000-000000000000":
        print(
            f"!!! Не найдена базовая единица измерения для {row.get(NOMENCLATURE_COLUMN)}"
        )
        return False
    url = (
        f"{URL_CONST}InformationRegister_ЦеныНоменклатуры_RecordType?$orderby=Period desc"
        "&$format=json"
        "&$top=1"
        f"&$filter=ТипЦен_Key eq guid'{price_type_key}' "
        f"and Валюта_Key eq guid'{currency_key}' "
        f"and ЕдиницаИзмерения_Key eq guid'{last_unit_key}' "
        f"and Номенклатура_Key eq guid'{nomenclature_key}'"
    )
    result = get_data_1c(url)
    if result and result.get("value"):
        last_price = result.get("value")[0].get("Цена")
    else:
        print(f"!!! Не найдена цена для {row.get(NOMENCLATURE_COLUMN)},\n{url}")
    return last_price


# если карточка номенклатуры уже есть, находим базовую единицу измерения
# нужна для извлечения цены
async def get_base_unit_key(nomenclature_key: str) -> list:
    url = (
        f"{URL_CONST}Catalog_Номенклатура(guid'{nomenclature_key}')?$format=json"
        f"&$select=БазоваяЕдиницаИзмерения_Key, ЕдиницаХраненияОстатков_Key"
    )
    result = get_data_1c(url)
    if result and result.get("БазоваяЕдиницаИзмерения_Key"):
        base_unit_key = result.get("БазоваяЕдиницаИзмерения_Key")
        last_unit_key = result.get("ЕдиницаХраненияОстатков_Key")
        return [base_unit_key, last_unit_key]
    else:
        print(f"!!! Не найдена базовая единица измерения для {nomenclature_key}")
    return []


# получим список nomenclature_key, созданных ботом. В комментарии указано 'bot'
async def get_nomenclature_key_bot():
    url = (
        f"{URL_CONST}Catalog_Номенклатура?$format=json"
        f"&$inlinecount=allpages&$filter=substringof('bot', Комментарий)&$select=Description,Ref_Key"
    )
    result = get_data_1c(url)
    if result and result.get("value"):
        return result.get("value")
    return []


# создаем карточки номенклатуры (если нет) и обновляем данные
async def create_or_update_nomenclature(df):
    for index, row in df.iterrows():
        last_price = 0
        sku = row.get(NOMENCLATURE_COLUMN).strip()
        nomenclature_key = row.get("nomenclature_key")
        print(f"{sku}, {row.get('nomenclature_key')}")
        if (
            not nomenclature_key
            or pd.isna(nomenclature_key)
            or nomenclature_key == "None"
        ):
            # создаем карточку номенклатуры
            units_list = await get_units_key(
                row
            )  # получаем список словарей unit_key, unit_name из df
            base_unit_key = [
                unit for unit in units_list if "base_unit_key" in unit.keys()
            ]
            last_unit_key = [
                unit for unit in units_list if "last_unit_key" in unit.keys()
            ]
            r = dict(row)
            r.update(base_unit_key[0])
            r.update(last_unit_key[0])
            row = pd.Series(r)
            df.at[index, "base_unit_key"] = base_unit_key[0].get("unit_key")
            df.at[index, "last_unit_key"] = last_unit_key[0].get("unit_key")
            nomenclature_key = await create_nomenclature(row)
            if nomenclature_key:
                nomenclature = {"nomenclature_key": nomenclature_key}
                r = dict(row)
                r.update(nomenclature)
                row = pd.Series(r)
        else:
            # обновляем данные
            base_unit_key, last_unit_key = await get_base_unit_key(nomenclature_key)
            r = dict(row)
            r.update({"base_unit_key": base_unit_key})
            r.update({"last_unit_key": last_unit_key})
            row = pd.Series(r)
            df.at[index, "base_unit_key"] = base_unit_key
            df.at[index, "last_unit_key"] = last_unit_key

        if (
            not row.get("nomenclature_key")
            or pd.isna(row.get("nomenclature_key"))
            or row.get("nomenclature_key") == "None"
        ):
            raise ValueError(
                f"!!! Не удалось создать номенклатуру {row.get(NOMENCLATURE_COLUMN)}"
            )

        # добавляем в 1С срок годности и количество месяцев хранения
        if pd.isna(row.get("expiry_date")):
            sms = f"!!! Не найден срок годности для {sku}"
            print(sms)
            await send_telegram(sms)
        else:
            result = await add_expiry_date_and_months_to_1c(row)
            if not result:
                raise ValueError(
                    f"!!! Не удалось добавить срок годности и месяцы хранения для {sku}"
                )

        # обновляем данные
        await create_nomenclature_units(row)

        # если товар участвовал в продаже, добавляем цену
        if row.get("is_sale"):
            # добавляем цену, если есть
            last_price = await add_last_price(row)  # ДИСТРИБ’ЮТОР 2022 01.11
            price = {"last_price": last_price}
            r = dict(row)
            r.update(price)
            row = pd.Series(r)
            df.at[index, "last_price"] = last_price

        # повторно вызываем функцию.
        # т.к. в row добавились новые данные, которые не были занесены при создании карточки
        # created_bot = await get_nomenclature_key_bot()
        # if nomenclature_key in created_bot:
        if nomenclature_key and not row.get("is_sale"):
            # Карточка номенклатуры создана ботом. Обновляем данные
            if isinstance(base_unit_key, str) and isinstance(last_unit_key, str):
                if "00000000-0000-0000-0000-000000000000" in [base_unit_key, last_unit_key]:
                    raise ValueError(f"!!! Не найдена базовая единица измерения для {sku}")
            elif "00000000-0000-0000-0000-000000000000"in [
                    base_unit_key[0]["base_unit_key"],
                    last_unit_key[0]["last_unit_key"],
                ]:
                raise ValueError(f"!!! Не найдена базовая единица измерения для {sku}")

            is_new = True
        else:
            is_new = False
            if pd.isna(row.get("expiry_date")):
                sms = f"!!! Не найден срок годности для {sku}"
                print(sms)
                await send_telegram(sms)
            else:
                # Карточка номенклатуры создана НЕ ботом. Обновляем данные сроки годности и месяцы хранения
                await add_expiry_date_and_months_to_1c(row)

        await create_nomenclature(row, is_new)

    # возвращаем df с обновленными данными. т.к в дальнейшем будем использовать его(загружать цены...)
    return df


# проверяем есть ли все необходимые колонки в Excel.
# нам нужны NEED_COLUMNS_IN_EXCEL
def check_columns_in_excel(df: pd.DataFrame) -> list:
    columns = df.columns
    exclude_columns = []
    for column in NEED_COLUMNS_IN_EXCEL:
        if column not in columns:
            exclude_columns.append(column)
    return exclude_columns


async def get_parent_key(folder: str) -> str:
    url = (
        f"{URL_CONST}Catalog_Номенклатура"
        f"?$format=json"
        f"&$filter=DeletionMark eq false"
        f" and substringof('{folder}', Description)"
        f" and IsFolder eq true"
        f"&$top=1"
    )

    result = get_data_1c(url)
    if result and result.get("value"):
        return result.get("value")[0].get("Ref_Key")
    return ""


# напротив каждого sku вставляем parent_key из 1с. (Ref_Key Папки в которую загружаем товар)
async def df_insert_nomenclature_parent_key(df: pd.DataFrame) -> pd.DataFrame:
    folder = df["folder"][0]
    parent_key = await get_parent_key(folder)
    if not parent_key:
        raise ValueError(f"!!! Не найден parent_key для {folder}")
    df["parent_key"] = parent_key
    return df


# Проверяем открыт ли файл
def is_excel_file_open(file_path):
    try:
        # Try to open the file in exclusive mode
        with open(file_path, "r+"):
            pass
    except (IOError, PermissionError):
        # If an IOError or PermissionError is raised, the file is open
        return True
    return False


# точка входа
# порядок выполнения функций не менять
async def main_create_nomenclature_in_1c_async(s_full_path_to_excel: str):
    if not check_file_exists(s_full_path_to_excel):
        print(f"Файл {s_full_path_to_excel} не найден")
        return

    if is_excel_file_open(s_full_path_to_excel):
        print(f"Файл {s_full_path_to_excel} открыт")
        return

    # Перед загрузкой надо физически проверить чтобы в Excel была колонка NOMENCLATURE_COLUMN
    df = await df_load_excel(s_full_path_to_excel)

    # Заменяем пустые ячейки в числовых колонках нулями
    numeric_columns = df.select_dtypes(include=["number"]).columns
    df[numeric_columns] = df[numeric_columns].fillna(0)

    # Выводим список отсутствующие из необходимых колонок в Excel
    missing_fields = check_columns_in_excel(df)
    if missing_fields:
        print(f"!!! Не хватает колонок в Excel: {missing_fields}")
        return
    df["old_sku"] = df[NOMENCLATURE_COLUMN].copy()

    # удаляем строки, где NOMENCLATURE_COLUMN = None (строку с итоговыми данными или нет перевода)
    df = df[df[NOMENCLATURE_COLUMN].notnull()].reset_index(drop=True)

    # Напротив каждого sku вставляем parent_key из 1с. (Ref_Key Папки в которую загружаем товар)
    # В нее будем загружать новые товары
    df = await df_insert_nomenclature_parent_key(df)

    # Преобразуем название номенклатуры. Чтобы все были одного формата(убираем двойные пробелы...)
    df = await get_correct_sku1c_name(df)

    # теперь у нас есть 2 колонки со старой и новым наименованием sku
    # напротив каждого sku вставляем nomenclature_key из 1с (если есть)
    df = await get_nomenclature_key(df)

    # отберем данные у которых nomenclature_key = None. Т.е. новые товары
    # df = await get_data_without_nomenclature_key(df)
    # if df.empty:
    #     print("!!! Нет новых товаров")
    #     return

    # добавляем к df конечный срок годности и количество месяцев хранения. Переводим текст в дату
    df = await df_insert_end_date(df)

    # создаем карточки номенклатуры и добавляем их в 1С
    df = await create_or_update_nomenclature(df)
    with pd.ExcelWriter(
        s_full_path_to_excel, engine="openpyxl", if_sheet_exists="new", mode="a"
    ) as writer:
        sheet_name = f"{datetime.now().strftime('%Y%m%d')}"
        df.to_excel(writer, index=False, sheet_name=sheet_name)


if __name__ == "__main__":
    file_path = r"C:\Users\<USER>\Desktop\SlimMaket.xlsx"
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main_create_nomenclature_in_1c_async(file_path))
    loop.close()
