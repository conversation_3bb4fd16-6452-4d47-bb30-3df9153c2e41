import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_tax_schema"
DOCUMENT = "Catalog_СхемыНалогообложения"
SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Predefined,Code,Description,НалогНаПрибыль,НДС,ЕдиныйНалог"
SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code numeric(10,4) NOT NULL DEFAULT 0,  -- Code
        description varchar(50) NULL,  -- Description
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        income_tax boolean NOT NULL DEFAULT FALSE,  -- НалогНаПрибыль
        vat boolean NOT NULL DEFAULT FALSE,  -- НДС
        single_tax boolean NOT NULL DEFAULT FALSE,  -- ЕдиныйНалог
        dataversion varchar(50) NULL,  -- DataVersion
        ref_key varchar(50) NULL,  -- Ref_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.income_tax IS 'НалогНаПрибыль';
    COMMENT ON COLUMN {TABLE_NAME}.vat IS 'НДС';
    COMMENT ON COLUMN {TABLE_NAME}.single_tax IS 'ЕдиныйНалог';

'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME} ( 
        ref_key, 
        dataversion, 
        deletion_mark, 
        predefined, 
        code, 
        description, 
        income_tax, 
        vat, 
        single_tax
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        deletion_mark = EXCLUDED.deletion_mark,
        predefined = EXCLUDED.predefined,
        income_tax = EXCLUDED.income_tax,
        vat = EXCLUDED.vat,
        single_tax = EXCLUDED.single_tax,
        dataversion = EXCLUDED.dataversion        
    ;
    '''
    return sql.replace("'", "")


async def main_cat_tax_schema_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_tax_schema_async())
