--
--    -- the date_send is inserted, but the logistic is empty
--    CREATE OR REPLACE VIEW v_one_logistic_kiev_in_order_carrier_isempty AS
--    SELECT *
--    FROM t_one_sale_logistics
--    WHERE (doc_date >= '01.03.23')
--        AND (date_send < current_date)
--        AND (date_send != '0001-01-01' OR date_send IS NULL)
--        AND ((add_to_delivery_address = '')
--        OR (add_to_delivery_address IS NULL))
--    ;
--    GRANT SELECT ON TABLE v_one_logistic_kiev_in_order_carrier_isempty TO user_prestige;
--
