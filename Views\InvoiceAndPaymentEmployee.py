# расхождение между суммами покупки и оплаты за товар сотрудниками
import asyncio
import os
import time
import sys
sys.path.append(r"D:\Prestige\Python\Prestige\Cash")
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderReceiptDetails import main_doc_cash_order_receipt_details_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Cash.docCashWarrantReceiptDetails import main_doc_cash_warrant_receipt_details_async
from CreateAndRunSQLScripts import create_views
from Document.docBuyerOrder import main_doc_buyer_order_async
from Document.docBuyerOrderDetails import main_doc_buyer_order_details_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
from Document.docSaleOfGoodsServicesGoods import main_doc_sale_of_goods_services_goods_async
from async_Postgres import  async_save_pg
from views_pg import main_views_pg_async

from Config_path import CONFIG_PATH, AUTHORIZE_PATH

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

VIEW_NAME = 'v_one_invoice_and_payment_employee'  # продажи сотрудникам и оплаты
VIEW_REPEAT_ORDERS = 'v_one_order_with_multi_payments'  # Заказы, по которым есть несколько оплат
SQL_CREATE_VIEW = f'''
    -- DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;

    CREATE OR REPLACE VIEW {VIEW_NAME} AS    
    SELECT DISTINCT *
    FROM (
        SELECT counterparties.description AS клиент, 
            sell.doc_number номерРН, sell.doc_date датаРН,
            (sell.rate_settlement * sell.document_amount)  AS суммаРН,
            sell.description AS комментРН,
            rcp_cash.doc_date AS датаПКО, 
            rcp_cash.doc_number AS номерПКО,
            rcp_cash.amount_of_payment AS суммаПКО,
            rcp_bank.doc_date AS датаБанк, 
            rcp_bank.doc_number AS номерБанк,
            rcp_bank.amount_of_payment AS суммаБанк,
            orders.a_comment комментарийЗаказа,
            orders.doc_date AS датаЗаказа,
            orders.doc_number AS номерЗаказа
        FROM t_one_doc_sale_of_goods_services AS sell -- Document_РеализацияТоваровУслуг
            LEFT JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                ON orders.ref_key = sell.deal 
            LEFT JOIN t_one_cat_counterparties AS counterparties -- Catalog_Контрагенты
                ON counterparties.ref_key = sell.account_key
            LEFT JOIN (
                SELECT DISTINCT receipt.doc_date, receipt.doc_number, receipt_details.base_key, 
                    receipt_details.amount_of_payment
                FROM t_one_doc_cash_warrant_receipt AS receipt -- Document_ПриходныйКассовыйОрдер
                    LEFT JOIN t_one_doc_cash_warrant_receipt_details AS receipt_details -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                        ON receipt.ref_key = receipt_details.ref_key
                WHERE receipt.doc_date >= '01.01.2023'::date
                    AND receipt.is_management AND receipt.posted
            ) AS rcp_cash
                ON rcp_cash.base_key = orders.ref_key
            LEFT JOIN (
                SELECT DISTINCT receipt.doc_date, receipt.doc_number, 
                    receipt_details.document_of_settlements_with_a_counterparty_key as base_key,
                    receipt_details.amount_of_payment, receipt_details.deal_key
                FROM t_one_doc_cash_order_receipt AS receipt -- Document_ПлатежноеПоручениеВходящее
                    LEFT JOIN t_one_doc_cash_order_receipt_details AS receipt_details -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                        ON receipt.ref_key = receipt_details.ref_key
                WHERE receipt.doc_date >= '01.01.2023'::date
                    AND receipt.is_management AND receipt.posted
            ) AS rcp_bank
                ON rcp_bank.deal_key = orders.ref_key OR rcp_bank.base_key = sell.ref_key 
        WHERE sell.doc_date >= '01.01.2023'::date
            AND sell.is_management AND sell.posted
            AND sell.account_key IN ('163e78bd-2b01-11eb-80fc-001dd8b72b55') -- Сотрудники Офис
        ) AS t
    WHERE (abs(COALESCE(t.суммаРН,0) - COALESCE(t.суммаПКО,0) - COALESCE(t.суммаБанк,0))) > 2
    ORDER BY датаРН
    ;

    GRANT SELECT ON TABLE {VIEW_NAME} TO user_prestige;

'''

SQL_CREATE_VIEW_REPEAT_ORDERS = f'''
    -- DROP VIEW IF EXISTS {VIEW_REPEAT_ORDERS} CASCADE;

    CREATE OR REPLACE VIEW {VIEW_REPEAT_ORDERS} AS    
    SELECT  dtl."source" источник,
        dtl.pay_date датаОплаты, dtl.pay_number номерДокОплаты, 
        dtl.order_date датаЗаказа, dtl.order_number номерЗаказа,
        dtl.sell_date AS датаПродажи,
        dtl.sell_number AS номерПродажи,
        dtl.amount_sell AS суммаПродажи,
        dtl.amount_pay AS оплатаПоДок,
        sum(dtl.amount_pay) OVER (PARTITION BY dtl.sell_key ORDER BY dtl.pay_date) AS оплаченоВсегоЗаРН
    FROM (
        SELECT order_key
        FROM (
            SELECT DISTINCT 
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                orders.ref_key AS order_key,
                'ПКО' AS source
            FROM t_one_doc_cash_warrant_receipt AS main -- Document_ПриходныйКассовыйОрдер
                INNER JOIN t_one_doc_cash_warrant_receipt_details AS sub -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.base_key = orders.ref_key 
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted 
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
            UNION ALL 
            SELECT DISTINCT 
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                orders.ref_key AS order_key,
                'ППВ' AS source
            FROM t_one_doc_cash_order_receipt AS main -- Document_ПлатежноеПоручениеВходящее
                INNER JOIN t_one_doc_cash_order_receipt_details AS sub -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.deal_key  = orders.ref_key 
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted 
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
            ) AS t
        GROUP BY order_key
        HAVING count(*) > 1 
        ) AS qnt
        LEFT JOIN 
            (
            SELECT DISTINCT 
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                sell.doc_date AS sell_date,
                sell.doc_number AS sell_number,
                sell.document_amount AS amount_sell,
                sub.amount_of_payment AS amount_pay,
                orders.ref_key AS order_key,
                sell.ref_key AS sell_key,
                'ПКО' AS source
            FROM t_one_doc_cash_warrant_receipt AS main -- Document_ПриходныйКассовыйОрдер
                INNER JOIN t_one_doc_cash_warrant_receipt_details AS sub -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.base_key = orders.ref_key 
                INNER JOIN t_one_doc_sale_of_goods_services AS sell
                    ON sell.deal = orders.ref_key
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted 
                AND sell.is_management AND sell.posted
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
            UNION ALL 
            SELECT DISTINCT 
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                sell.doc_date AS sell_date,
                sell.doc_number AS sell_number,
                sell.document_amount AS amount_sell,
                sub.amount_of_payment AS amount_pay,
                orders.ref_key AS order_key,
                sell.ref_key AS sell_key,
                'ППВ' AS source
            FROM t_one_doc_cash_order_receipt AS main -- Document_ПлатежноеПоручениеВходящее
                INNER JOIN t_one_doc_cash_order_receipt_details AS sub -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.deal_key  = orders.ref_key 
                INNER JOIN t_one_doc_sale_of_goods_services AS sell
                    ON sell.deal = orders.ref_key
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted 
                AND sell.is_management AND sell.posted
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
        ) AS dtl
        ON qnt.order_key = dtl.order_key
    ORDER BY dtl.order_key, pay_date, pay_number
    ;

    GRANT SELECT ON TABLE {VIEW_REPEAT_ORDERS} TO user_prestige;

'''


async def main_invoice_and_payment_employee_async():
    logger.info(f"START")
    await main_doc_cash_order_receipt_async()
    await main_doc_cash_order_receipt_details_async()
    await main_doc_sale_of_goods_services_async()
    await main_doc_sale_of_goods_services_goods_async()
    await main_doc_buyer_order_async()
    await main_doc_buyer_order_details_async()
    await main_doc_cash_warrant_receipt_async()
    await main_doc_cash_warrant_receipt_details_async()
    await create_views()
    await async_save_pg(SQL_CREATE_VIEW)
    await async_save_pg(SQL_CREATE_VIEW_REPEAT_ORDERS)
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_invoice_and_payment_employee_async())
