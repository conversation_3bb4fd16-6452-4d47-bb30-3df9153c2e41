# Document_ОприходованиеТоваров
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_inventory"
DOCUMENT = "Document_ИнвентаризацияТоваровНаСкладе"
SELECT_COLUMNS = "DataVersion,Date,DeletionMark,Number,Posted,Комментарий,Ref_Key,Организация_Key,Склад_Key"

SQL_CREATE_TABLE = f"""        
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NULL,  -- DataVersion
            doc_date timestamp NOT NULL,  -- Date
            deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
            doc_number varchar(50) NULL,  -- Number
            posted boolean NOT NULL DEFAULT FALSE,  -- Posted
            a_comment varchar(300) NULL,  -- Комментарий
            ref_key varchar(50) NULL,  -- Ref_Key
            organization_key varchar(50) NULL,  -- Организация_Key
            warehouse_key varchar(50) NULL,  -- Склад_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
        COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
        COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
        COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        dataversion,
        doc_date,
        deletion_mark,
        doc_number,
        posted,
        a_comment,
        ref_key,
        organization_key,
        warehouse_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        deletion_mark = EXCLUDED.deletion_mark,
        doc_number = EXCLUDED.doc_number,
        posted = EXCLUDED.posted,
        a_comment = EXCLUDED.a_comment,
        organization_key = EXCLUDED.organization_key,
        warehouse_key = EXCLUDED.warehouse_key
    ;
    """
    return sql.replace("'", "")


async def main_doc_inventory_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_inventory_async())
