import os
from pathlib import Path
CUR_DIR = os.path.dirname(os.path.abspath(__file__))
# Исходный путь
FOLDER_PATH = os.path.abspath('SQL')

# Количество частей пути, которые мы хотим включить
d = len(str(Path(FOLDER_PATH)).split('\\')) - str(Path(FOLDER_PATH)).split('\\')[::-1].index('Prestige')

# Преобразуем путь в строку и разбиваем его на части по разделителю '\\'
parts = str(Path(FOLDER_PATH)).split('\\')[:d]

# Объединяем части пути в абсолютный путь
absolute_path = os.path.join('\\'.join(parts), 'SQL')


if __name__ == '__main__':
    print(absolute_path)
