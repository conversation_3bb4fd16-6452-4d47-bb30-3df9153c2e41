# продажи в виде перекрестной таблицы

import asyncio
import os

from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)


SQL_V_ONE_SALE_YEARS_MONTH = '''
    -- DROP VIEW IF EXISTS v_one_sale_years_month;
    
    CREATE OR REPLACE VIEW v_one_sale_years_month AS
    SELECT * FROM crosstab(
       'SELECT extract(''YEAR'' from doc_date) as year, extract(''MONTH'' from doc_date) as month, SUM(amount)
        FROM t_one_sale 
        WHERE doc_type IN (''продажа'', ''возврат'')
            AND organization_key <> ''f51a0e76-4821-11ec-811d-001dd8b72b55'' -- Альфа-Бест
        GROUP BY extract(''YEAR'' from doc_date), extract(''MONTH'' from doc_date)
        ORDER BY year',
       'select m from generate_series(1,12) m'
    ) AS (
      YEAR NUMERIC,
      "Jan" NUMERIC,
      "Feb" NUMERIC,
      "Mar" NUMERIC,
      "Apr" NUMERIC,
      "May" NUMERIC,
      "Jun" NUMERIC,
      "Jul" NUMERIC,
      "Aug" NUMERIC,
      "Sep" NUMERIC,
      "Oct" NUMERIC,
      "Nov" NUMERIC,
      "Dec" NUMERIC
    );
    
    COMMENT ON VIEW v_one_sale_years_month IS 'продажи по годам';
    GRANT SELECT ON TABLE v_one_sale_years_month TO user_prestige;
    
'''


async def main_sale_years_month_async():
    logger.info(f"START")
    await async_save_pg(SQL_V_ONE_SALE_YEARS_MONTH)
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_sale_years_month_async())
