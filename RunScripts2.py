import asyncio
import glob
import os
import re

import aiofiles
from asyncpg import create_pool

from CreateAndRunSQLScripts import write_to_file, detect_encoding_async, convert_to_utf8
from async_Postgres import username, psw, basename, hostname, port

dsn = f"postgresql://{username}:{psw}@{hostname}:{port}/{basename}"

# Получите список всех SQL-файлов в папке
sql_files = glob.glob('SQL/*.sql')
# Путь к папке SQL
sql_folder = "SQL"

# Удалите расширение .sql из каждого имени файла, чтобы получить имя представления
VIEWS = [os.path.splitext(os.path.basename(sql_file))[0] for sql_file in sql_files]


async def execute_sql_file(pool, file_path):
    print(f"Executing SQL script: {file_path}")
    async with aiofiles.open(file_path, mode='r') as f:
        sql_script = await f.read()
    async with pool.acquire() as connection:
        try:
            await connection.execute(sql_script)
        except Exception as e:
            print(f"Error occurred while executing {file_path}: {str(e)}")
            raise e


async def check_view_exists(pool, view_name):
    async with pool.acquire() as connection:
        result = await connection.fetch(f"SELECT to_regclass('{view_name}')")
        return result[0]['to_regclass'] is not None


async def create_view_if_not_exists(pool, view_name, sql_file_path):
    if not await check_view_exists(pool, view_name):
        await execute_sql_file(pool, sql_file_path)


async def execute_sql_file_with_dependency_resolution(pool, file_path):
    try:
        # Попытка выполнить SQL-скрипт
        await execute_sql_file(pool, file_path)
    except Exception as e:
        # Если возникает ошибка, проверьте, указывает ли она на отсутствие таблицы или представления
        if (('relation' in str(e) and 'does not exist' in str(e)) or
                ('отношение' in str(e) and 'не существует' in str(e)) in str(e)):
            # Извлеките имя отсутствующей таблицы или представления из сообщения об ошибке
            missing_relation = str(e).split('"')[1]
            # Найдите и выполните соответствующий SQL-скрипт
            missing_sql_file_path = f'SQL/{missing_relation}.sql'
            if os.path.exists(missing_sql_file_path):
                await execute_sql_file_with_dependency_resolution(pool, missing_sql_file_path)
                # Повторите попытку выполнения исходного скрипта
                # await execute_sql_file(pool, file_path)
        else:
            # Если ошибка не связана с отсутствием таблицы или представления, повторно вызовите исключение
            raise e


async def main():
    # Создайте пул соединений с базой данных
    pool = await create_pool(dsn)

    # Для каждого представления в списке
    for view in VIEWS:
        # Проверьте, существует ли представление в базе данных
        # if not await check_view_exists(pool, view):
        # Если представление не существует, проверьте, есть ли SQL-скрипт для его создания
        sql_file_path = f'SQL/{view}.sql'
        if os.path.exists(sql_file_path):
            # Если скрипт существует, выполните его с обработкой зависимостей
            await execute_sql_file_with_dependency_resolution(pool, sql_file_path)

    # Закройте пул соединений с базой данных
    await pool.close()


async def select_variables():
    # Чтение файла views_pg.py
    with open('views_pg.py', 'r', encoding='utf8') as file:
        content = file.read()

    # Поиск всех переменных, которые содержат v_
    variables = re.findall(r'[a-zA-Z0-9_]*v_[a-zA-Z0-9_]*', content, re.IGNORECASE)
    variables = list(set(variables))
    # Для каждой переменной создайте SQL-файл с соответствующим именем
    for var_name, sql_var in zip(variables, variables):
        # Создайте путь к файлу
        file_path = os.path.join(sql_folder, f"{var_name}.sql")
        # Запишите SQL-запрос в файл
        await write_to_file(file_path, sql_var)
        # Определите кодировку файла
        encoding = await detect_encoding_async(file_path)
        # Если кодировка не UTF-8, преобразуйте файл в UTF-8
        if encoding.lower() != 'utf-8':
            await convert_to_utf8(file_path, encoding)

    # # Создание SQL-скриптов для каждой найденной переменной
    # for variable in set(variables):
    #     # Получение значения переменной
    #     value = re.search(f'{variable} = \'\'\'(.*?)\'\'\'', content, re.MULTILINE | re.DOTALL)
    #     if value is not None:
    #         value = value.group(1)
    #     else:
    #         value = "'''"
    #
    #     # Создание SQL-скрипта с содержимым переменной
    #     with open(f'SQL/{variable}.sql', 'w', encoding='utf8') as file:
    #         file.write(value)


if __name__ == '__main__':
    asyncio.run(select_variables())
    # asyncio.run(main())
