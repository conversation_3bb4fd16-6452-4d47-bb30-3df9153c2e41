import asyncio
import os
import sys

# sys.path.append("C:\\Rasim\\Python\\Prestige")
# Добавляем путь к родительской директории
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_correction_series_details"
DOCUMENT = "Document_КорректировкаСерийИХарактеристикТоваров_Товары"
SELECT_COLUMNS = (
    "LineN<PERSON><PERSON>,Количество,Коэффициент,<PERSON>е<PERSON>,Ref_Key,ЕдиницаИзмерения_Key,Качество_Key,Номенклатура_Key,"
    "СерияНоменклатуры_Key,СерияНоменклатурыНовая_Key"
)

SQL_CREATE_TABLE = f"""    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial not null,
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        quantity numeric(10, 4) NOT NULL DEFAULT 0,  -- Количество
        coefficient numeric(10, 4) NOT NULL DEFAULT 0,  -- Коэффициент
        price numeric(10, 4) NOT NULL DEFAULT 0,  -- Цена
        ref_key varchar(50) NULL,  -- Ref_Key
        unit_of_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
        quality_key varchar(50) NULL,  -- Качество_Key
        nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
        nomenclature_series_key varchar(50) NULL,  -- СерияНоменклатуры_Key
        nomenclature_series_new_key varchar(50) NULL,  -- СерияНоменклатурыНовая_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.quality_key IS 'Качество_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_new_key IS 'СерияНоменклатурыНовая_Key';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        line_number, 
        quantity, 
        coefficient, 
        price, 
        ref_key, 
        unit_of_key, 
        quality_key, 
        nomenclature_key,
        nomenclature_series_key,
        nomenclature_series_new_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number= EXCLUDED.line_number,
        quantity= EXCLUDED.quantity,
        coefficient= EXCLUDED.coefficient,
        price= EXCLUDED.price,
        ref_key= EXCLUDED.ref_key,
        unit_of_key= EXCLUDED.unit_of_key,
        quality_key= EXCLUDED.quality_key,
        nomenclature_key= EXCLUDED.nomenclature_key,
        nomenclature_series_key= EXCLUDED.nomenclature_series_key,
        nomenclature_series_new_key= EXCLUDED.nomenclature_series_new_key
    """
    return sql.replace("'", "")


async def main_doc_correction_series_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    # for data in query_lists:
    #     await async_save_pg(sql, data)

    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    await async_sql_create_index(TABLE_NAME, "nomenclature_key")

    logger.info(f"FINISH")


# def run_main():
#     loop = asyncio.get_event_loop()
#     if loop.is_running():
#         # If the event loop is already running, we use it directly
#         loop.create_task(main_doc_correction_series_details_async())
#     else:
#         asyncio.run(main_doc_correction_series_details_async())


# if __name__ == "__main__":
#     asyncio.run(main_doc_correction_series_details_async())
if __name__ == "__main__":
    asyncio.run(main_doc_correction_series_details_async())
    # with Pool() as pool:
    #     pool.apply_async(run_main)
    #     pool.close()
    #     pool.join()
