import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_tax_sale_details"
DOCUMENT = "Document_НалоговаяНакладная_Товары"
SELECT_COLUMNS = (
    "Ref_Key,LineNumber,Количество,Коэффициент,Номенклатура_Key,СтавкаНДС,Сумма,СуммаНДС,Цена,"
    "СтатьяДекларацииНДСНалоговыеОбязательства_Key,КодУКТВЭД_Key,НомерСтрокиНН"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        line_number numeric(10,4) NOT NULL DEFAULT 0,  -- LineNumber
        quantity numeric(10,4) NOT NULL DEFAULT 0,  -- Количество
        price numeric(10,3) NOT NULL,  -- Цена
        total numeric(15,3) NOT NULL,  -- Сумма
        coefficient numeric(10,4) NOT NULL DEFAULT 0,  -- Коэффициент
        vat_rate varchar(10) NULL,  -- СтавкаНДС
        sum_vat numeric(10,3) NULL,  -- СуммаНДС
        line_doc_number_tax numeric(10,4) NOT NULL DEFAULT 0,  -- НомерСтрокиНН
        ref_key varchar(50) NULL,  -- Ref_Key
        nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
        code_uktvd_key varchar(50) NULL,  -- КодУКТВЭД_Key
        declaration_article_vat_tax_obligations_key varchar(50) NULL,  -- СтатьяДекларацииНДСНалоговыеОбязательства_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.vat_rate IS 'СтавкаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.total IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.sum_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';
    COMMENT ON COLUMN {TABLE_NAME}.declaration_article_vat_tax_obligations_key 
        IS 'СтатьяДекларацииНДСНалоговыеОбязательства_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code_uktvd_key IS 'КодУКТВЭД_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_doc_number_tax IS 'НомерСтрокиНН';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        line_number,
        quantity,
        coefficient,
        nomenclature_key,
        vat_rate,
        total,
        sum_vat,
        price,
        declaration_article_vat_tax_obligations_key,
        code_uktvd_key,
        line_doc_number_tax
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        quantity = EXCLUDED.quantity,
        coefficient = EXCLUDED.coefficient,
        nomenclature_key = EXCLUDED.nomenclature_key,
        vat_rate = EXCLUDED.vat_rate,
        total = EXCLUDED.total,
        sum_vat = EXCLUDED.sum_vat,
        price = EXCLUDED.price,
        declaration_article_vat_tax_obligations_key = EXCLUDED.declaration_article_vat_tax_obligations_key,
        code_uktvd_key = EXCLUDED.code_uktvd_key,
        line_doc_number_tax = EXCLUDED.line_doc_number_tax
    ;
    """
    return sql.replace("'", "")


async def main_doc_tax_details_sale_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
        
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_tax_details_sale_async())
