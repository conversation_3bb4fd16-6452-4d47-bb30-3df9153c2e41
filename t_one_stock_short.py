import os
import asyncio

from async_Postgres import async_sql_create_index, async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_stock_short"

SQL_CREATE = f'''

    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    id bigserial NOT NULL,
    supplier varchar(300) NULL,
    sku varchar(300) NOT NULL,
    doc_date date not null,
    doc_date_previos date null,
    doc_date_last_receipt date null,
    doc_date_last_sale date null,
    tobox numeric(10,3) not null,
    entry_amount numeric(10,3) not null,
    maliyet_amount numeric(10,3) not null,
    stock numeric(10,3) not null,
    stock_previos numeric(10,3) null,
    nomenclature_key char(50) not null,
    CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id)    
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'остатки товара на "Основной склад товаров 2022" и "МЕРЕЖІ"';
    COMMENT ON COLUMN {TABLE_NAME}.supplier IS 'Поставщик';
    COMMENT ON COLUMN {TABLE_NAME}.sku IS 'Номенклатура';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'дата док';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_previos IS 'предыдущ дата док';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_last_receipt IS 'дата посл поставки';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_last_sale IS 'дата посл продажи';
    COMMENT ON COLUMN {TABLE_NAME}.tobox IS 'кол-во';
    COMMENT ON COLUMN {TABLE_NAME}.entry_amount IS 'сум вх';
    COMMENT ON COLUMN {TABLE_NAME}.maliyet_amount IS 'сум с/сть';
    COMMENT ON COLUMN {TABLE_NAME}.stock IS 'остаток';
    COMMENT ON COLUMN {TABLE_NAME}.stock_previos IS 'остаток';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'nomenclature_key';
    
    GRANT ALL ON TABLE {TABLE_NAME} TO user_prestige;    
'''

SQL_DELETE = f'''TRUNCATE TABLE {TABLE_NAME};'''

SQL_INSERT = f'''
    INSERT INTO {TABLE_NAME} (supplier, sku, doc_date, tobox, entry_amount, maliyet_amount, stock,  
        nomenclature_key, doc_date_previos, stock_previos)
    SELECT supplier, sku, doc_date, tobox, entry_amount, amount_maliyet, stock, nomenclature_key, 
        lag(doc_date) OVER (PARTITION BY sku ORDER BY doc_date) as doc_date_previos,
        COALESCE(lag(stock) OVER (PARTITION BY sku ORDER BY doc_date),0) as stock_previos
    FROM (
        SELECT supplier, sku, doc_date, tobox, entry_amount, amount_maliyet,
            sum(tobox) OVER (PARTITION BY sku ORDER BY doc_date) AS stock, nomenclature_key
        FROM ( 
            SELECT supplier, sku, doc_date::date, sum(tobox) AS tobox, sum(entry_amount) as entry_amount, 
                sum(amount_maliyet) as amount_maliyet, nomenclature_key 
            FROM t_one_stock
            WHERE organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
                AND warehouse_key IN ('4b40b865-6d2f-11ec-8125-001dd8b72b55','381c5d92-4acb-11ed-8148-001dd8b72b55')
            GROUP BY supplier, sku, doc_date::date, nomenclature_key
        ) AS t
    ) as tt 
    ORDER BY sku, doc_date
    ;
'''

sql_doc_date_last_receipt = f'''
    UPDATE {TABLE_NAME} AS x
    SET doc_date_last_receipt = (SELECT max(doc_date) FROM t_one_stock 
                WHERE doc_type IN ('поступление', 'поступление возврат') 
                    AND organization_key::text <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
                    AND doc_date::date = x.doc_date
                    AND sku = x.sku
                    );                                
'''

sql_doc_date_last_sale = f'''    
    UPDATE {TABLE_NAME} AS x
    SET doc_date_last_sale = (SELECT max(doc_date) FROM t_one_stock WHERE doc_type IN ('продажа', 'продажа возврат') 
                    AND organization_key::text <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
                    AND doc_date::date = x.doc_date
                    AND sku = x.sku
                    );
'''

SQL_GRANT = f'''
    GRANT SELECT ON TABLE t_one_stock TO user_prestige;
    GRANT ALL ON TABLE {TABLE_NAME} TO user_prestige;
'''


async def main_t_one_stock_short_async():
    # await create_views()
    result = await async_save_pg(SQL_CREATE)
    logger.info(f"{result}, SQL_CREATE")

    result = await async_save_pg(SQL_DELETE)
    logger.info(f"{result}, SQL_DELETE")

    result = await async_save_pg(SQL_INSERT)
    logger.info(f"{result}, SQL_INSERT")

    result = await async_save_pg(sql_doc_date_last_receipt)
    logger.info(f"{result}, sql_doc_date_last_receipt")

    result = await async_save_pg(sql_doc_date_last_sale)
    logger.info(f"{result}, sql_doc_date_last_sale")

    await async_sql_create_index(TABLE_NAME, "sku")
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_date_previos")
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")

    result = await async_save_pg(SQL_GRANT)
    logger.info(f"{result}, SQL_GRANT")


if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(main_t_one_stock_short_async())
    logger.info(f"FINISH")
