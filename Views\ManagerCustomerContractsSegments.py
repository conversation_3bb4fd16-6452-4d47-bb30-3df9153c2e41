# договора с клиентами, с указанием менеджера, клиент, количества дней отсрочки платежа
import asyncio
import os
import sys
from pathlib import Path

my_path = os.path.dirname(os.path.abspath(__file__))
path = Path(my_path)
components = path.parts
python_index = None
if "Python" in components:
    python_index = components.index("Python")
my_path_list = str(my_path).split('\\')[1:python_index + 1]
config_path = os.path.join(os.path.splitdrive(my_path)[0],'\\', *my_path_list, "Config")
sys.path.append(my_path)
sys.path.append(config_path)
sys.path.append(path.parent.__str__())
from async_Postgres import  async_save_pg

from logger_prestige import get_logger
FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

VIEW_NAME = 'v_one_manager_counterparty_contracts_segments'

SQL_CREATE_VIEW = f'''
    -- DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
    SELECT  
        segments_parent.description AS segment_folder,
        segments.description AS segment,
        mngr.description AS manager,
        client.description AS customer,
        client.edrpou,
        client.inn,
        contract.limit_days_credit_ua as contract_days,
        contract.contract_name,
        contract.contract_type,
        client.ref_key AS customer_key,
        contract.ref_key AS contract_key
    FROM t_one_cat_counterparties AS client
        LEFT JOIN t_one_cat_counterparty_segment AS segments
            ON client.segment_key = segments.ref_key
        LEFT JOIN 
            (SELECT description, ref_key
            FROM t_one_cat_counterparty_segment
            WHERE isfolder
                AND (parent_key = '00000000-0000-0000-0000-000000000000')
            ) AS segments_parent
            ON segments_parent.ref_key = segments.parent_key
        LEFT JOIN 
        (
        SELECT description, ref_key
        FROM t_one_cat_counterparties
        WHERE isfolder 
            AND parent_key = 'ad421841-905f-11e6-80c4-c936aa9c817c' -- 010 Покупатели
        ) AS mngr
        ON mngr.ref_key = client.parent_key
        LEFT JOIN 
        (
        SELECT limit_days_credit_ua, description as contract_name, owner_key, ref_key, contract_type
        FROM t_one_cat_contracts_counterparties
        ) AS contract
        ON client.ref_key = contract.owner_key
    WHERE NOT client.isfolder
    ORDER BY mngr.description, client.description
    ;
    
    COMMENT ON VIEW {VIEW_NAME} IS 'договора с клиентами';
    GRANT SELECT ON TABLE {VIEW_NAME} TO user_prestige;
'''


async def main_manager_customer_contracts_segments_async():
    result = await async_save_pg(SQL_CREATE_VIEW)
    logger.info(f"{result}, SQL_CREATE_VIEW'")
    return result


if __name__ == '__main__':
    logger.info("START")
    asyncio.run(main_manager_customer_contracts_segments_async())
    logger.info("FINIS")
