import asyncio
import json
import traceback
from datetime import datetime

import aiohttp
import requests

from async_Postgres import C_LOGIN, C_PSW, URL_CONST
from multiThread import async_fetch_async


async def async_url_count(DOCUMENT):
    return URL_CONST + DOCUMENT + "/$count"


async def async_url_details(DOCUMENT):
    return URL_CONST + DOCUMENT + "/?$top=%s&$skip=%s&$format=json"


async def async_url_accumulate(DOCUMENT):
    return URL_CONST + DOCUMENT + "/?$top=%s&$skip=%s&$format=json&$filter=Active eq true"


async def async_url_main(DOCUMENT):
    return await async_url_details(DOCUMENT) + "&$filter=Posted eq true and ОтражатьВУправленческомУчете eq true"


async def async_url_main_select(DOCUMENT):
    return await async_url_main(DOCUMENT) + "&$select=%s"


async def async_url_details_select(DOCUMENT):
    return await async_url_details(DOCUMENT) + "&$select=%s"


async def async_fetch(session, url):
    async with session.get(url) as response:
        return await response.read()


async def get_json_from_1c(DOCUMENT, SELECT_COLUMNS=''):
    url = await async_url_count(DOCUMENT)
    connector = aiohttp.TCPConnector(limit=50)
    tasks = []
    async with aiohttp.ClientSession(connector=connector, auth=aiohttp.BasicAuth(C_LOGIN, C_PSW),
                                     json_serialize=json.dumps) as session:
        count = int(await async_fetch(session, url))
        # count = 15000
        top = 10000
        skip = 0
        time_start = datetime.now()
        print(skip, datetime.now())
        while skip <= count:
            url = await async_url_details_select(DOCUMENT) % (top, skip, SELECT_COLUMNS)
            skip += top
            tasks.append(loop.create_task(async_fetch(session, url)))
            print(skip, datetime.now())
        d = await asyncio.wait(tasks)
        print(datetime.now())
        print(d)
        # data_raw = await asyncio.gather(*tasks)
        print('process_time', datetime.now(), time_start - datetime.now())
        json_list = []
        for i, task in enumerate(tasks):
            try:
                data_json = json.loads(task)
                data_list = data_json['value']
                for j, item in enumerate(data_list):
                    try:
                        print(i, j)
                        json_list.append(item.values())
                    except:
                        print(traceback.print_exc())

            except:
                print(traceback.print_exc())

        print(datetime.now())


# синхронный get запрос на 1С
def get_data_1c(url):
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    return response.json()


# синхронный patch запрос на 1С. Частичное изменение данных
def patch_data_1c(url, data):
    # url = "http://195.128.227.141/utp_prestige/odata/standard.odata//Catalog_СерииНоменклатуры(guid'...')?$format=json"
    response = requests.patch(url, auth=(C_LOGIN, C_PSW), json=data)
    return response.json()


# синхронный post запрос на 1С. Добавление данных
def post_data_1c(url, data):
    response = requests.post(url, auth=(C_LOGIN, C_PSW), json=data)
    return response.json()


# синхронный post запрос на 1С. Удаление данных
def delete_data_to_1c(url):
    response = requests.delete(url, auth=(C_LOGIN, C_PSW))
    return response.status_code


# асинхронный get запрос на 1С
async def get_1c_async(url):
    connector = aiohttp.TCPConnector(limit=50)
    async with aiohttp.ClientSession(connector=connector, auth=aiohttp.BasicAuth(C_LOGIN, C_PSW)) as session:
        try:
            answer = await async_fetch_async(session, url)
            if answer and answer.get('value'):
                return answer['value']
        except Exception as e:
            sms = f"Ошибка: {e}, URL: {url}"
            print(sms)
        finally:
            await session.close()

    return None


if __name__ == '__main__':
    DOCUMENT = "Document_РеализацияТоваровУслуг_Товары"
    loop = asyncio.get_event_loop()
    loop.run_until_complete(get_json_from_1c(DOCUMENT))
