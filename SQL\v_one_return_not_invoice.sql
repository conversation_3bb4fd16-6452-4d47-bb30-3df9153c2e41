    CREATE OR REPLACE VIEW v_one_return_not_invoice AS
    SELECT DISTINCT
        users.description AS автор,
        client.customer контрагент,
        main.doc_date ВН_дата,
        main.doc_number ВН_номер,
        nom.description AS номенклатура
    FROM t_one_doc_return_of_goods_from_customers AS main
        INNER JOIN t_one_doc_return_of_goods_from_customers_goods AS det
            ON main.ref_key = det.ref_key
        INNER JOIN v_one_manager_counterparty_contracts_segments AS client
            ON client.customer_key = main.account_key
        INNER JOIN t_one_cat_users AS users
            ON users.individual_key = main.user_key
        INNER JOIN t_one_cat_nomenclature AS nom
            ON nom.ref_key = det.nomenclature_key
    WHERE coalesce(base_doc_key,'') = ''
    ORDER BY
        users.description DESC,
        client.customer,
        main.doc_date DESC,
        main.doc_number,
        nom.description
    ;

    GRANT SELECT ON TABLE v_one_return_not_invoice TO user_prestige;
