CREATE OR REPLACE FUNCTION insert_into_t_coworker()
RETURNS void AS $$
BEGIN
    -- Первый запрос
    INSERT INTO t_coworker(ref_key, coworker_name)
    SELECT DISTINCT
        tt.ref_key::uuid,
        upper(trim(tt.description))
    FROM t_cash_register AS reg
    LEFT JOIN (
        SELECT DISTINCT
            ref_key::uuid,
            trim(description) AS description
        FROM (
            SELECT
                ref_key::uuid,
                description
            FROM t_one_cat_individuals
            UNION ALL
            SELECT
                ref_key,
                description
            FROM t_one_cat_employees
        ) AS t
    ) AS tt
    ON reg.employee_key::uuid = tt.ref_key::uuid
    WHERE reg.employee_key IS NOT NULL
    ON CONFLICT (coworker_name) DO NOTHING;

    -- Второй запрос
    INSERT INTO t_coworker(ref_key, coworker_name)
    SELECT DISTINCT
        ref_key::uuid,
        upper(trim(t.description))
    FROM (
        SELECT
            ref_key::uuid,
            description
        FROM t_one_cat_individuals
        UNION ALL
        SELECT
            ref_key::uuid,
            description
        FROM t_one_cat_employees
    ) AS t
    ON CONFLICT (coworker_name) DO NOTHING;
END;
$$ LANGUAGE plpgsql;