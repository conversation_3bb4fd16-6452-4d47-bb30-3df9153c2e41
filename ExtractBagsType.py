import re
import pandas as pd


def parse_product(product):
    # Паттерны для разных форматов записи
    patterns = [
        # Стандартный формат с явными разделителями
        r'(\d+(?:\.\d+)?)\s*(?:g|gr|gx|grx|g x|gr x|ml|mlx|G|GR|ML|GRX)[^\d]*(\d+)[^\d]*(\d+)(?:boxes|box|jars|trays?|vases|bags|tray|jar|bag|vase|JELLY CANDY)',
        # Формат с * в качестве разделителя
        r'(\d+(?:\.\d+)?)\s*(?:g|gr|G|GR|ml|ML|gr)\s*\*\s*(\d+)\s*\*\s*(\d+)',
        # Формат с X в качестве разделителя (большими буквами)
        r'(\d+(?:\.\d+)?)\s*(?:G|GR|ML)X(\d+)X(\d+)',
        # Формат с запятой в числе
        r'(\d+,\d+)\s*(?:G|GR|ML)X(\d+)X(\d+)',
        # Формат с x после числа
        r'(\d+(?:\.\d+)?)\s*(?:G|GR|ML|gr|g)(?:\s*x\s*|\s+x\s+)(\d+)(?:\s*pcs\s*x\s*|\s+x\s+)(\d+)',
        # Формат MAXXI (10X48X28G)
        r'(\d+)X(\d+)X(\d+)G',
        # Формат с gr в конце (900grx6)
        r'(\d+)gr(?:x|\s+x\s+)(\d+)',
        # Формат с gr в конце и множественными разделителями
        r'(\d+)\s*\*\s*(\d+)\s*\*\s*(\d+)gr',
        # Формат с единицей измерения в конце (500Gx 8Boxes)
        r'(\d+)(?:G|GR|ML)x\s*(\d+)Boxes',
    ]

    for pattern in patterns:
        match = re.search(pattern, product)
        if match:
            break

    if match:
        # Для форматов с gr в конце и множественными разделителями (12*24*18gr)
        if match.re.pattern == r'(\d+)\s*\*\s*(\d+)\s*\*\s*(\d+)gr':
            containers = int(match.group(1))  # Первое число - количество упаковок
            pieces = int(match.group(2))  # Второе число - количество штук
            weight_str = match.group(3)  # Третье число - вес
        else:
            # Обработка веса (замена запятой на точку)
            weight_str = match.group(1).replace(',', '.')

            # Для форматов с двумя числами (например, 900grx6)
            if len(match.groups()) == 2:
                pieces = 1  # Предполагаем 1 штуку в упаковке
                containers = int(match.group(2))
            else:
                # Для форматов MAXXI (10X48X28G) - последнее число это вес
                if match.re.pattern == r'(\d+)X(\d+)X(\d+)G':
                    weight_str = match.group(3)
                    pieces = int(match.group(2))
                    containers = int(match.group(1))
                else:
                    pieces = int(match.group(2))  # Количество штук
                    containers = int(match.group(3))  # Количество коробок/банок/etc

        weight = float(weight_str.replace(',', '.'))  # Вес в граммах/мл

        return {
            'product': product,
            'weight': weight,
            'pieces': pieces,
            'containers': containers
        }
    return None


def format_weight(weight):
    """Форматирует вес, убирая .0 для целых чисел"""
    return f"{weight:g}" if weight == int(weight) else f"{weight}"


def get_unit_type(product):
    """Определяет тип единицы измерения из строки продукта"""
    if 'box' in product.lower():
        return 'блок'
    elif 'jar' in product.lower():
        return 'банка'
    elif 'tray' in product.lower():
        return 'лоток'
    elif 'vase' in product.lower():
        return 'ваза'
    elif 'bag' in product.lower():
        return 'пакет'
    return 'блок'  # по умолчанию


def extract_info(products):
    import pandas as pd
    pd.set_option('display.max_rows', None)  # Показать все строки
    pd.set_option('display.max_colwidth', None)  # Показать полный текст в ячейках
    pd.set_option('display.width', None)  # Автоматическая ширина

    data = []
    success = 0
    failed = 0

    for product in products:
        result = parse_product(product)
        if result:
            data.append({
                'sku': result['product'],
                'грм': format_weight(result['weight']),
                'шт': result['pieces'],
                'блоки': result['containers'],
                'ед.изм': get_unit_type(result['product'])
            })
            success += 1
        else:
            print(f"✗ Не удалось разобрать: {product}")
            failed += 1

    # Создаем DataFrame
    df = pd.DataFrame(data)
    # сохраняем в Excel файл
    df.to_excel('products.xlsx', index=False)

    # Выводим DataFrame
    print("\nТаблица продуктов:")
    print(df.to_string(index=False))

    # Выводим статистику
    total = success + failed
    print(f"\nСтатистика:")
    print(f"Всего продуктов: {total}")
    print(f"Успешно разобрано: {success} ({success / total * 100:.1f}%)")
    print(f"Не удалось разобрать: {failed} ({failed / total * 100:.1f}%)")


if __name__ == "__main__":
    # Список всех продуктов
    test_products = [
        "Diamond Light Candy 6gx24pcsx12boxes",
        "Ice Lolly Jelly 14gx30pcsx20boxes",
        "Jelly cup in Monkey Jar 13gx100pcsx6jars",
        "Mini Cup Jelly (Panda Bag) 13gx100pcsx6jars",
        "Jelly cup in Koala Jar 13gx100pcsx6jars",
        "Mini cup Jelly (Duck Jar) 13gx100pcsx6jars",
        "Mini cup Jelly (Owl Jar) 13gx100pcsx6jars",
        "Mini cup Jelly (Hippo Jar) 13gx100pcsx6jars",
        "Mini Jelly cup in (Trush can) 13gx100pcsx6jars",
        "Mini cup jlly (Benben bear Jar) 13gx100pcsx6jars",
        "Winx Spray Candy 30gx12pcsx12boxes",
        "Baby bottle(Whistle candy) 9gx100pcsx6jars",
        "COBRA GUMMY 16g*24pcs*12boxes",
        "HOTDOG GUMMY 16g*24pcs*12boxes",
        "5D Fruits Gummy Candy 10gx50pcsx12jars",
        "Dinosaur Gummy 8gx12pcsx20boxes",
        "Super Car Gummy 20gx20pcsx12boxes",
        "Toy and Jelly 30gx50pcsx6jars",
        "Snake jelly 35gx48pcsx6jars",
        "Crocodile jelly 35gx48pcsx6jars",
        "2in1 Crazy Hair Candy 20gx20pcsx12boxes",
        "Cola Tin Spray Candy 30mlx24pcsx12boxes",
        "Cow Eyes Gummy 7gx60pcsx12jars",
        "Bear Pudding 40gx20pcsx18tray",
        "EYE CANDY STAND 3,5 GX24X8",
        "EYEBALL CANDY SATND 3,5 GX24X8",
        "Biberon Liquid Candy 30mlX30pcsx18boxes",
        "Long CC Stick Candy 4gx50pcsx40bags",
        "Snake Gummy 8gx30pcsx20boxes",
        "Birthday Cake Gummy 12gx20pcsx12boxes",
        "Jelly (Skeleton shape) 35gx55pcsx6jars",
        "Mini Jackpot Gum Ball 20gx12pcsx 12boxes",
        "Bear Jelly 35gx30pcsx12boxes",
        "Butterfly Jelly 35gx30pcsx12boxes",
        "3in1 Squeeze Candy 45gx12pcsx12boxes",
        "Ice Cream 3in1 16gx24pcsx10boxes",
        "Mouse Candy 6gx30pcsx24trays",
        "Rabbit Candy 6gx30pcsx24trays",
        "Fruits Press Candy 10gx30pcsx20boxes",
        "Super Sour Hard Candy 12gx24pcsx20boxes",
        "Big Burger Gummy 32gx8pcsx8boxes",
        "Mini Lollipop Shooter 3gx12pcsx 24boxes",
        "Big Foot lollipop+Sour Powder Candy 7.5gx30pcsx24boxes",
        "Roll Gum 7g×24pcs×24boxes",
        "Twins eyeball Gummy 4g*100pcs*12jars",
        "Stretch frog candy 5gx12pcsx6boxes",
        "Skull Candy + bear Candy 6gx60pcsx12jars",
        "Microphone pudding 33gx30pcsx12jars",
        "Mega sour jam 30gx20pcsx12boxes",
        "Bracelet Candy 10gx48pcsx12boxes UNI",
        "Lighting New Spray Candy 25gx30pcsx20boxes",
        "Toilet Candy 13gx24pcsx12boxes",
        "Biberon liquit candy 40g*30pcs*16boxes",
        "Stick grape gummy 10g*30pcs*20jars",
        "stick eyeball gummy 10g*30pcs*20jars",
        "DEVIL GUMMY 8g*30pcs*20jars",
        "BILLARDS GUMMY 8g*30pcs*20jars",
        "LADY BIRD GUMMY 8g*30pcs*20jars",
        "WATERMELON GUMMY 8g*30pcs*20jars",
        "Colour Candy Ball Stick 12gx40pcsx8vases",
        "5in1 Sour Powder 10gX50pcsx20boxes",
        "Birthday gummy 46gx6pcsx12box",
        "Marshmallow Hamburger Pop 18gx24pcsx12boxes",
        "harmonica Candy +fish Candy 3gx60pcsx12jars",
        "Skeleton Pop candy 7gx30pcsx24boxes",
        "Lighting Torch Candy 4.5gx20pcsx12boxes",
        "Animal shape + heart marshmallow stick 11gx30pcsx24boxes",
        "Skeleton Jelly 32gx70pcsx6jars",
        "Pizza Jelly Grape 50gx30pcsx10boxes",
        "Queen Cup Jelly (Strawberry) 100g x 8 pcs x 12tray",
        "Skateboard Crazy Hair candy 20gx20pcsx12boxes",
        "Cup Coffee Candy 15gx12pcsx12boxes",
        "Ice Cream Lolly Marshmallow 12gx30pcsx24boxes",
        "Ice Cream Popsicle Marshmallow 11gx30pcsx24boxes",
        "500g Macaron Cookies (Mixed Flavors) 500Gx 8Boxes",
        "LOLLIPOP MAXXI SOUR CHERRY 10X48X28G",
        "LOLLIPOP MAXXI WATERMELON 10X48X28G",
        "POP MANIA MAXXI GREEN APPLE 10X48X28G",
        "POP MANIA MAXXI RASPBERRY 10X48X28G",
        "POP MANIA MAXXI STRAWBERRY 10X48X28G",
        "SLIM TRANSPARENT EGG 6GR*6*12 army tank",
        "kokolin draje şek.kaplm.12*24*18gr choco punto",
        "Kokolin draje şeker kaplamalı 12*24*12gr PUNTO",
        "JELLYREX MIX 15g*12*24 JELLY CANDY",
        "Princess (Mini sandwich with strw cream) 900grx6"
    ]

    extract_info(test_products)