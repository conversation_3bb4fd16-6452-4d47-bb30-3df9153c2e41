import asyncio
import os

from async_Postgres import async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_doc_cash_correction_of_register'
DOCUMENT = "Document_КорректировкаЗаписейРегистров"
SELECT_COLUMNS = "Date,Number,Комментарий,Posted,DeletionMark,ИспользоватьЗаполнениеДвижений,DataVersion,Ref_Key"

SQL_CREATE_TABLE = f'''


    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL DEFAULT 0,  -- Number
        description varchar NULL,  -- Комм<PERSON>нтарий
        posted bool NOT NULL DEFAULT false,  -- Posted
        deletion_mark bool NOT NULL DEFAULT false,  -- DeletionMark
        use_fill_movements bool NOT NULL DEFAULT false,  -- ИспользоватьЗаполнениеДвижений
        dataversion varchar(50) NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        CONSTRAINT t_one_doc_cash_correction_of_register_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.use_fill_movements 
        IS 'ИспользоватьЗаполнениеДвижений';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';

'''

SQL_DELETE_DELETION_MARK = f'DELETE FROM {TABLE_NAME} WHERE deletion_mark = TRUE;'

async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        doc_date,
        doc_number,
        description,
        posted,
        deletion_mark,
        use_fill_movements,
        dataversion,
        ref_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        doc_date = EXCLUDED.doc_date,
        doc_number = EXCLUDED.doc_number,
        description = EXCLUDED.description,
        posted = EXCLUDED.posted,
        deletion_mark = EXCLUDED.deletion_mark,
        use_fill_movements = EXCLUDED.use_fill_movements,
        dataversion = EXCLUDED.dataversion
    '''
    return sql.replace("'", "")


async def main_doc_cash_correction_of_register_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(8)
    sql = await sql_insert(maket)
    sql = sql.replace("$1,", "to_timestamp($1, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    # Удаление помеченных на удаление записей
    await async_save_pg(SQL_DELETE_DELETION_MARK)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_correction_of_register_async())
