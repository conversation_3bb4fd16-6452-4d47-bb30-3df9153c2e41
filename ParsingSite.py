# https://vc.ru/newtechaudit/109368-web-parsing-osnovy-na-python
# pip3 install bs4
# pip3 install lxml
# берем курс из сайта minfin.com.ua заносим в базу
import time
import pandas as pd
import sys
import os
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timed<PERSON>ta
from prestige_authorize import CONFIG_PATH, con_postgres_psycopg2, change_data_in_table_bl

sys.path.append(os.path.abspath(CONFIG_PATH))
from error_log import add_to_log
from rate import main_rate

sql_create = '''
  CREATE TABLE IF NOT EXISTS t_course (
      id_course serial4 NOT NULL,
      course_date date NOT NULL,  -- дата курса
      course_usd_nbu numeric(10, 7) NULL DEFAULT 0,  -- курс usd НБУ
      course_euro_nbu numeric(10, 7) NULL DEFAULT 0,  -- курс usd НБУ
      course_usd_commercial_buying numeric(10, 5) NULL DEFAULT 0,  -- курс покупки среднее из сайта минфина
      course_usd_commercial_sales numeric(10, 5) NULL DEFAULT 0,  -- курс продажи среднее из сайта минфина
      date_create timestamp NULL DEFAULT now(),  -- дата/время записи
      CONSTRAINT t_course_pkey PRIMARY KEY (id_course)
  );
  COMMENT ON TABLE t_course IS 'курс нбу и в обменниках';
  
  -- Column comments
  
  COMMENT ON COLUMN t_course.course_date IS 'дата курса';
  COMMENT ON COLUMN t_course.course_usd_nbu IS 'КурсНБУ_USD';
  COMMENT ON COLUMN t_course.course_euro_nbu IS 'КурсНБУ_EURO';
  COMMENT ON COLUMN t_course.course_usd_commercial_buying IS 'КомерКурсПокупкиUSD';
  COMMENT ON COLUMN t_course.course_usd_commercial_sales IS 'КомерКурсПродажиUSD';
  COMMENT ON COLUMN t_course.date_create IS 'дата/время записи';
  '''


# парсим страницу продаж usd
def rate_usd_minfin(url):
    try:

        col = ['kur', 'phone', 'kur_time']
        df = pd.DataFrame(columns=col)
        kur_time = None
        phone = ''

        r = requests.get(url)  # отправляем HTTP запрос и получаем результат
        soup = BeautifulSoup(r.text)  # Отправляем полученную страницу в библиотеку для парсинга

        tables = soup.find_all('div', attrs={'class': 'compact-card'})  # Получаем все таблицы с вопросами

        i = 0
        for item in tables:
            try:
                i += 1

                _kur_time = item.find_all('div', attrs={'class': 'compact-card__item'})[1].text[:5]
                kur_time = datetime.strftime(datetime.today(), '%Y-%m-%d') + ' ' + _kur_time
                kur_time = datetime.strptime(kur_time, '%Y-%m-%d %H:%M')
                # если дата курса больше актуального, тогда дату курса ставим на дата - 1 (вчерашний день)
                # проблема возникает в полночь. текущая дата новая, а курс вчерашнего дня
                if kur_time > datetime.today():
                    kur_time = kur_time - timedelta(days=1)

                kur = item.find_all('div', attrs={'class': 'compact-card__item'})[0].text[:5]
                print("kur", kur)
                if kur != '-- --':
                    kur = float(kur.replace(',', '.'))

                    # _phone = item.find_all('div', attrs={'class': 'phone-reveal-button__phone'})[0].text
                    # phone = _phone.replace(' ', '') # курс средний - поэтому номер тлф не используем

                    row = {'kur': kur, 'phone': phone, 'kur_time': kur_time}
                    row2df = pd.DataFrame.from_records([row])
                    df = pd.concat([df, row2df], ignore_index=True, axis=0)  # how to handle index depends on context

            except Exception as e:
                msj = "ParsingSite:rate_usd_minfin: " + str(e)
                await add_to_log(msj)

        if kur_time is not None:
            df = df.sort_values('kur_time', ascending=True).tail(5).sort_values('kur_time', ascending=False)

        max_time = 0
        if len(df) > 0:
            avg_kur_last3 = float("%.2f" % df['kur'].head(5).mean())  # средний курс
            max_kur_last3 = float("%.2f" % df['kur'].head(5).max())  # самый высокий курс

            max_time = (df['kur_time']).max()

            print('средний/максимальный курс последних 5 значений: ', avg_kur_last3, max_kur_last3)
        else:
            avg_kur_last3 = 0

        return avg_kur_last3, max_time

    except Exception as e:
        msj = "ParsingSite:dfParsinKur: " + str(e)
        await add_to_log(msj)


# заносим курсы в базу PG
def main_parsing_site():
    conpg = con_postgres_psycopg2()
    change_data_in_table_bl(sql_create, conpg)
    kur_alis = 0
    kur_satis = 0

    try:

        url_alis = 'https://minfin.com.ua/currency/auction/usd/buy/kiev/?compact=true&order=newest'
        url_satis = 'https://minfin.com.ua/currency/auction/usd/sell/kiev/?compact=true&order=newest'

        kur_alis, alis_max_time = rate_usd_minfin(url_alis)
        kur_satis, satis_max_time = rate_usd_minfin(url_satis)

        kur_time = satis_max_time if satis_max_time > alis_max_time else alis_max_time
        kur_date = datetime.date(kur_time)
        kur_time = datetime.time(kur_time)
        sql_insert = '''
          INSERT INTO t_rate
          (rate_usd_buying, rate_usd_sales, rate_usd_nbu, rate_date, rate_time )
          SELECT %s, %s, %s, %s, %s
          WHERE NOT EXISTS        
            (SELECT * FROM t_rate 
              WHERE %s = (SELECT rate_usd_sales FROM t_rate 
                            ORDER BY date_create desc limit 1)
                AND %s = (SELECT rate_usd_buying FROM t_rate 
                        ORDER BY date_create desc limit 1)
            );
        '''
        rate_usd_nbu, rate_euro_nbu = main_rate(kur_date)
        odata = (kur_alis, kur_satis, rate_usd_nbu, rate_euro_nbu, kur_date, kur_time, kur_satis, kur_alis)
        result = change_data_in_table_bl(sql_insert, conpg, odata)
        print(result)

    except Exception as e:
        msj = "ParsingSite:main_parsing_site: " + str(e)
        await add_to_log(msj)

    finally:
        return kur_alis, kur_satis


if __name__ == '__main__':
    # current_dt = datetime.now() + timedelta(seconds=295)
    # while current_dt > datetime.now():
    while True:
        main_parsing_site()
        time.sleep(300)
