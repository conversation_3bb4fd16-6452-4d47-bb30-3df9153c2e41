import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_infreg_barcodes"
DOCUMENT = "InformationRegister_Штрихкоды"
SELECT_COLUMNS = "ЕдиницаИзмерения_Key,Качество_Key,СерияНоменклатуры_Key,ТипШтрихкода_Key," \
                 "ХарактеристикаНоменклатуры_Key,Владелец,ПредставлениеШтрихкода,скАктуальность,Штрихкод"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        barcode varchar(20) NOT NULL,
        sk_relevance bool NOT NULL,
        barcode_presentation varchar(50) NOT NULL,
        unit_of_key varchar(50) NOT NULL,
        quality_key varchar(50) NOT NULL,
        nomenclature_series_key varchar(50) NOT NULL,
        barcode_type_key varchar(50) NOT NULL,
        item_characteristic_key varchar(50) NOT NULL,
        nomenclature_key varchar(50) NOT NULL,
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (barcode, nomenclature_series_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.quality_key IS 'Качество_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.barcode_type_key IS 'ТипШтрихкода_Key';
    COMMENT ON COLUMN {TABLE_NAME}.item_characteristic_key IS 'ХарактеристикаНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Владелец';
    COMMENT ON COLUMN {TABLE_NAME}.barcode_presentation IS 'ПредставлениеШтрихкода';
    COMMENT ON COLUMN {TABLE_NAME}.sk_relevance IS 'скАктуальность';
    COMMENT ON COLUMN {TABLE_NAME}.barcode IS 'Штрихкод';
    '''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
        (
        unit_of_key,
        quality_key,
        nomenclature_series_key,
        barcode_type_key,
        item_characteristic_key,
        nomenclature_key,
        barcode_presentation,
        sk_relevance,
        barcode
        )
    VALUES {maket}
    ON CONFLICT (barcode, nomenclature_series_key) 
    DO UPDATE SET
        unit_of_key = EXCLUDED.unit_of_key,
        quality_key = EXCLUDED.quality_key,
        nomenclature_series_key = EXCLUDED.nomenclature_series_key,
        barcode_type_key = EXCLUDED.barcode_type_key,
        item_characteristic_key = EXCLUDED.item_characteristic_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        barcode_presentation = EXCLUDED.barcode_presentation,
        sk_relevance = EXCLUDED.sk_relevance,
        barcode = EXCLUDED.barcode;
    '''
    return sql.replace("'", "")


async def main_info_reg_barcodes_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_info_reg_barcodes_async())
