    DROP VIEW IF EXISTS v_one_counterparty_contracts_parameters CASCADE;
    -- не удалять. выкидывает ошибку, если нельзя менять наименование колонок
    CREATE OR REPLACE VIEW v_one_counterparty_contracts_parameters AS
    SELECT
        segments_parent.description AS segment_folder,
        segments.description AS segment,
        mngr.description AS manager,
        client.description AS customer,
        client.edrpou,
        client.inn,
        contract.limit_days_credit_ua as contract_days,
        currency.description AS currency_name,
        properties.description AS parameter_name,
        parameter.percent,
        contract.contract_name,
        contract.contract_type,
        mngr.a_comment,
        client.ref_key AS customer_key,
        contract.ref_key AS contract_key,
        contract.currency_key,
        mngr.ref_key AS manager_key
    FROM t_one_cat_counterparties AS client
        LEFT JOIN t_one_cat_counterparty_segment AS segments
            ON client.segment_key = segments.ref_key
        LEFT JOIN
            (SELECT description, ref_key
            FROM t_one_cat_counterparty_segment
            WHERE isfolder
                AND (parent_key = '00000000-0000-0000-0000-000000000000')
            ) AS segments_parent
            ON segments_parent.ref_key = segments.parent_key
        LEFT JOIN
            (
            SELECT
                description,
                a_comment,
                ref_key
            FROM t_one_cat_counterparties
            WHERE isfolder
--                AND parent_key = 'ad421841-905f-11e6-80c4-c936aa9c817c' -- 010 Покупатели
            ) AS mngr
        ON mngr.ref_key = client.parent_key
        LEFT JOIN (
            SELECT limit_days_credit_ua, description as contract_name, owner_key, ref_key, contract_type,
                settlement_currency_key as currency_key
            FROM t_one_cat_contracts_counterparties
        ) AS contract
        ON client.ref_key = contract.owner_key
        LEFT JOIN t_one_cat_currencies AS currency
            ON contract.currency_key = currency.ref_key
        LEFT JOIN
            (
            SELECT
                object_key,
                property_key,
                "value"::float AS percent
            FROM t_one_cat_object_property_values
            WHERE value_type = 'Edm.Double'
            ) AS parameter
        ON parameter.object_key = contract.ref_key
        LEFT JOIN t_one_cat_object_properties as properties
        ON properties.ref_key = parameter.property_key
    WHERE NOT client.isfolder
    ORDER BY mngr.description, client.description
    ;

    COMMENT ON VIEW v_one_manager_counterparty_contracts_segments IS 'договора с клиентами';
    GRANT SELECT ON TABLE v_one_manager_counterparty_contracts_segments TO user_prestige;

