import asyncio
import sys

sys.path.append(r'D:\Prestige\Python\Config')
from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread
import os

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_gtd_import"
DOCUMENT = "Document_ГТДИмпорт"
SELECT_COLUMNS = (
    "DataVersion,Ref_Key,Date,ВалютаДокумента_Key,КурсДокумента,НомерГТД,Number,Posted,"
    "ПоставщикТоваров_Key,ТаможенныйСбор,ОтражатьВБухгалтерскомУчете,ОтражатьВУправленческомУчете"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NOT NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        currency_key varchar(50) NOT NULL,  -- ВалютаДокумента_Key
        supplier_of_goods_key varchar(50) NOT NULL,  -- ПоставщикТоваров_Key
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        posted bool NOT NULL DEFAULT false,  -- Posted
        is_accounting bool NULL DEFAULT false,  -- ОтражатьВБухгалтерскомУчете
        is_management bool NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        document_course numeric(10, 4) NOT NULL DEFAULT 0,  -- КурсДокумента
        doc_number_gtd varchar(50) NOT NULL,  -- НомерГТД
        customs_duty numeric(10, 4) NOT NULL DEFAULT 0,  -- ТаможенныйСбор
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.supplier_of_goods_key IS 'ПоставщикТоваров_Key';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.document_course IS 'КурсДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number_gtd IS 'НомерГТД';
    COMMENT ON COLUMN {TABLE_NAME}.customs_duty IS 'ТаможенныйСбор';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        dataversion,
        ref_key,
        doc_date,
        currency_key,
        document_course,
        doc_number_gtd,
        doc_number,
        posted,
        supplier_of_goods_key,
        customs_duty,
        is_accounting,
        is_management
        )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion= EXCLUDED.dataversion,
        ref_key= EXCLUDED.ref_key,
        doc_date= EXCLUDED.doc_date,
        currency_key= EXCLUDED.currency_key,
        document_course= EXCLUDED.document_course,
        doc_number_gtd= EXCLUDED.doc_number_gtd,
        doc_number= EXCLUDED.doc_number,
        posted= EXCLUDED.posted,
        supplier_of_goods_key= EXCLUDED.supplier_of_goods_key,
        customs_duty= EXCLUDED.customs_duty,
        is_accounting= EXCLUDED.is_accounting,
        is_management= EXCLUDED.is_management
    ;    
    """
    return sql.replace("'", "")


async def main_gtd_import_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, "", "", True)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")

    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


def run_main():
    asyncio.run(main_gtd_import_async())


if __name__ == "__main__":
    asyncio.run(main_gtd_import_async())
    # # Создаем процесс для выполнения асинхронного кода
    # process = Process(target=run_main)
    # process.start()
    # process.join()
