import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_vacation_official_details"
DOCUMENT = "Document_НачислениеОтпускаРаботникамОрганизаций_Начисления"
SELECT_COLUMNS = (
    "LineN<PERSON>ber,Авторасчет,ДатаНачала,ДатаОкончания,РезультатУпр,Ref_Key,ВидРасчета_Key,"
    "Назначение_Key,ПодразделениеОрганизации_Key,Сотрудник_Key"
)

SQL_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            id bigserial NOT NULL,
            line_number numeric(10,4) NOT NULL DEFAULT 0,  -- LineNumber
            autocalculation boolean NOT NULL DEFAULT FALSE,  -- Авторасчет
            start_doc_date timestamp NOT NULL,  -- ДатаНачала
            expiration_doc_date timestamp NOT NULL,  -- ДатаОкончания
            result_control numeric(10,4) NOT NULL DEFAULT 0,  -- РезультатУпр
            ref_key varchar(50) NULL,  -- Ref_Key
            type_of_calculation_key varchar(50) NULL,  -- ВидРасчета_Key
            purpose_key varchar(50) NULL,  -- Назначение_Key
            organization_division_key varchar(50) NULL,  -- ПодразделениеОрганизации_Key
            employee_key uuid NULL,  -- Сотрудник_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
        COMMENT ON COLUMN {TABLE_NAME}.autocalculation IS 'Авторасчет';
        COMMENT ON COLUMN {TABLE_NAME}.start_doc_date IS 'ДатаНачала';
        COMMENT ON COLUMN {TABLE_NAME}.expiration_doc_date IS 'ДатаОкончания';
        COMMENT ON COLUMN {TABLE_NAME}.result_control IS 'РезультатУпр';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.type_of_calculation_key IS 'ВидРасчета_Key';
        COMMENT ON COLUMN {TABLE_NAME}.purpose_key IS 'Назначение_Key';
        COMMENT ON COLUMN {TABLE_NAME}.organization_division_key IS 'ПодразделениеОрганизации_Key';
        COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'Сотрудник_Key';
    """


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}(
            line_number, 
            autocalculation, 
            start_doc_date, 
            expiration_doc_date, 
            result_control, 
            ref_key, 
            type_of_calculation_key, 
            purpose_key, 
            organization_division_key, 
            employee_key
        )
        VALUES {maket}
        ON CONFLICT (ref_key, line_number)
        DO UPDATE SET
            line_number = EXCLUDED.line_number, 
            autocalculation = EXCLUDED.autocalculation, 
            start_doc_date = EXCLUDED.start_doc_date, 
            expiration_doc_date = EXCLUDED.expiration_doc_date, 
            result_control = EXCLUDED.result_control, 
            type_of_calculation_key = EXCLUDED.type_of_calculation_key, 
            purpose_key = EXCLUDED.purpose_key, 
            organization_division_key = EXCLUDED.organization_division_key, 
            employee_key = EXCLUDED.employee_key
    """
    return sql.replace("'", "")


async def main_doc_vacation_official_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$4,", "to_timestamp($4, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_vacation_official_details_async())
