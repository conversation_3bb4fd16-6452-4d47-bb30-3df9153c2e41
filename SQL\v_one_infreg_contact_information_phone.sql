
    CREATE OR REPLACE VIEW v_one_infreg_contact_information_phone AS
    SELECT 
    regexp_replace (UNNEST(REGEXP_MATCHES(REPLACE(performance,',','; '),
    '.?\d{10}|\(?\d{3}\)?.\d{7}|\(?\d{3}\)?.\d{2}.\d{2}.\d{3}|\d{3}.\d{3}.\d{2}.\d{2}|\d{3}.\d{2}.\d{3}.\d{2}|\d{3}.\d{3}.\d{4}|\d{4}.\d{6}|\d{6}.\d{2}.\d{2}|\d{6}.\d{2}.\d{2}|\d{8}.\d{2}|\(?\d{3}\)?.\d{5}.\d{2}','gm'))
    ,'[-(). ]*','','gm')  AS phone,
    couterpart.*,
    inf.object_type
    FROM t_one_infreg_contact_information AS inf
        LEFT JOIN t_one_cat_counterparties AS couterpart
            ON couterpart.ref_key = inf.an_object
;
    