DROP VIEW IF EXISTS v_gtd CASCADE;
CREATE OR REPLACE VIEW v_gtd AS
SELECT
	gtd_date,
	tarih,
	gtd_number,
	sum(part_customs_value) part_customs_value,
	sum(part_duty_amount) part_duty_amount,
	sum(part_amount_vat) part_amount_vat,
	serv_rate,
	sum(duty_usd) duty_usd,
	sum(vat_usd) vat_usd,
	gtd_cur,
	ref_key,
	batch_document,
	currency_key
FROM (
	SELECT DISTINCT
		imp.doc_date::date AS gtd_date,
		imp.doc_date AS tarih,
		imp.doc_number AS gtd_number,
		COALESCE(part.customs_value, 0::numeric)::numeric(10,2) AS part_customs_value,
		COALESCE(part.duty_rate, 0) AS part_duty_rate,
		COALESCE(part.duty_amount, 0::numeric)::numeric(10,2) AS part_duty_amount,
		COALESCE(part.amount_vat, 0::numeric)::numeric(10,2) AS part_amount_vat,
		(COALESCE(serv.rate_settlement, 0::numeric)
		    / COALESCE(serv.multiplicity_of_mutual_settlements, 1::numeric))::numeric(15,4) AS serv_rate,
		CASE
		    WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN  -- грн
		        (COALESCE(part.duty_amount, 0::numeric)
		            / (COALESCE(serv.rate_settlement, 1::numeric)
		            / COALESCE(serv.multiplicity_of_mutual_settlements, 1::numeric)))::numeric(10,4)
		    ELSE COALESCE(part.duty_amount, 0::numeric)
		END AS duty_usd,
		CASE
		    WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN  -- грн
		        (COALESCE(part.amount_vat, 0::numeric)
		        / (COALESCE(serv.rate_settlement, 1::numeric)
		        / COALESCE(serv.multiplicity_of_mutual_settlements, 1::numeric)))::numeric(10,4)
		    ELSE COALESCE(part.amount_vat, 0::numeric)
		END AS vat_usd,
		cur.description AS gtd_cur,
		imp.ref_key,
		goods.batch_document,
		imp.currency_key as currency_key
	FROM t_one_doc_gtd_import imp
		LEFT JOIN t_one_doc_gtd_import_goods goods ON imp.ref_key::text = goods.ref_key::text
		LEFT JOIN t_one_doc_gtd_import_partitions part ON part.ref_key::text = goods.ref_key::text
		JOIN t_one_cat_currencies cur ON cur.ref_key::text = imp.currency_key::text
		LEFT JOIN t_one_doc_receipt_of_goods_services serv ON serv.ref_key::text = goods.batch_document::text
	WHERE imp.posted
) AS t
GROUP BY
	gtd_date,
	tarih,
	gtd_number,
	serv_rate,
	gtd_cur,
	ref_key,
	batch_document,
	currency_key
;

COMMENT ON VIEW v_gtd IS 'таможенные расходы';