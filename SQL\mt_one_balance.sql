DROP MATERIALIZED VIEW IF EXISTS mt_one_balance CASCADE;
CREATE MATERIALIZED VIEW mt_one_balance AS
WITH
balance AS (
	SELECT
		segment,
		manager,
		customer,
		organization,
		currency_name,
	    doc_sum,
	    sum(doc_sum) OVER (PARTITION BY
				segment,
				manager,
				customer,
				organization,
				currency_name
			)
		AS customer_sum,
	    COALESCE(sum(doc_sum) FILTER (WHERE doc_sum < 0)
	    	OVER (PARTITION BY
				segment,
				manager,
				customer,
				organization,
				currency_name
			),0)
		    + COALESCE(sum(doc_sum) FILTER (WHERE doc_sum > 0)
		    	OVER (PARTITION BY
					segment,
					manager,
					customer,
					organization,
					currency_name
				ORDER BY
					last_date, doc_date
			),0)
		AS customer_sum_pdz,
	    COALESCE(sum(doc_sum)
	    	OVER (PARTITION BY
				segment,
				manager,
				customer,
				organization,
				currency_name
			ORDER BY
				last_date, doc_date
		),0)
		AS customer_pdz,  -- общий долг по документам
	    doc_date,
	    last_date,
		CASE
			WHEN
				last_date = max(last_date)
							OVER (PARTITION BY
								segment,
								manager,
								customer,
								organization,
								currency_name
								)
					AND ROW_NUMBER() OVER (PARTITION BY segment, manager, customer, organization, currency_name
                              ORDER BY last_date DESC) = 1
					AND last_date <= CURRENT_DATE
				THEN
					CURRENT_DATE
			ELSE
				last_date
		END AS finish_date,
		edrpou,
	    manager_key
	FROM (
		SELECT
			COALESCE(trim(seg.segment), '') segment,
			COALESCE(trim(seg.manager), '') manager,
			COALESCE(trim(seg.customer), '') customer,
			COALESCE(trim(org.organization), '') organization,
			COALESCE(trim(seg.currency_name), '') currency_name,
			dtperiod::date AS doc_date,
			CASE
				WHEN recordtype = 'Receipt' THEN
					sum(amount)
				ELSE
					-sum(amount)
			END doc_sum,
		    CASE
		        WHEN recorder_type = 'StandardODATA.Document_РеализацияТоваровУслуг' THEN
		            (dtperiod::date + INTERVAL '1 DAYS' * seg.contract_days)::date
		        ELSE
		            dtperiod::date
		    END
		    AS last_date,
		    COALESCE(trim(seg.edrpou),'') edrpou,
		    manager_key
		FROM t_one_doc_acc_reg_reciprocal_settlements_details AS req
		    LEFT JOIN v_one_manager_counterparty_contracts_segments AS seg
		        ON req.contract_key = seg.contract_key
		    LEFT JOIN v_one_organization_and_type as org
		        ON org.ref_key = req.organization_key
		GROUP BY
			dtperiod::date,
			recordtype,
			recorder_type,
			COALESCE(trim(seg.segment), ''),
			COALESCE(trim(seg.manager), ''),
			COALESCE(trim(seg.customer), ''),
			COALESCE(trim(org.organization), ''),
			COALESCE(trim(seg.currency_name), ''),
			seg.contract_days,
		    COALESCE(trim(seg.edrpou),''),
			seg.contract_days,
			seg.manager_key
		) AS t
)
SELECT
	segment,
	manager,
	customer,
	organization,
	currency_name,
	doc_sum,
	customer_pdz,  -- общий долг по иерархии
	customer_sum_pdz,  -- общий долг без иерархии
	doc_date,
	last_date,
	CASE
		WHEN last_date >= CURRENT_DATE THEN
			CURRENT_DATE - finish_date
		ELSE
			sum(between_days)
				OVER (PARTITION BY
					segment,
					manager,
					customer,
					organization,
					currency_name
					ORDER BY
						last_date, doc_date
					)
	END AS leave_days,
	edrpou,
	manager_key
FROM (
	SELECT
		segment,
		manager,
		customer,
		organization,
		currency_name,
		doc_sum,
		customer_pdz,
		doc_date,
		last_date,
		finish_date,
		customer_sum_pdz,
		CASE
		    WHEN last_date > CURRENT_DATE OR customer_sum_pdz <= 10 THEN
				0
			ELSE
				CASE
					WHEN finish_date = max(finish_date)  -- последняя строка
							OVER (PARTITION BY
								segment,
								manager,
								customer,
								organization,
								currency_name)
							THEN
						-- если последняя строка и она единстенная, т.е LAG is NULL, берем last_date из той же строки
						finish_date - COALESCE(lag(finish_date)
							OVER (PARTITION BY
								segment,
								manager,
								customer,
								organization,
								currency_name
							),last_date)
					WHEN COALESCE(lag(customer_sum_pdz)
							OVER (PARTITION BY
								segment,
								manager,
								customer,
								organization,
								currency_name
							),0) > 0
							THEN
						finish_date - COALESCE(lag(finish_date)
							OVER (PARTITION BY
								segment,
								manager,
								customer,
								organization,
								currency_name
								ORDER BY
									last_date, doc_date
							),last_date)
				END
		END	AS between_days,
		edrpou,
		manager_key
	FROM balance
) AS tl
ORDER BY
	segment,
	manager,
	customer,
	organization,
	currency_name,
	last_date,
	doc_date
;

GRANT SELECT ON mt_one_balance TO user_prestige;
