# Document_ОприходованиеТоваров
import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_price_details"
DOCUMENT = "Document_УстановкаЦенНоменклатуры_Товары"
SELECT_COLUMNS = (
    "LineNumber,Цена,ПроцентСкидкиНаценки,Ref_Key,Номенклатура_Key,ХарактеристикаНоменклатуры_Key,"
    "Валюта_Key,ЕдиницаИзмерения_Key,Тип<PERSON>ен_Key"
)

SQL_CREATE_TABLE = f"""        
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            id bigserial NOT NULL,
            line_number numeric(10,4) NOT NULL DEFAULT 0,  -- LineNumber
            price numeric(10,4) NOT NULL DEFAULT 0,  -- Цена
            percent_discount_markup numeric(10,4) NOT NULL DEFAULT 0,  -- ПроцентСкидкиНаценки
            ref_key varchar(50) NULL,  -- Ref_Key
            nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
            characteristics_key varchar(50) NULL,  -- ХарактеристикаНоменклатуры_Key
            currency_key varchar(50) NULL,  -- Валюта_Key
            unit_measurement_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
            price_type_key varchar(50) NULL,  -- ТипЦен_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
        COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';
        COMMENT ON COLUMN {TABLE_NAME}.percent_discount_markup IS 'ПроцентСкидкиНаценки';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
        COMMENT ON COLUMN {TABLE_NAME}.characteristics_key IS 'ХарактеристикаНоменклатуры_Key';
        COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'Валюта_Key';
        COMMENT ON COLUMN {TABLE_NAME}.unit_measurement_key IS 'ЕдиницаИзмерения_Key';
        COMMENT ON COLUMN {TABLE_NAME}.price_type_key IS 'ТипЦен_Key';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        line_number,
        price,
        percent_discount_markup,
        ref_key,
        nomenclature_key,
        characteristics_key,
        currency_key,
        unit_measurement_key,
        price_type_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        price = EXCLUDED.price,
        percent_discount_markup = EXCLUDED.percent_discount_markup,
        ref_key = EXCLUDED.ref_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        characteristics_key = EXCLUDED.characteristics_key,
        currency_key = EXCLUDED.currency_key,
        unit_measurement_key = EXCLUDED.unit_measurement_key,
        price_type_key = EXCLUDED.price_type_key
    """
    return sql.replace("'", "")


async def main_doc_price_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_sql_create_index(TABLE_NAME, "ref_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_price_details_async())
