
    CREATE OR REPLACE VIEW v_one_giris
    AS 
    SELECT 
        goods.id,
        serv.doc_date::date AS doc_date,
        serv.doc_date AS tarih,
        serv.doc_number,
        nom.sku,
        ns.sell_by,
        CASE 
            WHEN ns.count_of_months_of_storage <> 0 THEN 
                ns.count_of_months_of_storage
            ELSE 
                nom.count_of_months_of_storage
        END AS count_of_months_of_storage,    
        CASE 
            WHEN ns.count_of_months_of_storage <> 0 THEN 
                (ns.sell_by + '1 mon'::interval * ns.count_of_months_of_storage::double precision)::date - ns.sell_by
            ELSE 
                (ns.sell_by + '1 mon'::interval * nom.count_of_months_of_storage::double precision)::date - ns.sell_by
        END AS count_of_days_of_storage,    
        CASE
            WHEN (ns.count_of_months_of_storage) <> 0 and ((ns.sell_by - serv.doc_date::date)::int) = 0 THEN
                (ns.sell_by - serv.doc_date::date)::numeric(10,3) / -- days_between
                    ((ns.sell_by::date + interval '1 month' 
                        * ns.count_of_months_of_storage)::date - ns.sell_by::date + 1)::numeric(10,3) -- count_of_days_of_storage
            WHEN (nom.count_of_months_of_storage) <> 0 and ((ns.sell_by - serv.doc_date::date)::int) = 0 THEN
                (ns.sell_by - serv.doc_date::date)::numeric(10,3) / -- days_between
                    ((ns.sell_by::date + interval '1 month' 
                        * nom.count_of_months_of_storage)::date - ns.sell_by::date + 1)::numeric(10,3) -- count_of_days_of_storage
            ELSE
                0
        END as percent,
        CASE
            WHEN COALESCE(ns.sell_by,current_date) = current_date THEN
                0
            ELSE
                (ns.sell_by - serv.doc_date::date)::int 
        END as days_between,
        nom.inbox,
        goods.quantity,
        goods.coefficient,
        goods.quantity * goods.coefficient AS ed,
        goods.quantity * goods.coefficient / nom.inbox AS tobox,
        goods.amount,
        goods.amount_vat,
        goods.price_tr,
        (goods.price_tr * goods.quantity) as amount_price_tr,
        serv.rate,
        serv.rate_settlement,
        serv.multiplicity_of_mutual_settlements,
        serv.rate_nbu,
        serv.amount_includes_vat,
        serv.is_accounting,
        serv.is_management,
        serv.posted,
        'поступление'::text AS doc_type,
        org.description AS organization,
        nom.supplier,
        warehouses.description AS warehouse,
        serv.ref_key,
        serv.organization_key,
        goods.nomenclature_key,
        goods.nomenclature_series_key,
        goods.unit_of_key,
        nom.supplier_key,
        serv.currency_key AS currency_key,
        goods.warehouse_key,
        serv.contract_key,
        goods.line_number
       FROM t_one_doc_receipt_of_goods_services serv
         JOIN t_one_doc_receipt_of_goods_services_goods goods USING (ref_key)
         JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
         LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::TEXT
         LEFT JOIN t_one_cat_warehouses AS warehouses ON warehouses.ref_key = goods.warehouse_key
         LEFT JOIN t_one_cat_nomenclature_series AS ns ON ns.ref_key = goods.nomenclature_series_key
      ORDER BY serv.doc_date, serv.doc_number, nom.sku;
;

    COMMENT ON VIEW v_one_giris IS 'поступление';
    COMMENT ON COLUMN v_one_giris.id IS '№';
    COMMENT ON COLUMN v_one_giris.doc_date  IS 'дата';
    COMMENT ON COLUMN v_one_giris.tarih  IS 'дата_время';
    COMMENT ON COLUMN v_one_giris.ed IS 'ед по накл';
    COMMENT ON COLUMN v_one_giris.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_giris.sell_by IS 'годен до';
    COMMENT ON COLUMN v_one_giris.tobox IS 'в перечт на крб';
    COMMENT ON COLUMN v_one_giris.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_giris.days_between IS 'годен до - дата поступления';
    COMMENT ON COLUMN v_one_giris.count_of_months_of_storage IS 'кол-во мес хранения';
    COMMENT ON COLUMN v_one_giris.count_of_days_of_storage IS 'кол-во дней хранения';
    COMMENT ON COLUMN v_one_giris.doc_number IS 'номер';
    COMMENT ON COLUMN v_one_giris.sku IS 'sku';
    COMMENT ON COLUMN v_one_giris.quantity IS 'кол-во';
    COMMENT ON COLUMN v_one_giris.coefficient IS 'коэф';
    COMMENT ON COLUMN v_one_giris.ed IS 'кол-во*коэф';
    COMMENT ON COLUMN v_one_giris.doc_type IS 'тип';
    COMMENT ON COLUMN v_one_giris.supplier IS 'поставщик'; 

    GRANT SELECT ON TABLE v_one_giris TO user_prestige;       
    