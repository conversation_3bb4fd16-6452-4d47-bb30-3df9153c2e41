
    CREATE OR REPLACE VIEW v_one_salary_paid_cash
    AS 
    SELECT 
        CASE
            WHEN assignment_clause = 'зарплата' and organization_key = '62968b02-f06f-11ec-8136-001dd8b740bc' THEN
                'Мушлі Фатіх'
            ELSE
                counterparty
        END AS employee,
        dtperiod::date as doc_date,
        doc_number,
        CASE 
            WHEN EXTRACT('day' FROM dtperiod::date) < 15 AND salary_period = NULL THEN  
              concat(EXTRACT(YEAR FROM (dtperiod::date - INTERVAL '1 month'))::TEXT, 
                '.', trim(TO_CHAR(EXTRACT(MONTH FROM (dtperiod::date - INTERVAL '1 month')), '00')), 1)::date
            WHEN salary_period = NULL THEN 
              concat(EXTRACT(YEAR FROM dtperiod::date)::TEXT, '.', 
                trim(TO_CHAR(EXTRACT(MONTH FROM dtperiod::date), '00')), 1)::date
            ELSE
                salary_period        
        END salary_period,
        CASE
            WHEN type_of_cash= 'Наличные' THEN 'cash'
            ELSE 'bank'
        END sources,        
        amount_control as amount,
        description,
        recorder_key as ref_key,
        contract_key
    FROM t_one_accreg_cash_recordtype
    WHERE (assignment_clause in ('зарплата', 'Бонус') AND type_of_cash = 'Безналичные' and amount < 0)
        OR (assignment_clause in ('зарплата', 'Бонус') AND type_of_cash = 'Наличные')
        AND dtperiod::date >= '01.01.2023'::date
        AND contract_key <> 'c0347612-b071-11ec-812e-001dd8b740bc' -- Правобережне ВУВД ФССУ у м.Києві
    ;
