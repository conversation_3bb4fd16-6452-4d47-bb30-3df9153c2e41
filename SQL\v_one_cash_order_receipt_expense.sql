CREATE OR REPLACE VIEW v_one_cash_order_receipt_expense AS
SELECT DISTINCT
    t.doc_date,
    t.tarih,
    t.doc_number,
    t.customer,
    t.contract,
    --    t.amount_of_payment,
    --    t.amount_vat,
    t.amount_of_payment_sum as amount,
    t.amount_of_vat_sum as vat,
    t.amount_of_payment_sum_usd as amount_usd,
    t.amount_of_vat_sum_usd as vat_usd,
    t.a_comment,
    t.contract_days,
    t.rate_usd_nbu,
    t.currency,
    t.main_cash_flow_item_name,
    t.det_cash_flow_item_name,
    t.ref_key,
    t.currency_key,
    t.account_key,
    t.contract_key,
    t.main_cash_flow_item_key,
    t.det_cash_flow_item_key,
    t.receipt
FROM (
    SELECT doc_date,
        tarih,
        doc_number,
        customer,
        contract,
        amount_of_payment,
        amount_vat,
        amount_of_payment_sum,
        amount_of_vat_sum,
        amount_of_payment_sum_usd,
        amount_of_vat_sum_usd,
        contract_days,
        rate_usd_nbu,
        currency,
        main_cash_flow_item_name,
        det_cash_flow_item_name,
        ref_key,
        currency_key,
        account_key,
        contract_key,
        main_cash_flow_item_key::uuid,
        det_cash_flow_item_key::uuid,
        a_comment,
        'receipt'::varchar(10) AS receipt
    FROM v_one_cash_order_receipt
    UNION ALL
    SELECT
        doc_date,
        tarih,
        doc_number,
        customer,
        contract,
        -amount_of_payment,
        -amount_vat,
        -amount_of_payment_sum,
        -amount_of_vat_sum,
        -amount_of_payment_sum_usd,
        -amount_of_vat_sum_usd,
        contract_days,
        rate_usd_nbu,
        currency,
        main_cash_flow_item_name,
        det_cash_flow_item_name,
        ref_key,
        currency_key,
        account_key,
        contract_key,
        main_cash_flow_item_key::uuid,
        det_cash_flow_item_key::uuid,
        a_comment,
        'expense'::varchar(10) AS expense
    FROM v_one_cash_order_expense
   ) t
ORDER BY t.doc_date, t.doc_number;

COMMENT ON VIEW v_one_cash_order_receipt_expense IS 'ПлатежноеПоручениеВходящееИсходящее >= 01.01.2023';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.doc_number IS 'номер';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.customer IS 'контрагент';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.contract IS 'договор';
--COMMENT ON COLUMN v_one_cash_order_receipt_expense.amount_of_payment IS 'сумма';
--COMMENT ON COLUMN v_one_cash_order_receipt_expense.amount_vat IS 'НДС';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.amount IS 'сумма';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.vat IS 'НДС';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.amount_usd IS 'сумма по документу usd';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.vat_usd IS 'НДС по документу usd';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.contract_days IS 'контракт дней';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.receipt IS 'приход/расход';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.currency_key IS 'валюта_key';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.rate_usd_nbu IS 'курс НБУ';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.main_cash_flow_item_name IS 'статья движения денежных средств';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.a_comment IS 'комментарий';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.ref_key IS 'ref_key';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.account_key IS 'контрагент_key';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.contract_key IS 'договор_key';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.det_cash_flow_item_name IS 'статья движения денежных средств_key';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.currency IS 'валюта';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.doc_date IS 'дата';
COMMENT ON COLUMN v_one_cash_order_receipt_expense.tarih IS 'дата';
