import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_infreg_organization_codes"
DOCUMENT = "InformationRegister_КодыОрганизации"
SELECT_COLUMNS = "ДатаРегистрации,НомерРегистрации,Организация_Key,КодПоЕДРПОУ,ИНН"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        date_registration timestamp NOT NULL,  -- ДатаРегистрации
        number_registration varchar(25) NULL,  -- НомерРегистрации
        inn_okpo varchar(15) NULL,  -- ИННКодПоЕДРПОУ
        organization_key varchar(50) NOT NULL,  -- Организация_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.date_registration IS 'ДатаРегистрации';
    COMMENT ON COLUMN {TABLE_NAME}.number_registration IS 'НомерРегистрации';
    COMMENT ON COLUMN {TABLE_NAME}.inn_okpo IS 'ИННКодПоЕДРПОУ';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    '''

SQL_V_ONE_CREATE_VIEW_ORGANIZATIONS = f'''
    CREATE OR REPLACE VIEW v_one_organization_and_type AS
    SELECT 
        org.ref_key,
        org.description AS organization,
        (
        SELECT trim(value)
        FROM regexp_split_to_table(STRING_AGG(codes.number_registration, ', '), ',') AS value
        WHERE value != ''
        LIMIT 1)    
        AS number_registration,
        (
        SELECT trim(value)
        FROM regexp_split_to_table(STRING_AGG(codes.inn_okpo, ', '), ',') AS value
        WHERE value != ''
        LIMIT 1)  AS inn_okpo,
        CASE 
            WHEN (SELECT length(trim(value))
                FROM regexp_split_to_table(STRING_AGG(codes.inn_okpo, ', '), ',') AS value
                WHERE value != ''
                LIMIT 1) = 8 THEN True
            ELSE False
        END::boolean AS organization_type
    FROM t_one_cat_organizations AS org
        LEFT JOIN {TABLE_NAME} AS codes
            ON org.ref_key = codes.organization_key 
    GROUP BY org.ref_key, org.description
    ORDER BY org.description
    ;
    
    COMMENT ON VIEW v_one_organization_and_type IS 'организации с юр типом';

'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        date_registration,
        number_registration,
        organization_key,
        inn_okpo
    )
    VALUES {maket}
    '''
    return sql.replace("'", "")


get_edrpou = '''
    CASE
        WHEN COALESCE($4,'') <> '' THEN
            $4
        ELSE
            $5
    END
    '''


async def main_cat_organization_codes_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(4)
    sql = await sql_insert(maket)
    sql = sql.replace("$1,", "to_timestamp($1, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$4", get_edrpou)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(SQL_V_ONE_CREATE_VIEW_ORGANIZATIONS)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_organization_codes_async())
