import asyncio
import os
import sys

# Добавляем пути к модулям
my_path = os.path.dirname(os.path.abspath(__file__))
parent_path = os.path.dirname(my_path)
sys.path.insert(0, parent_path)

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_return_of_goods_from_customers"
DOCUMENT = "Document_ВозвратТоваровОтПокупателя"
SELECT_COLUMNS = (
    "Date, Number, DataVersion, СуммаДокумента, Posted, КратностьВзаиморасчетов, КурсВзаиморасчетов,"
    "НомерВходящегоДокументаЭлектронногоОбмена, Комментарий, ОтражатьВБухгалтерскомУчете,"
    "ОтражатьВУправленческомУчете, ВидОперации, ВидПоступления, СуммаВключаетНДС,УчитыватьНДС,"
    "Ref_Key, ВалютаДокумента_Key, Контрагент_Key, Организация_Key, СкладОрдер, ДоговорКонтрагента_Key,"
    "ДатаВозврата, НомерВозврата, Автор_Key, Сделка,ДатаСоздания, Ответственный_Key, АвтоматическиСоздаватьРеализацию"
)

SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NOT NULL,  --  DataVersion
        doc_date timestamp NOT NULL,  --  Date
        doc_number varchar(50) NOT NULL,  --  Number
        document_amount numeric(15, 4) NOT NULL DEFAULT 0,  --  СуммаДокумента
        amount_vat numeric(15, 2) NOT NULL DEFAULT 0,  --  СуммаНДС
        posted bool NOT NULL DEFAULT false,  --  Posted
        customer_date date NULL,  -- дата РН клиента
        customer_number varchar(50) NULL,  -- номер РН клиента
        multiplicity_of_mutual_settlements numeric(10, 4) NOT NULL DEFAULT 0,  --  КратностьВзаиморасчетов
        settlement_rate numeric(10, 4) NOT NULL DEFAULT 0,  --  КурсВзаиморасчетов
        doc_date_of_incoming_electronic_exchange_document timestamp(0) NULL,  --  ДатаВходящегоДокументаЭлектронногоОбмена
        doc_number_of_the_incoming_document_of_electronic_exchange varchar(50) NULL,  --  НомерВходящегоДокументаЭлектронногоОбмена
        description text NULL,  --  Комментарий
        is_accounting bool NOT NULL DEFAULT false,  --  ОтражатьВБухгалтерскомУчете
        is_management bool NOT NULL DEFAULT false,  --  ОтражатьВУправленческомУчете
        operation_type varchar(50) NOT NULL,  --  ВидОперации
        receipt_type varchar(50) NOT NULL,  --  ВидПоступления
        amount_includes_vat bool NOT NULL DEFAULT false,  --  СуммаВключаетНДС
        consider_vat bool NOT NULL DEFAULT false,  --  УчитыватьНДС
        date_create timestamp NULL,  -- дата создания ВН
        auto_sale boolean NOT NULL DEFAULT false,  -- АвтоматическиСоздаватьРеализацию
        ref_key varchar(50) NOT NULL,  --  Ref_Key
        currency_key varchar(50) NOT NULL,  --  ВалютаДокумента_Key
        account_key varchar(50) NOT NULL,  --  Контрагент_Key
        organization_key varchar(50) NOT NULL,  --  Организация_Key
        warehouse_key varchar(50) NOT NULL,  --  Склад_Key
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key   
        deal_key varchar(50) NULL,  -- Сделка
        user_key varchar(50) NULL,  -- Автор_Key            
        responsible_key varchar(50) NULL,  -- Ответственный_Key            
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements IS 'КратностьВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_rate IS 'КурсВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_of_incoming_electronic_exchange_document 
        IS 'ДатаВходящегоДокументаЭлектронногоОбмена';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number_of_the_incoming_document_of_electronic_exchange 
        IS 'НомерВходящегоДокументаЭлектронногоОбмена';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_type IS 'ВидПоступления';
    COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
    COMMENT ON COLUMN {TABLE_NAME}.consider_vat IS 'УчитыватьНДС';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';    
    COMMENT ON COLUMN {TABLE_NAME}.user_key IS 'Автор_Key';    
    COMMENT ON COLUMN {TABLE_NAME}.customer_date IS 'дата РН клиента';
    COMMENT ON COLUMN {TABLE_NAME}.customer_number IS 'номер РН клиента';    
    COMMENT ON COLUMN {TABLE_NAME}.deal_key IS 'Сделка';    
    COMMENT ON COLUMN {TABLE_NAME}.date_create IS 'дата создания ВН';    
    COMMENT ON COLUMN {TABLE_NAME}.responsible_key IS 'Ответственный_Key';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.auto_sale IS 'АвтоматическиСоздаватьРеализацию';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        doc_date,
        doc_number,
        dataversion,
        document_amount,
        posted,
        multiplicity_of_mutual_settlements,
        settlement_rate,
        doc_number_of_the_incoming_document_of_electronic_exchange,
        description,
        is_accounting,
        is_management,
        operation_type,
        receipt_type,
        amount_includes_vat,
        consider_vat,
        ref_key,
        currency_key,
        account_key,
        organization_key,
        warehouse_key,
        contract_key,
        customer_date,
        customer_number,
        user_key,
        deal_key,
        date_create,
        responsible_key,
        auto_sale
    )
    VALUES {maket}
    ON CONFLICT (ref_key) DO UPDATE 
    SET 
        doc_date = excluded.doc_date,
        doc_number = excluded.doc_number,
        dataversion = excluded.dataversion,
        document_amount = excluded.document_amount,
        posted = excluded.posted,
        multiplicity_of_mutual_settlements = excluded.multiplicity_of_mutual_settlements,
        settlement_rate = excluded.settlement_rate,
        doc_number_of_the_incoming_document_of_electronic_exchange = excluded.doc_number_of_the_incoming_document_of_electronic_exchange,
        description = excluded.description,
        is_accounting = excluded.is_accounting,
        is_management = excluded.is_management,
        operation_type = excluded.operation_type,
        receipt_type = excluded.receipt_type,
        amount_includes_vat = excluded.amount_includes_vat,
        consider_vat = excluded.consider_vat,
        currency_key = excluded.currency_key,
        account_key = excluded.account_key,
        organization_key = excluded.organization_key,
        warehouse_key = excluded.warehouse_key,
        contract_key = excluded.contract_key,
        customer_date = excluded.customer_date,
        customer_number = excluded.customer_number,
        user_key = excluded.user_key,
        deal_key = excluded.deal_key,
        date_create = excluded.date_create,
        responsible_key = excluded.responsible_key,
        auto_sale = excluded.auto_sale
    """
    return sql.replace("'", "")


SQL_UPDATE_COUNTERPART_NUMBER = f'''
    UPDATE {TABLE_NAME}
    SET customer_number = trim(customer_number)
    WHERE customer_number IS NOT NULL
    ;
'''

SQL_UPDATE_VAT = f"""
    UPDATE {TABLE_NAME}
    SET amount_vat = 
        CASE 
            WHEN amount_includes_vat THEN 
                (document_amount * 0.2 / 1.2)::numeric(10,4)  -- сумма продажи включает НДС
            ELSE 
                (document_amount * 0.2)::numeric(10,4)  -- сумма продажи без учета НДС
        END        
    WHERE consider_vat    
    ;
"""


async def main_doc_return_of_goods_from_customers_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, True, True)
    maket = await create_model_async(28)
    sql = await sql_insert(maket)
    sql = sql.replace("$1,", "to_timestamp($1, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$22,", "to_timestamp($22, 'YYYY-MM-DDThh24:mi:ss')::date,")
    sql = sql.replace("$26,", "to_timestamp($26, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(SQL_UPDATE_VAT)
    await async_save_pg(SQL_UPDATE_COUNTERPART_NUMBER)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    await async_sql_create_index(TABLE_NAME, "account_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")
    await async_sql_create_index(TABLE_NAME, "customer_date")
    await async_sql_create_index(TABLE_NAME, "customer_number")

    logger.info(f"FINISH")


if __name__ == "__main__":
    # main_doc_return_of_goods_from_customers()
    asyncio.run(main_doc_return_of_goods_from_customers_async())
