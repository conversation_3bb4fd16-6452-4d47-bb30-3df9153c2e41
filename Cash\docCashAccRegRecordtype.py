import asyncio
import os

from Cash.CashAssignmentGroup import main_cash_assignment_group_async
from CreateAndRunSQLScripts import create_views
from Views.AccountsReceivable import main_accounts_receivable_async
from async_Postgres import async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_accreg_cash_recordtype'
DOCUMENT = "AccumulationRegister_ДенежныеСредства_RecordType"
SELECT_COLUMNS = "Active,LineNumber,Period,Recorder,БанковскийСчетКасса,ВидДенежныхСредств,Сумма,СуммаУпр, \
                Организация_Key,Recorder_Type,RecordType,БанковскийСчетКасса_Type"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        assignment_clause varchar(300) NULL,  -- Назначение/Статья
        assignment_key varchar(50) NULL,  -- Назначение_Key
        cash_flow_item varchar(50) NULL,  -- СтатьяДвижения_Key
        cost_item_key varchar(50) NULL,  -- СтатьяЗатрат_Key
        assignment varchar(300) NULL,  -- Назначение
        clause varchar(300) NULL,  -- Статья движение дс
        cost_item  varchar(300) NULL,  -- Статья затрат
        description varchar(500) NULL,  -- Комментарий
        dtperiod timestamp NOT NULL,  -- Period
        doc_number varchar(20) NULL,  -- Номер док
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        type_of_cash varchar(50) NULL,  -- ВидДенежныхСредств
        organization varchar(50) NULL,  -- Организация
        counterparty varchar(150) NULL,  -- Контрагент
        amount numeric(15, 2) NULL,  -- Сумма
        amount_control numeric(15, 2) NULL,  -- СуммаУпр
        amount_uah numeric(15, 2) NULL,  -- Сумма в грн
        currency varchar(10) NULL,  -- Валюта
        bank_account_cashier_type varchar(100) NULL,  -- БанковскийСчетКасса_Type
        active bool NOT NULL DEFAULT false,  -- Active
        rate numeric(10,3) default 0,  -- курс
        is_accounting bool default false,  -- БухУчет
        salary_period date NULL,  -- зарплата за период
        recorder_type varchar(100) NULL,  -- Recorder_Type
        record_type varchar(50) NULL,  -- RecordType
        recorder_key varchar(50) NULL,  -- Recorder
        bank_account_cashier_key varchar(50) NULL,  -- БанковскийСчетКасса
        organization_key varchar(50) NULL,  -- Организация_Key
        currency_key varchar(50) NULL,  -- Валюта_Key
        counterparty_key varchar(50) NULL,  -- Контрагент_Key
        contract_key varchar(50) NULL,  -- Договор_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (recorder_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';   
    COMMENT ON COLUMN {TABLE_NAME}.dtperiod IS 'Period';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.clause IS 'Стать Движенияя';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item IS 'СтатьяЗатрат';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item_key IS 'СтатьяЗатрат_Key';
    COMMENT ON COLUMN {TABLE_NAME}.assignment_clause IS 'Назначение/Статья';
    COMMENT ON COLUMN {TABLE_NAME}.assignment IS 'Назначение';
    COMMENT ON COLUMN {TABLE_NAME}.assignment_key IS 'Назначение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер док';
    COMMENT ON COLUMN {TABLE_NAME}.currency IS 'Валюта';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'БухУчет';
    COMMENT ON COLUMN {TABLE_NAME}.salary_period IS 'зарплата за период';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'Валюта_Key';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty IS 'Контрагент';
    COMMENT ON COLUMN {TABLE_NAME}.organization IS 'Организация';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.type_of_cash IS 'ВидДенежныхСредств';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_control IS 'СуммаУпр';
    COMMENT ON COLUMN {TABLE_NAME}.amount_uah IS 'Сумма в грн';
    COMMENT ON COLUMN {TABLE_NAME}.active IS 'Active';
    COMMENT ON COLUMN {TABLE_NAME}.rate IS 'Курс';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_key IS 'Recorder';
    COMMENT ON COLUMN {TABLE_NAME}.bank_account_cashier_key IS 'БанковскийСчетКасса';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'Статья_Key';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS 'Recorder_Type';
    COMMENT ON COLUMN {TABLE_NAME}.record_type IS 'RecordType';
    COMMENT ON COLUMN {TABLE_NAME}.bank_account_cashier_type IS 'БанковскийСчетКасса_Type';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'Договор_Key';
    
    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;

'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        active,
        line_number,
        dtperiod,
        recorder_key,
        recorder_type,
        bank_account_cashier_key,
        bank_account_cashier_type,
        type_of_cash,
        amount,
        amount_control,
        organization_key,
        record_type
    )
    VALUES {maket}
    ON CONFLICT (recorder_key, line_number)
    DO UPDATE SET
        active = EXCLUDED.active,
        dtperiod = EXCLUDED.dtperiod,
        recorder_type = EXCLUDED.recorder_type,
        bank_account_cashier_key = EXCLUDED.bank_account_cashier_key,
        bank_account_cashier_type = EXCLUDED.bank_account_cashier_type,
        type_of_cash = EXCLUDED.type_of_cash,
        amount = EXCLUDED.amount,
        amount_control = EXCLUDED.amount_control,
        organization_key = EXCLUDED.organization_key,
        record_type = EXCLUDED.record_type
    ;
    '''
    return sql.replace("'", "")


SQL_UPDATE_ORGANIZATIONS = '''
-- Catalog_Организации
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET organization = org.description
    FROM t_one_cat_organizations AS org
    WHERE org.ref_key = rec.organization_key
'''

SQL_UPDATE_CLAUSE = '''
-- Обновление статьи платежа
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET clause = sub.description 
    FROM t_one_cat_cash_flow_item AS sub
    WHERE rec.cash_flow_item = sub.ref_key
    ;    
'''

SQL_UPDATE_ASSIGNMENT = '''
-- Обновления назначения
    UPDATE t_one_accreg_cash_recordtype
    SET "assignment" = sub.description
    FROM t_one_cat_cash_assignment AS sub
    WHERE sub.ref_key = assignment_key
        AND assignment IS NULL
    ;
'''

SQL_UPDATE_COUNTERPARTY = '''
-- Catalog_Контрагенты
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET counterparty = sub.description
    FROM t_one_cat_counterparties AS sub
    WHERE rec.counterparty_key = sub.ref_key
        AND rec.counterparty IS NULL
    ;
'''

SQL_UPDATE_INDIVIDUALS = '''
-- Catalog_ФизическиеЛица
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET counterparty = individuals.description 
    FROM t_one_cat_individuals AS individuals
    WHERE individuals.ref_key = rec.counterparty_key 
    AND COALESCE(rec.counterparty,'') = ''
    AND COALESCE(rec.counterparty_key,'00000000-0000-0000-0000-000000000000') <> '00000000-0000-0000-0000-000000000000'
    ;
'''

SQL_UPDATE_EMPLOYEES = '''
-- Catalog_СотрудникиОрганизаций
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET counterparty = employees.description 
    FROM t_one_cat_employees AS employees
    WHERE employees.ref_key::varchar(50) = rec.counterparty_key::varchar(50) 
        AND COALESCE(rec.counterparty,'') = ''
    ;
'''

SQL_UPDATE_CURRENCY = '''
-- Catalog_Валюты
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET currency = sub.description
    FROM t_one_cat_currencies AS sub
    WHERE rec.currency_key::varchar(50) = sub.ref_key::varchar(50)
    ;
'''

SQL_IS_ACCOUNTING = '''
-- БухУчет
    UPDATE t_one_accreg_cash_recordtype AS t
    SET is_accounting = (type_of_cash = 'Безналичные')
    ;
'''

SQL_COST_ITEMS = '''
-- Catalog_СтатьиЗатрат
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET cost_item = sub.description
    FROM t_one_cat_cash_cost_item AS sub
    WHERE rec.cost_item_key = sub.ref_key
    ;
'''

# ********************************

SQL_UPDATE_DATA_ORDER_RECEIPT = '''
-- ПлатежноеПоручениеВходящее
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET currency_key = sub.currency_key,
        counterparty_key = sub.account_key,
        doc_number = sub.doc_number,
        description = sub.a_comment,
        cash_flow_item = sub.cash_flow_item,
        cost_item_key = sub.cost_item_key,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_order_receipt AS sub
    WHERE sub.ref_key = rec.recorder_key
;     
'''

SQL_UPDATE_DATA_ORDER_RECEIPT_DETAILS = '''
-- ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET cash_flow_item = sub.cash_flow_item,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_order_receipt_details AS sub
    WHERE sub.ref_key = rec.recorder_key
    ;
'''

SQL_UPDATE_DATA_ORDER_EXPENSE = '''
-- ПлатежноеПоручениеИсходящее
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET currency_key = sub.currency_key,
        counterparty_key = sub.account_key,
        doc_number = sub.doc_number,
        description = sub.a_comment,
        cash_flow_item = sub.cash_flow_item,
        cost_item_key = sub.cost_item_key,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_order_expense AS sub
    WHERE sub.ref_key = rec.recorder_key
;     
'''

SQL_UPDATE_DATA_ORDER_EXPENSE_DETAILS = '''
-- ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET cash_flow_item = sub.cash_flow_item,
    contract_key = sub.contract_key
    FROM t_one_doc_cash_order_expense_details AS sub
    WHERE sub.ref_key = rec.recorder_key
    ;
'''

SQL_UPDATE_MOVEMENT = '''
-- Document_ВнутреннееПеремещениеНаличныхДенежныхСредств
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET currency_key = sub.currency_key,
        doc_number = sub.doc_number,
        description = sub.a_comment,
        cash_flow_item = sub.cash_flow_item,
        assignment_clause = 'перемещение/transfer'        
    FROM t_one_doc_cash_movement AS sub
    WHERE sub.ref_key = rec.recorder_key
    ;
'''

SQL_UPDATE_MONEY_CHECK = '''
-- Document_ДенежныйЧек
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET currency_key = sub.currency_key,
        doc_number = sub.doc_number,
        description = sub.a_comment,
        cash_flow_item = sub.cash_flow_item
    FROM t_one_doc_cash_money_check AS sub
    WHERE sub.ref_key = rec.recorder_key
    ;
'''

SQL_UPDATE_CORRECTION_OF_REGISTER = '''
-- Document_КорректировкаЗаписейРегистров
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET doc_number = sub.doc_number,
        description = sub.description,
        assignment_clause = 'перемещение/transfer'
    FROM t_one_doc_cash_correction_of_register AS sub
    WHERE sub.ref_key = rec.recorder_key
    ;
'''

SQL_UPDATE_WARRANT_EXPENSE = '''
-- Document_РасходныйКассовыйОрдер
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET doc_number = sub.doc_number,
        dtperiod = sub.doc_date,
        description = sub.a_comment,
        currency_key = sub.currency_key,
        cash_flow_item = sub.cash_flow_item,
        counterparty_key = sub.contractor_key,
        assignment_key = sub.assignment_key,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_warrant_expense AS sub
    WHERE rec.recorder_key = sub.ref_key
'''

SQL_UPDATE_WARRANT_EXPENSE_DETAILS = '''
-- Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
    UPDATE t_one_accreg_cash_recordtype AS rec 
    SET cash_flow_item = sub.cash_flow_item,
        amount_control = amount * 
            CASE
                WHEN sub.multiplicity_settlements <> 0 THEN
                    sub.settlement_rate/sub.multiplicity_settlements
                ELSE 
                    sub.settlement_rate
            END,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_warrant_expense_details AS sub
    WHERE rec.recorder_key = sub.ref_key
        AND rec.currency_key <> '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' --грн    
    ;
'''

SQL_UPDATE_WARRANT_RECEIPT = '''
-- Document_ПриходныйКассовыйОрдер
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET doc_number = sub.doc_number,
        dtperiod = sub.doc_date,
        description = sub.a_comment,
        currency_key = sub.currency_key,
        cash_flow_item = sub.cash_flow_item,
        counterparty_key = sub.contractor_key,
        assignment_key = sub.assignment_key,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_warrant_receipt AS sub
    WHERE rec.recorder_key = sub.ref_key;
'''

SQL_UPDATE_WARRANT_RECEIPT_DETAILS = '''
-- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
    UPDATE t_one_accreg_cash_recordtype AS rec 
    SET cash_flow_item = sub.cash_flow_item,
        amount_control = amount * 
            CASE
                WHEN sub.multiplicity_settlements <> 0 THEN
                    sub.settlement_rate/sub.multiplicity_settlements
                ELSE 
                    sub.settlement_rate
            END,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_warrant_receipt_details AS sub
    WHERE rec.recorder_key = sub.ref_key
        AND rec.currency_key <> '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' --грн
    ;
'''

SQL_UPDATE_WITHDRAWAL_OF_FUNDS = '''
-- Document_ПлатежныйОрдерСписаниеДенежныхСредств
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET doc_number = sub.doc_number,
        dtperiod = sub.doc_date,
        description = sub.a_comment,
        currency_key = sub.currency_key,
        cash_flow_item = sub.cash_flow_item,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_withdrawal_of_funds AS sub
    WHERE rec.recorder_key = sub.ref_key;
'''

SQL_UPDATE_WITHDRAWAL_OF_FUNDS_DETAILS = '''
-- Document_ПлатежныйОрдерСписаниеДенежныхСредств_РасшифровкаПлатежа
    UPDATE t_one_accreg_cash_recordtype AS rec 
    SET cash_flow_item = sub.cash_flow_item,
        contract_key = sub.contract_key
    FROM t_one_doc_cash_withdrawal_of_funds_details AS sub
    WHERE rec.recorder_key = sub.ref_key;
'''

SQL_UPDATE_ASSIGNMENT_AND_CLAUSE = '''
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET assignment_clause = sub.assign_clause
    FROM t_cash_assignment_group AS sub
    WHERE COALESCE(assignment_clause,'') = '' AND 
        ((rec.cash_flow_item = sub.cash_flow_item AND rec.assignment_key IS NULL)
        OR (rec.assignment_key = sub.assign_key AND rec.cash_flow_item IS NULL)
        OR (rec.cash_flow_item = sub.cash_flow_item AND rec.assignment_key = sub.assign_key))        
    ;
'''

SQL_UPDATE_MOVE = '''
-- перемещение между счетами
    UPDATE t_one_accreg_cash_recordtype AS rec
    SET assignment_clause = ag.assign_clause
    FROM t_cash_assignment_group AS ag
    WHERE rec.counterparty_key = ag.counterparty_key 
        AND rec.organization_key = ag.organization_key
        AND rec.cash_flow_item NOT IN ('7a15ee70-84ce-11e7-80d4-001dd8b79079',  -- Командировочные расходы
            '2fab422e-a66e-11e6-80c4-c936aa9c817c' -- зарплата
        )
    ;
'''

SQL_UPDATE_SING = '''
    UPDATE t_one_accreg_cash_recordtype
    SET amount = -amount,
        amount_control = -amount_control
    WHERE record_type = 'Expense';
'''

SQL_UPDATE_RECORDER_TYPE = '''
    UPDATE t_one_accreg_cash_recordtype
    SET recorder_type = REPLACE(recorder_type, 'StandardODATA.Document_', '')
    ;
'''

SQL_UPDATE_TAX = '''
    UPDATE t_one_accreg_cash_recordtype
    SET assignment_clause = 'Налог/Vergi'
    WHERE cash_flow_item = 'ed72b929-5bb0-11e9-80ea-001dd8b79079' -- 'Расчеты по налогам'
    ;
'''

GRANT_USER = '''
    GRANT SELECT ON TABLE t_one_accreg_cash_recordtype TO user_prestige;
'''


async def main_doc_cash_acc_reg_recordtype_async():
    logger.info(f"START")
    await main_cash_assignment_group_async()
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    await async_sql_create_index(TABLE_NAME, 'recorder_key')
    await async_sql_create_index(TABLE_NAME, 'dtperiod')
    await async_sql_create_index(TABLE_NAME, 'bank_account_cashier_key')
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(SQL_UPDATE_MOVEMENT)
    await async_save_pg(SQL_UPDATE_MONEY_CHECK)
    await async_save_pg(SQL_UPDATE_DATA_ORDER_EXPENSE)
    await async_save_pg(SQL_UPDATE_DATA_ORDER_EXPENSE_DETAILS)
    await async_save_pg(SQL_UPDATE_DATA_ORDER_RECEIPT)
    await async_save_pg(SQL_UPDATE_DATA_ORDER_RECEIPT_DETAILS)
    await async_save_pg(SQL_UPDATE_WARRANT_EXPENSE)
    await async_save_pg(SQL_UPDATE_WARRANT_EXPENSE_DETAILS)
    await async_save_pg(SQL_UPDATE_WARRANT_RECEIPT)
    await async_save_pg(SQL_UPDATE_WARRANT_RECEIPT_DETAILS)
    await async_save_pg(SQL_UPDATE_WITHDRAWAL_OF_FUNDS)
    await async_save_pg(SQL_UPDATE_WITHDRAWAL_OF_FUNDS_DETAILS)
    await async_save_pg(SQL_UPDATE_CORRECTION_OF_REGISTER)
    await async_save_pg(SQL_UPDATE_MOVE)

    await async_save_pg(SQL_UPDATE_CLAUSE)
    await async_save_pg(SQL_UPDATE_ASSIGNMENT)
    await async_save_pg(SQL_UPDATE_ORGANIZATIONS)
    await async_save_pg(SQL_UPDATE_COUNTERPARTY)
    await async_save_pg(SQL_UPDATE_INDIVIDUALS)
    await async_save_pg(SQL_UPDATE_EMPLOYEES)
    await async_save_pg(SQL_UPDATE_CURRENCY)
    await async_save_pg(SQL_COST_ITEMS)
    await async_save_pg(SQL_UPDATE_TAX)
    await async_save_pg(SQL_UPDATE_ASSIGNMENT_AND_CLAUSE)  # should be here
    await async_save_pg(SQL_UPDATE_RECORDER_TYPE)
    await async_save_pg(SQL_UPDATE_SING)
    await async_save_pg(SQL_IS_ACCOUNTING)

    await create_views()
    await main_accounts_receivable_async()
    await async_save_pg(GRANT_USER)
    logger.info(f"FINISH")

#
# async def main():
#     '''постоянно выкидывает ошибку:
#     asyncpg.exceptions.InFailedSQLTransactionError:
#     текущая транзакция прервана, команды до конца блока транзакции игнорируются
#     '''
#
#     tasks = [
#         async_save_pg(SQL_UPDATE_MOVEMENT),
#         async_save_pg(SQL_UPDATE_MONEY_CHECK),
#         async_save_pg(SQL_UPDATE_DATA_ORDER_EXPENSE),
#         async_save_pg(SQL_UPDATE_DATA_ORDER_EXPENSE_DETAILS),
#         async_save_pg(SQL_UPDATE_DATA_ORDER_RECEIPT),
#         async_save_pg(SQL_UPDATE_DATA_ORDER_RECEIPT_DETAILS),
#         async_save_pg(SQL_UPDATE_WARRANT_EXPENSE),
#         async_save_pg(SQL_UPDATE_WARRANT_EXPENSE_DETAILS),
#         async_save_pg(SQL_UPDATE_WARRANT_RECEIPT),
#         async_save_pg(SQL_UPDATE_WARRANT_RECEIPT_DETAILS),
#         async_save_pg(SQL_UPDATE_WITHDRAWAL_OF_FUNDS),
#         async_save_pg(SQL_UPDATE_WITHDRAWAL_OF_FUNDS_DETAILS),
#         async_save_pg(SQL_UPDATE_CORRECTION_OF_REGISTER),
#         async_save_pg(SQL_UPDATE_MOVE),
#         async_save_pg(SQL_UPDATE_CLAUSE),
#         async_save_pg(SQL_UPDATE_ASSIGNMENT),
#         async_save_pg(SQL_UPDATE_ORGANIZATIONS),
#         async_save_pg(SQL_UPDATE_COUNTERPARTY),
#         async_save_pg(SQL_UPDATE_INDIVIDUALS),
#         async_save_pg(SQL_UPDATE_EMPLOYEES),
#         async_save_pg(SQL_UPDATE_CURRENCY),
#         async_save_pg(SQL_COST_ITEMS),
#         async_save_pg(SQL_UPDATE_TAX),
#         async_save_pg(SQL_UPDATE_ASSIGNMENT_AND_CLAUSE),
#         async_save_pg(SQL_UPDATE_RECORDER_TYPE),
#         async_save_pg(SQL_UPDATE_SING),
#         async_save_pg(SQL_IS_ACCOUNTING),
#         async_save_pg(GRANT_USER)
#     ]
#
#     await asyncio.gather(*tasks)
#

if __name__ == "__main__":
    asyncio.run(main_doc_cash_acc_reg_recordtype_async())
    # asyncio.run(main())
