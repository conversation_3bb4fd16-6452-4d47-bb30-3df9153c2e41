import os
import subprocess
import sys
import time


def get_service_status(service_name):
    try:
        result = subprocess.run(['sc', 'query', service_name], capture_output=True, text=True, check=True)
        output = result.stdout

        if "STATE" in output:
            for line in output.split('\n'):
                if "STATE" in line:
                    state = int(line.split(':')[1].strip().split(' ')[0])
                    return state
        return None

    except subprocess.CalledProcessError as e:
        print(f"Error occurred while querying the service status: {e}")
        return None


def restart_postgresql(service_name, max_attempts=5, delay=5):
    try:
        for attempt in range(max_attempts):
            state = get_service_status(service_name)

            if state == 4:  # RUNNING
                print(f'Service {service_name} is running, restarting...')
                subprocess.run(['net', 'stop', service_name], check=True)
                time.sleep(delay)  # Ждем, пока служба полностью остановится
                subprocess.run(['net', 'start', service_name], check=True)
                print(f'Service {service_name} restarted successfully.')
                return True
            elif state == 1:  # STOPPED
                print(f'Service {service_name} is stopped, starting...')
                subprocess.run(['net', 'start', service_name], check=True)
                print(f'Service {service_name} started successfully.')
                return True
            elif state == 3:  # STOP_PENDING
                print(f'Service {service_name} is stopping. Attempt {attempt + 1}/{max_attempts}')
                time.sleep(delay)  # Ждем, пока служба остановится
            else:
                print(f'Service {service_name} is in a transitional state: {state}')
                time.sleep(delay)

        print(f'Failed to restart service {service_name} after {max_attempts} attempts.')
        return False

    except subprocess.CalledProcessError as e:
        print(f'Error occurred while managing PostgreSQL service: {e}')
        return False


def find_postgresql_service_name():
    try:
        result = subprocess.run(['sc', 'query', 'type=', 'service', 'state=', 'all'], capture_output=True, text=True,
                                check=True)
        output = result.stdout

        import re
        pattern = re.compile(r'SERVICE_NAME:\s+postgresql.*', re.IGNORECASE)
        matches = pattern.findall(output)

        if matches:
            service_name = matches[0].split(':')[1].strip()
            return service_name
        else:
            return None

    except subprocess.CalledProcessError as e:
        print(f"Error occurred while querying the service list: {e}")
        return None


def restart_postgresql_service():
    # Добавляем корневой каталог проекта в sys.path
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
    service_name = find_postgresql_service_name()
    if service_name:
        print(f'Found PostgreSQL service: {service_name}')

        success = restart_postgresql(service_name)
        if success:
            print('PostgreSQL service managed successfully.')
        else:
            print('Failed to manage PostgreSQL service.')
    else:
        print('PostgreSQL service not found.')



if __name__ == "__main__":
    restart_postgresql_service()
