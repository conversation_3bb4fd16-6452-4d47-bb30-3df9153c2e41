DROP VIEW IF EXISTS v_one_cash_order_expense CASCADE;
CREATE OR REPLACE VIEW v_one_cash_order_expense AS
SELECT
    r.doc_date::date AS doc_date,
    r.doc_date AS tarih,
    r.doc_number,
    clients.description AS customer,
    contracts.description AS contract,
    COALESCE(rd.amount_of_payment, 0::numeric) AS amount_of_payment,
    COALESCE(rd.amount_vat, 0::numeric) AS amount_vat,
    sum(rd.amount_of_payment) OVER (PARTITION BY rd.ref_key) AS amount_of_payment_sum,
    sum(rd.amount_vat) OVER (PARTITION BY rd.ref_key) AS amount_of_vat_sum,
    CASE
        WHEN r.currency_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- UAH
            round(sum(rd.amount_of_payment) OVER (PARTITION BY rd.ref_key) / rate.rate_usd_nbu, 3)
        ELSE
            round(sum(rd.amount_of_payment) OVER (PARTITION BY rd.ref_key), 3)
    END AS amount_of_payment_sum_usd,
    CASE
        WHEN r.currency_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN
            round(sum(rd.amount_vat) OVER (PARTITION BY rd.ref_key) / rate.rate_usd_nbu, 3)
        ELSE
            round(sum(rd.amount_vat) OVER (PARTITION BY rd.ref_key), 3)
    END AS amount_of_vat_sum_usd,
    COALESCE(contracts.limit_days_credit_ua, 0) AS contract_days,
    rate.rate_usd_nbu,
    item_main.description AS main_cash_flow_item_name,
    item_det.description AS det_cash_flow_item_name,
    cur.description AS currency,
    r.a_comment,
    rd.line_number,
    r.ref_key,
    r.organization_key,
    r.currency_key,
    r.account_key,
    rd.contract_key,
    r.cash_flow_item::varchar(40) AS main_cash_flow_item_key,
    rd.cash_flow_item::varchar(40) AS det_cash_flow_item_key,
    r.is_management
FROM t_one_doc_cash_order_expense r
    LEFT JOIN t_one_doc_cash_order_expense_details rd
        USING (ref_key)
    LEFT JOIN t_one_cat_currencies cur
        ON r.currency_key::text = cur.ref_key::text
    LEFT JOIN t_one_cat_counterparties clients
        ON r.account_key::text = clients.ref_key::text
    LEFT JOIN t_one_cat_contracts_counterparties contracts
        ON rd.contract_key::text = contracts.ref_key::text
    LEFT JOIN t_rate_nbu rate
        ON rate.rate_date = r.doc_date::date
    LEFT JOIN t_one_cat_cash_flow_item item_main
        ON rd.cash_flow_item::varchar(40) = item_main.ref_key::varchar(40)
    LEFT JOIN t_one_cat_cash_flow_item item_det
        ON r.cost_item_key::varchar(40) = item_det.ref_key::varchar(40)
WHERE r.isposted
    AND doc_date::Date >= '01.01.2023'
    AND operation_type <> 'ПереводНаДругойСчет'
ORDER BY r.doc_date, r.doc_number;

COMMENT ON VIEW v_one_cash_order_expense IS 'ПлатежноеПоручениеИсходящее >= 01.01.2023';

COMMENT ON COLUMN v_one_cash_order_expense.doc_number IS 'номер документа';
COMMENT ON COLUMN v_one_cash_order_expense.customer IS 'контрагент';
COMMENT ON COLUMN v_one_cash_order_expense.contract IS 'договор';
COMMENT ON COLUMN v_one_cash_order_expense.amount_of_payment IS 'сумма оплаты';
COMMENT ON COLUMN v_one_cash_order_expense.amount_vat IS 'НДС';
COMMENT ON COLUMN v_one_cash_order_expense.amount_of_payment_sum IS 'сумма по документу';
COMMENT ON COLUMN v_one_cash_order_expense.amount_of_vat_sum IS 'НДС по документу';
COMMENT ON COLUMN v_one_cash_order_expense.amount_of_payment_sum_usd IS 'сумма по документу usd';
COMMENT ON COLUMN v_one_cash_order_expense.amount_of_vat_sum_usd IS 'НДС по документу usd';
COMMENT ON COLUMN v_one_cash_order_expense.contract_days IS 'лимит дней задолженности';
COMMENT ON COLUMN v_one_cash_order_expense.rate_usd_nbu IS 'курс НБУ';
COMMENT ON COLUMN v_one_cash_order_expense.currency IS 'валюта';
COMMENT ON COLUMN v_one_cash_order_expense.ref_key IS 'Ref_Key';
COMMENT ON COLUMN v_one_cash_order_expense.currency_key IS 'Валюта_Key';
COMMENT ON COLUMN v_one_cash_order_expense.account_key IS 'Контрагент_Key';
COMMENT ON COLUMN v_one_cash_order_expense.contract_key IS 'ДоговорКонтрагента_Key';
COMMENT ON COLUMN v_one_cash_order_expense.doc_date IS 'дата документа';
COMMENT ON COLUMN v_one_cash_order_expense.a_comment IS 'комментарий';
COMMENT ON COLUMN v_one_cash_order_expense.line_number IS 'номер строки';
COMMENT ON COLUMN v_one_cash_order_expense.organization_key IS 'Организация_Key';
COMMENT ON COLUMN v_one_cash_order_expense.main_cash_flow_item_key IS 'СтатьяДвиженияДенежныхСредств_Key';
COMMENT ON COLUMN v_one_cash_order_expense.det_cash_flow_item_key IS 'СтатьяДвиженияДенежныхСредствДет_Key';
COMMENT ON COLUMN v_one_cash_order_expense.main_cash_flow_item_name IS 'СтатьяДвиженияДенежныхСредств';
COMMENT ON COLUMN v_one_cash_order_expense.det_cash_flow_item_name IS 'СтатьяДвиженияДенежныхСредствДет';
