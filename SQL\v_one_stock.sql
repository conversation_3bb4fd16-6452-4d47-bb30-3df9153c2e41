
    CREATE OR REPLACE VIEW v_one_stock
    AS SELECT t.supplier,
        t.customer,
        t.sku,
        t.warehouse,
        t.doc_date,
        t.tarih,
        t.doc_number,
        t.inbox,
        t.quantity,
        t.coefficient,
        t.ed,
        sum(t.ed) OVER (PARTITION BY t.warehouse_key, t.nomenclature_key, t.nomenclature_series_key ORDER BY t.tarih, 
            t.doc_number, t.id) AS stock_ed,
        t.ed / t.inbox AS tobox,
        sum(t.ed / t.inbox) OVER (PARTITION BY t.warehouse_key, t.nomenclature_key, t.nomenclature_series_key 
            ORDER BY t.tarih, t.doc_number, t.id) AS stock_box,
        coalesce(t.maliyet,0) as maliyet,
        coalesce(t.price_tr,0) AS entry_price,
        t.doc_type,
        t.organization,
        t.organization_key,
        t.nomenclature_key,
        t.nomenclature_series_key,        
        t.document_key,
        t.unit_of_key,
        t.warehouse_key,
        t.supplier_key,
        t.customer_key,
        t.contract_key,
        t.line_number,
        t.auto_sale
       FROM ( SELECT
                srv.id, 
                srv.sku,
                srv.inbox,
                srv.tarih::date AS doc_date,
                srv.tarih,
                srv.doc_number,
                srv.quantity,
                srv.coefficient,
                srv.ed,
                mlt.maliyet,
                mlt.price_tr,
                srv.doc_type,
                srv.nomenclature_key,
                srv.nomenclature_series_key,        
                srv.unit_of_key,
                srv.ref_key AS document_key,
                srv.organization,
                srv.organization_key,
                srv.supplier,
                srv.supplier_key,
                NULL::text AS customer,
                NULL::text AS customer_key,
                srv.warehouse,
                srv.warehouse_key,
                srv.contract_key,
                srv.line_number,
                FALSE auto_sale
               FROM v_one_giris_and_girisiade srv
                 LEFT JOIN v_service_tc_maliyet mlt 
                    ON mlt.batch_document::text = srv.ref_key::text AND mlt.id = srv.id
            UNION ALL
            SELECT
                srv.id,
                srv.sku,
                srv.inbox,
                srv.doc_date::date doc_date,
                srv.doc_date as tarih,
                srv.doc_number,
                srv.quantity,
                srv.coefficient,
                srv.ed,
                0 AS maliyet,
                0 AS entry_price,
                srv.doc_type,
                srv.nomenclature_key,
                srv.series_key,
                srv.unit_key,
                srv.ref_key AS document_key,
                srv.organization,
                srv.organization_key,
                srv.supplier,
                srv.supplier_key,
                srv.customer,
                srv.customer_key,
                srv.warehouse,
                srv.warehouse_key,
                NULL::text as contract_key,
                srv.line_number,
                FALSE auto_sale
            FROM v_one_completion srv
            UNION ALL
             SELECT 
                srv.id, 
                srv.sku,
                srv.inbox,
                srv.doc_date,
                srv.tarih,
                srv.doc_number,
                srv.quantity,
                srv.coefficient,
                srv.ed,
                0 AS maliyet,
                0 AS entry_price,
                srv.doc_type,
                srv.nomenclature_key,
                srv.nomenclature_series_key,
                srv.unit_of_key,
                srv.ref_key AS document_key,
                srv.organization,
                srv.organization_key,
                srv.supplier,
                srv.supplier_key,
                srv.customer,
                srv.customer_key,
                srv.warehouse,
                srv.warehouse_key,
                NULL::text as contract_key,
                srv.line_number,
                FALSE auto_sale
               FROM v_one_trebovanie srv
            UNION ALL
                SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    srv.quantity,
                    srv.coefficient,
                    srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.ref_key AS document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    srv.customer,
                    srv.customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    srv.contract_key,
                    srv.line_number,
                    srv.auto_sale
                FROM v_one_sale_and_salereturn srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.ref_key AS document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    srv.customer,
                    srv.customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text as contract_key,
                    srv.line_number,
                    FALSE auto_sale
                   FROM v_one_oprihodivanie srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.ref_key AS document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    srv.customer,
                    srv.customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text as contract_key,
                    srv.line_number,
                    FALSE auto_sale
                   FROM v_one_spisanie srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    srv.quantity,
                    srv.coefficient,
                    srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse_in AS warehouse,
                    srv.warehouse_in_key AS warehouse_key,
                    NULL::text contract_key,
                    srv.line_number,
                    FALSE auto_sale
                   FROM v_one_movement srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text contract_key,
                    srv.line_number,
                    FALSE auto_sale
                   FROM v_one_correction srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    srv.quantity,
                    srv.coefficient,
                    srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_new_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse,
                    srv.warehouse_key,
                    NULL::text contract_key,
                    srv.line_number,
                    FALSE auto_sale
                 FROM v_one_correction srv
            UNION ALL
                 SELECT 
                    srv.id, 
                    srv.sku,
                    srv.inbox,
                    srv.doc_date,
                    srv.tarih,
                    srv.doc_number,
                    -srv.quantity,
                    srv.coefficient,
                    -srv.ed,
                    0 AS maliyet,
                    0 AS entry_price,
                    srv.doc_type,
                    srv.nomenclature_key,
                    srv.nomenclature_series_key,
                    srv.unit_of_key,
                    srv.document_key,
                    srv.organization,
                    srv.organization_key,
                    srv.supplier,
                    srv.supplier_key,
                    NULL::text AS customer,
                    NULL::text AS customer_key,
                    srv.warehouse_out AS warehouse,
                    srv.warehouse_out_key AS warehouse_key,
                    NULL::text contract_key,
                    srv.line_number,
                    FALSE auto_sale
                   FROM v_one_movement srv
        ) t
         LEFT JOIN
         	(SELECT *
         	FROM t_one_cat_units
         	WHERE NOT deletion_mark
         	)AS units
            ON t.unit_of_key::text = units.ref_key::text 
                AND t.nomenclature_key::text = units.nomenclature_key::text
      ORDER BY t.nomenclature_key, t.tarih
  ;
