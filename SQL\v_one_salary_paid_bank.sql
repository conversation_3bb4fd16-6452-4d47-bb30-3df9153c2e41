
CREATE OR REPLACE VIEW v_one_salary_paid_bank
AS SELECT DISTINCT 
    org.description AS organization,
    bank.doc_date::date AS doc_date,
    bank.doc_number,
    emp.description AS employee,
    sp.amount_management AS amount,
    concat(EXTRACT(year FROM slr.registration_period), '.', 
        TRIM(BOTH FROM to_char(EXTRACT(month FROM slr.registration_period), '00'::text)))::numeric(6,2) AS salary_period,
    clause.description AS clause,
    sp.ref_key,
    sp.employee_key,
    cash_flow_item
   FROM t_one_cat_organizations org  -- Catalog_Организации
     JOIN t_one_doc_salary slr  -- Document_ЗарплатаКВыплатеОрганизаций
        ON org.ref_key::text = slr.organization_key::text
     LEFT JOIN t_one_doc_salary_payment sp -- Document_ЗарплатаКВыплатеОрганизаций_РаботникиОрганизации
        ON slr.ref_key::text = sp.ref_key::text
     LEFT JOIN t_one_cat_employees emp  -- Catalog_СотрудникиОрганизаций
        ON emp.ref_key::text = sp.employee_key::text
     LEFT JOIN t_one_doc_salary_payment_of_wages wgs  -- Document_ПлатежноеПоручениеИсходящее_ВыплатаЗаработнойПлаты
        ON wgs.payroll_key::text = slr.ref_key::text
     LEFT JOIN t_one_doc_cash_order_expense bank  -- Document_ПлатежноеПоручениеИсходящее
        ON bank.ref_key::text = wgs.ref_key::TEXT
     LEFT JOIN t_one_cat_cash_flow_item AS clause  -- Catalog_СтатьиДвиженияДенежныхСредств
        ON clause.ref_key = cash_flow_item
     WHERE slr.doc_date::date >= '01.01.2023'::date
    ;
