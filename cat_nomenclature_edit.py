from datetime import datetime, <PERSON><PERSON>ta
import sys
import os
from prestige_authorize import CONFIG_PATH
sys.path.append(os.path.abspath(CONFIG_PATH))
from configPrestige import URL_CONST
from prestige_authorize import send_request

date_start = datetime.date(datetime.today() - timed<PERSON>ta(days=2494))


def main_rate_nbu():
    method = "PATCH"
    DOCUMENT = 'Catalog_Номенклатура/'
    url = URL_CONST + DOCUMENT

    data_json = {
        "Ref_Key": 'd14e9f1b-35b8-11ed-8145-001dd8b72b55',
        "КоличествоМесяцевХранения": 12
    }

    result = send_request(url=url, method=method, data_json=data_json)
    print(result)
    pass


if __name__ == "__main__":
    main_rate_nbu()
