# -*- coding: utf-8 -*-

import asyncio
import os
from datetime import datetime
from datetime import date
from aiogram import types  # pip install -U aiogram
from aiogram.types import Message
from dateutil.parser import parse

from asyncPostgres import (
    async_save_pg,
    get_data_from_one_rows,
    get_one_value_from_one_column_async,
)
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_stickers"

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id serial4 NOT NULL,
        idchat numeric NOT NULL,
        datesticker date NOT NULL,
        quantity numeric(10, 2) NOT NULL,
        sku_code varchar NULL,
        amount numeric(15, 2) NULL,
        date_create timestamp NULL DEFAULT now(),
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id)
    );
"""

SQL_FN_STICKER = """
    CREATE OR REPLACE FUNCTION fn_sticker()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
        BEGIN

            IF NEW.idchat IN (132774669, 490323168) THEN 
                NEW.quantity = -abs(NEW.quantity);
            END IF;

            IF lower(NEW.sku_code) = 'gy' THEN
                NEW.amount = NEW.quantity * 0.8;        
                NEW.sku_code = 'G&Y';    
            ELSEIF lower(NEW.sku_code) = 'ч' AND NEW.datesticker <'27.10.2023'::date THEN
                NEW.amount = NEW.quantity * 2;        
                NEW.sku_code = 'чай весовой без пломбы';    
            ELSEIF lower(NEW.sku_code) = 'ч' THEN
                NEW.amount = NEW.quantity * 1.5;        
                NEW.sku_code = 'чай весовой без пломбы';    
            ELSEIF lower(NEW.sku_code) = 'чп' AND NEW.datesticker <'27.10.2023'::date THEN
                NEW.amount = NEW.quantity * 5;
                NEW.sku_code = 'чай пакетированный';
            ELSEIF lower(NEW.sku_code) = 'чп' AND NEW.datesticker <'23.11.2023'::date 
                AND NEW.datesticker >='27.10.2023'::date THEN
                    NEW.amount = NEW.quantity * 3;
                    NEW.sku_code = 'чай пакетированный';
            ELSEIF lower(NEW.sku_code) = 'чп' THEN
                NEW.amount = NEW.quantity * 3;
                NEW.sku_code = 'чай пакетированный';
            ELSEIF lower(NEW.sku_code) = 'mesh' THEN
                NEW.amount = NEW.quantity * 6;
                NEW.sku_code = 'чай mesh PMS 12шт + 1 на ящ';
            ELSEIF lower(NEW.sku_code) = 'ms' THEN
                NEW.amount = NEW.quantity * 0.2;
                NEW.sku_code = 'чай mesh PMS стикер';
            ELSEIF lower(NEW.sku_code) = 'mskrb' THEN
                NEW.amount = NEW.quantity * 0.1;
                NEW.sku_code = 'чай mesh PMS крб';
            ELSEIF lower(NEW.sku_code) = 'с' THEN
                NEW.sku_code = 'стикер для чая';
                NEW.amount = NEW.quantity * 0.5;
            ELSEIF lower(NEW.sku_code) = 'чс' AND NEW.datesticker <'23.11.2023'::date THEN
                NEW.sku_code = 'чай весовой стикированный';
                NEW.amount = NEW.quantity * 2;
            ELSEIF lower(NEW.sku_code) = 'чс' AND NEW.datesticker >= '28.11.2023'::date THEN
                NEW.sku_code = 'чай весовой стикированный';
                NEW.amount = NEW.quantity * 2;
            ELSEIF lower(NEW.sku_code) = 'чс' THEN
                NEW.sku_code = 'чай весовой стикированный';
                NEW.amount = NEW.quantity * 1.5;
            ELSEIF lower(NEW.sku_code) = 'lpkrb' THEN
                NEW.sku_code = 'Luppo стикер на коробку';
                NEW.amount = NEW.quantity * 2;
            ELSEIF lower(NEW.sku_code) IN ('brgr') AND NEW.datesticker >= '01.08.2024'::date THEN
                NEW.sku_code = 'маркировка';
                NEW.amount = NEW.quantity * 1.05;
            ELSEIF (lower(NEW.sku_code) IN ('brgr') OR lower(NEW.sku_code) IN ('brgr15'))
                    AND NEW.datesticker >= '30.04.2024'::date THEN
                NEW.sku_code = 'маркировка';
                NEW.amount = NEW.quantity * 1.5;
            ELSEIF lower(NEW.sku_code) IN ('brgr') AND NEW.datesticker < '30.04.2024'::date THEN
                NEW.sku_code = 'даты на Бургер';
                NEW.amount = NEW.quantity * 2;
            ELSEIF lower(NEW.sku_code) IN ('dt') THEN
                NEW.sku_code = 'даты';
                NEW.amount = NEW.quantity * 0.8;
            ELSEIF lower(NEW.sku_code) = 'fs' AND CURRENT_DATE >= '22.07.2024'::date THEN
                NEW.sku_code = 'расфасовка просрочки';
                NEW.amount = 850;
            ELSEIF lower(NEW.sku_code) = 'fs' THEN
                NEW.sku_code = 'расфасовка просрочки';
                NEW.amount = 750;
            ELSEIF (SELECT fn_is_numeric(NEW.sku_code)) AND CURRENT_DATE >= '26.07.2024'::date THEN 
                NEW.amount = NEW.quantity * 0.3;
            ELSEIF (SELECT fn_is_numeric(NEW.sku_code)) THEN 
                NEW.amount = NEW.quantity * 0.15;
            ELSE 
                RETURN NULL;
            END IF;            

            RETURN NEW;

        END;
    $function$
    ;
"""

SQL_FN_IS_NUMERIC = """
    CREATE OR REPLACE FUNCTION fn_is_numeric(input_string text)
     RETURNS boolean
     LANGUAGE plpgsql
    AS $function$
    BEGIN
        RETURN input_string ~ '^[0-9]+$';
    END;
    $function$
    ;

"""

SQL_CREATE_TRIGGER = f"""
    CREATE TRIGGER bfr_stickers BEFORE
    INSERT
        OR
    UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_sticker();
"""


# получить общее количество и сумму за день
async def total_for_day(idchata, date):
    result = []
    try:
        sql = (
            f"SELECT sum(quantity), sum(amount) FROM {TABLE_NAME} "
            f"WHERE idchat = $1 and datesticker = to_timestamp($2, 'DD.MM.YY')::date"
        )
        result.append(date)
        rows = await get_data_from_one_rows(sql, idchata, date)
        result.append(rows[0])
        result.append(rows[1])
        logger.info(result)

    except Exception as e:
        logger.info(f"ERROR: {e}")

    return result


async def total_for_month(idchata, date):
    result = []
    try:
        month = parse(date, dayfirst=True).month
        year = parse(date, dayfirst=True).year
        odata = (idchata, year, month)
        sql = (
            f"SELECT sum(amount) FROM {TABLE_NAME} "
            "WHERE idchat = $1 "
            "    AND EXTRACT(YEAR FROM datesticker) = $2"
            "    AND EXTRACT(MONTH FROM datesticker) = $3"
        )
        rows = await get_data_from_one_rows(sql, *odata)
        result.append(rows[0])
        logger.info(f"{date} {result}")

    except Exception as e:
        logger.info(f"ERROR: {e}")

    return result


async def is_date_first_args(msg: str):
    try:
        return parse(msg, dayfirst=True).date()
    except Exception as e:
        return None


async def is_not_correct_date(msg_date):
    result = None
    msg_date = is_date(msg_date)
    if not msg_date:
        return None

    interval = datetime.today().date() - msg_date
    if interval.days < 0:
        result = "Дата стикера не может быть будущей.\nДанные не сохранены!"
    elif interval.days > 7:
        result = "Интервал не может быть больше 7 дней.\nДанные не сохранены!"
    return result


def is_date(string):
    try:
        if not isinstance(string, date):
            return parse(string, dayfirst=True).date()
        else:
            return string

    except ValueError:
        return False


async def is_numeric_args(msg: str):
    try:
        if msg.isnumeric():
            return True
        else:
            return False
    except ValueError:
        return False


async def is_correct_count_args(msg: str):
    count = len(msg.split(" "))
    if count == 3:
        return msg.split(" ")
    return None


async def is_stickers(message: types.Message):
    result = {}
    msg = message.text
    try:
        chatid = message.chat.id

        # проверяем первый аргумент - это дата?
        date_sticker = msg[0]  # await is_date_first_args(msg[0])
        quantity = msg[1]  # await get_type_word(msg[1])
        sku = msg[2]  # msg[2]

        # первый аргумент - это дата. проверяем на корректность относительно текущей даты
        date_not_correct = await is_not_correct_date(date_sticker)
        if date_not_correct:  # здесь именно так, не if not date_not_correct
            return {"False": date_not_correct}  # date_not_correct содержит текст ошибки
        date = date_sticker.strftime("%d.%m.%Y")

        data = (chatid, date, quantity, sku)
        id = await save_sticker(*data)
        if id:
            for_day = await total_for_day(chatid, date)
            date_from_msg = for_day[0]
            quantity = for_day[1]
            amount = for_day[2]

            # result.append({"за день: ": str(for_day[0])})
            # result.append({"количество: ": str(for_day[1])})
            # result.append({"сумма: ": str(for_day[2])})
            for_month = await total_for_month(chatid, date)
            # result.append({"мес: ": str(for_month[0])})
            result = f"За день: {date_from_msg}\nколичество: {quantity}\nсумма: {amount}\nмес: {for_month[0]}"
            return {True: result}
        else:
            return {False, "Не распознано SKU.\nДанные не сохранены!"}
    except Exception as e:
        logger.info(f"ERROR: {e}")
        return {
            False:
                "ERROR: Произошла ошибка при сохранении данных.\nДанные не сохранены!",
        }


async def save_sticker(*args):  # chatid, date, quantity, sku
    if len(args) != 4:
        raise ValueError("Error: Invalid number of arguments to save_sticker")

    sql = (
        f"INSERT INTO {TABLE_NAME}(idchat, datesticker, quantity, sku_code) "
        f"VALUES ($1,to_timestamp($2, 'DD.MM.YYYY')::date,$3,$4) RETURNING id;"
    )
    logger.info(f"save_sticker: {args}")
    result = await get_one_value_from_one_column_async(sql, *args)

    return result


async def main_stickers(message: Message):
    result = await async_save_pg(SQL_CREATE_TABLE)
    logger.info(f"{result}, SQL_CREATE_TABLE")
    result = await async_save_pg(SQL_FN_STICKER)
    logger.info(f"{result}, SQL_FN_STICKER")
    result = await async_save_pg(SQL_FN_IS_NUMERIC)
    logger.info(f"{result}, SQL_FN_IS_NUMERIC")
    result = await async_save_pg(SQL_CREATE_TRIGGER)
    logger.info(f"{result}, SQL_CREATE_TRIGGER")

    return await is_stickers(message)


if __name__ == "__main__":
    asyncio.run(
        main_stickers(Message(text="12.12.2021 122 12", chat=types.Chat(id=490323168)))
    )
