import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_doc_cash_order_expense_details'
DOCUMENT = "Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа"
SELECT_COLUMNS = "LineNumber,ДокументРасчетовСКонтрагентом,КратностьВзаиморасчетов,КурсВзаиморасчетов," \
                 "КурсВзаиморасчетовПлан,Сделка,СуммаВзаиморасчетов,<PERSON>уммаНДС,СуммаПлатежа,Ref_Key," \
                 "ДоговорКонтрагента_Key,СтатьяДвиженияДенежныхСредств_Key,СчетУчетаРасчетовСКонтрагентом_Key"

SQL_CREATE_TABLE = f'''    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME}(
        id bigserial NOT NULL,
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        multiplicity_of_mutual_settlements numeric(10,4) NOT NULL DEFAULT 0,  -- КратностьВзаиморасчетов
        settlement_rate numeric(10,4) NOT NULL DEFAULT 0,  -- КурсВзаиморасчетов
        settlement_rate_plan numeric(10,4) NOT NULL DEFAULT 0,  -- КурсВзаиморасчетовПлан
        amount_of_settlements numeric(15,3) DEFAULT 0 NOT NULL,  -- СуммаВзаиморасчетов
        amount_vat numeric(15,3) DEFAULT 0 NOT NULL,  -- СуммаНДС
        amount_of_payment numeric(15,3) DEFAULT 0 NOT NULL,  -- СуммаПлатежа
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        deal_key varchar(50) NULL,  -- Сделка
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        cash_flow_item varchar(50) NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        document_of_settlements_with_a_counterparty_key varchar(50) NULL,  -- ДокументРасчетовСКонтрагентом
        account_of_settlements_with_counterparty_key varchar(50) NULL,  -- СчетУчетаРасчетовСКонтрагентом_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.document_of_settlements_with_a_counterparty_key IS 'ДокументРасчетовСКонтрагентом';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements IS 'КратностьВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_rate IS 'КурсВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_rate_plan IS 'КурсВзаиморасчетовПлан';
    COMMENT ON COLUMN {TABLE_NAME}.deal_key IS 'Сделка';
    COMMENT ON COLUMN {TABLE_NAME}.amount_of_settlements IS 'СуммаВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_of_payment IS 'СуммаПлатежа';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_of_settlements_with_counterparty_key IS 'СчетУчетаРасчетовСКонтрагентом_Key';
'''

async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        line_number,
        document_of_settlements_with_a_counterparty_key,
        multiplicity_of_mutual_settlements,
        settlement_rate,
        settlement_rate_plan,
        deal_key,
        amount_of_settlements,
        amount_vat,
        amount_of_payment,
        ref_key,
        contract_key,
        cash_flow_item,
        account_of_settlements_with_counterparty_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        document_of_settlements_with_a_counterparty_key = EXCLUDED.document_of_settlements_with_a_counterparty_key,
        multiplicity_of_mutual_settlements = EXCLUDED.multiplicity_of_mutual_settlements,
        settlement_rate = EXCLUDED.settlement_rate,
        settlement_rate_plan = EXCLUDED.settlement_rate_plan,
        deal_key = EXCLUDED.deal_key,
        amount_of_settlements = EXCLUDED.amount_of_settlements,
        amount_vat = EXCLUDED.amount_vat,
        amount_of_payment = EXCLUDED.amount_of_payment,
        contract_key = EXCLUDED.contract_key,
        cash_flow_item = EXCLUDED.cash_flow_item,
        account_of_settlements_with_counterparty_key = EXCLUDED.account_of_settlements_with_counterparty_key
    '''
    return sql.replace("'", "")


async def main_doc_cash_order_expense_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(13)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, 'ref_key')
    await async_sql_create_index(TABLE_NAME, 'contract_key')
    await async_sql_create_index(TABLE_NAME, 'cash_flow_item')
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_doc_cash_order_expense_details_async())
