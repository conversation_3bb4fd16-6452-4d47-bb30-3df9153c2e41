import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_units"
DOCUMENT = "Catalog_ЕдиницыИзмерения"
SELECT_COLUMNS = ("Ref_Key,ЕдиницаПоКлассификатору_Key,Code,DeletionMark,Description,Owner,Predefined,Вес,"
                  "Коэффициент,Объем")

SQL_CREATE_TABLE = f'''    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        unit_by_classifier_key varchar(50) NOT NULL,  -- ЕдиницаПоКлассификатору_Key
        code bpchar(15) NOT NULL DEFAULT 0,  -- Code
        deletion_mark bool NOT NULL,  -- DeletionMark
        description varchar(150) NOT NULL,  -- Description
        nomenclature_key varchar(50) NOT NULL,  -- Owner
        predefined bool NOT NULL,  -- Predefined
        the_weight numeric(10, 4) NOT NULL DEFAULT 0,  -- Вес
        coefficient numeric(10, 4) NOT NULL DEFAULT 0,  -- Коэффициент
        volume numeric(10, 4) NOT NULL DEFAULT 0,  -- Объем
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_by_classifier_key IS 'ЕдиницаПоКлассификатору_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Owner';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.the_weight IS 'Вес';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.volume IS 'Объем';    
    '''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        ref_key,
        unit_by_classifier_key,
        code,
        deletion_mark,
        description,
        nomenclature_key,
        predefined,
        the_weight,
        coefficient,
        volume
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        unit_by_classifier_key = EXCLUDED.unit_by_classifier_key,
        code = EXCLUDED.code,
        deletion_mark = EXCLUDED.deletion_mark,
        description = EXCLUDED.description,
        nomenclature_key = EXCLUDED.nomenclature_key,
        predefined = EXCLUDED.predefined,
        the_weight = EXCLUDED.the_weight,
        coefficient = EXCLUDED.coefficient,
        volume = EXCLUDED.volume
    ;
    '''
    return sql.replace("'", "")


SQL_DELETE_DELETION_MARK = f'''
    DELETE FROM {TABLE_NAME}
    WHERE deletion_mark;
    '''


async def main_cat_units_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(SQL_DELETE_DELETION_MARK)
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_units_async())
