
    CREATE OR REPLACE VIEW v_one_cash AS
    SELECT DISTINCT 
        t.dtperiod::date AS doc_date,
        t.dtperiod AS tarih,
        t.docnumber,
        t.amount_receipt,
        t.amount_expense,
        t.operation_type,
        clause.description AS clause,
        t.client,
        podotchetniy,
        t.a_comment,
        t.kasa,
        t.line_number,
        t.type_of_cash,
        t.active,
        t.record_type,
        t.recorder_type,
        t.bank_account_cashier_type,
        t.recorder_key,
        --t.contract_key,
        t.bank_account_cashier_key,
        t.cash_flow_item,
        t.organization_key
    FROM (
        SELECT
            concat(
                warrant_receipt.operation_type,
                warrant_expense.operation_type
            ) as operation_type, 
            CASE 
                WHEN recorder_type = 'StandardODATA.Document_КорректировкаЗаписейРегистров' THEN 
                    'перемещение между кассами'
                ELSE 
                    concat(
                        cor_reg.description,
                        money_check.a_comment,
                        movement.a_comment,
                        order_expense.a_comment,
                        order_receipt.a_comment,
                        warrant_expense.a_comment,
                        warrant_receipt.a_comment,
                        spisanie.a_comment)
            END	AS a_comment,
            concat(
                cash.description,
                bank.description
            ) AS kasa,
            CASE
                WHEN record_type ILIKE '%Receipt%' THEN amount_control
                ELSE 0
            END AS amount_receipt,
            CASE
                WHEN record_type ILIKE '%Expense%' THEN -amount_control
                ELSE 0
            END AS amount_expense,
            concat(
                warrant_receipt.doc_number, 
                warrant_expense.doc_number, 
                order_receipt.doc_number,
                order_expense.doc_number, 
                movement.doc_number,
                money_check.doc_number, 
                spisanie.doc_number, 
                cor_reg.doc_number 
            ) AS docnumber,
            concat(
                client_spisanie.description,
                client_warrant_receipt.description,
                warrant_expense.give_out,
                client_warrant_expense.description,
                client_order_receipt.description,
                client_order_expense.description
            ) AS client,
            acc.*,
        concat(individuals2.description,individuals.description) AS podotchetniy
        FROM t_one_accreg_cash_recordtype AS acc
            LEFT JOIN t_one_cat_cash AS cash -- Catalog_Кассы
                ON cash.ref_key = acc.bank_account_cashier_key
            LEFT JOIN t_one_cat_cash_bank_accounts AS bank -- Catalog_БанковскиеСчета
                ON bank.ref_key = acc.bank_account_cashier_key
            LEFT JOIN t_one_doc_cash_money_check AS money_check -- Document_ДенежныйЧек
                ON money_check.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_receipt AS warrant_receipt -- Document_ПриходныйКассовыйОрдер
                ON warrant_receipt.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_receipt_details AS warrant_receipt_details -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                ON warrant_receipt_details.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_expense AS warrant_expense -- Document_РасходныйКассовыйОрдер
                ON warrant_expense.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_warrant_expense_details AS warrant_expense_details -- Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
                ON warrant_expense_details.ref_key = acc.recorder_key 
            LEFT JOIN t_one_doc_cash_order_receipt AS order_receipt -- Document_ПлатежноеПоручениеВходящее
                ON order_receipt.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_order_receipt_details AS order_receipt_details -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                ON order_receipt_details.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_order_expense AS order_expense -- Document_ПлатежноеПоручениеИсходящее
                ON order_expense.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_order_expense_details AS order_expense_details -- Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
                ON order_expense_details.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_withdrawal_of_funds AS spisanie -- Document_ПлатежныйОрдерСписаниеДенежныхСредств
                ON spisanie.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_withdrawal_of_funds_details AS spisanie_details -- Document_ПлатежныйОрдерСписаниеДенежныхСредств_РасшифровкаПлатежа
                ON spisanie_details.ref_key = acc.recorder_key		
            LEFT JOIN t_one_doc_cash_movement AS movement -- Document_ВнутреннееПеремещениеНаличныхДенежныхСредств
                ON movement.ref_key = acc.recorder_key
            LEFT JOIN t_one_doc_cash_correction_of_register AS cor_reg -- Document_КорректировкаЗаписейРегистров
                ON cor_reg.ref_key = acc.recorder_key
            LEFT JOIN t_one_cat_currencies AS cur_kasa -- currency cash
                ON cur_kasa.ref_key = cash.currency_key
            LEFT JOIN t_one_cat_currencies AS cur_bank -- currency bank
                ON cur_bank.ref_key = bank.currency_key
            LEFT JOIN t_one_cat_counterparties AS client_spisanie
                ON spisanie.account_key = client_spisanie.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_warrant_receipt
                ON warrant_receipt.contractor_key = client_warrant_receipt.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_warrant_expense
                ON warrant_expense.contractor_key = client_warrant_expense.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_order_receipt
                ON order_receipt.account_key = client_order_receipt.ref_key
            LEFT JOIN t_one_cat_counterparties AS client_order_expense
                ON order_expense.account_key = client_order_expense.ref_key
            LEFT JOIN t_one_cat_individuals AS individuals 
                ON warrant_expense.contractor_key = individuals.ref_key  
            LEFT JOIN t_one_cat_individuals AS individuals2 
                ON warrant_receipt.contractor_key = individuals2.ref_key  
        WHERE active = TRUE
    ) AS t
        LEFT JOIN t_one_cat_cash_flow_item AS clause
            ON t.cash_flow_item = clause.ref_key
    ORDER BY dtperiod DESC, docnumber
    ;

    GRANT SELECT ON TABLE v_one_cash TO user_prestige;

