-- Данные по клиентам: продажам, возвратам, списаниям, банковским операциям
DROP VIEW IF EXISTS public.v_one_customers_profit CASCADE;

CREATE OR REPLACE VIEW public.v_one_customers_profit AS
WITH 
-- продажи клиентам
doc_sale AS(
    SELECT 
        EXTRACT('YEAR' FROM doc_date) AS years,
        to_char(doc_date, 'TMMonth') AS months,
        sum(COALESCE(tobox, 0) * COALESCE(inbox, 0)) AS sale_ed,
        sum(COALESCE(amount, 0)) AS sale_amount,
        sum(COALESCE(amount_maliyet_uah, 0)) AS sale_costs,
        customer_key,
        currency_key
    FROM t_one_sale
    WHERE issale = 1
        AND doc_date::date >= '01.01.2024'
    GROUP BY 
        EXTRACT('YEAR' FROM doc_date),
        to_char(doc_date, 'TMMonth'),
        customer_key,
        currency_key
),
-- все возвраты клиентов
doc_return AS(
    SELECT 
        EXTRACT('YEAR' FROM doc_date) AS years,
        to_char(doc_date, 'TMMonth') AS months,
        sum(COALESCE(tobox, 0) * COALESCE(inbox, 0)) AS return_ed,
        sum(COALESCE(amount, 0)) AS return_amount,
        sum(COALESCE(amount_maliyet_uah, 0)) AS return_costs,
        customer_key,
        currency_key
    FROM t_one_sale
    WHERE issale = -1
        AND doc_date::date >= '01.01.2024'
    GROUP BY 
        EXTRACT('YEAR' FROM doc_date),
        to_char(doc_date, 'TMMonth'),
        customer_key,
        currency_key
),
-- все виртуальные возвраты (списания) через продажи виртуальному клиенту
doc_trash AS(
    SELECT 
        EXTRACT('YEAR' FROM doc_date) AS years,
        to_char(doc_date, 'TMMonth') AS months,
        sum(COALESCE(tobox, 0) * COALESCE(inbox, 0)) AS trash_ed,
        sum(COALESCE(amount, 0)) AS trash_amount,
        sum(COALESCE(amount_maliyet_uah, 0)) AS trash_costs,
        customer_key,
        currency_key
    FROM t_one_sale
    WHERE auto_sale
        AND doc_date::date >= '01.01.2024'
    GROUP BY 
        EXTRACT('YEAR' FROM doc_date),
        to_char(doc_date, 'TMMonth'),
        customer_key,
        currency_key
),
-- СписаниеЗадолженности на бонусы через корректировку долга
debt_correction as (
		SELECT
			EXTRACT(YEAR FROM doc.doc_date) AS years,
			to_char(doc.doc_date, 'TMMonth'::text) AS months,
			sum(debt.amount) bonus,
			doc.counterparty_debtor_key customer_key,
			doc.currency_key
		FROM t_one_doc_debt_correction AS doc
			INNER JOIN t_one_doc_debt_correction_debt_amount AS debt
				ON doc.ref_key = debt.ref_key 
		WHERE doc.posted
			AND doc.doc_date::date >= '01.01.2024'::date
			AND doc.operation_type = 'СписаниеЗадолженности'
		GROUP BY 
			EXTRACT(YEAR FROM doc.doc_date),
			to_char(doc.doc_date, 'TMMonth'::text),
			doc.counterparty_debtor_key,
			doc.currency_key
),
-- перенесение долга с одного клиента на другого. Данные получателя
moved_debt AS (
	SELECT
		EXTRACT(YEAR FROM doc.doc_date) AS years,
		to_char(doc.doc_date, 'TMMonth'::text) AS months,
		sum(det.total) moved,
		doc.counterparty_debtor_key customer_key,
		doc.currency_key
	FROM t_one_doc_debt_correction AS doc
		INNER JOIN t_one_doc_debt_correction_debt_amount det
			ON det.ref_key = doc.ref_key
	WHERE posted
		AND doc.doc_date::date >= '01.01.2024'::date
		AND operation_type = 'ПроведениеВзаимозачета' 
		AND counterparty_debtor_key <> counterparty_creditor_key
	GROUP BY 
		doc.counterparty_debtor_key,
		doc.currency_key,
		EXTRACT(YEAR FROM doc.doc_date),
		to_char(doc.doc_date, 'TMMonth'::text)	
),
-- перенесение долга с одного клиента на другого. Данные поставшика
moved_credit AS (
	SELECT 
		EXTRACT(YEAR FROM doc.doc_date) AS years,
		to_char(doc.doc_date, 'TMMonth'::text) AS months,
		-sum(det.total) moved,
		counterparty_creditor_key customer_key,
		doc.currency_key
	FROM t_one_doc_debt_correction AS doc
		INNER JOIN t_one_doc_debt_correction_debt_amount det
			ON det.ref_key = doc.ref_key
	WHERE posted
		AND doc.doc_date::date >= '01.01.2024'::date
		AND operation_type = 'ПроведениеВзаимозачета' 
		AND counterparty_debtor_key <> counterparty_creditor_key
	GROUP BY 
		doc.counterparty_creditor_key,
		doc.currency_key,
		EXTRACT(YEAR FROM doc.doc_date),
		to_char(doc.doc_date, 'TMMonth'::text)	
),
-- Взаимозачеты между нашей организацией и клиентами
vzaimozachet as (
		SELECT
			EXTRACT(YEAR FROM doc.doc_date) AS years,
			to_char(doc.doc_date, 'TMMonth'::text) AS months,
			sum(det.total) vz_oplata,
			0 vz_bonus,
			doc.counterparty_debtor_key customer_key,
			doc.currency_key
		FROM t_one_doc_debt_correction AS doc
			INNER JOIN t_one_doc_debt_correction_debt_amount det
				ON det.ref_key = doc.ref_key
		WHERE posted
			AND doc.doc_date::date >= '01.01.2024'::date
			AND operation_type = 'ПроведениеВзаимозачета' 
			AND counterparty_debtor_key = counterparty_creditor_key
		GROUP BY 
			doc.counterparty_debtor_key,
			doc.currency_key,
			EXTRACT(YEAR FROM doc.doc_date),
			to_char(doc.doc_date, 'TMMonth'::text)	
		UNION ALL 
			SELECT 
			EXTRACT(YEAR FROM doc.doc_date) AS years,
			to_char(doc.doc_date, 'TMMonth'::text) AS months,
			0 vz_oplata,
			sum(det.total) vz_bonus,
			counterparty_creditor_key customer_key,
			doc.currency_key
		FROM t_one_doc_debt_correction AS doc
			INNER JOIN t_one_doc_debt_correction_debt_amount det
				ON det.ref_key = doc.ref_key
		WHERE posted
			AND doc.doc_date::date >= '01.01.2024'::date
			AND operation_type = 'ПроведениеВзаимозачета' 
			AND counterparty_debtor_key = counterparty_creditor_key
		GROUP BY 
			doc.counterparty_creditor_key,
			doc.currency_key,
			EXTRACT(YEAR FROM doc.doc_date),
			to_char(doc.doc_date, 'TMMonth'::text)
),
-- все банковские операции
bank AS (
	SELECT 
		sub_bank.years,
		sub_bank.months,
		sum(COALESCE(sub_bank.receipt,0)) receipt,
		sum(COALESCE(sub_bank.expense,0)) expense,
		sub_bank.customer_key,
		sub_bank.currency_key
	FROM (
	        SELECT  
	          expense.account_key AS customer_key,
		        EXTRACT('YEAR' FROM doc_date) AS years,
		        to_char(doc_date, 'TMMonth') AS months,
                0 AS receipt,
                -expense.amount AS expense,
                currency_key
	        FROM t_one_doc_cash_order_expense expense
	        WHERE expense.isposted
	          AND expense.doc_date::date >= '01.01.2024'::date
	        UNION ALL 
	        SELECT
	          receipt.account_key AS customer_key,
		        EXTRACT('YEAR' FROM doc_date) AS years,
		        to_char(doc_date, 'TMMonth') AS months,
                receipt.amount AS receipt,
                0 AS expense,
                currency_key
	        FROM t_one_doc_cash_order_receipt receipt
	        WHERE receipt.posted
	          AND receipt.doc_date::date >= '01.01.2024'::date
		) sub_bank
	GROUP BY 
		years,
		months,
		customer_key,
		currency_key
)
SELECT DISTINCT 
	client.manager, 
	client.segment_folder, 
	client.segment, 
	client.customer, 
	years, 
	months, 
	round(COALESCE(sale_ed,0),2) sale_ed, 
	round(COALESCE(sale_amount,0),2) sale_amount,
	round(COALESCE(sale_costs,0),2) sale_costs,
	round(COALESCE(rent,0),2) rent,
	round(COALESCE(return_ed,0),2) return_ed,
	round(COALESCE(return_amount,0),2) return_amount,
	round(COALESCE(return_costs,0),2) return_costs,
	round(COALESCE(trash_ed,0),2) trash_ed,
	round(COALESCE(trash_amount,0),2) trash_amount,
	round(COALESCE(trash_costs,0),2) trash_costs,
	round(COALESCE(pr_return_ed,0),2) pr_return_ed,
	round(COALESCE(pr_return_amount,0),2) pr_return_amount,
	round(COALESCE(pr_trash_ed,0),2) pr_trash_ed,
	round(COALESCE(pr_trash_amount,0),2) pr_trash_amount,
	round(COALESCE(receipt,0),2) receipt,
	round(COALESCE(expense,0),2) expense,
	round(COALESCE(bonus,0),2) bonus,
	round(COALESCE(moved,0),2) moved,
	round(COALESCE(vz_oplata,0),2) vz_oplata,
	round(COALESCE(vz_bonus,0),2) vz_bonus,
	client.currency_name AS currency	
FROM (
	SELECT 
	    COALESCE(doc_sale.customer_key, 
				COALESCE(doc_return.customer_key, 
					COALESCE(doc_trash.customer_key, 
						COALESCE(bank.customer_key, 
							COALESCE(debt_correction.customer_key, 
								COALESCE(moved_debt.customer_key,
									COALESCE(moved_credit.customer_key, vzaimozachet.customer_key))))))) AS customer_key,
	    COALESCE(doc_sale.currency_key, 
				COALESCE(doc_return.currency_key, 
					COALESCE(doc_trash.currency_key, 
						COALESCE(bank.currency_key, 
							COALESCE(debt_correction.currency_key,
								COALESCE(moved_debt.currency_key, 
									COALESCE(moved_credit.currency_key, vzaimozachet.currency_key))))))) AS currency_key,
	    COALESCE(doc_sale.years, 
				COALESCE(doc_return.years, 
					COALESCE(doc_trash.years, 
						COALESCE(bank.years, 
							COALESCE(debt_correction.years,
								COALESCE(moved_debt.years,
									COALESCE(moved_credit.years, vzaimozachet.years))))))) AS years,
	    COALESCE(doc_sale.months, 
				COALESCE(doc_return.months, 
					COALESCE(doc_trash.months, 
						COALESCE(bank.months,
							COALESCE(debt_correction.months,
								COALESCE(moved_debt.months, 
									COALESCE(moved_credit.months, vzaimozachet.months))))))) AS months,
	    sum(doc_sale.sale_ed) AS sale_ed,
	    sum(doc_sale.sale_amount) AS sale_amount,
	    sum(doc_sale.sale_costs) AS sale_costs,
	    CASE 
	        WHEN sum(doc_sale.sale_amount) <> 0 THEN 
	            round(sum(doc_sale.sale_costs) / sum(doc_sale.sale_amount), 2) * 100
	        ELSE 
	            0
	    END AS rent,
	    sum(doc_return.return_ed) AS return_ed,
	    sum(doc_return.return_amount) AS return_amount,
	    sum(doc_return.return_costs) AS return_costs,
	    sum(doc_trash.trash_ed) AS trash_ed,
	    sum(doc_trash.trash_amount) AS trash_amount,
	    sum(doc_trash.trash_costs) AS trash_costs,
	    CASE 
	        WHEN sum(doc_sale.sale_ed) <> 0 THEN 
	            round(sum(doc_return.return_ed) / sum(doc_sale.sale_ed) * 100, 2)
	        ELSE 
	            0
	    END AS pr_return_ed,
	    CASE 
	        WHEN sum(doc_sale.sale_amount) <> 0 THEN 
	            round(sum(doc_return.return_amount) / sum(doc_sale.sale_amount) * 100, 2)
	        ELSE 
	            0
	    END AS pr_return_amount,
	    CASE 
	        WHEN sum(doc_return.return_ed) <> 0 THEN 
	            round(sum(doc_trash.trash_ed) / sum(doc_return.return_ed) * 100, 2)
	        ELSE 
	            0
	    END AS pr_trash_ed,
	    CASE 
	        WHEN sum(doc_return.return_amount) <> 0 THEN 
	            round(sum(doc_trash.trash_amount) / sum(doc_return.return_amount) * 100, 2)
	        ELSE 
	            0
	    END AS pr_trash_amount,
	    COALESCE(sum(bank.receipt), 0) AS receipt,
	    COALESCE(sum(bank.expense), 0) AS expense,
	    COALESCE(sum(debt_correction.bonus), 0) AS bonus,
	    COALESCE(sum(moved_credit.moved), 0) + COALESCE(sum(moved_debt.moved), 0) AS moved,
			COALESCE(sum(vzaimozachet.vz_oplata), 0) AS vz_oplata,
			COALESCE(sum(vzaimozachet.vz_bonus), 0) AS vz_bonus
FROM doc_sale
	FULL JOIN vzaimozachet 
	    ON doc_sale.customer_key = vzaimozachet.customer_key
	    AND doc_sale.currency_key = vzaimozachet.currency_key
	    AND doc_sale.years = vzaimozachet.years
	    AND doc_sale.months = vzaimozachet.months
	FULL JOIN moved_debt 
	    ON doc_sale.customer_key = moved_debt.customer_key
	    AND doc_sale.currency_key = moved_debt.currency_key
	    AND doc_sale.years = moved_debt.years
	    AND doc_sale.months = moved_debt.months
	FULL JOIN moved_credit 
	    ON doc_sale.customer_key = moved_credit.customer_key
	    AND doc_sale.currency_key = moved_credit.currency_key
	    AND doc_sale.years = moved_credit.years
	    AND doc_sale.months = moved_credit.months
	FULL JOIN debt_correction 
	    ON doc_sale.customer_key = debt_correction.customer_key
	    AND doc_sale.currency_key = debt_correction.currency_key
	    AND doc_sale.years = debt_correction.years
	    AND doc_sale.months = debt_correction.months
	FULL JOIN doc_return 
	    ON doc_sale.customer_key = doc_return.customer_key
	    AND doc_sale.currency_key = doc_return.currency_key
	    AND doc_sale.years = doc_return.years
	    AND doc_sale.months = doc_return.months
	FULL JOIN doc_trash 
	    ON doc_sale.customer_key = doc_trash.customer_key
	    AND doc_sale.currency_key = doc_trash.currency_key
	    AND doc_sale.years = doc_trash.years
	    AND doc_sale.months = doc_trash.months
	FULL JOIN bank
	    ON doc_sale.customer_key = bank.customer_key
	    AND doc_sale.currency_key = bank.currency_key
	    AND doc_sale.years = bank.years
	    AND doc_sale.months = bank.months
	GROUP BY 
	    COALESCE(doc_sale.customer_key, 
				COALESCE(doc_return.customer_key, 
					COALESCE(doc_trash.customer_key, 
						COALESCE(bank.customer_key, 
							COALESCE(debt_correction.customer_key, 
								COALESCE(moved_debt.customer_key,
									COALESCE(moved_credit.customer_key, vzaimozachet.customer_key))))))),
	    COALESCE(doc_sale.currency_key, 
				COALESCE(doc_return.currency_key, 
					COALESCE(doc_trash.currency_key, 
						COALESCE(bank.currency_key, 
							COALESCE(debt_correction.currency_key,
								COALESCE(moved_debt.currency_key, 
									COALESCE(moved_credit.currency_key, vzaimozachet.currency_key))))))),
	    COALESCE(doc_sale.years, 
				COALESCE(doc_return.years, 
					COALESCE(doc_trash.years, 
						COALESCE(bank.years, 
							COALESCE(debt_correction.years,
								COALESCE(moved_debt.years,
									COALESCE(moved_credit.years, vzaimozachet.years))))))),
	    COALESCE(doc_sale.months, 
				COALESCE(doc_return.months, 
					COALESCE(doc_trash.months, 
						COALESCE(bank.months,
							COALESCE(debt_correction.months,
								COALESCE(moved_debt.months, 
									COALESCE(moved_credit.months, vzaimozachet.months)))))))
) AS t
INNER JOIN 
	(
		SELECT DISTINCT 
			manager, 
			segment, 
			segment_folder, 
			customer, 
			customer_key,
			currency_key,
			currency_name 
		FROM v_one_manager_counterparty_contracts_segments
	) AS client
	ON client.customer_key = t.customer_key
		AND client.currency_key = t.currency_key
ORDER BY 
	client.manager,
	client.segment_folder,
	client.segment,
	client.customer,
	client.currency_name
;

COMMENT ON COLUMN public.v_one_customers_profit.bonus IS 'бонус чз коррект';
COMMENT ON COLUMN public.v_one_customers_profit.customer IS 'контрагент';
COMMENT ON COLUMN public.v_one_customers_profit.expense IS 'оплачено нами';
COMMENT ON COLUMN public.v_one_customers_profit.receipt IS 'поступило нам';
COMMENT ON COLUMN public.v_one_customers_profit.moved IS 'вззчт м/у их клиентами';
COMMENT ON COLUMN public.v_one_customers_profit.pr_return_amount IS '% возврата сумм';
COMMENT ON COLUMN public.v_one_customers_profit.pr_return_ed IS '% возврата кол-ва';
COMMENT ON COLUMN public.v_one_customers_profit.pr_trash_amount IS '% списания сумм';
COMMENT ON COLUMN public.v_one_customers_profit.pr_trash_ed IS '% списания кол-ва';
COMMENT ON COLUMN public.v_one_customers_profit.rent IS 'рентабельность';
COMMENT ON COLUMN public.v_one_customers_profit.return_amount IS 'возврат сумм';
COMMENT ON COLUMN public.v_one_customers_profit.return_costs IS 'возврат себест';
COMMENT ON COLUMN public.v_one_customers_profit.return_ed IS 'возврат кол-ва';
COMMENT ON COLUMN public.v_one_customers_profit.sale_amount IS 'продажи сумм';
COMMENT ON COLUMN public.v_one_customers_profit.sale_costs IS 'продажи себест';
COMMENT ON COLUMN public.v_one_customers_profit.sale_ed IS 'продажи кол-ва';
COMMENT ON COLUMN public.v_one_customers_profit.trash_amount IS 'списание сумм';
COMMENT ON COLUMN public.v_one_customers_profit.trash_costs IS 'списание себест';
COMMENT ON COLUMN public.v_one_customers_profit.trash_ed IS 'списание кол-ва';
COMMENT ON COLUMN public.v_one_customers_profit.vz_bonus IS 'взаимозачет бонусов';
COMMENT ON COLUMN public.v_one_customers_profit.vz_oplata IS 'взаимозачет оплаты';
COMMENT ON COLUMN public.v_one_customers_profit.segment IS 'сегмент';
COMMENT ON COLUMN public.v_one_customers_profit.segment_folder IS 'папка сегмента';
COMMENT ON COLUMN public.v_one_customers_profit.currency IS 'валюта';
COMMENT ON COLUMN public.v_one_customers_profit.manager IS 'менеджер';
COMMENT ON COLUMN public.v_one_customers_profit.months IS 'месяц';
COMMENT ON COLUMN public.v_one_customers_profit.years IS 'год';

