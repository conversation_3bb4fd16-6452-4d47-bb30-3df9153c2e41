import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_salary_payment_of_wages"
DOCUMENT = "Document_ПлатежноеПоручениеИсходящее_ВыплатаЗаработнойПлаты"
SELECT_COLUMNS = "Ref_Key,LineN<PERSON>ber,СуммаПлатежа,Ведомость_Key"

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    idt bigserial NOT NULL,
    ref_key varchar(50) NULL,  -- Ref_Key
    line_number numeric(7) NULL,  -- LineNumber
    amount numeric(10,4) NOT NULL DEFAULT 0,  -- Сумма
    payroll_key varchar(50) NULL,  -- ЗарплатаКВыплатеОрганизаций
    CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key,line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.payroll_key IS 'ЗарплатаКВыплатеОрганизаций';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME} (
        ref_key,
        line_number,
        amount,
        payroll_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key,line_number)
    DO UPDATE SET
        amount = EXCLUDED.amount,
        payroll_key = EXCLUDED.payroll_key
    """
    return sql.replace("'", "")


async def main_salary_payment_of_wages_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(4)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_salary_payment_of_wages_async())
