from datetime import datetime, timedelta
from ParsingSite import main_parsing_site as df
import sys
import psycopg2

current_dt = datetime.now() + timedelta(seconds=295)


# в таб t_rate добавляем курс (если последнее значение неактуально, т.е. изменилось)
def add_rate_to_db():
  try:
    # df = pd.DataFrame(dfParsinKurSatis())
    for i in range(len(df())):
      # print(i)
      nKurSatis = df()['kur'][i]
      nkontakt = df()['phone'][i]
      ntarih = df()['kur_time'][i]

      strSql = '''INSERT INTO t_rate(nkursatis, kontakt, dttarih) SELECT replace('%s'::text , ',', '.')::numeric,'%s','%s' 
          WHERE NOT EXISTS(SELECT * From t_rate WHERE kontakt = '%s' and dttarih = '%s')
          ;''' % (nKurSatis, nkontakt, ntarih, nkontakt, ntarih)

      with conn:
        with conn.cursor() as cur:
          cur.execute(strSql)
          conn.commit()

      print('курс добавлен', datetime.now())

  except Exception as e:
    print(str(e))
    conn.rollback()  # отменяем транзакцию

  finally:
    cur.close()
    conn.close
    sys.exit(
      0)  # выход с нуля, который обычно интерпретируется как успех. Ненулевые коды обычно рассматриваются как ошибки. По умолчанию выход с нуля.


while current_dt > datetime.now():
  try:
    # tokenMinfin = 'b8af64ecf20bdb306d357c147d0d7179a66a893b'
    conn = psycopg2.connect(dbname='Madoc', user='postgres', port='5432', password='hfvpfc15', host='127.0.0.1')
    cur = conn.cursor()

    add_rate_to_db()

  except Exception as e:
    print(str(e))

  # finally:
  #   cur.close()
  #   conn.close()

else:
  cur.close()
  conn.close
  sys.exit(
    0)  # выход с нуля, который обычно интерпретируется как успех. Ненулевые коды обычно рассматриваются как ошибки. По умолчанию выход с нуля.
