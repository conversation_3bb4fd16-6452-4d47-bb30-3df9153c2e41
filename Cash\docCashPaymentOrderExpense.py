# ПлатежноеПоручениеИсходящее

import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_doc_cash_order_expense'
DOCUMENT = "Document_ПлатежноеПоручениеИсходящее"
SELECT_COLUMNS = (
    "DataVersion,Date,Number,Комментарий,НазначениеПлатежа,СуммаДокумента,Posted,Оплачено,ОтражатьВУправленческомУчете,"
    "ВидОперации,Ref_Key,ВалютаДокумента_Key,Контрагент_Key,СтатьяДвиженияДенежныхСредств_Key,СубконтоДт2,"
    "СубконтоДт2_Type,ДоговорКонтрагента_Key,Организация_Key,bankDockId")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        a_comment varchar NULL,  -- Комментарий
        operation_type varchar(250) NOT NULL,  -- ВидОперации
        cost_item_key varchar(40) NULL,  -- СубконтоДт2 (СтатьяЗатрат_Key)
        cost_item_type varchar(100) NULL,  -- СубконтоДт2_Type (статья затрат)
        assignment varchar NULL,  -- НазначениеПлатежа
        amount numeric(20, 4) NOT NULL DEFAULT 0,  -- СуммаДокумента
        isposted bool NOT NULL DEFAULT false,  -- Posted
        ispaid bool NOT NULL DEFAULT false,  -- Оплачено
        is_management bool NOT NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        dataversion varchar(15) NOT NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        currency_key varchar(50) NOT NULL,  -- ВалютаДокумента_Key
        account_key varchar(50) NOT NULL,  -- Контрагент_Key
        cash_flow_item varchar(50) NOT NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        contract_key varchar(50) NULL,  -- Договор_Key
        organization_key varchar(40) NULL,  -- Организация_Key
        bankDockId varchar(40) NULL,  -- bankDockId
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.assignment IS 'НазначениеПлатежа';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item_key IS 'СтатьяЗатрат_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item_type IS 'тип статьи затрат)';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.isposted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.ispaid IS 'Оплачено';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'Договор_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.bankDockId IS 'bankDockId';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        dataversion,
        doc_date,
        doc_number,
        a_comment,
        assignment,
        amount,
        isposted,
        ispaid,
        is_management,
        operation_type,
        ref_key,
        currency_key,
        account_key,
        cash_flow_item,
        cost_item_key,
        cost_item_type,
        contract_key,
        organization_key,
        bankDockId
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        doc_number = EXCLUDED.doc_number,
        a_comment = EXCLUDED.a_comment,
        assignment = EXCLUDED.assignment,
        amount = EXCLUDED.amount,
        isposted = EXCLUDED.isposted,
        ispaid = EXCLUDED.ispaid,
        is_management = EXCLUDED.is_management,
        operation_type = EXCLUDED.operation_type,
        currency_key = EXCLUDED.currency_key,
        account_key = EXCLUDED.account_key,
        cash_flow_item = EXCLUDED.cash_flow_item,
        cost_item_key = EXCLUDED.cost_item_key,
        cost_item_type = EXCLUDED.cost_item_type,
        contract_key = EXCLUDED.contract_key,
        organization_key = EXCLUDED.organization_key,
        bankDockId = EXCLUDED.bankDockId
    ;
    '''
    return sql.replace("'", "")


async def main_doc_cash_order_expense_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(19)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, 'ref_key')
    await async_sql_create_index(TABLE_NAME, 'cash_flow_item')
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_order_expense_async())
