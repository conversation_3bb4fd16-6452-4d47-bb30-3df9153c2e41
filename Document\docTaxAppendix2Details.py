import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_tax_appendix_2_details"
DOCUMENT = "Document_Приложение2КНалоговойНакладной_Товары"
SELECT_COLUMNS = (
    "Ref_<PERSON>,<PERSON><PERSON><PERSON><PERSON>,ЕдиницаИзмерения_Key,ЕдиницаИзмеренияМест_Key,ИзменениеКоличества,"
    "ИзменениеСуммы,ИзменениеСуммыНДС,ИзменениеЦены,Количество,КоличествоМест,Коэффициент,"
    "Номенклатура_Key,Причина,СтавкаНДС,Сумма,СуммаНДС,ХарактеристикаНоменклатуры_Key,Цена,"
    "КодУКТВЭД_Key,НомерГТД_Key,НомерСтрокиНН,НомерГруппы,КодПричины"
)

SQL_CREATE_TABLE = f"""        
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            id serial4 NOT NULL,
            line_number numeric(10,4) NOT NULL DEFAULT 0,  -- LineNumber
            quantity_change numeric(10,4) NOT NULL DEFAULT 0,  -- ИзменениеКоличества
            change_amount numeric(10,4) NOT NULL DEFAULT 0,  -- ИзменениеСуммы
            change_amount_vat numeric(10,4) NOT NULL DEFAULT 0,  -- ИзменениеСуммыНДС
            price_change numeric(10,4) NOT NULL DEFAULT 0,  -- ИзменениеЦены
            quantity numeric(10,4) NOT NULL DEFAULT 0,  -- Количество
            doc_number_of_seats numeric(10,4) NOT NULL DEFAULT 0,  -- КоличествоМест
            coefficient numeric(10,4) NOT NULL DEFAULT 0,  -- Коэффициент
            cause varchar(50) NULL,  -- Причина
            vat_rate varchar(50) NULL,  -- СтавкаНДС
            total numeric(10,4) NOT NULL DEFAULT 0,  -- Сумма
            amount_vat numeric(10,4) NOT NULL DEFAULT 0,  -- СуммаНДС
            price numeric(10,4) NOT NULL DEFAULT 0,  -- Цена
            line_doc_number numeric(10,4) NOT NULL DEFAULT 0,  -- НомерСтрокиНН
            group_doc_number numeric(10,4) NOT NULL DEFAULT 0,  -- НомерГруппы
            reason_code numeric(10,4) NOT NULL DEFAULT 0,  -- КодПричины
            ref_key varchar(50) NULL,  -- Ref_Key
            nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
            unit_of_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
            location_unit_key varchar(50) NULL,  -- ЕдиницаИзмеренияМест_Key
            item_characteristic_key varchar(50) NULL,  -- ХарактеристикаНоменклатуры_Key
            code_uktved_key varchar(50) NULL,  -- КодУКТВЭД_Key
            doc_number_gtd_key varchar(50) NULL,  -- НомерГТД_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
        );

        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
        COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
        COMMENT ON COLUMN {TABLE_NAME}.location_unit_key IS 'ЕдиницаИзмеренияМест_Key';
        COMMENT ON COLUMN {TABLE_NAME}.quantity_change IS 'ИзменениеКоличества';
        COMMENT ON COLUMN {TABLE_NAME}.change_amount IS 'ИзменениеСуммы';
        COMMENT ON COLUMN {TABLE_NAME}.change_amount_vat IS 'ИзменениеСуммыНДС';
        COMMENT ON COLUMN {TABLE_NAME}.price_change IS 'ИзменениеЦены';
        COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number_of_seats IS 'КоличествоМест';
        COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
        COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
        COMMENT ON COLUMN {TABLE_NAME}.cause IS 'Причина';
        COMMENT ON COLUMN {TABLE_NAME}.vat_rate IS 'СтавкаНДС';
        COMMENT ON COLUMN {TABLE_NAME}.total IS 'Сумма';
        COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
        COMMENT ON COLUMN {TABLE_NAME}.item_characteristic_key IS 'ХарактеристикаНоменклатуры_Key';
        COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';
        COMMENT ON COLUMN {TABLE_NAME}.code_uktved_key IS 'КодУКТВЭД_Key';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number_gtd_key IS 'НомерГТД_Key';
        COMMENT ON COLUMN {TABLE_NAME}.line_doc_number IS 'НомерСтрокиНН';
        COMMENT ON COLUMN {TABLE_NAME}.group_doc_number IS 'НомерГруппы';
        COMMENT ON COLUMN {TABLE_NAME}.reason_code IS 'КодПричины';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        line_number,
        unit_of_key,
        location_unit_key,
        quantity_change,
        change_amount,
        change_amount_vat,
        price_change,
        quantity,
        doc_number_of_seats,
        coefficient,
        nomenclature_key,
        cause,
        vat_rate,
        total,
        amount_vat,
        item_characteristic_key,
        price,
        code_uktved_key,
        doc_number_gtd_key,
        line_doc_number,
        group_doc_number,
        reason_code
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        unit_of_key = EXCLUDED.unit_of_key,
        location_unit_key = EXCLUDED.location_unit_key,
        quantity_change = EXCLUDED.quantity_change,
        change_amount = EXCLUDED.change_amount,
        change_amount_vat = EXCLUDED.change_amount_vat,
        price_change = EXCLUDED.price_change,
        quantity = EXCLUDED.quantity,
        doc_number_of_seats = EXCLUDED.doc_number_of_seats,
        coefficient = EXCLUDED.coefficient,
        nomenclature_key = EXCLUDED.nomenclature_key,
        cause = EXCLUDED.cause,
        vat_rate = EXCLUDED.vat_rate,
        total = EXCLUDED.total,
        amount_vat = EXCLUDED.amount_vat,
        item_characteristic_key = EXCLUDED.item_characteristic_key,
        price = EXCLUDED.price,
        code_uktved_key = EXCLUDED.code_uktved_key,
        doc_number_gtd_key = EXCLUDED.doc_number_gtd_key,
        line_doc_number = EXCLUDED.line_doc_number,
        group_doc_number = EXCLUDED.group_doc_number,
        reason_code = EXCLUDED.reason_code
    ;
    """
    return sql.replace("'", "")


async def main_doc_tax_appendix2_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(23)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "code_uktved_key")
    await async_sql_create_index(TABLE_NAME, "ref_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_tax_appendix2_details_async())
