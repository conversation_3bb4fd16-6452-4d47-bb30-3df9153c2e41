
    CREATE OR REPLACE VIEW v_service_tc_dop_second
    AS SELECT 
        org.description AS organization,
        clients.description AS client,
        cur.description AS doviz,
        dop.doc_date::date AS dop_date_second,
        dop.doc_date as tarih,
        dop.doc_number AS dop_number_second,
        dop.document_amount,
        dop.amount_vat AS dop_vat_second,
        dop.ref_key AS dop_ref_key_second,
        dop.organization_key AS dop_organization_key_second,
        dop.currency_key AS dop_currency_key_second,
        dop_rfk.batch_document
       FROM t_one_doc_incoming_additional_expenses dop
         JOIN ( SELECT DISTINCT dop_goods.ref_key,
                dop_goods.batch_document
               FROM t_one_doc_incoming_additional_expenses_goods dop_goods
                 JOIN ( SELECT DISTINCT dp.ref_key
                       FROM t_one_doc_receipt_of_goods_services_goods dp
                         JOIN ( SELECT DISTINCT v_service_tc_goods.nomenclature_key
                               FROM v_service_tc_goods) nomen_key 
                                ON nomen_key.nomenclature_key::text = dp.nomenclature_key::text
                      WHERE NOT (dp.ref_key::text IN (SELECT DISTINCT v_service_tc.batch_document
                               FROM v_service_tc))) rfk ON rfk.ref_key::text = dop_goods.batch_document::text) dop_rfk 
                                ON dop.ref_key::text = dop_rfk.ref_key::text
         JOIN t_one_cat_currencies cur ON cur.ref_key::text = dop.currency_key::text
         JOIN t_one_cat_organizations org ON org.ref_key::text = dop.organization_key::text
         JOIN t_one_cat_counterparties clients ON clients.ref_key::text = dop.account_key::text
      WHERE NOT (dop.account_key::text IN ( SELECT DISTINCT v_service_tc.account_key
               FROM v_service_tc))
      ORDER BY dop.doc_date DESC;
    