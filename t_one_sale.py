# add sales and sales returns to table {TABLE_NAME}
# https://habr.com/ru/company/wunderfund/blog/581994/
import asyncio
import os
import sys

from CreateAndRunSQLScripts import create_views
from async_Postgres import async_truncate_table, async_sql_create_index, async_save_pg
from logger_prestige import get_logger
from prestige_authorize import CONFIG_PATH
from rate_nbu import main_rate_nbu
from t_one_stock import main_t_one_stock_async

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
sys.path.append(os.path.abspath(CONFIG_PATH))

TABLE_NAME = "t_one_sale"

sql_create = f'''
    DROP TABLE IF EXISTS {TABLE_NAME} cascade;

    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        idt serial4 NOT NULL,
        organization varchar(100) NULL,  -- организация
        segment varchar(50) NULL,  -- ПодСеть
        segment_folder varchar(50) NULL,  -- сеть
        supplier varchar(100) NULL,  -- поставщик
        warehouse varchar NULL,  -- склад
        customer varchar(100) NULL,  -- покупатель
        distributor varchar(100) NULL,  -- дистрибьютер        
        doc_date timestamp NOT NULL,  -- дата
        sell_by  date null,  -- годен до
        doc_number varchar(15) NOT NULL,  -- номер
        sku varchar(150) NULL,  -- номенклатура
        inbox numeric(10, 4) NOT NULL DEFAULT 0,  -- в коробке
        tobox numeric(10, 4) NOT NULL DEFAULT 0,  -- в пересчете на коробку
        quantity numeric(10, 3) NOT NULL DEFAULT 0,  -- кол-во
        coef_sale numeric(10, 4) NOT NULL DEFAULT 0,  -- коэф
        price numeric(10, 3) NOT NULL DEFAULT 0,  -- цена
        price_unit numeric(10, 3) NOT NULL DEFAULT 0,  -- цена за баз ед.изм
        amount numeric(15, 3) NOT NULL DEFAULT 0,  -- сумма
        amount_ue numeric(15, 3) NOT NULL DEFAULT 0,  -- сумма продажи в ue
        price_tr numeric(10, 3) NOT NULL DEFAULT 0,  -- цена закупа
        amount_tr numeric(10, 3) NOT NULL DEFAULT 0,  -- сумма закупа
        maliyet numeric(10, 3) NOT NULL DEFAULT 0,  -- с/сть ue
        amount_maliyet numeric(15, 3) NOT NULL DEFAULT 0,  -- сумма с/сти ue
        amount_maliyet_uah numeric(15, 3) NOT NULL DEFAULT 0,  -- сумма с/сти uah
        barcode varchar(30) NULL,  -- штрихкод 
        rate_nbu numeric(15, 4) NOT NULL DEFAULT 0,  -- курс НБУ usd
        rate_sum numeric(15, 4) NOT NULL DEFAULT 0,  -- сумма курса tobox*rate_nbu
        settlement_rate numeric(15, 4) NOT NULL DEFAULT 0,  -- курса по накл
        multiplicity_of_mutual_settlements numeric(15, 4) NOT NULL DEFAULT 0,  -- кратность взаиморасчетов
        unit varchar(5) NULL,  -- ед.изм
        currency varchar(5) NULL,  -- валюта
        amount_vat numeric(10,2) default 0 NOT NULL,  -- НДС
        consider_vat bool default false,  -- УчитыватьНДС
        amount_includes_vat bool default false,  -- СуммаВключаетНДС
        issale numeric(1) default 1,  -- true-поступление false-расход,
        is_accounting bool default false,  -- отображать в бух учете
        auto_sale boolean DEFAULT FALSE,  -- АвтоматическиСоздаватьРеализацию
        doc_type varchar(40) NOT NULL,  -- тип документа
        ref_key varchar(40) NULL,  -- refkey док.продажи/возврата
        organization_key varchar(40) NULL,  -- refkey организации
        supplier_key varchar(40) NULL,  -- refkey поставщика
        customer_key varchar(40) NULL,  -- refkey покупателя
        distributor_key varchar(40) NULL,  -- refkey дистрибьютера
        nomenclature_key varchar(40) NULL,  -- refkey номенклатуры
        nomenclature_series_key varchar(40) NULL,  -- СерияНоменклатуры_Key
        currency_key varchar(40) NULL,  -- refkey валюты
        unit_key varchar(40) NULL,  -- refkey ед.изм
        warehouse_key varchar(50) NOT NULL,  -- Склад_key
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key           
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (idt)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'продажи';
    COMMENT ON COLUMN {TABLE_NAME}.organization IS 'организация';
    COMMENT ON COLUMN {TABLE_NAME}.supplier IS 'поставщик';
    COMMENT ON COLUMN {TABLE_NAME}.customer IS 'покупатель';
    COMMENT ON COLUMN {TABLE_NAME}.distributor IS 'менеджер';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'дата';
    COMMENT ON COLUMN {TABLE_NAME}.sell_by IS 'годен до';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'номер';
    COMMENT ON COLUMN {TABLE_NAME}.sku IS 'номенклатура';
    COMMENT ON COLUMN {TABLE_NAME}.inbox IS 'в коробке';
    COMMENT ON COLUMN {TABLE_NAME}.tobox IS 'в пересчете на коробку';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'кол-во';
    COMMENT ON COLUMN {TABLE_NAME}.coef_sale IS 'коэф';
    COMMENT ON COLUMN {TABLE_NAME}.price IS 'цена с НДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма с НДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_ue IS 'сумма в уе с НДС';
    COMMENT ON COLUMN {TABLE_NAME}.price_tr IS 'цена закупа';
    COMMENT ON COLUMN {TABLE_NAME}.amount_tr IS 'сумма закупа';
    COMMENT ON COLUMN {TABLE_NAME}.maliyet IS 'с/сть ue';
    COMMENT ON COLUMN {TABLE_NAME}.amount_maliyet IS 'сумма с/сти ue';
    COMMENT ON COLUMN {TABLE_NAME}.amount_maliyet_uah IS 'сумма с/сти uah';
    COMMENT ON COLUMN {TABLE_NAME}.barcode IS 'штрихкод';
    COMMENT ON COLUMN {TABLE_NAME}.rate_nbu IS 'курс НБУ usd';
    COMMENT ON COLUMN {TABLE_NAME}.rate_sum IS 'сумма курса tobox*rate_nbu';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_rate IS 'курс по док';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements IS 'кратность взаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.unit IS 'ед.изм';
    COMMENT ON COLUMN {TABLE_NAME}.currency IS 'валюта';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'НДС';
    COMMENT ON COLUMN {TABLE_NAME}.consider_vat IS 'УчитыватьНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
    COMMENT ON COLUMN {TABLE_NAME}.issale IS '1-продажа -1 - возврат';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'отображать в бух учете';
    COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'ТипДок';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'refkey док.продажи';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'refkey организации';
    COMMENT ON COLUMN {TABLE_NAME}.supplier_key IS 'refkey поставщика';
    COMMENT ON COLUMN {TABLE_NAME}.customer_key IS 'refkey покупателя';
    COMMENT ON COLUMN {TABLE_NAME}.distributor_key IS 'refkey дистрибьютера';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'refkey номенклатуры';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'refkey валюты';
    COMMENT ON COLUMN {TABLE_NAME}.unit_key IS 'refkey ед.изм';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.segment IS 'ПодСеть';
    COMMENT ON COLUMN {TABLE_NAME}.segment_folder IS 'сеть';
    COMMENT ON COLUMN {TABLE_NAME}.auto_sale IS 'АвтоматическиСоздаватьРеализацию';
    COMMENT ON COLUMN {TABLE_NAME}.price_unit IS 'цена за баз ед.изм';
    
    ALTER TABLE {TABLE_NAME} OWNER TO postgres;
    GRANT ALL ON TABLE {TABLE_NAME} TO postgres;
    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;
    '''

sql_insert = f"""
    INSERT INTO {TABLE_NAME} (
        doc_date,
        doc_number,
        quantity,
        coef_sale,
        price,
        amount,
        settlement_rate,
        multiplicity_of_mutual_settlements,
        amount_vat,
        consider_vat,
        amount_includes_vat,
        issale,
        doc_type,
        ref_key,
        organization_key,
        customer_key,
        nomenclature_key,
        currency_key,
        warehouse,
        warehouse_key,
        is_accounting,
        contract_key,
        auto_sale
    )
    SELECT 
        doc_date,
        doc_number,
        quantity,
        coefficient,
        price,
        amount,
        kur,
        multiplicity_of_mutual_settlements,
        amount_vat,
        consider_vat,
        amount_includes_vat,
        issale,
        doc_type,
        document_key,
        organization_key,
        account_key,
        nomenclature_key,
        currency_key,
        warehouse.description AS warehouse,
        warehouse_key,
        is_accounting,
        contract_key,
        auto_sale
    FROM (
            SELECT 
                serv.doc_date,
                serv.doc_number,
                goods.quantity,
                goods.coefficient,
                goods.price,
                goods.amount,
                serv.rate_settlement / serv.multiplicity_of_mutual_settlements AS kur,
                serv.multiplicity_of_mutual_settlements,     
                goods.amount_vat,   
                serv.consider_vat,
                serv.amount_includes_vat,
                1 AS issale,
                'продажа' as doc_type,
                serv.ref_key as document_key,
                serv.organization_key,
                serv.account_key,
                goods.nomenclature_key,
                goods.nomenclature_series_key,
                serv.currency_key,
                goods.unit_of_key,
                goods.warehouse_key,
                serv.is_accounting,
                serv.contract_key,
                False as auto_sale
            FROM t_one_doc_sale_of_goods_services AS serv -- Document_РеализацияТоваровУслуг
                INNER JOIN t_one_doc_sale_of_goods_services_goods AS goods -- Document_РеализацияТоваровУслуг_Товары
                USING(ref_key)
            WHERE serv.is_management AND serv.posted
        UNION ALL 
            SELECT 
                serv.doc_date,
                serv.doc_number,
                -goods.quantity,
                goods.coefficient,
                goods.price,
                -goods.amount,
                CASE
                    WHEN serv.multiplicity_of_mutual_settlements <> 0 THEN
                        serv.settlement_rate / serv.multiplicity_of_mutual_settlements
                    ELSE
                        serv.settlement_rate
                END AS kur,
                serv.multiplicity_of_mutual_settlements,     
                -goods.amount_vat,   
                serv.consider_vat,
                serv.amount_includes_vat,
                -1 AS issale,
                'возврат' as doc_type,
                serv.ref_key as document_key,
                serv.organization_key,
                serv.account_key,
                goods.nomenclature_key,
                goods.nomenclature_series_key,
                serv.currency_key,
                goods.unit_of_key,
                goods.warehouse_key,
                serv.is_accounting,
                serv.contract_key,
                serv.auto_sale
            FROM t_one_doc_return_of_goods_from_customers AS serv -- Document_ВозвратТоваровОтПокупателя                
                INNER JOIN t_one_doc_return_of_goods_from_customers_goods AS goods -- Document_ВозвратТоваровОтПокупателя_Товары
                USING(ref_key)
            WHERE is_management and posted
        UNION ALL 
            SELECT 
                serv.tarih AS doc_date,
                serv.doc_number,
                serv.quantity,
                serv.coefficient,
                0 AS price,
                0 AS amount,
                1 AS settlement_rate,
                1 AS multiplicity_of_mutual_settlements,     
                0 AS amount_vat,   
                false AS consider_vat,
                false AS amount_includes_vat,
                -1 AS issale,
                'оприходывание' as doc_type,
                serv.ref_key as document_key,
                serv.organization_key,
                NULL AS account_key,
                serv.nomenclature_key,
                serv.nomenclature_series_key,
                NULL AS currency_key,
                serv.unit_of_key,
                serv.warehouse_key,
                serv.is_accounting,
                contract_key,
                False as auto_sale
            FROM v_one_oprihodivanie AS serv -- Document_Оприходование
        UNION ALL 
            SELECT 
                serv.tarih as doc_date,
                serv.doc_number,
                serv.quantity,
                serv.coefficient,
                0 AS price,
                0 AS amount,
                1 AS settlement_rate,
                1 AS multiplicity_of_mutual_settlements,
                0 AS amount_vat,   
                false AS consider_vat,
                false AS amount_includes_vat,
                1 AS issale,
                'списание' as doc_type,
                serv.ref_key as document_key,
                serv.organization_key,
                NULL AS account_key,
                serv.nomenclature_key,
                serv.nomenclature_series_key,
                NULL AS currency_key,
                serv.unit_of_key,
                serv.warehouse_key,
                serv.is_accounting,
                NULL as contract_key,
                False as auto_sale
            FROM v_one_spisanie as serv -- Document_Списание
        ) AS t
        LEFT JOIN t_one_cat_warehouses AS warehouse
            ON warehouse.ref_key = t.warehouse_key
    WHERE t.doc_date::date <= current_date
        AND t.nomenclature_key 
            IN ( SELECT nom_2.ref_key
                  FROM t_one_cat_nomenclature nom_2
                  WHERE nom_2.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c')
    ORDER BY t.doc_date DESC;
    """

# ************* UPDATE
sql_update_organization = f'''
    UPDATE {TABLE_NAME} AS tos
    SET organization = org.description
    FROM t_one_cat_organizatiONs AS org
    WHERE org.ref_key = tos.organizatiON_key;
'''

# поставщик заполняется из документов поступления
sql_update_supplier = f'''
    UPDATE {TABLE_NAME} AS tos
    SET supplier = mlt.client,
        supplier_key = mlt.account_key
    FROM v_service_tc_maliyet AS mlt
    WHERE mlt.nomenclature_key = tos.nomenclature_key;
'''

sql_update_customer = f'''
    UPDATE {TABLE_NAME} AS tos
    SET customer = clients.customer,
        segment_folder = clients.segment_folder,
        segment = clients.segment
    FROM v_one_manager_counterparty_contracts_segments AS clients
    WHERE clients.customer_key = tos.customer_key;
'''

sql_update_customer_oprihodivanie = f'''
    UPDATE {TABLE_NAME} AS tos
    SET customer = 'Оприходывание'
    WHERE doc_type = 'оприходывание';
'''

sql_update_customer_spisanie = f'''
    UPDATE {TABLE_NAME} AS tos
    SET customer = 'Списание'
    WHERE doc_type = 'списание';
'''

sql_update_distributor_key = f'''
        UPDATE {TABLE_NAME} AS tos
        SET	distributor = prnt.description,
            distributor_key = prnt.parent_key 
        FROM t_one_cat_counterparties AS prnt
        WHERE prnt.parent_key = tos.customer_key;
    '''

sql_update_distributor_customer = f'''
        UPDATE {TABLE_NAME} AS tos
        SET customer = prnt.description,
            distributor = manager.description,
            distributor_key = manager.ref_key 
        FROM t_one_cat_counterparties AS prnt
            INNER JOIN t_one_cat_counterparties AS manager
                ON prnt.parent_key = manager.ref_key
        WHERE tos.customer_key = prnt.ref_key
    '''

sql_update_sku = f'''
        UPDATE {TABLE_NAME} AS tos
        SET sku = trim(nom.description) 
        FROM t_one_cat_nomenclature AS nom
        WHERE nom.ref_key = tos.nomenclature_key;
    '''

sql_update_unit = f'''
        UPDATE {TABLE_NAME} AS tos
        SET unit = units.description
        FROM t_one_cat_units AS units
        WHERE units.nomenclature_key = tos.nomenclature_key 
            AND units.ref_key = tos.unit_key
            AND NOT deletion_mark
        ;
    '''

sql_update_currency = f'''
        UPDATE {TABLE_NAME}
        SET currency = cur.description 
        FROM t_one_cat_currencies AS cur
        WHERE cur.ref_key = {TABLE_NAME}.currency_key;
    '''

sql_update_inbox = f'''
        UPDATE {TABLE_NAME} AS tos
        SET inbox  = units.coefficient
        FROM (
            SELECT nomenclature_key, max(coefficient) AS coefficient
            FROM t_one_cat_units
            WHERE NOT deletion_mark
            GROUP BY nomenclature_key
            ) AS units
        WHERE units.nomenclature_key = tos.nomenclature_key;
    '''

sql_update_tobox = f'''
        UPDATE {TABLE_NAME} AS tos
        SET tobox = ((quantity * coef_sale) / inbox)
        FROM (
            SELECT nomenclature_key, max(coefficient) AS coefficient
            FROM t_one_cat_units
            WHERE NOT deletion_mark
            GROUP BY nomenclature_key
            ) AS units
        WHERE units.nomenclature_key = tos.nomenclature_key
            AND inbox <> 0;
    '''

sql_update_rate_nbu = f'''
        UPDATE {TABLE_NAME} AS tos
        SET rate_nbu  = rate_usd_nbu
        FROM t_rate_nbu AS trnbu
        WHERE tos.doc_date::date = trnbu.rate_date::date
            AND tos.rate_nbu in (0,1);
    '''

sql_update_rate_sum = f'''
    UPDATE {TABLE_NAME} AS tos
    SET rate_sum = tobox *
        CASE
            WHEN COALESCE(settlement_rate,1) = 1 THEN 
                rate_nbu
            ELSE
                settlement_rate
        END
    ;
'''

sql_update_maliyet_avg = f'''			
    UPDATE {TABLE_NAME}
    SET maliyet = avg_maliyet
    FROM (
            SELECT distinct 
                ss.doc_date::date, 
                ssg.nomenclature_key,
                sum(mlt.maliyet_tutar) / sum(mlt.quantity) AS avg_maliyet
            FROM t_one_doc_sale_of_goods_services AS ss
                INNER JOIN t_one_doc_sale_of_goods_services_goods AS ssg
                    ON ss.ref_key = ssg.ref_key 
                LEFT JOIN v_service_tc_maliyet AS mlt
                    ON mlt.nomenclature_key = ssg.nomenclature_key 
            WHERE coalesce(mlt.maliyet,0) <> 0
                AND ss.is_management AND ss.posted
                AND mlt.serv_date::date <= ss.doc_date::date
                AND mlt.serv_date::date >= (ss.doc_date::date - '3 month'::interval)::date
                AND mlt.quantity <> 0
            GROUP BY ss.doc_date::date, ssg.nomenclature_key
        ) AS smlt
    WHERE {TABLE_NAME}.nomenclature_key = smlt.nomenclature_key
        AND smlt.doc_date::date = {TABLE_NAME}.doc_date::date
        AND {TABLE_NAME}.maliyet = 0
    ;
'''

sql_update_price_tr_avg = f'''
    UPDATE {TABLE_NAME}
    SET price_tr = avg_price_tr
    FROM (
            SELECT distinct 
                ss.doc_date::date, 
                ssg.nomenclature_key,
                sum(mlt.amount_tr) / sum(mlt.quantity) AS avg_price_tr
            FROM t_one_doc_sale_of_goods_services AS ss
                INNER JOIN t_one_doc_sale_of_goods_services_goods AS ssg
                    ON ss.ref_key = ssg.ref_key 
                LEFT JOIN v_service_tc_maliyet AS mlt
                    ON mlt.nomenclature_key = ssg.nomenclature_key 
            WHERE coalesce(mlt.price_tr,0) <> 0
                AND ss.is_management AND ss.posted
                AND mlt.serv_date::date <= ss.doc_date::date
                AND mlt.serv_date::date >= (ss.doc_date::date - '3 month'::interval)::date
                AND mlt.quantity <> 0
            GROUP BY ss.doc_date::date, ssg.nomenclature_key
            ORDER BY ss.doc_date::date  DESC
        ) AS smlt
    WHERE {TABLE_NAME}.nomenclature_key = smlt.nomenclature_key
        AND smlt.doc_date::date = {TABLE_NAME}.doc_date::date
        AND {TABLE_NAME}.price_tr = 0
    ;
'''

sql_update_price_amount_vat = f'''
    UPDATE {TABLE_NAME}
    SET price = (COALESCE(amount,0) + COALESCE(amount_vat,0)) / quantity,
        amount = abs(COALESCE(amount,0) + COALESCE(amount_vat,0)) * issale
    WHERE consider_vat AND NOT amount_includes_vat AND (COALESCE(quantity,0) <> 0);
'''

sql_update_amount_ue_no_uah = f'''
    UPDATE {TABLE_NAME}
    SET amount_ue = amount
    WHERE currency_key != '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c';
'''

sql_update_amount_ue_uah = f'''
    UPDATE {TABLE_NAME}
    SET amount_ue = amount /
        CASE
            WHEN COALESCE(settlement_rate,1) = 1 THEN 
                rate_nbu
            ELSE
                settlement_rate
        END 
    WHERE currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'
    ;
'''

sql_update_price_amount_no_uah = f'''
    UPDATE {TABLE_NAME}
    SET price = round(price * 
        CASE
            WHEN COALESCE(settlement_rate,1) = 1 THEN 
                rate_nbu
            ELSE
                settlement_rate
        END,3),
        amount = abs(round(amount * 
            CASE
                WHEN COALESCE(settlement_rate,1) = 1 THEN 
                    rate_nbu
                ELSE
                    settlement_rate
            END        
        ,3)) * issale
    WHERE currency_key !='3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c';
'''

sql_update_price_tr_last = f'''
    UPDATE {TABLE_NAME} AS usale
    SET price_tr = price_source.last_price_tr
    FROM (
        SELECT 
            idt,
            coalesce((
            SELECT price_tr
            FROM v_service_tc_maliyet AS mlt
            WHERE mlt.serv_date::date <= sale.doc_date::date 
                AND mlt.nomenclature_key = sale.nomenclature_key 
            ORDER BY mlt.serv_date DESC 
            LIMIT 1
            ),0) AS last_price_tr 
        FROM {TABLE_NAME} AS sale
        WHERE sale.price_tr = 0
    ) AS price_source
    WHERE price_source.idt = usale.idt
        AND usale.price_tr = 0;
'''

sql_update_maliyet_last = f'''
    UPDATE {TABLE_NAME} AS usale
    SET maliyet = price_source.last_maliyet
    FROM (
        SELECT 
            idt,
            coalesce((
            SELECT maliyet 
            FROM v_service_tc_maliyet AS mlt
            WHERE mlt.serv_date <= sale.doc_date 
                AND mlt.nomenclature_key = sale.nomenclature_key 
            ORDER BY mlt.serv_date DESC 
            LIMIT 1
            ),0) AS last_maliyet 
        FROM {TABLE_NAME} AS sale
        WHERE sale.maliyet = 0
    ) AS price_source
    WHERE price_source.idt = usale.idt
        AND usale.maliyet = 0;
'''

sql_update_amount_tr_maliyet = f'''
    UPDATE {TABLE_NAME} AS usale
    SET amount_tr  = abs(price_tr * tobox) * issale,
        amount_maliyet = abs(maliyet * tobox) * issale,
        amount_maliyet_uah = abs(maliyet * tobox * 
            CASE
                WHEN COALESCE(settlement_rate,1) = 1 THEN 
                    rate_nbu
                ELSE
                    settlement_rate
            END 
        ) * issale;
'''

sql_update_sell_day = '''
    UPDATE t_one_sale AS sale
    SET sell_by = ser.sell_by
    FROM t_one_cat_nomenclature_series AS ser
    WHERE sale.nomenclature_series_key = ser.ref_key
'''

sql_update_price_tr_and_maliyet = f'''
    UPDATE {TABLE_NAME} AS sale
    SET price_tr = COALESCE(stock.entry_price,0),
        maliyet = COALESCE(stock.maliyet,0),
        amount_tr  = abs(COALESCE(stock.entry_price,0) * sale.tobox) * sale.issale,
        amount_maliyet = abs(COALESCE(stock.maliyet,0) * sale.tobox) * sale.issale,
        amount_maliyet_uah = abs(COALESCE(stock.maliyet,0) * sale.tobox * 
            CASE
                WHEN COALESCE(settlement_rate,1) = 1 THEN 
                    sale.rate_nbu
                ELSE
                    settlement_rate
            END
        ) * sale.issale
    FROM t_one_stock AS stock
    WHERE stock.document_key = sale.ref_key
        AND stock.nomenclature_key = sale.nomenclature_key
    ;
'''

SQL_IS_ACCOUNTING = '''
    UPDATE t_one_sale sale
    SET is_accounting = (sale.is_accounting AND org.organization_type)
    FROM v_one_organization_and_type AS org
    WHERE sale.organization_key = org.ref_key
;
'''

# если инф о поставщике отсутствует, тогда заполняем из номенклатуры
ISNULL = "********-0000-0000-0000-********0000"
SQL_UPDATE_SUPPLIER_KEY_IN_STOCK = f"""
    UPDATE {TABLE_NAME} AS stock
    SET supplier_key = nom.supplier_key 
    FROM t_one_cat_nomenclature AS nom
    WHERE stock.nomenclature_key = nom.ref_key 
        AND COALESCE(stock.supplier_key,'{ISNULL}') = '{ISNULL}'
    ;
"""

SQL_UPDATE_SUPPLIER_IN_STOCK = f"""
    UPDATE {TABLE_NAME} AS stock
    SET supplier = client.description
    FROM t_one_cat_counterparties AS client
    WHERE stock.supplier IS NULL
        AND COALESCE(stock.supplier_key,'{ISNULL}') <> '{ISNULL}'
        AND stock.supplier_key = client.ref_key 
    ;
"""

SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET_REST = f"""
    UPDATE {TABLE_NAME} AS sale
    SET price_tr = abs(stock.entry_price),
        maliyet = abs(stock.maliyet),
        amount_tr = abs(stock.entry_amount) * issale,
        amount_maliyet = abs(stock.amount_maliyet) * issale,
        amount_maliyet_uah = abs(stock.amount_maliyet) * issale * rate_nbu
    FROM t_one_stock AS stock
    WHERE stock.document_key = sale.ref_key
        AND stock.nomenclature_key = sale.nomenclature_key
    ;
"""

UPDATE_PRICE_UNIT = f"""
    UPDATE {TABLE_NAME} AS sale
    SET price_unit = amount / (quantity * coef_sale)
"""


async def main_sale_async():
    main_rate_nbu()
    logger.info("main_rate_nbu")

    result = await async_save_pg(sql_create)
    logger.info(f"{result}, sql_create")

    result = await async_truncate_table(TABLE_NAME)
    logger.info(f"{result}, TRUNCATE")

    await create_views()
    logger.info('create_views')

    result = await async_save_pg(sql_insert)
    logger.info(f"{result}, sql_insert")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    # ***************** UPDATE ON
    result = await async_save_pg(sql_update_sell_day)
    logger.info(f"{result}, sql_update_sell_day")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_organization)
    logger.info(f"{result}, sql_update_organization")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")
    # size =  await select_pg(f"SELECT pg_size_pretty( pg_total_relation_size('{TABLE_NAME}'));")
    # print(size[0]['pg_size_pretty'])

    result = await async_save_pg(sql_update_supplier)
    logger.info(f"{result}, sql_update_supplier")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_distributor_customer)
    logger.info(f"{result}, sql_update_distributor_customer")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_customer)
    logger.info(f"{result}, sql_update_customer")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_customer_oprihodivanie)
    logger.info(f"{result}, sql_update_customer_oprihodivanie")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_customer_spisanie)
    logger.info(f"{result}, sql_update_customer_spisanie")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_distributor_key)
    logger.info(f"{result}, sql_update_distributor_key")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_sku)
    logger.info(f"{result}, sql_update_sku")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_unit)
    logger.info(f"{result}, sql_update_unit")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_currency)
    logger.info(f"{result}, sql_update_currency")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_inbox)
    logger.info(f"{result}, sql_update_inbox")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_tobox)
    logger.info(f"{result}, sql_update_tobox")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_rate_nbu)
    logger.info(f"{result}, sql_update_rate_nbu")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_price_amount_vat)
    logger.info(f"{result}, sql_update_price_amount_vat")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_amount_ue_no_uah)
    logger.info(f"{result}, sql_update_amount_ue_no_uah")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_amount_ue_uah)
    logger.info(f"{result}, sql_update_amount_ue_uah")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_price_amount_no_uah)
    logger.info(f"{result}, sql_update_price_amount_no_uah")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(SQL_UPDATE_SUPPLIER_IN_STOCK)
    logger.info(f"{result}, SQL_UPDATE_SUPPLIER_IN_STOCK")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(SQL_UPDATE_SUPPLIER_KEY_IN_STOCK)
    logger.info(f"{result}, SQL_UPDATE_SUPPLIER_KEY_IN_STOCK")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    # ************************************************
    await create_views()
    logger.info('create_views')
    # ************************************************

    result = await async_save_pg(sql_update_price_tr_and_maliyet)
    logger.info(f"{result}, sql_update_price_tr_and_maliyet")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(sql_update_rate_sum)
    logger.info(f"{result}, sql_update_rate_sum")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(UPDATE_PRICE_UNIT)
    logger.info(f"{result}, UPDATE_PRICE_UNIT")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(SQL_IS_ACCOUNTING)
    logger.info(f"{result}, SQL_IS_ACCOUNTING")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_save_pg(SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET_REST)
    logger.info(f"{result}, SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET_REST")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")
    # ***************** UPDATE OFF

    result = await async_sql_create_index(TABLE_NAME, "customer_key")
    logger.info(f"{result} async_sql_create_index {TABLE_NAME} customer_key")
    if not result:
        result = await async_truncate_table(TABLE_NAME)
        logger.info(f"{result}, async_truncate_table")

    result = await async_sql_create_index(TABLE_NAME, "sku")
    logger.info(f"{result} async_sql_create_index {TABLE_NAME} sku")

    result = await async_sql_create_index(TABLE_NAME, "doc_type")
    logger.info(f"{result} async_sql_create_index {TABLE_NAME} doc_type")

    result = await async_sql_create_index(TABLE_NAME, "doc_date")
    logger.info(f"{result} async_sql_create_index {TABLE_NAME} doc_date")

    result = await async_sql_create_index(TABLE_NAME, "ref_key")
    logger.info(f"{result} async_sql_create_index {TABLE_NAME} ref_key")


if __name__ == '__main__':
    logger.info(f"START")

    asyncio.run(main_t_one_stock_async())
    logger.info("main_t_one_stock_async")

    asyncio.run(main_sale_async())
    logger.info(f"FINISH")
