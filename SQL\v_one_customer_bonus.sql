DROP VIEW IF EXISTS v_one_customer_bonus;
CREATE OR REPLACE VIEW v_one_customer_bonus AS
SELECT
	t.manager,
	t.customer,
	sum(COALESCE(t.amount,0)) AS amount
FROM (
	SELECT
		sp.manager,
		sp.customer,
		sp.doctype,
		COALESCE(sp.amount,0) AS amount,
		bt.description AS bonus_type,
		sp.customer_key,
		sp.contract_key
	FROM v_one_customers_sales_payment AS sp -- товар и банк
		INNER JOIN t_customer_bonus AS bns
			ON bns.contract_key = sp.contract_key
		INNER JOIN t_bonus_type AS bt
			ON bt.id = bns.bonus_key
	WHERE doctype = 'товар' AND bt.description = 'от продаж'
	UNION ALL
	SELECT
		sp.manager,
		sp.customer,
		sp.doctype,
		COALESCE(sp.amount,0) AS amount,
		bt.description AS bonus_type,
		sp.customer_key,
		sp.contract_key
	FROM v_one_customers_sales_payment AS sp -- товар и банк
		INNER JOIN t_customer_bonus AS bns
			ON bns.contract_key = sp.contract_key
		INNER JOIN t_bonus_type AS bt
			ON bt.id = bns.bonus_key
	WHERE doctype = 'платеж' AND bt.description = 'от возврата денег'
	UNION ALL
	SELECT DISTINCT
		sp.manager,
		sp.customer,
		'платеж' doctype,
		COALESCE(cash.amount,0) AS amount,
		'от возврата денег' AS bonus_type,
		sp.customer_key,
		sp.contract_key
	FROM v_one_manager_counterparty_contracts_segments AS sp
		INNER JOIN
		(
		SELECT
			customer_key,
			sum(COALESCE(amount,0)) amount
		FROM v_cash_register_rapor
		WHERE docdate:: date >='01.07.2024'
			AND docdate:: date <='31.07.2024'
			AND item_name = 'оплата покупателя' --'eb3df34e-24c9-46b6-9967-3ab8deacf6d3' -- 'оплата покупателя'
			AND customer NOT IN ('ТОВ "ВАЙН ХОЛЛ"', 'ТОВ "ДЕМАРТ"', 'ТОВ "ОМЕГА"', 'ФОП Федоренко О.В.')
		GROUP BY customer_key
		) AS cash
		ON cash.customer_key = sp.customer_key
) AS t
GROUP BY
	t.manager,
	t.customer
ORDER BY 1,2,3
;

--
---- продажа товара клиентам и оплата по договорам, в т.ч маркетинг
---- даты установлены вручную для теста
--DROP VIEW IF EXISTS v_one_customers_sales_payment;
--
--CREATE OR REPLACE VIEW v_one_customers_sales_payment AS
--SELECT
--	manager,
--	customer,
--	sum(amount) amount,
--	sum(bonus) bonus,
--	periods
--FROM (
--	SELECT
--		manager,
--		customer,
--		sum(amount) amount,
--		sum(bonus) bonus,
--		bonus_value,
--		description,
--		periods,
--		counterparty_key
--	FROM (
--		SELECT DISTINCT
--			mng.manager,
--			mng.customer,
--			reg.recorder,
--			CASE
--				WHEN reg.recordtype = 'Receipt' THEN
--					(coalesce(reg.amount,0))
--				WHEN bt.is_percent AND reg.recordtype = 'Expense' THEN
--					-(coalesce(reg.amount,0))
--				ELSE
--					0
--			END amount,
--			CASE
--				WHEN bt.is_percent AND reg.recordtype = 'Receipt' THEN
--					(coalesce(reg.amount,0)) * (bns.bonus_value / 100)
--				WHEN bt.is_percent AND reg.recordtype = 'Expense' THEN
--					-(coalesce(reg.amount,0)) * (bns.bonus_value / 100)
--				ELSE
--					0
--			END bonus,
--			bns.bonus_value,
--			bt.description,
--			reg.periods,
--			reg.counterparty_key
--		FROM
--			(
--			SELECT
--				contract_key,
--				counterparty_key,
--				recordtype,
--				recorder,
--				amount,
--				to_char(dtperiod,'YYYY.MM') periods
--			FROM t_one_doc_acc_reg_reciprocal_settlements_details
--			WHERE dtperiod::date >= '01.07.2024'
--				AND recorder_type IN ('StandardODATA.Document_РеализацияТоваровУслуг',
--					'StandardODATA.Document_ВозвратТоваровОтПокупателя')
--			)AS reg
--			INNER JOIN t_customer_bonus AS bns
--				ON bns.contract_key = reg.contract_key
--			INNER JOIN
--				(
--				SELECT *
--				FROM t_bonus_type
--				WHERE description = 'от продаж'
--				) AS bt
--				ON bt.id = bns.bonus_key
--			INNER JOIN v_one_manager_counterparty_contracts_segments AS mng
--				ON mng.customer_key = reg.counterparty_key
--	) t
--	GROUP BY
--		manager,
--		customer,
--		bonus_value,
--		description,
--		periods,
--		counterparty_key
--	-- оплпта
--	UNION ALL
--	SELECT
--		manager,
--		customer,
--		sum(amount) amount,
--		sum(bonus) bonus,
--		bonus_value,
--		description,
--		periods,
--		counterparty_key
--	FROM (
--		SELECT DISTINCT
--			mng.manager,
--			mng.customer,
--			reg.recorder,
--			CASE
--				WHEN reg.recordtype = 'Receipt' THEN
--					-(coalesce(reg.amount,0))
--				WHEN bt.is_percent AND reg.recordtype = 'Expense' THEN
--					(coalesce(reg.amount,0))
--				ELSE
--					0
--			END amount,
--			CASE
--				WHEN bt.is_percent AND reg.recordtype = 'Receipt' THEN
--					-(coalesce(reg.amount,0)) * (bns.bonus_value / 100)
--				WHEN bt.is_percent AND reg.recordtype = 'Expense' THEN
--					(coalesce(reg.amount,0)) * (bns.bonus_value / 100)
--				ELSE
--					0
--			END bonus,
--			bns.bonus_value,
--			bt.description,
--			reg.periods,
--			reg.counterparty_key
--		FROM
--			(
--			SELECT
--				contract_key,
--				counterparty_key,
--				recordtype,
--				recorder,
--				amount,
--				to_char(dtperiod,'YYYY.MM') periods
--			FROM t_one_doc_acc_reg_reciprocal_settlements_details
--			WHERE dtperiod::date >= '01.07.2024'
--				AND recorder_type IN (
--					'StandardODATA.Document_ПлатежноеПоручениеВходящее',
--					'StandardODATA.Document_ПлатежноеПоручениеИсходящее',
--					'StandardODATA.Document_ПриходныйКассовыйОрдер',
--					'StandardODATA.Document_РасходныйКассовыйОрдер'
--					)
--			)AS reg
--			INNER JOIN t_customer_bonus AS bns
--				ON bns.contract_key = reg.contract_key
--			INNER JOIN
--				(
--				SELECT *
--				FROM t_bonus_type
--				WHERE description = 'от возврата денег'
--				) AS bt
--				ON bt.id = bns.bonus_key
--			INNER JOIN v_one_manager_counterparty_contracts_segments AS mng
--				ON mng.customer_key = reg.counterparty_key
--	) t
--	GROUP BY
--		manager,
--		customer,
--		bonus_value,
--		description,
--		periods,
--		counterparty_key
--	ORDER BY 1,2,3
--) AS tt
--GROUP BY
--	manager,
--	customer,
--	periods
--ORDER BY 1,2,3
--;
--COMMENT ON VIEW public.v_one_customers_sales_payment IS 'продажа товара клиентам и оплата по договорам, в т.ч маркетинг';
