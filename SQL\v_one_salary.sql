
    CREATE OR REPLACE VIEW v_one_salary AS 
    SELECT doc_date, doc_number,sources,
        REPLACE(REPLACE(employee, '/лалю/', ''), '/', '') AS employee, 
        salary_period, sum(amount) AS amount
    FROM (
        SELECT doc_date, doc_number,trim(employee) AS employee, amount, salary_period::text, 'bank' AS sources
        FROM v_one_salary_paid_bank AS bank
        WHERE doc_date >= '01.01.2023'::date
        UNION all
        SELECT doc_date, doc_number, trim(employee) AS employee, -amount, salary_period::text, sources
        FROM v_one_salary_paid_cash AS cash
        WHERE doc_date >= '01.01.2023'::date)
    AS t
    GROUP BY salary_period, employee, doc_date, doc_number,sources
    ORDER BY salary_period, employee
    ;

    GRANT SELECT ON TABLE v_one_salary TO user_prestige;

