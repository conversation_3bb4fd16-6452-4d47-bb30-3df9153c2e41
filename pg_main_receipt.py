# запуск всех скриптов
# 09.09.2022
import os
import sys
import asyncio

from Document.docReceiptOfGoodsServices import main_receipt_of_goods_services_async
from Document.docReceiptOfGoodsServicesGoods import main_receipt_of_goods_services_goods_async
from prestige_authorize import CONFIG_PATH
# from views_pg import main_create_views

sys.path.append(os.path.abspath(CONFIG_PATH))

from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catNomenclature import main_cat_nomenclature_async
from Catalog.catNomenclatureSeries import main_cat_nomenclature_series_async
from createUser import main_create_user_async
from datetime import datetime


async def pg_main_receipt():
    await main_cat_nomenclature_async()
    print(datetime.now(), "\n")
    await main_cat_nomenclature_series_async()
    print(datetime.now(), "\n")
    await main_cat_contracts_counterparties_async()
    print(datetime.now(), "\n")
    await main_receipt_of_goods_services_async()
    print(datetime.now(), "\n")
    await main_receipt_of_goods_services_goods_async()
    print(datetime.now(), "\n")
    await main_create_user_async()
    print(datetime.now(), "\n")


if __name__ == '__main__':
    asyncio.run(pg_main_receipt())
