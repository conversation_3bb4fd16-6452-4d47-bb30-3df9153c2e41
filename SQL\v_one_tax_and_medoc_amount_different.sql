    CREATE OR REPLACE VIEW v_one_tax_and_medoc_amount_different AS
    SELECT
        sell.is_accounting БухУчет,
        sell.edrpou ОКПО,
        sell.customer as Контрагент,
        sell.tax_doc_date НН_дата,
        sell.tax_doc_number НН_Номер,
        sell.tax_amount НН_Сумма,
        medoc.docsum::numeric(15,2) AS Medoc_Сумма,
        sell.tax_amount-medoc.docsum::numeric(15,2) РазницаСумм,
        medoc.doc_date Medoc_Дата,
        medoc.docname Medoc_Наименование,
        medoc.sendsttname Medoc_Статус
    FROM
        (
        SELECT *
        FROM (
            SELECT
                reestr.docsum::numeric(15,2) docsum,
                reestr.doc_date,
                reestr.doc_num,
                info.sendsttname,
                info.lastupdate = max(info.lastupdate) OVER (PARTITION BY doc_id) AS last_updated,
                info.docname,
                reestr.partner_edrpou
            FROM medoc_reestr AS reestr
                LEFT JOIN medoc_doc_info AS info
                    ON reestr.doc_id = info.docid
            ) AS t
        WHERE t.last_updated
        ) AS medoc
        INNER JOIN
        (
        SELECT DISTINCT
            organization_type = COALESCE(sale.is_accounting,FALSE) AS is_accounting,
            client.customer,
            tax_sale.doc_date tax_doc_date,
            tax_sale.doc_number tax_doc_number,
            tax_sale.doc_number_short tax_doc_number_short,
            tax_sale.document_amount tax_amount,
            sale.document_amount sale_amount,
            sale.doc_date sale_doc_date,
            sale.doc_number sale_number,
            sale.doc_number_short sale_doc_number_short,
            tax_sale.is_consolidated_invoice,
            client.edrpou
        FROM
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,10)::int::text doc_number_short,
                document_amount,
                CASE
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        document_base_key
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        deal_key
                END base_key,
                CASE
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        document_base_type
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        deal_type
                END base_type,
                account_key,
                organization_key,
                is_consolidated_invoice
            FROM t_one_doc_tax_sale
            WHERE doc_date >= '01.01.2022'::date
            ) AS tax_sale
            LEFT JOIN
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,8)::int::text doc_number_short,
                document_amount::numeric(15,2) AS document_amount,
                ref_key,
                organization_key,
                is_accounting
            FROM t_one_doc_sale_of_goods_services
            ) AS sale
            ON tax_sale.base_key = sale.ref_key
            LEFT JOIN v_one_manager_counterparty_contracts_segments AS client
                ON tax_sale.account_key = client.customer_key
            LEFT JOIN v_one_organization_and_type AS org
                ON org.ref_key = tax_sale.organization_key
        )
        AS sell
        ON 	medoc.doc_num = sell.tax_doc_number_short
            AND medoc.doc_date= sell.tax_doc_date
                AND sell.edrpou = medoc.partner_edrpou
    WHERE  abs(sell.tax_amount-medoc.docsum::numeric(15,2)) > 0.2
        AND NOT sell.is_consolidated_invoice
    ORDER BY sell.customer, medoc.doc_date DESC
    ;

    COMMENT ON VIEW v_one_tax_and_medoc_amount_different IS 'расхождение сумм между налоговой накладной и medoc';
    GRANT SELECT ON TABLE  v_one_tax_and_medoc_amount_different TO user_prestige;
