import asyncio
import os
import sys

path_cash = os.path.join(os.path.split(os.path.dirname(__file__))[0], 'Cash')
sys.path.append(path_cash)
path_cash = os.path.split(os.path.dirname(__file__))[0]
sys.path.append(path_cash)

from CreateAndRunSQLScripts import create_views
from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catCounterparties import main_cat_counterparties_async
from Catalog.catCounterpartySegment import main_cat_counterparty_segment_async
from Cash.docCashCorrectionOfRegister import main_doc_cash_correction_of_register_async
from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderWithdrawalOfFunds import main_doc_cash_payment_order_withdrawal_of_funds_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Document.docDebtCorrection import main_debt_correction_async
from Document.docGtdImport import main_gtd_import_async
from Document.docIncomingAdditionalExpenses import main_incoming_additional_expenses_async
from Document.docReceiptOfGoodsServices import main_receipt_of_goods_services_async
from Document.docReturnOfGoodsFromCustomers import main_doc_return_of_goods_from_customers_async
from Document.docReturnOfGoodsToSupplier import main_doc_return_of_goods_to_supplier_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
from async_Postgres import async_truncate_table, async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_acc_reg_reciprocal_settlements_details"
DOCUMENT = "AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType"
SELECT_COLUMNS = ("Period,LineNumber,Recorder,Recorder_Type,Active,RecordType,ДоговорКонтрагента_Key,Сделка,"
                  "Сделка_Type,Организация_Key,Контрагент_Key,СуммаВзаиморасчетов,СуммаУпр")

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        dtperiod timestamp NOT NULL,  -- Period
        line_number numeric(7) NULL,  -- LineNumber
        doc_number varchar(15) NULL,  -- НомерДокумента
        is_accounting char(5) DEFAULT 'Ф1' NULL,  -- БухУчет
        active bool NOT NULL DEFAULT FALSE,  -- Active
        recordtype varchar(50) NULL,  -- RecordType
        amount numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаВзаиморасчетов
        amount_uah numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаУпр
        recorder varchar(50) NULL,  -- Recorder
        recorder_type varchar(300) NULL,  -- Recorder_Type
        deal varchar(50) NULL,  -- Сделка
        deal_type varchar(300) NULL,  -- Сделка_Type
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        counterparty_key varchar(50) NULL,  -- Контрагент_Key
        currency_key varchar(50) NULL,  -- Валюта_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (recorder, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dtperiod IS 'Period';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'НомерДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.recorder IS 'Recorder';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS 'Recorder_Type';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'БухУчет';
    COMMENT ON COLUMN {TABLE_NAME}.active IS 'Active';
    COMMENT ON COLUMN {TABLE_NAME}.recordtype IS 'RecordType';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.deal IS 'Сделка';
    COMMENT ON COLUMN {TABLE_NAME}.deal_type IS 'Сделка_Type';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'Валюта_Key';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.amount_uah IS 'СуммаУпр';

"""

SQL_UPDATE = f"""
    DO $$
    DECLARE 
       _table_name text;
       _query text;
       _is_accounting_exists boolean;
       _is_currency_key_exists boolean;
    BEGIN
        FOR _table_name IN
            SELECT table_name
            FROM doc_types
            WHERE COALESCE(table_name,'') <> ''
       LOOP
          BEGIN
          
              -- Проверяем, существует ли поле is_accounting
              SELECT EXISTS (
                  SELECT 1
                  FROM pg_attribute
                  WHERE attrelid = _table_name::regclass
                    AND attname = 'is_accounting'
              ) INTO _is_accounting_exists;
    
              IF _is_accounting_exists THEN
                  _query := format('
                        UPDATE {TABLE_NAME} as a
                            SET doc_number = b.doc_number,
                                is_accounting = (
                                    CASE 
                                        WHEN COALESCE(b.is_accounting,FALSE) 
                                            AND COALESCE(org.organization_type,FALSE) THEN 
                                                ''Ф1''
                                        ELSE 
                                            ''Ф2''
                                    END)
                        FROM %I AS b, v_one_organization_and_type as org
                        WHERE a.recorder = b.ref_key
                            AND a.organization_key = org.ref_key', _table_name
                        );
              ELSE
                  _query := format('
                        UPDATE {TABLE_NAME} as a
                        SET doc_number = b.doc_number,
                            is_accounting = (
                                CASE 
                                    WHEN COALESCE(org.organization_type,FALSE) THEN 
                                        ''Ф1''
                                    ELSE 
                                        ''Ф2''
                                END)
                        FROM %I AS b, v_one_organization_and_type as org
                        WHERE a.recorder = b.ref_key
                            AND a.organization_key = org.ref_key', _table_name
                        );
              END IF;
                
              EXECUTE _query;
              
          EXCEPTION
              WHEN others THEN
                  RAISE NOTICE 'Ошибка при обновлении таблицы %', _table_name;
          END;
       END LOOP;
                     
    END $$;
"""

SQL_DELETE_NOT_ACTIVE = f"DELETE FROM {TABLE_NAME} WHERE NOT active;"


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}
    (
        dtperiod,
        line_number,
        recorder, 
        recorder_type, 
        active, 
        recordtype, 
        contract_key, 
        deal, 
        deal_type, 
        organization_key, 
        counterparty_key, 
        amount, 
        amount_uah
    )
    VALUES {maket}
    ON CONFLICT (recorder, line_number)
    DO UPDATE SET
        dtperiod = EXCLUDED.dtperiod,
        line_number = EXCLUDED.line_number::INTEGER,
        recorder = EXCLUDED.recorder,
        recorder_type = EXCLUDED.recorder_type,
        active = EXCLUDED.active,
        recordtype = EXCLUDED.recordtype,
        contract_key = EXCLUDED.contract_key,
        deal = EXCLUDED.deal,
        deal_type = EXCLUDED.deal_type,
        organization_key = EXCLUDED.organization_key,
        counterparty_key = EXCLUDED.counterparty_key,
        amount = EXCLUDED.amount,
        amount_uah = EXCLUDED.amount_uah;
    """
    return sql.replace("'", "")


UPDATE_IS_ACCOUNTING = f"""
   -- если в док КорректировкаЗаписейРегистров есть запись с is_accounting = 'Ф2', 
   -- то устанавливаем в 'Ф2' у всех записей с таким же recorder
    UPDATE {TABLE_NAME} AS a
    SET is_accounting = 'Ф2'
    FROM (
        SELECT DISTINCT 
            recorder
        FROM {TABLE_NAME}
        WHERE trim(is_accounting) = 'Ф2'
            AND recorder_type ILIKE '%КорректировкаЗаписейРегистров%'
        ) AS b
    WHERE a.recorder = b.recorder
    ;
"""

SQL_UPDATE_CURRENCY_KEY = f"""
    -- Document_РеализацияТоваровУслуг
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_sale_of_goods_services AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ВозвратТоваровОтПокупателя
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_return_of_goods_from_customers AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_КорректировкаДолга
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_debt_correction AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПлатежноеПоручениеВходящее
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_cash_order_receipt AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПлатежноеПоручениеИсходящее
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_cash_order_expense AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПриходныйКассовыйОрдер
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_cash_warrant_receipt AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПриходныйКассовыйОрдер
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_cash_warrant_expense AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ВозвратТоваровПоставщику
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_return_of_goods_to_supplier AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПоступлениеТоваровУслуг
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_receipt_of_goods_services AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ГТДИмпорт
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_gtd_import AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПоступлениеДопРасходов
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_incoming_additional_expenses AS doc
    WHERE reg.recorder = doc.ref_key 
    ;
    
    -- Document_ПлатежныйОрдерСписаниеДенежныхСредств
    UPDATE {TABLE_NAME} AS reg
    SET currency_key = doc.currency_key 
    FROM t_one_doc_cash_withdrawal_of_funds AS doc
    WHERE reg.recorder = doc.ref_key 
    ;

"""


async def load_data():
    tasks = [
        # "Catalog_Контрагенты"
        main_cat_counterparties_async(),
        # "Catalog_ДоговорыКонтрагентов"
        main_cat_contracts_counterparties_async(),
        # "Catalog_СегментыКонтрагентов"
        main_cat_counterparty_segment_async(),
        # Document_ВозвратТоваровОтПокупателя 1
        main_doc_return_of_goods_from_customers_async(),
        # Document_ВозвратТоваровПоставщику 2
        main_doc_return_of_goods_to_supplier_async(),
        # Document_ГТДИмпорт 3
        main_gtd_import_async(),
        # Document_КорректировкаДолга 4
        main_debt_correction_async(),
        # Document_КорректировкаЗаписейРегистров 5
        main_doc_cash_correction_of_register_async(),
        # Document_ПлатежноеПоручениеВходящее 6
        main_doc_cash_order_receipt_async(),
        # Document_ПлатежноеПоручениеИсходящее 7
        main_doc_cash_order_expense_async(),
        # Document_ПлатежныйОрдерСписаниеДенежныхСредств 8
        main_doc_cash_payment_order_withdrawal_of_funds_async(),
        # Document_ПоступлениеДопРасходов 9
        main_incoming_additional_expenses_async(),
        # Document_ПоступлениеТоваровУслуг 10
        main_receipt_of_goods_services_async(),
        # Document_ПриходныйКассовыйОрдер 11
        main_doc_cash_warrant_receipt_async(),
        # Document_РасходныйКассовыйОрдер 12
        main_doc_cash_warrant_expense_async(),
        # Document_РеализацияТоваровУслуг 13
        main_doc_sale_of_goods_services_async(),
    ]

    # Запускаем все задачи и ждем их завершения
    # await asyncio.gather(*tasks)

    # # Запускаем задачи по 3 и ждем их завершения
    for i in range(0, len(tasks), 3):
        await asyncio.gather(*tasks[i:i + 3])


async def main_doc_reciprocal_settlements_details_async():
    # update data for now
    await load_data()
    logger.info("load_data")

    # create table
    await async_save_pg(SQL_CREATE_TABLE)
    logger.info("SQL_CREATE_TABLE")
    result = await async_truncate_table(TABLE_NAME)
    logger.info(f"{result}, TRUNCATE")
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(13)
    sql = await sql_insert(maket)
    sql = sql.replace("$1,", "to_timestamp($1, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    result = await async_save_pg(SQL_UPDATE)
    logger.info(f"{result}, SQL_UPDATE")

    result = await async_save_pg(SQL_DELETE_NOT_ACTIVE)
    logger.info(f"{result}, SQL_DELETE_NOT_ACTIVE")
    result = await async_save_pg(UPDATE_IS_ACCOUNTING)
    logger.info(f"{result}, UPDATE_IS_ACCOUNTING")
    result = await async_save_pg(SQL_UPDATE_CURRENCY_KEY)
    logger.info(f"{result}, SQL_UPDATE_CURRENCY_KEY")

    await create_views()


if __name__ == "__main__":
    logger.info("START")
    asyncio.run(main_doc_reciprocal_settlements_details_async())
    logger.info("FINISH")
