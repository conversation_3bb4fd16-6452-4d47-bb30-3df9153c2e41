--DROP VIEW IF EXISTS v_doc_cash_bank_all_from_table CASCADE;
CREATE VIEW v_doc_cash_bank_all_from_table AS
SELECT
    segment.manager,
    cust.customer,
    t.doc_date,
    t.doc_number,
    t.amount,
    t.description,
    t.document_type,
    t.item_period,
    TO_CHAR(t.item_period::date, 'YYYY.MM') AS periods,
    item.description AS item_name,
    cur.description AS currency,
    org.description AS organization,
    t.recorder_type
FROM t_doc_cash_bank_all AS t
    LEFT JOIN (
        SELECT DISTINCT
            manager,
            customer,
            customer_key::varchar(40) customer_key
        FROM v_one_manager_counterparty_contracts_segments
        )AS segment
        ON segment.customer_key = t.customer_key
    LEFT JOIN t_one_cat_currencies AS cur
        ON cur.ref_key = t.currency_key
    LEFT JOIN t_one_cat_organizations AS org
        ON org.ref_key = t.organization_key
    LEFT JOIN (
        SELECT ref_key::varchar(40) ref_key, customer FROM v_one_customer_manager
        UNION ALL
        SELECT ref_key::varchar(40) ref_key, description FROM t_one_cat_employees
        UNION ALL
        SELECT ref_key::varchar(40) ref_key, description FROM t_one_cat_individuals
        ) AS cust
        ON cust.ref_key::varchar(40)= t.customer_key
    LEFT JOIN t_one_cat_cash_flow_item AS item
        ON item.ref_key = t.item_key
ORDER BY
    doc_date,
    doc_number
;

COMMENT ON VIEW v_doc_cash_bank_all_from_table IS 'Документы по кассе и банку';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.manager IS 'Менеджер';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.customer IS 'Контрагент';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.doc_date IS 'Дата';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.doc_number IS 'Номер';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.amount IS 'Сумма';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.description IS 'Примечание';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.document_type IS 'Тип документа';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.item_period IS 'Период';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.item_name IS 'Статья';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.currency IS 'Валюта';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.organization IS 'Организация';
COMMENT ON COLUMN v_doc_cash_bank_all_from_table.recorder_type IS '1- приход; -1 - расход';

GRANT SELECT ON TABLE v_doc_cash_bank_all_from_table TO user_prestige;
