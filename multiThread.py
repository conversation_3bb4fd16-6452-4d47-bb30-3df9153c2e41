import asyncio
import json
import os
import sys

import aiohttp
import requests
from aiohttp import BasicAuth

sys.path.insert(0, r"d:\Prestige\Python\Config")
from configPrestige import C_LOGIN, C_PSW, URL_CONST, DATA_AUTH
from logger_prestige import get_logger

auth = BasicAuth(C_LOGIN, C_PSW)

# from concurrent.futures import ProcessPoolExecutor
# import requests

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

select_columns = (
    "LineNumber,Количество,Коэффициент,ПроцентСкидкиНаценки,Цена,Сумма,СуммаНДС,Ref_Key,"
    "ЕдиницаИзмерения_Key,ЕдиницаИзмеренияМест_Key,ЗаказПокупателя_Key,Качество_Key,"
    "НалоговоеНазначение_Key,НалоговоеНазначениеДоходовИЗатрат_Key,Номенклатура_Key,"
    "ПереданныеСчетУчетаБУ_Key,СерияНоменклатуры_Key,Склад_Key,СхемаРеализации_Key"
)


async def async_url_count(document):
    return URL_CONST + document + "/$count"


async def async_url_details(document):
    return URL_CONST + document + "/?$top=%s&$skip=%s&$format=json"


async def async_url_accumulate(document):
    return (
            URL_CONST + document + "/?$top=%s&$skip=%s&$format=json&$filter=Active eq true"
    )


async def async_url_main(document):
    return (
            await async_url_details(document)
            + "&$filter=Posted eq true and ОтражатьВУправленческомУчете eq true"
    )


async def async_url_posted(document):
    return await async_url_details(document) + "&$filter=Posted eq true&$select=%s"


async def async_url_main_select(document):
    return await async_url_main(document) + "&$select=%s"


async def async_url_details_select(document):
    return await async_url_details(document) + "&$select=%s"


async def async_url_managment_select(document):
    return await async_url_details(document) + "&$select=%s&$filter=Posted eq true"


async def get_json_from_url(url):
    result = requests.get(url, auth=DATA_AUTH)
    if result.status_code == 200:
        return result.json()
    return None


def sync_fetch(url):
    response = requests.get(url, auth=(C_LOGIN, C_PSW))
    if response.status_code == 200:
        # logger.info(f"Sync Status: {response.status_code}, URL: {url}")
        return response.json()
    else:
        logger.info(f"Error: {response.status_code}, message='{response.reason}'; URL: {url}")
        return None


async def async_fetch_async(session, url, timeout=10):
    try:
        async with session.get(url, timeout=timeout) as response:
            # logger.info(f"Async Status: {response.status}, URL: {url}")
            if response.status == 200:
                result = await response.text()
                return json.loads(result)
            else:
                return sync_fetch(url)
    except Exception as e:
        return sync_fetch(url)


# группировка по 3 запроса, используя семафор.
# Ограничение на 3 одновременные задачи. т.е если одна задача завершится, то запустится следующая,
# но не более 3 одновременно
async def fetch_with_sem(sem, session, url, timeout):
    async with sem:
        return await async_fetch_async(session, url, timeout)


async def run_map_get_list(session, urls, timeout=10):
    sem = asyncio.Semaphore(3)  # Ограничение на 3 одновременные задачи
    tasks = [fetch_with_sem(sem, session, url, timeout) for url in urls]
    return await asyncio.gather(*tasks)


# группировка по 3 запроса, после завершения предыдущей группы, запускается следующая
# т.е пока не завершатся все задачи предыдущей группы, следующая не запустится
# async def run_map_get_list(session, urls, timeout=10, batch_size=3):
#     results = []
#     for i in range(0, len(urls), batch_size):
#         batch = urls[i:i + batch_size]
#         tasks = [async_fetch_async(session, url, timeout) for url in batch]
#         batch_results = await asyncio.gather(*tasks)
#         results.extend(batch_results)
#     return results


async def main_thread(document, select_columns, ismain=False, ismanagment=False, isposted=False):
    select_columns = select_columns.replace(" ", "")
    url = await async_url_count(document)
    connector = aiohttp.TCPConnector(limit=50)
    async with aiohttp.ClientSession(connector=connector, auth=aiohttp.BasicAuth(C_LOGIN, C_PSW),
                                     json_serialize=json.dumps) as session:
        answer = await async_fetch_async(session, url)
        if answer is None:
            logger.error("Failed to fetch initial count")
            return []
        count = int(answer)
        logger.info(f"{document}. Количество записей: {count}")
        top = 20000
        skip = 0
        urls = []
        while skip <= count:
            if ismanagment:
                url = await async_url_managment_select(document) % (top, skip, select_columns)
            elif isposted:
                url = await async_url_posted(document) % (top, skip, select_columns)
            elif ismain:
                url = await async_url_main_select(document) % (top, skip, select_columns)
            else:
                url = await async_url_details_select(document) % (top, skip, select_columns)
            skip += top
            urls.append(url)

        responses = await run_map_get_list(session, urls)

        list_parent = []

        for item in responses:
            if item is None:
                continue

            source = item.get("value", [])
            if source and source[0].get("RecordSet"):
                source = [
                    (
                        {**k["RecordSet"][0], "Recorder": item[0].get("Recorder")}
                        if item[0].get("Recorder")
                        else k["RecordSet"][0]
                    )
                    for k in item
                ]

            list_child = [tuple(item_child.values()) for item_child in source]
            list_parent.append(list_child)

        return list_parent


if __name__ == "__main__":
    pass
    # document = "Document_РеализацияТоваровУслуг_Товары"
    # loop = asyncio.get_event_loop()
    # loop.run_until_complete(main_thread(document, select_columns))
