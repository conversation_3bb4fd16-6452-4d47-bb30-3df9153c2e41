    CREATE OR REPLACE VIEW v_one_tax_and_medoc_diff AS
    SELECT DISTINCT
        client.customer контрагент,
        medoc.doc_date AS МедокДата,
        medoc.doc_num AS МедокНомер,
        round(medoc.docsum::NUMERIC,2) AS МедокСумма,
        nn.document_amount AS НН_сумма,
        app2.document_amount AS РК_Сумма,
        CASE
            WHEN COALESCE(app2.doc_number,'') = '' THEN
                abs(COALESCE(medoc.docsum::numeric,0) - COALESCE(nn.document_amount,0))
            ELSE
                abs(COALESCE(medoc.docsum::numeric,0) - COALESCE(app2.document_amount,0))
        END AS Отклонение,
        CASE
            WHEN (COALESCE(nn.doc_number,'') <> '' and COALESCE(nn.document_amount,0) < 0) THEN
                'Продажа. Сумма НН меньше 0'
            WHEN (COALESCE(app2.doc_number,'') <> '' and COALESCE(medoc.docsum,0) > 0) THEN
                'Возврат. Сумма в Медке больше 0'
            WHEN (COALESCE(nn.doc_number,'') <> '' and COALESCE(medoc.docsum,0) < 0) THEN
                'Продажа. Сумма в Медке меньше 0'
            WHEN (COALESCE(app2.doc_number,'') <> '' and COALESCE(app2.document_amount,0) > 0) THEN
                'Возврат. Сумма РК больше 0'
            ELSE 'расхождение сумм'
        END Замечание,
        medoc.docname МедокНаимДокумента,
        medoc.sendsttname Статус,
        medoc.sendstt КодСтатуса,
        org.organization организация,
        medoc.partner_edrpou ОКПО
    FROM v_medoc_reestr_and_docinfo AS medoc
        LEFT JOIN t_one_doc_tax_sale AS nn
            ON medoc.doc_num = RIGHT(nn.doc_number,8)::int::TEXT
                AND medoc.doc_date::date = nn.doc_date::date
                AND medoc.docname ILIKE '%Податкова накладна%'
        LEFT JOIN t_one_doc_tax_appendix_2 AS app2
            ON medoc.doc_num = RIGHT(app2.doc_number,8)::int::TEXT
                AND medoc.doc_date::date = app2.doc_date::date
                AND medoc.docname ILIKE '%Додаток%'
        INNER JOIN v_one_manager_counterparty_contracts_segments AS client
            ON (nn.account_key = client.customer_key
                OR app2.account_key = client.customer_key)
            AND
                CASE
                    WHEN length(trim(client.edrpou)) > 8 AND medoc.partner_edrpou IS NULL THEN NULL
                    ELSE trim(client.edrpou)
                END = medoc.partner_edrpou
        INNER JOIN v_one_organization_and_type AS org
            ON (org.ref_key = nn.organization_key
                OR org.ref_key = app2.organization_key)
                AND org.inn_okpo = medoc.firm_edrpou
    WHERE (nn.posted OR app2.posted)
        AND medoc.doc_date > '01.01.2022'::date
        AND (
                ((COALESCE(nn.doc_number,'') <> '' and COALESCE(nn.document_amount,0) < 0)) OR
                ((COALESCE(nn.doc_number,'') <> '' and COALESCE(medoc.docsum,0) < 0)) OR
                ((COALESCE(app2.doc_number,'') <> '' and COALESCE(app2.document_amount,0) > 0)) OR
                ((COALESCE(app2.doc_number,'') <> '' and COALESCE(medoc.docsum,0) > 0)) OR
                (abs(COALESCE(medoc.docsum::numeric,0) - (COALESCE(nn.document_amount,0)) -
                    COALESCE(app2.document_amount,0)) > 1
                )
            )
    ORDER BY client.customer, medoc.doc_date
    ;

    COMMENT ON VIEW v_one_tax_and_medoc_diff IS 'расхождение между налогНакл/РасчКорректировки и Medoc';
    GRANT SELECT ON TABLE  v_one_tax_and_medoc_diff TO user_prestige;
