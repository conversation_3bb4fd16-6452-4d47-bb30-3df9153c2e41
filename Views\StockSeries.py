# остатки по сериям

import asyncio
import os
import sys

if os.environ['COMPUTERNAME'] == 'PRESTIGEPRODUCT':
    CONFIG_PATH = r"D:\Prestige\Python\Prestige"
else:
    CONFIG_PATH = r"C:\Rasim\Python\Config"

sys.path.append(os.path.abspath(CONFIG_PATH))

from async_Postgres import  async_save_pg

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

# загрузим данные из файла fn_stock_series.sql
CUR_DIR = os.path.dirname(os.path.abspath(__file__))
FOLDER_PATH = os.path.join(CUR_DIR, "SQL")
SQL_FILE_PATH = r"D:\Prestige\Python\Prestige\SQL\fn_stock_series.sql"
with open(SQL_FILE_PATH, 'r', encoding='utf-8') as file:
    SQL_CREATE_FUNCTION_STOCK_SERIES = file.read()


async def main_stock_series_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_FUNCTION_STOCK_SERIES)
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_stock_series_async())
