--DROP VIEW IF EXISTS v_doc_cash_bank_all;
CREATE OR REPLACE VIEW v_doc_cash_bank_all AS
WITH
bank_receipt AS (
    SELECT
        tarih,
        doc_number,
        ref_key,
        organization_key,
        currency_key,
        account_key,
        amount_of_payment AS amount,
        main_cash_flow_item_name,
        det_cash_flow_item_name,
        main_cash_flow_item_key::varchar(40) main_cash_flow_item_key,
        det_cash_flow_item_key::varchar(40) det_cash_flow_item_key,
        a_comment,
        'ППВ' doc_type,
        line_number,
        doc_date as period,
        1 as recorder_type
    FROM v_one_cash_order_receipt
    WHERE is_management
),
bank_expense AS (
    SELECT
        tarih,
        doc_number,
        ref_key,
        organization_key,
        currency_key,
        account_key,
        -amount_of_payment,
        main_cash_flow_item_name,
        det_cash_flow_item_name,
        main_cash_flow_item_key::varchar(40) ,
        det_cash_flow_item_key::varchar(40) ,
        a_comment,
        'ППИ',
        line_number,
        doc_date,
        -1 as recorder_type
    FROM v_one_cash_order_expense
    WHERE is_management
),
cashier_receipt AS (
    SELECT
        doc_date,
        doc_number,
        ref_key,
        organization_key,
        currency_key,
        customer_key,
        amount,
        main_cash_flow_item_name,
        det_cash_flow_item_name,
        main_cash_flow_item_key::varchar(40),
        det_cash_flow_item_key::varchar(40),
        a_comment,
        'ПКО',
        line_number,
        doc_date,
        1 as recorder_type
    FROM v_one_cash_warrant_receipt
    WHERE is_management
),
cashier_expense AS (
    SELECT
        doc_date,
        doc_number,
        ref_key,
        organization_key,
        currency_key,
        customer_key,
        document_amount amount,
        main_cash_flow_item_name,
        det_cash_flow_item_name,
        main_cash_flow_item_key::varchar(40),
        det_cash_flow_item_key::varchar(40),
        a_comment,
        'РКО',
        line_number,
        doc_date,
        -1 as recorder_type
    FROM v_one_cash_warrant_expense
    WHERE is_management
)
SELECT
    segment.manager,
    segment.customer,
    t.tarih,
    t.doc_number,
    t.amount,
    t.a_comment,
    t.doc_type,
    t.period,
    COALESCE(t.det_cash_flow_item_name, t.main_cash_flow_item_name) AS item_name,
    cur.description AS currency,
    org.description AS organization,
    t.ref_key,
    CASE
        WHEN COALESCE(det_cash_flow_item_key,'********-0000-0000-0000-************') = '********-0000-0000-0000-************' THEN
            main_cash_flow_item_key
        ELSE
            det_cash_flow_item_key
    END AS cash_flow_item_key,
    organization_key,
    currency_key,
    account_key,
    line_number,
    t.recorder_type,
    rate.rate_usd_nbu
FROM (
    SELECT * FROM bank_receipt
    UNION ALL
    SELECT * FROM bank_expense
    UNION ALL
    SELECT * FROM cashier_receipt
    UNION ALL
    SELECT * FROM cashier_expense
) AS t
    LEFT JOIN (
        SELECT DISTINCT
            manager,
            customer,
            customer_key
        FROM v_one_manager_counterparty_contracts_segments
        )AS segment
        ON segment.customer_key = t.account_key
    LEFT JOIN t_one_cat_currencies AS cur
        ON cur.ref_key = t.currency_key
    LEFT JOIN t_one_cat_organizations AS org
        ON org.ref_key = t.organization_key
    LEFT JOIN t_rate_nbu AS rate
        ON rate.rate_date::date = t.tarih::date
ORDER BY
    tarih,
    doc_number
;

COMMENT ON VIEW v_doc_cash_bank_all IS 'Документы по кассе и банку';
COMMENT ON COLUMN v_doc_cash_bank_all.manager IS 'Менеджер';
COMMENT ON COLUMN v_doc_cash_bank_all.customer IS 'Контрагент';
COMMENT ON COLUMN v_doc_cash_bank_all.tarih IS 'Дата';
COMMENT ON COLUMN v_doc_cash_bank_all.doc_number IS 'Номер';
COMMENT ON COLUMN v_doc_cash_bank_all.amount IS 'Сумма';
COMMENT ON COLUMN v_doc_cash_bank_all.a_comment IS 'Примечание';
COMMENT ON COLUMN v_doc_cash_bank_all.doc_type IS 'Тип документа';
COMMENT ON COLUMN v_doc_cash_bank_all.period IS 'Период';
COMMENT ON COLUMN v_doc_cash_bank_all.item_name IS 'Статья';
COMMENT ON COLUMN v_doc_cash_bank_all.currency IS 'Валюта';
COMMENT ON COLUMN v_doc_cash_bank_all.organization IS 'Организация';
COMMENT ON COLUMN v_doc_cash_bank_all.ref_key IS 'Ref_Key';
COMMENT ON COLUMN v_doc_cash_bank_all.cash_flow_item_key IS 'Статья движения денежных средств';
COMMENT ON COLUMN v_doc_cash_bank_all.organization_key IS 'Организация_Key';
COMMENT ON COLUMN v_doc_cash_bank_all.currency_key IS 'Валюта_Key';
COMMENT ON COLUMN v_doc_cash_bank_all.account_key IS 'Контрагент_Key';
COMMENT ON COLUMN v_doc_cash_bank_all.line_number IS 'Номер строки';
COMMENT ON COLUMN v_doc_cash_bank_all.recorder_type IS '1- приход; -1 - расход';
COMMENT ON COLUMN v_doc_cash_bank_all.rate_usd_nbu IS 'Курс НБУ';
