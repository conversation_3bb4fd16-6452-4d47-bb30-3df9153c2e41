# reading data from Document_ГТДИмпорт and add to pg in table {TABLE_NAME
# *********** импортируем данные для подключения к базам
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_debt_correction_debt_amount"
DOCUMENT = "Document_КорректировкаДолга_СуммыДолга"
SELECT_COLUMNS = (
    "LineNumber,Сделка,КурсВзаиморасчетов,КратностьВзаиморасчетов,СтавкаНДС,СуммаНДС,ТипКонтрагента,"
    "НетНалоговойНакладной,Сумма,СуммаВзаиморасчетов,ВидЗадолженности,РасчетыВозврат,ЗаТару,"
    "ДокументРасчетовСКонтрагентом,Ref_Key,ДоговорКонтрагента_Key,СчетУчетаРасчетов_Key,"
    "НалоговоеНазначение_Key,СчетУчетаНДС_Key,СчетУчетаНДСПодтвержденный_Key"
)

SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id serial4 NOT NULL,
        line_number numeric(4) NOT NULL DEFAULT 0,  -- LineNumber
        deal varchar(50) NULL,  -- Сделка
        rate numeric(10,4) NOT NULL DEFAULT 0,  -- КурсВзаиморасчетов
        multiplicity numeric(15,2) NOT NULL DEFAULT 0,  -- КратностьВзаиморасчетов
        vat_rate varchar(50) NULL,  -- СтавкаНДС
        amount_vat numeric(15,2) NOT NULL DEFAULT 0,  -- СуммаНДС
        counterparty_type varchar(50) NULL,  -- ТипКонтрагента
        no_tax_invoice boolean NOT NULL DEFAULT FALSE,  -- НетНалоговойНакладной
        total numeric(15,3) NOT NULL DEFAULT 0,  -- Сумма
        amount numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаВзаиморасчетов
        debt_type varchar(50) NULL,  -- ВидЗадолженности
        calculations_refund varchar(50) NULL,  -- РасчетыВозврат
        for_container boolean NOT NULL DEFAULT FALSE,  -- ЗаТару
        document_key varchar(50) NULL,  -- ДокументРасчетовСКонтрагентом
        ref_key varchar(50) NULL,  -- Ref_Key
        counterparty_contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        account_account_key varchar(50) NULL,  -- СчетУчетаРасчетов_Key
        tax_purpose_key varchar(50) NULL,  -- НалоговоеНазначение_Key
        account_vat_key varchar(50) NULL,  -- СчетУчетаНДС_Key
        account_vat_confirmed_key varchar(50) NULL,  -- СчетУчетаНДСПодтвержденный_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS ' LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.deal IS ' Сделка';
    COMMENT ON COLUMN {TABLE_NAME}.rate IS ' КурсВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity IS ' КратностьВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.vat_rate IS ' СтавкаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS ' СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_type IS ' ТипКонтрагента';
    COMMENT ON COLUMN {TABLE_NAME}.no_tax_invoice IS ' НетНалоговойНакладной';
    COMMENT ON COLUMN {TABLE_NAME}.total IS ' Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS ' СуммаВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.debt_type IS ' ВидЗадолженности';
    COMMENT ON COLUMN {TABLE_NAME}.calculations_refund IS ' РасчетыВозврат';
    COMMENT ON COLUMN {TABLE_NAME}.for_container IS ' ЗаТару';
    COMMENT ON COLUMN {TABLE_NAME}.document_key IS ' ДокументРасчетовСКонтрагентом';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_contract_key IS ' ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_account_key IS ' СчетУчетаРасчетов_Key';
    COMMENT ON COLUMN {TABLE_NAME}.tax_purpose_key IS ' НалоговоеНазначение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_vat_key IS ' СчетУчетаНДС_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_vat_confirmed_key IS ' СчетУчетаНДСПодтвержденный_Key';
    
    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;
"""

DEL_NOT_FOREIGN_KEY = f"""
        DELETE FROM {TABLE_NAME} debt_sub 
        WHERE NOT EXISTS ( SELECT 1 FROM t_one_doc_debt_correction debt 
            WHERE debt.ref_key = debt_sub.ref_key)
        ;
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        line_number, 
        deal, 
        rate, 
        multiplicity, 
        vat_rate, 
        amount_vat, 
        counterparty_type, 
        no_tax_invoice, 
        total, 
        amount, 
        debt_type, 
        calculations_refund, 
        for_container, 
        document_key, 
        ref_key, 
        counterparty_contract_key, 
        account_account_key, 
        tax_purpose_key, 
        account_vat_key, 
        account_vat_confirmed_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number= EXCLUDED.line_number,
        deal= EXCLUDED.deal,
        rate= EXCLUDED.rate,
        multiplicity= EXCLUDED.multiplicity,
        vat_rate= EXCLUDED.vat_rate,
        amount_vat= EXCLUDED.amount_vat,
        counterparty_type= EXCLUDED.counterparty_type,
        no_tax_invoice= EXCLUDED.no_tax_invoice,
        total= EXCLUDED.total,
        amount= EXCLUDED.amount,
        debt_type= EXCLUDED.debt_type,
        calculations_refund= EXCLUDED.calculations_refund,
        for_container= EXCLUDED.for_container,
        document_key= EXCLUDED.document_key,
        ref_key= EXCLUDED.ref_key,
        counterparty_contract_key= EXCLUDED.counterparty_contract_key,
        account_account_key= EXCLUDED.account_account_key,
        tax_purpose_key= EXCLUDED.tax_purpose_key,
        account_vat_key= EXCLUDED.account_vat_key,
        account_vat_confirmed_key= EXCLUDED.account_vat_confirmed_key
    ;    
    """
    return sql.replace("'", "")


async def main_debt_correction_debt_amount_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(20)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(DEL_NOT_FOREIGN_KEY)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_debt_correction_debt_amount_async())
