--DROP TABLE IF EXISTS t_cash_doc_types CASCADE;

CREATE TABLE IF NOT EXISTS t_cash_doc_types (
	id uuid DEFAULT uuid_generate_v4() NOT NULL,
	doc_name varchar(20) NOT NULL,  -- наименование документа
	description varchar(100) NULL,  -- комментарий
	CONSTRAINT t_cash_doc_types_pkey PRIMARY KEY (id),
	CONSTRAINT t_cash_doc_types_unq UNIQUE (doc_name)
);

COMMENT ON TABLE t_cash_doc_types IS 'группировка типдок документов по кассе';
COMMENT ON COLUMN t_cash_doc_types.doc_name IS 'наименование документа';
COMMENT ON COLUMN t_cash_doc_types.description IS 'комментарий';