
    CREATE OR REPLACE VIEW v_one_quantity_rows_in_document
    AS SELECT 
        t.type_,
        t.type_doc,
        t.rows_count,
        round(sum(t.rows_count) OVER (PARTITION BY t.type_ ORDER BY t.type_doc), 4) AS type_rows,
        round(sum(t.rows_count) OVER (ORDER BY t.type_doc), 4) AS type_doc_rows,
        round(t.ed,4) AS ed,
        round(sum(t.ed) OVER (PARTITION BY t.type_ ORDER BY t.type_doc), 4) AS type_ed,
        round(sum(t.ed) OVER (ORDER BY t.type_doc), 4) AS type_doc_ed,
        round(t.tobox,4) AS tobox,
        round(sum(t.tobox) OVER (PARTITION BY t.type_ ORDER BY t.type_doc), 4) AS type_box,
        round(sum(t.tobox) OVER (ORDER BY t.type_doc), 4) AS type_doc_box
       FROM ( SELECT 
                'оприходывание'::text AS type_doc,
                'пересорт'::text AS type_,
                count(1) AS rows_count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_oprihodivanie
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'списание'::text AS type_doc,
                'пересорт'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_spisanie
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'требование'::text AS type_doc,
                'требование'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_trebovanie
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'поступление'::text AS type_doc,
                'поступление'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_giris
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT
                'поступление возврат'::text AS type_doc,
                'поступление'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_giris_iade
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN (
                    SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'продажа'::text AS type_doc,
                'продажа'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_sale
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'продажа возврат'::text AS type_doc,
                'продажа'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
             FROM v_one_sale_return
             WHERE v_one_sale_return.doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'перемещение отправитель'::text AS type_doc,
                'перемещение'::text AS type_,
                count(1) AS count,
                - sum(ed) AS ed,
                - sum(tobox) AS tobox
               FROM v_one_movement
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
            UNION ALL
             SELECT 
                'перемещение получатель'::text AS type_doc,
                'перемещение'::text AS type_,
                count(1) AS count,
                sum(ed) AS ed,
                sum(tobox) AS tobox
               FROM v_one_movement
             WHERE doc_date <= CURRENT_DATE 
                AND (nomenclature_key::text IN 
                    ( SELECT nom.ref_key
                       FROM t_one_cat_nomenclature nom
                      WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
        ) t
      ORDER BY t.type_doc;

    COMMENT ON VIEW v_one_quantity_rows_in_document IS 'количество строк по типам документов до текущей даты';
