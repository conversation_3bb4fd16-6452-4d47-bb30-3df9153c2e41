import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_calculation_fss_maternity_benefits"
DOCUMENT = "Document_ЗаявлениеРасчетВФСС_Декретные"
SELECT_COLUMNS = ("Ref_Key,LineNumber,Документ_Key,Сотрудник_Key,Сумма,Дни")

SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        line_number varchar(4) NOT NULL DEFAULT 0, -- LineNumber
        amount numeric(15,4) NOT NULL DEFAULT 0, -- Сумма
        days int NOT NULL DEFAULT 0, -- Дни
        id uuid NOT NULL DEFAULT uuid_generate_v4(), -- Идентификатор
        document_key varchar(50) NULL, -- Document_Key
        employee_key varchar(50) NULL, -- Сотрудник_Key
        ref_key varchar(50) NULL, -- Ref_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_ref_key_unq UNIQUE (ref_key, line_number)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.days IS 'Дни';
    COMMENT ON COLUMN {TABLE_NAME}.id IS 'Идентификатор';
    COMMENT ON COLUMN {TABLE_NAME}.document_key IS 'Document_Key';
    COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'Сотрудник_Key';    
"""

SQL_UPDATE_RECEIPT_DETAILS = f"""
    UPDATE t_one_doc_cash_order_receipt_details AS det
    SET amount_of_payment = amount_sick
    FROM 
        (SELECT 
            ref_key,
            sum(amount) AS amount_sick
        FROM t_one_doc_calculation_fss_maternity_benefits
        GROUP BY 
            ref_key
        ) AS sick
    WHERE sick.ref_key = det.calculation_key;
"""

async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME} (ref_key, line_number, document_key, employee_key, amount, days)
    VALUES {maket} 
    """
    return sql.replace("'", "")


async def main_doc_calculation_VFSS_maternity_benefits_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(6)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    result = await async_save_pg(SQL_UPDATE_RECEIPT_DETAILS)
    logger.info(f"{result}, SQL_UPDATE_RECEIPT_DETAILS")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_calculation_VFSS_maternity_benefits_async())
