CREATE OR REPLACE VIEW v_service_tc_gtd
AS SELECT
    v_gtd.gtd_date,
    v_gtd.tarih,
    v_gtd.gtd_number,
    COALESCE(v_gtd.duty_usd, 0::numeric) AS duty_usd,
    COALESCE(v_gtd.vat_usd, 0::numeric) AS vat_usd,
    COALESCE(v_gtd.duty_usd, 0::numeric) + COALESCE(v_gtd.vat_usd, 0::numeric) AS gtd_sum_ue,
    (SELECT description
        FROM t_one_cat_currencies
        WHERE ref_key = serv.currency_key
    ) as cur_type,
    serv.batch_document,
    serv.currency_key
   FROM v_service_tc serv
     LEFT JOIN v_gtd ON v_gtd.batch_document::text = serv.batch_document::text;
