import asyncio
import os
import sys
import time
from pathlib import Path

path = Path(__file__).parent.parent
sys.path.append(os.path.join(path.parent, 'Config'))
sys.path.append(path)
from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_sale_of_goods_services_goods"
DOCUMENT = "Document_РеализацияТоваровУслуг_Товары"

SELECT_COLUMNS = ("LineNumber,Количество,Коэффициент,ПроцентСкидкиНаценки,<PERSON>е<PERSON>,Сумма,СуммаНДС,Ref_Key,"
                  "ЕдиницаИзмерения_Key,ЕдиницаИзмеренияМест_Key,ЗаказПокупателя_Key,Качество_Key,"
                  "НалоговоеНазначение_Key,НалоговоеНазначениеДоходовИЗатрат_Key,Номенклатура_Key,"
                  "ПереданныеСчетУчетаБУ_Key,СерияНоменклатуры_Key,Склад_Key,СхемаРеализации_Key")

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id serial4 NOT NULL,
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        coefficient numeric(10,4) NOT NULL DEFAULT 0,  -- Коэффициент
        quantity numeric(10,4) NOT NULL DEFAULT 0,  -- Количество
        price numeric(10, 3) NOT NULL DEFAULT 0,  -- Цена
        amount numeric(15, 4) NOT NULL DEFAULT 0,  -- Сумма
        entry_price numeric(15, 4) NOT NULL DEFAULT 0,  -- Входящая цена            
        costs_product numeric(15, 4) NOT NULL DEFAULT 0,  -- Себестоимость
        amount_costs_product numeric(15, 4) NOT NULL DEFAULT 0,  -- СуммаСебестоимости
        markup_discount_percentage numeric(10, 4) NOT NULL DEFAULT 0,  -- ПроцентСкидкиНаценки
        amount_vat numeric(10, 2) NOT NULL DEFAULT 0,  -- СуммаНДС
        ref_key varchar(50) NULL,  -- Ref_Key
        unit_of_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
        location_unit_key varchar(50) NULL,  -- ЕдиницаИзмеренияМест_Key
        buyer_order_key varchar(50) NULL,  -- ЗаказПокупателя_Key
        quality_key varchar(50) NULL,  -- Качество_Key
        tax_destination_key varchar(50) NULL,  -- НалоговоеНазначение_Key
        tax_destination_of_income_and_expenses_key varchar(50) NULL,  -- НалоговоеНазначениеДоходовИЗатрат_Key
        nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
        transferred_account_account_used_key varchar(50) NULL,  -- ПереданныеСчетУчетаБУ_Key
        nomenclature_series_key varchar(50) NULL,  -- СерияНоменклатуры_Key
        warehouse_key varchar(50) NULL,  -- Склад_Key
        implementation_schema_key varchar(50) NULL,  -- СхемаРеализации_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );

    COMMENT ON TABLE {TABLE_NAME} IS 'Document_РеализацияТоваровУслуг_Товары';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.markup_discount_percentage IS 'ПроцентСкидкиНаценки';
    COMMENT ON COLUMN {TABLE_NAME}.entry_price IS 'Входящая цена';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.costs_product IS 'Себестоимость';
    COMMENT ON COLUMN {TABLE_NAME}.amount_costs_product IS 'СуммаСебестоимости';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.location_unit_key IS 'ЕдиницаИзмеренияМест_Key';
    COMMENT ON COLUMN {TABLE_NAME}.buyer_order_key IS 'ЗаказПокупателя_Key';
    COMMENT ON COLUMN {TABLE_NAME}.quality_key IS 'Качество_Key';
    COMMENT ON COLUMN {TABLE_NAME}.tax_destination_key IS 'НалоговоеНазначение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.tax_destination_of_income_and_expenses_key IS 'НалоговоеНазначениеДоходовИЗатрат_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.transferred_account_account_used_key IS 'ПереданныеСчетУчетаБУ_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.implementation_schema_key IS 'СхемаРеализации_Key';
    
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        line_number,
        quantity,
        coefficient,
        markup_discount_percentage,
        price,
        amount,
        amount_vat,
        ref_key,
        unit_of_key,
        location_unit_key,
        buyer_order_key,
        quality_key,
        tax_destination_key,
        tax_destination_of_income_and_expenses_key,
        nomenclature_key,
        transferred_account_account_used_key,
        nomenclature_series_key,
        warehouse_key,
        implementation_schema_key
      ) VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        quantity = EXCLUDED.quantity,
        coefficient = EXCLUDED.coefficient,
        markup_discount_percentage = EXCLUDED.markup_discount_percentage,
        price = EXCLUDED.price,
        amount = EXCLUDED.amount,
        amount_vat = EXCLUDED.amount_vat,
        ref_key = EXCLUDED.ref_key,
        unit_of_key = EXCLUDED.unit_of_key,
        location_unit_key = EXCLUDED.location_unit_key,
        buyer_order_key = EXCLUDED.buyer_order_key,
        quality_key = EXCLUDED.quality_key,
        tax_destination_key = EXCLUDED.tax_destination_key,
        tax_destination_of_income_and_expenses_key = EXCLUDED.tax_destination_of_income_and_expenses_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        transferred_account_account_used_key = EXCLUDED.transferred_account_account_used_key,
        nomenclature_series_key = EXCLUDED.nomenclature_series_key,
        warehouse_key = EXCLUDED.warehouse_key,
        implementation_schema_key = EXCLUDED.implementation_schema_key
    ;
    """
    return sql.replace("'", "")

SQL_UPDATE_NOMENCLATURE_KEY = f"""
    UPDATE {TABLE_NAME} t
    SET nomenclature_key = '17524c71-44f5-11eb-80fd-001dd8b72b55'  -- Mesh Чай з фенхелю 32 г (2 г Х 16 шт) Х 12 (790734 / 100515)
    WHERE t.nomenclature_key = '95adbe16-8895-11eb-8100-001dd8b72b55'  -- Mesh Чай з фенхелю 32 г (2 г Х 16 шт) Х 12 (100515 наш экспорт)
"""

async def main_doc_sale_of_goods_services_goods_async():
    time.sleep(3)
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(19)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    # обновление ключа номенклатуры у Mesh Чай з фенхелю 32 г (2 г Х 16 шт) Х 12
    await async_save_pg(SQL_UPDATE_NOMENCLATURE_KEY)

    await async_sql_create_index(TABLE_NAME, "ref_key")
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    # main_doc_sale_of_goods_services_goods()
    asyncio.run(main_doc_sale_of_goods_services_goods_async())
