-- продажа клиенту Виртуальный клиент (списание), не на основании возврата
-- НЕ виртуальное списание товаров
CREATE OR REPLACE VIEW v_one_writeoff AS
SELECT
	ord.doc_date,
	ord.doc_number,
	sale.document_amount,
	ord.a_comment
FROM t_one_doc_sale_of_goods_services sale
	INNER JOIN t_one_doc_buyer_order ord
		ON sale.deal = ord.ref_key 
WHERE sale.posted
	AND coalesce(return_owner_key,'********-0000-0000-0000-************') = '********-0000-0000-0000-************'
	AND sale.doc_date::date >= '01.01.2024'::date
	AND sale.account_key = 'adacf222-8cdf-11ed-815c-001dd8b740bc'
ORDER BY 
	ord.doc_date DESC,
	ord.doc_number
;

COMMENT ON VIEW v_one_writeoff IS 'Прочее Списание товаров';
COMMENT ON COLUMN v_one_writeoff.doc_date IS 'Дата Заказа';
COMMENT ON COLUMN v_one_writeoff.doc_number IS 'Номер Заказа';
COMMENT ON COLUMN v_one_writeoff.document_amount IS 'Сумма';
COMMENT ON COLUMN v_one_writeoff.a_comment IS 'Комментарий';