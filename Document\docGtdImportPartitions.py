# reading data from Document_ГТДИмпорт_Разделы and add to pg in table {TABLE_NAME}
# *********** импортируем данные для подключения к базам
import asyncio
import os

from async_Postgres import (
    async_truncate_table,
    async_save_pg,
    create_model_async
)
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_gtd_import_partitions"
DOCUMENT = "Document_ГТДИмпорт_Разделы"
SELECT_COLUMNS = (
    "Ref_Key,LineNumber,ТаможеннаяСтоимость,СтавкаПошлины,СтавкаНДС,СтавкаАкциза,"
    "СуммаПошлины,СуммаНДС,СуммаАкциза"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        customs_value numeric(10, 2) NOT NULL DEFAULT 0,  -- ТаможеннаяСтоимость
        duty_rate numeric NOT NULL DEFAULT 0,  -- СтавкаПошлины
        vat_rate varchar(50) NOT NULL,  -- СтавкаНДС
        excise_rate numeric NOT NULL DEFAULT 0,  -- СтавкаАкциза
        duty_amount numeric(10, 2) NOT NULL DEFAULT 0,  -- СуммаПошлины
        amount_vat numeric(10, 2) NOT NULL DEFAULT 0,  -- СуммаНДС
        excise_amount numeric(10, 2) NOT NULL DEFAULT 0,  -- СуммаАкциза
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)        
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.customs_value IS 'ТаможеннаяСтоимость';
    COMMENT ON COLUMN {TABLE_NAME}.duty_rate IS 'СтавкаПошлины';
    COMMENT ON COLUMN {TABLE_NAME}.vat_rate IS 'СтавкаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.excise_rate IS 'СтавкаАкциза';
    COMMENT ON COLUMN {TABLE_NAME}.duty_amount IS 'СуммаПошлины';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.excise_amount IS 'СуммаАкциза';
    
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        line_number,
        customs_value,
        duty_rate,
        vat_rate,
        excise_rate,
        duty_amount,
        amount_vat,
        excise_amount    
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        customs_value= EXCLUDED.customs_value,
        duty_rate= EXCLUDED.duty_rate,
        vat_rate= EXCLUDED.vat_rate,
        excise_rate= EXCLUDED.excise_rate,
        duty_amount= EXCLUDED.duty_amount,
        amount_vat= EXCLUDED.amount_vat,
        excise_amount= EXCLUDED.excise_amount
    ;
    """
    return sql.replace("'", "")


async def main_gtd_import_partitions_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_gtd_import_partitions_async())
