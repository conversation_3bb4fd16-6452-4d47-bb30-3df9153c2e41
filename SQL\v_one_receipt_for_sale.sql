-- для сверки поступлений и продаж по одному товару
-- чтобы сходилось количество * коэффициент от поставщика на АльфаБест и от АльфаБест на ПрестижПродукт

CREATE OR REPLACE VIEW v_one_receipt_for_sale AS
	SELECT
		supplier,
		concat(EXTRACT(YEAR FROM doc_date),'.',EXTRACT(MONTH FROM doc_date)) AS period,
		sku,
		sum(receipt) original,
		sum(sale) sell,
		sum(receipt) + 	sum(sale) AS diff
	FROM (
		SELECT
			supplier,
			doc_date::Date doc_date,
			doc_number,
			sku,
			sum(tobox) receipt,
			0 sale
		FROM t_one_stock tos
			LEFT JOIN v_one_manager_counterparty_contracts_segments contr
				ON contr.contract_key = tos.contract_key
		WHERE is_original_receipt
			AND organization_key = 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
			AND doc_type = 'поступление'
		GROUP BY
			supplier,
			doc_date::date,
			doc_number,
			sku
		UNION ALL
		SELECT
			supplier,
			doc_date::Date doc_date,
			doc_number,
			sku,
			0 receipt,
			-sum(tobox) sale
		FROM t_one_stock tos
			LEFT JOIN v_one_manager_counterparty_contracts_segments contr
				ON contr.contract_key = tos.contract_key
		WHERE
			organization_key = '56d4e33a-1f59-11e7-80cc-001dd8b79079' -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
			AND doc_type = 'поступление'
			AND contr.customer = 'ТОВ "АЛЬФА БЕСТ"'
		GROUP BY
			supplier,
			doc_date,
			doc_number,
			sku
		) AS t
	WHERE doc_date::date >= '01.01.2022'::date
	GROUP BY
		supplier,
		concat(EXTRACT(YEAR FROM doc_date),'.',EXTRACT(MONTH FROM doc_date)),
		sku
	HAVING (sum(receipt) + 	sum(sale)) <> 0
	ORDER BY
		concat(EXTRACT(YEAR FROM doc_date),'.',EXTRACT(MONTH FROM doc_date)) DESC,
		supplier,
		sku
	;

GRANT SELECT ON TABLE v_one_receipt_for_sale TO user_prestige;
