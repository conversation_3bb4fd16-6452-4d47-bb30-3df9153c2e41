# Возвратные накл. Контроль введенных в коммент даты и номер ВН клиента должен совпадать с полями дата и номер ВН клиента
# выводит список умышленно задвоенных накл
import asyncio
import os
import sys

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))

from async_Postgres import async_save_pg

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)


SQL_CREATE_VIEW_CONTROL_DESCRIPTION = '''
    -- DROP VIEW IF EXISTS v_one_return_control_description CASCADE;
    CREATE OR REPLACE VIEW v_one_return_control_description AS
    SELECT doc_date ВН_дата, doc_number ВН_номер, description комментарий, 
        customer_number "номер у клиента", number_from_description "номер в коммент", 
        customer_date "ВН_датаКлиента", date_from_description "дата указ в коммент",
    concat(
        CASE 
            WHEN (customer_date = date_from_description) THEN ''
            ELSE 'дата; '
        END,
        CASE 
            WHEN (customer_number = number_from_description) THEN '' 
            ELSE 'номер'
        END) нестыковка,
        date_create,
        user_name AS пользователь
    FROM (
        SELECT 
            rtrn.description,
            trim(customer_number) customer_number,
            (SELECT (regexp_matches((SELECT regexp_replace(rtrn.description, 
                '\d{1,2}(\/|-|\.|,)\d{1,2}(\/|-|\.|,)\d{2,4}|[Оо]т|[Bвв]ід', '', 'g')), 
                    '([а-яА-ЯёЁa-zA-ZА-Яа-яёЁЇїІіЄєҐґ0-9\-_/]+)'))[1]) number_from_description,
            customer_date,
            (SELECT (regexp_matches(( SELECT regexp_replace(rtrn.description, '\s', '', 'gi')), 
                '((3[01]|[12][0-9]|0?[1-9])(\/|-|\.)(1[0-2]|0?[1-9])(\/|-|\.)2([0-9]{2})?[0-9])'))[1])::date 
            AS date_from_description,
            doc_date, doc_number,date_create,
            users.description AS user_name,
            users.user_key
        FROM t_one_doc_return_of_goods_from_customers AS rtrn
        	LEFT JOIN t_one_cat_users AS users
        		ON users.individual_key = rtrn.user_key
        WHERE posted
            AND COALESCE(rtrn.description,'') <> ''
            AND (rtrn.description NOT IN ('просрочка','переоцінка','ПЕРЕОЦІНКА') OR COALESCE(trim(customer_number),'') = '' 
                OR COALESCE(customer_date,'0001-01-01')::date = '0001-01-01'::date)
            AND responsible_key = '091d06f1-e92d-11eb-810e-001dd8b72b55' -- Лужанская Света
            AND doc_date::date >= '01.01.2023'::date
            -- AND responsible_key = '52d9d1db-5cb9-11e7-80d1-001dd8b79079' -- Бебех Сергей 
            AND ((SELECT (regexp_matches((SELECT regexp_replace(rtrn.description, 
                '\d{1,2}(\/|-|\.|,)\d{1,2}(\/|-|\.|,)\d{2,4}|[Оо]т|[Bвв]ід', '', 'g')), 
                    '([а-яА-ЯёЁa-zA-ZА-Яа-яёЁЇїІіЄєҐґ0-9\-_/]+)'))[1]) <> trim(customer_number)
                OR  (SELECT (regexp_matches((SELECT regexp_replace(rtrn.description, 
                    '\s', '', 'gi')), 
                    '((3[01]|[12][0-9]|0?[1-9])(\/|-|\.)(1[0-2]|0?[1-9])(\/|-|\.)2([0-9]{2})?[0-9])'))[1])::date <> customer_date::date
            )
    ) AS t
    ORDER BY doc_date DESC;
        
    COMMENT ON VIEW v_one_return_control_description IS 'Возвратные накл. 
        Контроль введенных в коммент даты и номер ВН клиента должен совпадать с полями дата и номер ВН клиента';
    GRANT SELECT ON TABLE v_one_return_control_description TO user_prestige;

'''

SQL_CREATE_VIEW_CONTROL_RETURN_INVOICE_DOUBLE = '''
    -- DROP VIEW IF EXISTS v_one_return_control_double CASCADE;
    
    CREATE OR REPLACE VIEW v_one_return_control_double AS
    SELECT 
        data_all.doc_date AS ВН_дата, 
        data_all.doc_number AS ВН_номер,
        data_all.customer_date ВН_датаКлиента, 
        data_all.customer_number ВН_номерКлиента,
        sell.doc_date AS РН_дата,
        sell.doc_number	AS РН_номер,
        date_create датаСозданияДок,
        counterparties.description клиент,
        count(*) OVER (PARTITION BY data_all.customer_date, data_all.customer_number, sell.doc_date 
            ORDER BY data_all.customer_date, data_all.customer_number, sell.doc_date) as количествоПовторов
    FROM (
        SELECT DISTINCT main.*, sub.base_doc_key
        FROM t_one_doc_return_of_goods_from_customers_goods sub
            INNER JOIN t_one_doc_return_of_goods_from_customers AS main
                ON main.ref_key = sub.ref_key 
        ) AS data_all
        INNER JOIN 
        (SELECT customer_date, customer_number, deal_key, base_doc_key, count(*)
        FROM (
            SELECT DISTINCT main.ref_key, main.customer_date, main.customer_number,
                main.deal_key, sub.base_doc_key
            FROM t_one_doc_return_of_goods_from_customers_goods sub
                INNER JOIN t_one_doc_return_of_goods_from_customers AS main
                    ON main.ref_key = sub.ref_key 
            WHERE (main.user_key = '091d06f1-e92d-11eb-810e-001dd8b72b55' -- Света Лужанская
                    OR  main.responsible_key = '091d06f1-e92d-11eb-810e-001dd8b72b55')
                AND main.posted
                AND main.customer_date <> '0001-01-01'::date
            ) AS ta
        GROUP BY customer_date, customer_number, deal_key, base_doc_key
        HAVING count(*) > 1
        ) AS dbl
        ON data_all.customer_date = dbl.customer_date
            AND data_all.customer_number = dbl.customer_number
            AND data_all.deal_key = dbl.deal_key
            AND data_all.base_doc_key = dbl.base_doc_key
        INNER JOIN t_one_doc_sale_of_goods_services AS sell
            ON sell.ref_key = dbl.base_doc_key
        INNER JOIN t_one_cat_counterparties counterparties
            ON counterparties.ref_key = sell.account_key 
    WHERE data_all.doc_date::date >= '01.01.2023'
    ORDER BY data_all.doc_date::date DESC, ВН_номерКлиента, ВН_датаКлиента, dbl.base_doc_key
    ;
        
    COMMENT ON VIEW v_one_return_control_double IS 'список задвоенных накл';
    GRANT SELECT ON TABLE v_one_return_control_double TO user_prestige;
'''

SQL_CREATE_VIEW_CONTROL_RETURN_INVOICE_COUNTERPARTY_NUMBER = '''
    -- DROP VIEW IF EXISTS v_one_return_control_counterparty_number CASCADE;
    
    CREATE OR REPLACE VIEW v_one_return_control_counterparty_number AS
    SELECT DISTINCT t2.*
    FROM (
        SELECT counterparty Клиент, customer_date КлиентДата, count(DISTINCT lenght_number) количествоЗнаковВНомере
        FROM (
            SELECT customer_date, trim(customer_number) AS customer_number, counterparties.description counterparty, 
                LENGTH(trim(customer_number)) AS lenght_number
            FROM t_one_doc_return_of_goods_from_customers AS main
                INNER JOIN t_one_cat_counterparties AS counterparties
                    ON main.account_key = counterparties.ref_key 
            WHERE customer_date >= '01.01.2023'::date
            ) AS t
        GROUP BY Клиент, КлиентДата
        HAVING count(DISTINCT lenght_number) > 1
        ) AS t1
        INNER JOIN (
            SELECT customer_date, trim(customer_number) AS customer_number, counterparties.description counterparty, 
                LENGTH(trim(customer_number)) AS lenght_number
            FROM t_one_doc_return_of_goods_from_customers AS main
                INNER JOIN t_one_cat_counterparties AS counterparties
                    ON main.account_key = counterparties.ref_key 	
        ) AS t2
        ON t1.Клиент = t2.counterparty AND t1.КлиентДата = t2.customer_date
        ORDER BY t2.counterparty, t2.customer_date DESC 
    ;

    GRANT SELECT ON TABLE v_one_return_control_counterparty_number TO user_prestige;
'''


async def main_create_view_return_control_async():
    # result = await async_save_pg(SQL_UPDATE_COUNTERPART_NUMBER)
    # logger.info(f"{result}, SQL_UPDATE_COUNTERPART_NUMBER")
    result = await async_save_pg(SQL_CREATE_VIEW_CONTROL_DESCRIPTION)
    logger.info(f"{result}, SQL_CREATE_VIEW_CONTROL_DESCRIPTION")
    result = await async_save_pg(SQL_CREATE_VIEW_CONTROL_RETURN_INVOICE_DOUBLE)
    logger.info(f"{result}, SQL_CREATE_VIEW_CONTROL_RETURN_INVOICE_DOUBLE")
    result = await async_save_pg(SQL_CREATE_VIEW_CONTROL_RETURN_INVOICE_COUNTERPARTY_NUMBER)
    logger.info(f"{result}, SQL_CREATE_VIEW_CONTROL_RETURN_INVOICE_COUNTERPARTY_NUMBER")


if __name__ == '__main__':
    # from Document.docReturnOfGoodsFromCustomers import main_doc_return_of_goods_from_customers_async
    # from Document.docReturnOfGoodsFromCustomersGoods import main_doc_return_of_goods_from_customers_goods_async
    # from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
    # from Document.docSaleOfGoodsServicesGoods import main_doc_sale_of_goods_services_goods_async
    logger.info(f"START")
    # asyncio.run(main_doc_sale_of_goods_services_async())
    # asyncio.run(main_doc_sale_of_goods_services_goods_async())
    # asyncio.run(main_doc_return_of_goods_from_customers_async())
    # asyncio.run(main_doc_return_of_goods_from_customers_goods_async())
    asyncio.run(main_create_view_return_control_async())
    logger.info(f"FINISH")
