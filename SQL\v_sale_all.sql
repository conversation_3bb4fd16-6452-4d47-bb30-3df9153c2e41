
    CREATE OR REPLACE VIEW v_sale_all
    AS
    SELECT sale.id,
        sale.organization,
        sale.customer AS client,
        sale.tarih,
        sale.doc_number AS evrakno,
        sale.sku,
        units.description AS unit,
        sale.inbox,
        COALESCE(sale.quantity, 0) AS adet,
        ((COALESCE(sale.quantity, 0) * COALESCE(sale.coefficient, 0)) / sale.inbox) AS all_box,
        ( SELECT (sum(t.amount) / sum(t.quantity)) AS avg_price_tr
               FROM ( SELECT goods.quantity,
                          goods.price_tr,
                          goods.quantity * goods.price_tr AS amount
                      FROM t_one_doc_receipt_of_goods_services serv
                          LEFT JOIN t_one_doc_receipt_of_goods_services_goods goods 
                            ON serv.ref_key = goods.ref_key
                      WHERE goods.nomenclature_key = sale.nomenclature_key 
                        AND COALESCE(goods.price_tr, 0) <> 0
                        AND serv.doc_date::date <= sale.doc_date::date 
                        AND serv.doc_date::date >= (sale.doc_date::date - '1 year'::interval)
                      ) t
        ) AS giris_ft,
        (sale.tobox * 
                ( SELECT (sum(t.amount) / sum(t.quantity)) AS avg_price_tr
               FROM ( SELECT goods.quantity,
                        goods.price_tr,
                        goods.quantity * goods.price_tr AS amount
                       FROM t_one_doc_receipt_of_goods_services serv
                         LEFT JOIN t_one_doc_receipt_of_goods_services_goods goods 
                            ON serv.ref_key = goods.ref_key
                      WHERE goods.nomenclature_key = sale.nomenclature_key 
                        AND COALESCE(goods.price_tr, 0) <> 0
                        AND serv.doc_date::date <= sale.doc_date::date 
                        AND serv.doc_date::date >= (sale.doc_date::date - '1 year'::interval)
                      ORDER BY serv.doc_date) t))
        AS giris_tutar,
        CASE
            WHEN sale.amount_includes_vat = FALSE AND sale.consider_vat = TRUE THEN 
                ((COALESCE(sale.amount_vat, 0) + COALESCE(sale.amount, 0)) / 
                    COALESCE(sale.quantity, 0))
            ELSE (COALESCE(sale.amount_vat, 0) / COALESCE(sale.quantity, 0))
        END AS price1c_kdv,
        CASE
            WHEN sale.amount_includes_vat = FALSE AND sale.consider_vat = TRUE THEN 
                COALESCE(sale.amount, 0) + 	COALESCE(sale.amount_vat, 0)
            ELSE COALESCE(sale.amount, 0)
        END AS total_kdv,
        (( SELECT sum(v_service_tc_maliyet.maliyet_tutar) / sum(v_service_tc_maliyet.quantity)
          FROM v_service_tc_maliyet
          WHERE v_service_tc_maliyet.nomenclature_key = sale.nomenclature_key 
            AND v_service_tc_maliyet.serv_date <= sale.doc_date::date 
            AND v_service_tc_maliyet.serv_date >= (sale.doc_date::date - '1 year'::interval))) 
        AS maliyet,
        (sale.tobox * (
            ( SELECT sum(v_service_tc_maliyet.maliyet_tutar) / sum(v_service_tc_maliyet.quantity)
              FROM v_service_tc_maliyet
              WHERE v_service_tc_maliyet.nomenclature_key = sale.nomenclature_key 
                AND v_service_tc_maliyet.serv_date <= sale.doc_date::date 
                AND v_service_tc_maliyet.serv_date >= (sale.doc_date::date - '1 year'::interval))))::numeric(15,4) 
        AS maliyet_tutar,
        COALESCE(sale.coefficient::integer, 0) AS coef_doc,
        COALESCE(units.coefficient, 0) AS coef_cart,
        COALESCE(units.coefficient, 0) - COALESCE(sale.coefficient::integer, 0)AS diff,
        cur.description AS doviz,
        sale.doc_type,
        ( SELECT t_rate_nbu.rate_usd_nbu
          FROM t_rate_nbu
          WHERE t_rate_nbu.rate_date = sale.doc_date::date
        ) AS kur_nbu,
        sale.ref_key,
        sale.customer_key,
        sale.organization_key,
        sale.nomenclature_key,
        sale.unit_of_key,
        sale.currency_key
       FROM v_one_sale_and_salereturn AS sale
         JOIN t_one_cat_nomenclature nom ON nom.ref_key = sale.nomenclature_key
         LEFT JOIN t_one_cat_currencies cur ON cur.ref_key = sale.currency_key
         LEFT JOIN (SELECT 
                        ref_key,
                        nomenclature_key,
                        description,
                        coefficient,
                        max(coefficient) OVER(PARTITION BY nomenclature_key) AS in_box
                    FROM t_one_cat_units
                    WHERE NOT deletion_mark
                    ) AS units
                    ON nom.ref_key = units.nomenclature_key
                        AND units.ref_key = sale.unit_of_key 
      ORDER BY sale.doc_date DESC;    
    