# выполняет SQL-скрипты в определенном порядке согласно ключам словаря SQL_SORT

import asyncio
import os
from collections import defaultdict
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
CUR_DIR = os.path.dirname(os.path.abspath(__file__))
FOLDER_PATH = os.path.join(CUR_DIR, "SQL")
SQL_SORT = { 
    # 'v_one_logistic_kiev_in_order_carrier_isempty.sql': 1,
    # 'v_one_logistic_kiev_in_order_datesend_isempty.sql': 1,
    # 'v_one_logistics_not_find.sql': 1,
    "fn_t_cash_register_bfr.sql": 1,
    "fn_t_pb_after.sql": 1,
    "fn_add_to_coworker.sql": 1,
    "t_cash_doc_types.sql": 1,
    "t_change_log.sql": 1,
    "t_edin_units.sql": 1,
    "t_bonus_type.sql": 1,
    "v_court_erpn.sql": 1,
    "v_gtd.sql": 1,
    "v_log_cash.sql": 1,
#    "v_one_cash.sql": 1,
    "v_one_cash_order_expense.sql": 1,
    "v_one_cash_order_receipt.sql": 1,
    "v_one_cash_warrant_expense.sql": 1,
    "v_one_cash_warrant_receipt.sql": 1,
    "v_one_customer_manager.sql": 1,
    "v_one_debt_sale_salereturn.sql": 1,
    "v_one_doc_tax_gtd_sale.sql": 1,
    "v_one_doc_tax_gtd_sale_return.sql": 1,
    "v_one_infreg_contact_information_phone.sql": 1,
    "v_one_invoice_and_payment_employee.sql": 1,
    "v_one_manager_counterparty_contracts_segments.sql": 1,
    "v_one_counterparty_contracts_parameters.sql": 1,
    "v_one_managers_and_counterparts.sql": 1,
    "v_one_nomenclature_series_double.sql": 1,
    "v_one_offsetting.sql": 1,
    "v_one_order_with_multi_payments.sql": 1,
    "v_one_organization_and_type.sql": 1,
    "v_one_price.sql": 1,
    "v_one_return_control_counterparty_number.sql": 1,
    "v_one_return_control_description.sql": 1,
    "v_one_return_control_double.sql": 1,
    "v_one_salary_paid_bank.sql": 1,
    "v_one_salary_paid_cash.sql": 1,
    "v_one_sale_analize.sql": 1,
    "v_one_stock_maliyet.sql": 1,
    "v_one_stock_maliyet_rapor.sql": 1,
    "v_one_tax_and_sale_amount.sql": 1,
    "v_one_tax_sale.sql": 1,
    "v_one_vacations.sql": 1,
    "v_one_writeoff.sql": 1,
    "v_service_tc.sql": 1,
    "v_stickers.sql": 1,
    "fn_remove_duplicates.sql": 1,
    "fn_stock_days.sql": 2,
    "fn_stock_days_except.sql": 2,
    "fn_v_one_accounts_receivable.sql": 2,
    "t_cash_bank.sql": 2,
    "t_cash_register.sql": 2,
    "t_customer_bonus.sql": 2,
    "v_cash_register_rapor.sql": 2,
    "v_doc_cash_bank_all.sql": 2,
    "v_doc_cash_bank_all_from_table.sql": 2,
    "v_one_court_erpn.sql": 2,
    "v_one_tax_medoc_cabinet.sql": 2,
    "v_one_cash_order_receipt_expense.sql": 2,
    "v_one_cash_warrant_receipt_expense.sql": 2,
    # "v_one_clients_contact.sql": 2,
    "v_one_debt_bank.sql": 2,
    "v_one_debt_correction.sql": 2,
    "v_one_debt_checkout.sql": 2,
    "v_one_debt_receipt_receipt_return.sql": 2,
    "v_one_edin.sql": 2,
    "v_one_receipt_for_sale.sql": 2,
    "v_one_return_not_invoice.sql": 2,
    "v_one_salary.sql": 2,
    "v_one_stock_maliyet_rapor_warehouse.sql": 2,
    "v_one_tax_and_medoc_amount_different.sql": 2,
    "v_one_tax_and_medoc_diff.sql": 2,
    "v_one_tax_and_sale_amount_different.sql": 2,
    "v_one_tax_return_and_medoc_amount_different.sql": 2,
    "v_one_tax_return_and_sale_amount_different.sql": 2,
    "v_one_tax_sale_control_uktved.sql": 2,
    "v_service_tc_dop.sql": 2,
    "v_service_tc_gtd.sql": 2,
    "v_one_virtual_correction_debt.sql": 2,
    "fn_salary_bonus_manager.sql": 3,
    "v_one_balance.sql": 3,
    "mt_one_balance.sql": 3,
    "v_one_balance_erpn.sql": 3,
    "v_one_balance_details.sql": 3,
    "mt_one_balance_details.sql": 3,
    "v_one_debt.sql": 3,
    # "v_one_debt2.sql": 3,
    "v_one_customers_sales_payment.sql": 3,
    "v_service_tc_group.sql": 3,
    "v_one_customer_balance.sql": 4,
    "v_one_customer_balance_no_date.sql": 4,
    "v_one_balance_customer.sql": 4,
    "v_one_balance_customer_by_1c.sql": 4,
    "v_one_customer_bonus.sql": 4,
    "v_one_manager_bonus.sql": 4,
    "v_service_expense.sql": 4,
    "v_service_tc_goods.sql": 4,
    "fn_stock_series.sql": 5,
    "v_service_tc_dop_second.sql": 5,
    "v_service_tc_maliyet.sql": 5,
    "v_control_coef.sql": 6,
    "v_one_nomenclature_inbox_supplier.sql": 6,
    "v_one_completion.sql": 7,
    "v_one_correction.sql": 7,
    "v_one_correction_series.sql": 7,
    "v_one_giris.sql": 7,
    "v_one_giris_iade.sql": 7,
    "v_one_movement.sql": 7,
    "v_one_oprihodivanie.sql": 7,
    "v_one_sale.sql": 7,
    "v_one_sale_return.sql": 7,
    "v_one_spisanie.sql": 7,
    "v_one_giris_and_girisiade.sql": 8,
    "v_one_trebovanie.sql": 8,
    "v_one_sale_and_salereturn.sql": 8,
    "v_one_customers_profit.sql": 9,
    "v_one_customers_profit2.sql": 9,
    "v_one_quantity_rows_in_document.sql": 9,
    "v_one_stock.sql": 10,
}


async def run_sql_file(file_path):
    logger.info(f"Running: {os.path.abspath(file_path)}")
    # Откройте файл и прочитайте его содержимое
    with open(file_path, 'r', encoding='utf-8') as file:
        sql_query = file.read()

    # Выполните SQL-запрос
    result = await async_save_pg(sql_query)
    logger.info(f"{result}: {os.path.abspath(file_path)}")


async def create_views():
    # Сгруппируйте SQL-скрипты по порядку выполнения
    grouped_files = defaultdict(list)
    for file, order in SQL_SORT.items():
        grouped_files[order].append(file)

    # Для каждой группы скриптов, отсортированных по порядку выполнения
    for order in sorted(grouped_files.keys()):
        tasks = []
        for file_path in grouped_files[order]:
            # Создайте задачу для каждого SQL-скрипта
            task = asyncio.create_task(run_sql_file(os.path.join(FOLDER_PATH, file_path)))
            tasks.append(task)

        # # Запустите все задачи в группе и дождитесь их завершения
        # await asyncio.gather(*tasks)

        # # Запускаем задачи по 3 и ждем их завершения
        for i in range(0, len(tasks), 3):
            await asyncio.gather(*tasks[i: i + 3])


if __name__ == '__main__':
    logger.info("Start create views")
    asyncio.run(create_views())
    logger.info("End create views")
