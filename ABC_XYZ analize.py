# Description: Пример создания сводной таблицы в SQLite
# вычисляет ABC и XYZ -анализ по выручке, прибыли и количеству
import psycopg2
from datetime import datetime, date
from dateutil.parser import parse
from typing import List, Tuple
import pandas as pd
import numpy as np
from async_Postgres import CONN_PARAMS


def coefficient_of_variation(data):
    try:
        x_mean = np.mean(data)  # Среднее значение x̄
        n = len(data)  # Количество элементов n
        variance = sum((x - x_mean) ** 2 for x in data) / n  # Дисперсия
        std_dev = np.sqrt(variance)  # Стандартное отклонение
        v = (std_dev / x_mean) if x_mean != 0 else 0 # Коэффициент вариации в процентах
        return round(v,2)
    except Exception as e:
        return 0


def get_unique_months(cursor, date_start:date, date_finish:date) -> List[str]:
    sql = """
        SELECT DISTINCT 
            TO_CHAR(doc_date, 'YYYY.mm') as month
        FROM t_one_sale
        WHERE doc_date::date >= %s::date
            AND doc_date::date <= %s::date
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        ORDER BY month
    """
    cursor.execute(sql, (date_start, date_finish))
    months = [row[0] for row in cursor.fetchall()]
    return months


def create_pivot_query(months: List[str], date_start, date_finish) -> str:
    month_columns = []
    for month in months:
        case_stmt = f"""SUM(CASE 
            WHEN TO_CHAR(doc_date, 'YYYY.mm') = '{month}' THEN 
                COALESCE(amount,0) - COALESCE(amount_maliyet_uah,0)
            ELSE 
                0 
        END) as "{month}"
"""
        month_columns.append(case_stmt)

    # Join all month columns with commas
    month_columns_str = ",\n        ".join(month_columns)

    # Create the full pivot query
    query = f"""
        SELECT
            sku,
            sum(COALESCE(amount,0)) - sum(COALESCE(amount_maliyet_uah,0)) as прибыль,  -- прибыль
            {month_columns_str}
        FROM t_one_sale
        WHERE doc_date::date >= '{date_start}'::date
            AND doc_date::date <= '{date_finish}'::date
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55' -- ТОВ "АЛЬФА БЕСТ"
        GROUP BY sku
        ORDER BY sku
    """
    return query


def format_row(row: Tuple) -> str:
    return " | ".join(f"{str(val):12}" for val in row)


def main(date_start, date_finish):
    conn = None
    try:
        conn = psycopg2.connect(**CONN_PARAMS)
        cursor = conn.cursor()

        # Get unique months from the data
        months = get_unique_months(cursor, date_start, date_finish)

        if not months:
            print("No data found in the t_one_sale table")
            return

        # Create and execute the pivot query
        pivot_query = create_pivot_query(months, date_start, date_finish)
        df = pd.read_sql_query(pivot_query, conn)

        # отсортируем колонку прибыль по убыванию. сортировка нужная для анализа ABC
        df = df.sort_values(by='прибыль', ascending=False)
        df['итого'] = df['прибыль'].sum()
        df['доля'] = df['прибыль'] / df['итого']
        df['кумДоля'] = df['доля'].cumsum()
        df['ABC'] = df['кумДоля'].apply(lambda x: 'A' if x <= 0.75 else ('B' if x <= 0.9 else 'C'))
        # А - это 75% от общей прибыли, т.е. 25% товаров приносят 75% прибыли, т.е самые прибыльные
        # В - следующие 15%,
        # С - остальные 10%
        # анализ XYZ
        df['mean'] = df[months].mean(axis=1)
        df['std'] = df[months].std(axis=1)
        df['cv'] = np.where(df['mean'] != 0, df['std'] / df['mean'], np.nan)
        df['XYZ'] = df['cv'].apply(lambda x: 'X' if x <= 0.5 else ('Y' if x <= 1.0 else 'Z'))
        df['коэфВариации %'] = df.apply(lambda row: coefficient_of_variation(row[2:2+len(months)]), axis=1)
        df.to_excel("ABC_XYZ.xlsx", index=False)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        if conn:
            conn.close()


if __name__ == "__main__":
    date_start = parse('01.01.2024', dayfirst=True).date()
    date_finish = parse('31.12.2024', dayfirst=True).date()
    main(date_start, date_finish)
    print("Ok")
