# отпуска офиц и неофиц
import asyncio
import os

from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

SQL_CREATE_VACATION_OFFICIAL = '''
    -- DROP VIEW IF EXISTS v_one_vacations;
    
    CREATE OR REPLACE VIEW v_one_vacations AS
    SELECT 
    tt.employee AS "ФИО",
    tt.date_first AS "датаОТ",
    tt.date_last AS "датаПо",
    tt.work_days_count AS "рабДней",
    sum(tt.work_days_count) OVER (PARTITION BY tt.employee ORDER BY tt.date_first) AS "рабДнейВсего",
    COALESCE(compensation_days,0) as компенсация,
    sum(COALESCE(tt.work_days_count,0) + COALESCE(compensation_days,0)) 
        OVER (PARTITION BY tt.employee ORDER BY tt.date_first)::integer + 
        (sum(tt.work_days_count) OVER (PARTITION BY tt.employee ORDER BY tt.date_first)::integer / 5) * 2 
         AS "календДнейВсего",
    CASE
        WHEN is_official THEN '+'
        ELSE ''
    END "1С"    
    FROM (
        SELECT 
            employee,
            date_first,
            date_last,
            ( SELECT count(t_1.dates) AS count
              FROM ( SELECT dates.dates::date AS dates
                     FROM generate_series(t.date_first::timestamp with time zone, t.date_last::timestamp with time zone, '1 day'::interval) dates(dates)
                     WHERE NOT (dates.dates::date IN (
                            SELECT t_vacation_exception.date_exception
                            FROM t_vacation_exception
                            WHERE t_vacation_exception.date_exception IS NOT NULL)) 
                                AND EXTRACT(dow FROM dates.dates) >= 1::numeric AND EXTRACT(dow FROM dates.dates) <= 5::numeric
                    UNION ALL
                     SELECT dates.dates::date AS dates
                       FROM generate_series(t.date_first::timestamp with time zone, t.date_last::timestamp with time zone, '1 day'::interval) dates(dates)
                      WHERE dates.dates::date IN ( 
                            SELECT t_vacation_exception.date_add
                            FROM t_vacation_exception
                            WHERE t_vacation_exception.date_add IS NOT NULL)
                        ) t_1
            ) AS work_days_count,
            0 AS compensation_days,
            CASE 
                WHEN sum(is_official) > 0 THEN TRUE 
                ELSE FALSE 
            END is_official    
        FROM (
            SELECT DISTINCT 
                trim(emp.description) AS employee,
                COALESCE(prd.start_doc_date, det.start_doc_date)::date  date_first,    
                COALESCE(prd.expiration_doc_date, det.expiration_doc_date)::date  date_last,
                1 is_official
            FROM t_one_doc_vacation_official AS vac
                LEFT JOIN 
                    (
                    SELECT ref_key, min(start_doc_date) AS start_doc_date, 
                        max(expiration_doc_date) AS expiration_doc_date
                    FROM t_one_doc_vacation_official_details
                    GROUP BY ref_key
                    ) AS det
                    ON vac.ref_key = det.ref_key 
                LEFT JOIN t_one_doc_vacation_official_period AS prd
                    ON vac.ref_key = prd.ref_key
                LEFT JOIN t_one_cat_employees AS emp
                    ON vac.employee_key = emp.ref_key 
            WHERE vac.posted 
                AND doc_type = 'Отпускные'
                AND organization_key <> '3d7e2ad1-8ac8-11e6-80c4-c936aa9c817c' -- ТОВ "ПРЕСТИЖ ПРОДУКТ-К"
                AND vac.ref_key <> '98bdb0ca-4f32-11eb-80fd-001dd8b72b55' -- Бебех компенсация за 2020
            UNION ALL 
            SELECT 
                coworker,
                date_begin,
                date_out,
                0 AS is_official
            FROM t_vacation
            WHERE status
            ) AS t
        GROUP BY employee, date_first, date_last
    UNION ALL 
    SELECT DISTINCT 
        trim(emp.description) AS employee,
        COALESCE(prd.start_doc_date, det.start_doc_date)::date  date_first,    
        COALESCE(prd.expiration_doc_date, det.expiration_doc_date)::date  date_last,
        0 AS work_days_count,
        vac.compensation_days,
        TRUE is_official
    FROM t_one_doc_vacation_official AS vac
        LEFT JOIN 
            (
            SELECT ref_key, min(start_doc_date) AS start_doc_date, max(expiration_doc_date) AS expiration_doc_date
            FROM t_one_doc_vacation_official_details
            GROUP BY ref_key
            ) AS det
            ON vac.ref_key = det.ref_key 
        LEFT JOIN t_one_doc_vacation_official_period AS prd
            ON vac.ref_key = prd.ref_key
        LEFT JOIN t_one_cat_employees AS emp
            ON vac.employee_key = emp.ref_key 
    WHERE vac.posted 
        AND doc_type <> 'Отпускные'
        AND organization_key = '56d4e33a-1f59-11e7-80cc-001dd8b79079'
        AND vac.ref_key <> '98bdb0ca-4f32-11eb-80fd-001dd8b72b55' -- Бебех компенсация за 2020        
        ) AS tt
    ORDER BY ФИО, "датаОТ"
    ;

    GRANT SELECT ON TABLE v_one_vacations TO user_prestige;    
    
'''


async def main_vacation_async():
    result = await async_save_pg(SQL_CREATE_VACATION_OFFICIAL)
    logger.info(f"{result}, SQL_CREATE_VACATION_OFFICIAL")


if __name__ == '__main__':
    asyncio.run(main_vacation_async())
