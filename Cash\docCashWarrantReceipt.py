import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_cash_warrant_receipt"
DOCUMENT = "Document_ПриходныйКассовыйОрдер"
SELECT_COLUMNS = "DataVersion,Date,Number,Posted,ВидОперации,ДокументОснование,Комментарий,Контрагент,НомерОрдера," \
                 "Оплачено,Основание,ОтражатьВБухгалтерскомУчете,ОтражатьВУправленческомУчете,Приложение,СтавкаНДС," \
                 "СуммаДокумента,Ref_Key,ВалютаВзаиморасчетовРаботника_Key,ВалютаДокумента_Key,Касса_Key," \
                 "НазначениеДенежныхСредств_Key,Организация_Key,Ответственный_Key,Подразделение_Key," \
                 "СтатьяДвиженияДенежныхСредств_Key,СчетОрганизации_Key,ДоговорКонтрагента_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        operation_type varchar(250) NOT NULL,  -- ВидОперации
        a_comment varchar(250) NOT NULL,  -- Комментарий
        order_doc_number varchar(15) NOT NULL DEFAULT 0,  -- НомерОрдера
        posted bool NOT NULL DEFAULT false,  -- Posted
        paid bool NOT NULL DEFAULT false,  -- Оплачено
        base varchar NOT NULL,  -- Основание
        is_accounting bool NOT NULL DEFAULT false,  -- ОтражатьВБухгалтерскомУчете
        is_management bool NOT NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        application varchar(250) NOT NULL,  -- Приложение
        vat_rate varchar(250) NOT NULL,  -- СтавкаНДС
        amount numeric(15,2) NOT NULL default 0,  -- СуммаДокумента
        dataversion varchar(15) NOT NULL,  -- DataVersion
        ref_key varchar(250) NOT NULL,  -- Ref_Key
        document_base_key varchar(250) NOT NULL,  -- ДокументОснование
        worker_settlement_currency_key varchar(250) NOT NULL,  -- ВалютаВзаиморасчетовРаботника_Key
        contractor_key varchar(250) NOT NULL,  -- Контрагент
        currency_key varchar(250) NOT NULL,  -- ВалютаДокумента_Key
        checkout_key varchar(250) NOT NULL,  -- Касса_Key
        assignment_key varchar(250) NOT NULL,  -- НазначениеДенежныхСредств_Key
        organization_key varchar(250) NOT NULL,  -- Организация_Key
        responsible_key varchar(250) NOT NULL,  -- Ответственный_Key
        department_key varchar(250) NOT NULL,  -- Подразделение_Key
        cash_flow_item varchar(250) NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        organization_account_key varchar(250) NOT NULL,  -- СчетОрганизации_Key
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.document_base_key IS 'ДокументОснование';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.contractor_key IS 'Контрагент';
    COMMENT ON COLUMN {TABLE_NAME}.order_doc_number IS 'НомерОрдера';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.paid IS 'Оплачено';
    COMMENT ON COLUMN {TABLE_NAME}.base IS 'Основание';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.application IS 'Приложение';
    COMMENT ON COLUMN {TABLE_NAME}.vat_rate IS 'СтавкаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.worker_settlement_currency_key IS 'ВалютаВзаиморасчетовРаботника_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.checkout_key IS 'Касса_Key';
    COMMENT ON COLUMN {TABLE_NAME}.assignment_key IS 'НазначениеДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.responsible_key IS 'Ответственный_Key';
    COMMENT ON COLUMN {TABLE_NAME}.department_key IS 'Подразделение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_account_key IS 'СчетОрганизации_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        dataversion,
        doc_date,
        doc_number,
        posted,
        operation_type,
        document_base_key,
        a_comment,
        contractor_key,
        order_doc_number,
        paid,
        base,
        is_accounting,
        is_management,
        application,
        vat_rate,
        amount,
        ref_key,
        worker_settlement_currency_key,
        currency_key,
        checkout_key,
        assignment_key,
        organization_key,
        responsible_key,
        department_key,
        cash_flow_item,
        organization_account_key,
        contract_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        doc_number = EXCLUDED.doc_number,
        posted = EXCLUDED.posted,
        operation_type = EXCLUDED.operation_type,
        document_base_key = EXCLUDED.document_base_key,
        a_comment = EXCLUDED.a_comment,
        contractor_key = EXCLUDED.contractor_key,
        order_doc_number = EXCLUDED.order_doc_number,
        paid = EXCLUDED.paid,
        base = EXCLUDED.base,
        is_accounting = EXCLUDED.is_accounting,
        is_management = EXCLUDED.is_management,
        application = EXCLUDED.application,
        vat_rate = EXCLUDED.vat_rate,
        amount = EXCLUDED.amount,
        worker_settlement_currency_key = EXCLUDED.worker_settlement_currency_key,
        currency_key = EXCLUDED.currency_key,
        checkout_key = EXCLUDED.checkout_key,
        assignment_key = EXCLUDED.assignment_key,
        organization_key = EXCLUDED.organization_key,
        responsible_key = EXCLUDED.responsible_key,
        department_key = EXCLUDED.department_key,
        cash_flow_item = EXCLUDED.cash_flow_item,
        organization_account_key = EXCLUDED.organization_account_key,
        contract_key = EXCLUDED.contract_key
    '''
    return sql.replace("'", "")


async def main_doc_cash_warrant_receipt_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(27)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    await async_sql_create_index(TABLE_NAME, "contractor_key")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_warrant_receipt_async())
