import logging
import os


def get_logger(*args):
    # Создаем объект logger
    logger = logging.getLogger(*args)

    # Удаляем все предыдущие обработчики
    logger.handlers = []

    # Устанавливаем общий уровень логирования
    logger.setLevel(logging.INFO)

    # Создаем обработчик, который выводит сообщения с уровнем INFO и выше
    handler = logging.StreamHandler()

    # Создаем формат сообщений
    formatter = logging.Formatter('%(message)s, %(name)s, %(asctime)s')

    # Применяем формат к обработчику
    handler.setFormatter(formatter)

    # Добавляем обработчик к logger
    logger.addHandler(handler)

    # Отключаем распространение сообщений к корневому логгеру
    logger.propagate = False

    return logger


if __name__ == '__main__':
    FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
    logger = get_logger(FILE_NAME)
    logger.info("logger")
