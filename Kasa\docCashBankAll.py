import asyncio
import os
import sys
from pathlib import Path

from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Cash.docCashPaymentOrderExpenseDetails import main_doc_cash_order_expense_details_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderReceiptDetails import main_doc_cash_order_receipt_details_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashWarrantExpenseDetails import main_doc_cash_warrant_expense_details_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Cash.docCashWarrantReceiptDetails import main_doc_cash_warrant_receipt_details_async
from Catalog.catCashFlowItem import main_cat_cash_flow_items_async
from CreateAndRunSQLScripts import create_views
from Document.docSalaryPaymentOfWages import main_salary_payment_of_wages_async

cur_dir = Path(__file__).resolve().parent
sys.path.append(str(cur_dir))
sys.path.append(str(cur_dir.parent))
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_doc_cash_bank_all'
VIEW_NAME = 'v_doc_cash_bank_all'
VIEW_NAME2 = 'v_doc_cash_bank_all_from_table'

CREATE_TABLE = f'''
    -- НЕ УДАЛЯТЬ!!!
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL DEFAULT now(),  -- Дата
        doc_number varchar(50) NOT NULL, -- Номер
        amount numeric(15, 2) NOT NULL DEFAULT 0, -- check (amount <> 0), -- Сумма грн
        amount_usd numeric(15, 2) NOT NULL DEFAULT 0, -- Сумма usd
        description varchar(250) NULL, -- Примечание
        document_type varchar(25) NULL, -- Тип документа
        item_period date NULL, -- Период
        recorder_type numeric(3) NOT NULL, -- 1- приход; -1 - расход
        rate_nbu numeric(15, 4) NOT NULL DEFAULT 1, -- Курс НБУ
        line_number numeric(4) NOT NULL DEFAULT 1, -- Номер строки
        id varchar(40) NOT NULL DEFAULT uuid_generate_v4(),
        ref_key varchar(40) NOT NULL, -- Ref_Key
        organization_key varchar(40) NOT NULL, -- Организация
        currency_key varchar(40) NOT NULL, -- Валюта
        customer_key varchar(40) NULL, -- Контрагент
        item_key varchar(40) NOT NULL, -- Статья
        create_user varchar(30) NOT NULL DEFAULT CURRENT_USER, -- создал
        created_at timestamp(0) NOT NULL DEFAULT now(), -- создан
        update_user varchar(30) NULL, -- изменил
        updated_at timestamp(0) NULL, -- изменен
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_ref_key_unq UNIQUE (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'Документы по кассе и банку';
    COMMENT ON COLUMN {TABLE_NAME}.id IS 'Идентификатор';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key документа';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'Валюта';
    COMMENT ON COLUMN {TABLE_NAME}.customer_key IS 'Контрагент';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма грн';
    COMMENT ON COLUMN {TABLE_NAME}.amount_usd IS 'Сумма usd';
    COMMENT ON COLUMN {TABLE_NAME}.item_key IS 'Статья';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Примечание';
    COMMENT ON COLUMN {TABLE_NAME}.document_type IS 'Тип документа';
    COMMENT ON COLUMN {TABLE_NAME}.create_user IS 'создал';
    COMMENT ON COLUMN {TABLE_NAME}.created_at IS 'создан';
    COMMENT ON COLUMN {TABLE_NAME}.update_user IS 'изменил';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS '1- приход; -1 - расход';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'Номер строки'; 
    COMMENT ON COLUMN {TABLE_NAME}.item_period IS 'Период';
    COMMENT ON COLUMN {TABLE_NAME}.rate_nbu IS 'Курс НБУ';
    COMMENT ON COLUMN {TABLE_NAME}.updated_at IS 'изменен';
    
    GRANT ALL ON TABLE {TABLE_NAME} TO user_prestige;
'''

CREATE_TRIGGER_AFTER = f'''
    CREATE TRIGGER trg_{TABLE_NAME}_after AFTER
    INSERT
        OR
    DELETE
        OR
    UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_log_changes()
    ;
'''

CREATE_TRIGGER_BEFORE = f'''
    CREATE TRIGGER trg_{TABLE_NAME}_bfr BEFORE
    INSERT
        OR
    UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_{TABLE_NAME}_bfr()
    ;
'''

CREATE_FUNCTION_BEFORE = f'''
    -- DROP FUNCTION fn_{TABLE_NAME}_bfr();
    
    CREATE OR REPLACE FUNCTION fn_{TABLE_NAME}_bfr()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
    
    BEGIN    
        -- Проверка на существование и заполнение периода
        IF NEW.item_period IS NULL THEN
            RAISE EXCEPTION 'заполните период';
        END IF;
    
        -- Обновление пользователя при UPDATE
        IF TG_OP = 'UPDATE' THEN
            IF NEW.doc_date <> OLD.doc_date or NEW.doc_number <> OLD.doc_number or  NEW.amount <> OLD.amount or
                NEW.organization_key <> OLD.organization_key or NEW.currency_key <> OLD.currency_key or
                NEW.customer_key <> OLD.customer_key or NEW.item_key <> OLD.item_key or NEW.description <> OLD.description THEN
                    
                NEW.update_user := current_user;
                NEW.updated_at := now();
            END IF;
        END IF;
        
        -- Если курс НБУ = 1, значит некорректный курс. Устанавливаем последнее значение до этой даты        
        IF NEW.rate_nbu = 1 THEN
            NEW.rate_nbu = (SELECT rate_nbu 
                            FROM {TABLE_NAME} 
                            WHERE doc_date::date <= NEW.doc_date::date
                                AND rate_nbu <> 1
                            ORDER BY doc_date DESC
                            LIMIT 1
                            );
        END IF;
        
        -- Корректировка сумм
        NEW.amount = abs(NEW.amount) * NEW.recorder_type;
        NEW.amount_usd = abs(NEW.amount / NEW.rate_nbu) * NEW.recorder_type;

        IF NEW.customer_key = '13bc1e30-f5f8-11ec-8137-001dd8b72b55' THEN -- ТОВ "ТК ІМПУЛЬС"
            NEW.item_key = '28174f63-a992-11e7-80d5-001dd8b79079';  -- Бензин (транспортные) менеджеров
        ELSEIF NEW.customer_key IN (
                '4637da56-cfeb-11ee-81a4-001dd8b740bc',  -- ФОП Ярова Катерина Володимирівна (аудит)
                '5a83be88-f3d5-11ed-8184-001dd8b740bc',  -- ФОП Васильчук Світлана Станіславівна
                'c51b2918-8f91-11ee-8197-001dd8b72b55'   -- ФОП Чернишев Володимир Валерійович (аудит)
            )
            THEN
            NEW.item_key = '9972dac5-28a3-11eb-80fc-001dd8b72b55';  -- зарплата
        ELSEIF NEW.customer_key = '11a13122-2396-11e9-80e9-001dd8b79079' THEN -- ТОВ "Нова Пошта"
            NEW.item_key = 'c099839d-a285-11e7-80d5-001dd8b79079';  -- Транспорт
        ELSEIF NEW.item_key = '94a360c5-83dc-11e7-80d4-001dd8b79079' THEN -- Маркетинговые (услуги по предпродажной подготовки товаров)
            NEW.item_key = '33413219-51d9-11e7-80ce-001dd8b79079';  -- Маркетингові послуги
        ELSEIF NEW.customer_key IN (
                '53291133-d5a2-11e7-80d8-001dd8b79079',  -- Абдурахман
                'cef2b43d-02ea-11eb-80fa-001dd8b79079'   -- Артур Гаврил
            )
            AND NEW.item_key = 'eb3df34e-24c9-46b6-9967-3ab8deacf6d3' THEN -- Оплата покупателя
            NEW.item_key = '2740a4a6-f88e-11ed-8184-001dd8b740bc';  -- Перемещение денежных средств
        ELSEIF NEW.customer_key	= '4b0f6e7b-36a3-11ea-80ee-001dd8b79079' THEN  --	Складские расходы (упаков.мат, поддоны, спецодежда) ; ремонты.
            NEW.item_key = '3150b976-4a8d-11e7-80cd-001dd8b79079';  -- Складские расходы (этикетки,стикера)
        ELSEIF NEW.item_key IN ('3150b979-4a8d-11e7-80cd-001dd8b79079',  -- Брокерские услуги
                                '9aaf69d0-4c0f-11e7-80cd-001dd8b79079')   -- Брокерские услуги
                THEN
            NEW.item_key = '9aaf69cc-4c0f-11e7-80cd-001dd8b79079';  -- Транспорт импорт
        END IF;
        
        RETURN NEW;
    END;
    $function$
    ;
'''

INSERT_FROM_CASHIER_VIEW = f'''
    INSERT INTO {TABLE_NAME}(
        doc_date,
        doc_number,
        ref_key,
        organization_key,
        currency_key,
        customer_key,
        amount,
        item_key,
        description,
        document_type,
        item_period,
        line_number,
        recorder_type,
        rate_nbu
    )
    SELECT
        tarih,
        doc_number,
        ref_key,
        organization_key,
        currency_key,
        account_key,
        amount,
        cash_flow_item_key::uuid,
        a_comment,
        doc_type,
        period,
        line_number,
        recorder_type,
        rate_usd_nbu
    FROM {VIEW_NAME}
    WHERE tarih::date >= '01.10.2024'::date
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        amount = EXCLUDED.amount
--    DO NOTHING  -- НЕ УДАЛЯТЬ!!! Иначе не будет удалятся вручную внесенные изменения
    ;
'''

SQL_UPDATE_ITEMS = f'''
    UPDATE {TABLE_NAME} AS t
    SET item_key = '2740a4a6-f88e-11ed-8184-001dd8b740bc'  -- перемещение денежных средств
    WHERE update_user IS NULL  -- не удалять, иначе измененные сделанные вручную - заменит
        AND customer_key IN (
        '1dd2778f-a41e-11eb-8104-001dd8b72b55',  -- Альфа - Бест
        'f51a0e88-4821-11ec-811d-001dd8b72b55',  -- ТОВ "АЛЬФА БЕСТ"
        'e1bd7c8b-1863-11e7-80cb-001dd8b79079',  -- ТОВ "Престиж Продукт-К"
        'e8a44c07-c362-11e6-80c4-c936aa9c817c',  -- ТОВ "ПРЕСТИЖ ПРОДУКТ-К"
        '11c35ec4-abf3-11e6-80c4-c936aa9c817c',  -- ТОВ "ПРЕСТИЖ ПРОДУКТ-К"
        '80fd26e8-b2dc-11ed-816a-001dd8b72b55',  -- ТОВ "ПРЕСТИЖ ПРОДУКТ-К"
        '2c04025b-b29a-11e9-80ea-001dd8b79079',  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
        '60e0db27-36ca-11ea-80ee-001dd8b79079',  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
        '936f4618-48de-11eb-80fd-001dd8b72b55',  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
        '5e2ada8a-2227-11ee-8185-001dd8b72b55',  -- ТОВ "ГК ПРЕСТИЖ"
        'd3ba014e-b2d4-11ed-816a-001dd8b72b55',  -- ТОВ "ГК ПРЕСТИЖ"
        '96956354-4ace-11ed-8148-001dd8b72b55',  -- /Валяев Расим
        '9ca1341a-e80b-11ed-8182-001dd8b740bc',  -- Валяев Расим
        '8a68778b-853b-11ee-8196-001dd8b72b55',  -- Валяев Расим ФОП
        '63ab5695-87a7-11ee-8196-001dd8b72b55',  -- карта Валяев Расим
        '54b9a3b1-847a-11ee-8196-001dd8b72b55',  -- Оплата з ФОП Валяєв Р.
        'bd28b535-8461-11ee-8196-001dd8b72b55',  -- ФОП Валяев Расим
        'f86dc191-cd6e-11ea-80f8-001dd8b79079',  -- /Гаврил А.
        'cef2b43d-02ea-11eb-80fa-001dd8b79079',  -- Артур Гаврил
        '1af1e72f-ead9-11ec-8135-001dd8b740bc',  -- Гаврил А. (кредит)
        '8583fd44-e030-11ed-817a-001dd8b740bc',  -- Гаврил Артур  ФОП  
        'c7ffd494-bcb2-11ec-8130-001dd8b740bc',  -- Гаврил Артур (карта)
        'e0e22e76-06b6-11ee-8184-001dd8b740bc',  -- Гавріл Артур Ілліч
        '140cfd11-e99c-11ed-8182-001dd8b740bc',  -- Оплата з карти ФОП Гавріл А.
        '8deb7760-e35b-11ed-8181-001dd8b740bc',  -- ФОП Гаврил Артур Ілліч
        '0ad640e9-b3de-11ef-81d6-001dd8b740bc',  -- карта Еркилик Абдуррахман
        '3018c03b-a66a-11ef-81cd-001dd8b740bc',  -- Оплата з ФОП Еркілік А.
        '26cfba7c-60de-11e9-80ea-001dd8b79079',  -- ФОП Еркелик Абдуррахман
        '925cf28e-58d5-11e8-80e1-001dd8b79079',  -- ФОП ЕРКІЛІК АБДУРРАХМАН
        '413de47b-43e6-11e9-80ea-001dd8b79079',  -- /Мушли Фатих
        'f979199b-b087-11e6-80c4-c936aa9c817c',  -- /Мушлі Фатіх
        '570b93fe-5291-11e8-80e0-001dd8b79079',  -- МУШЛИ Фатих
        'c2afb244-fec5-11ec-813d-001dd8b72b55',  -- Мушли Фатих  (карта)    
        'fd98bdef-942e-11e6-80c4-c936aa9c817c',  -- Мушлі Фатіх
        'bfadf184-b637-11e6-80c4-c936aa9c817c',  -- Мушлі Фатіх
        'c2932869-8ec6-11e6-80c4-c936aa9c817c',  -- МУШЛІ ФАТІХ (MUSLI Fatih)
        'd1a663df-f2f6-11ec-8137-001dd8b72b55',  -- Оплата на  карту ФОП Мушлі Ф.
        '420ceb82-1737-11ed-8143-001dd8b72b55'  -- ФОП МУШЛІ Фатіх        
        )
'''

SQL_RESTORE_FROM_LOG = f'''
    INSERT INTO {TABLE_NAME}(
        doc_date,
        doc_number,
        amount,
        amount_usd,
        description,
        document_type,
        item_period,
        recorder_type,
        rate_nbu,
        line_number,
        id,
        ref_key,
        organization_key,
        currency_key,
        customer_key,
        item_key,
        create_user,
        created_at,
        update_user
    )
    SELECT *
    FROM 
    (WITH extract_data_from_log AS (
            SELECT DISTINCT 
                changed_at::timestamp(0) changed_at,
                operation,
                0 status,  -- old
                (old_data->>'amount')::numeric(15,2) as amount,
                (old_data->>'amount_usd')::numeric(15,2) as amount_usd,
                (old_data->>'create_user')::varchar(40) as create_user,
                (old_data->>'created_at')::timestamp(0) as created_at,
                (new_data->>'id')::varchar(40) as id,
                (old_data->>'currency_key')::varchar(40) as currency_key,
                (old_data->>'customer_key')::varchar(40) as customer_key,
                (old_data->>'description')::varchar(250) as description,
                (old_data->>'doc_date')::timestamp(0) as doc_date,
                (old_data->>'doc_number')::varchar(40) as doc_number,
                (old_data->>'document_type')::varchar(40) as document_type,
                (old_data->>'item_key')::varchar(40) as item_key,
                (old_data->>'item_period')::date as item_period,
                (old_data->>'line_number')::numeric(4) as line_number,
                (old_data->>'organization_key')::varchar(40) as organization_key,
                (old_data->>'rate_nbu')::numeric(15,2) as rate_nbu,
                (old_data->>'recorder_type')::numeric(15,2) as recorder_type,
                (old_data->>'ref_key')::varchar(40) as ref_key,
                (old_data->>'update_user')::varchar(40) as update_user
            FROM 
                t_change_log
            WHERE table_name in ('{TABLE_NAME}')
                AND changed_at::date < '02.01.2025'::date
            UNION ALL 
            SELECT DISTINCT 
                changed_at::timestamp(0),
                operation,
                1 status,  -- new
                (new_data->>'amount')::numeric(15,2) as amount,
                (new_data->>'amount_usd')::numeric(15,2) as amount_usd,
                (new_data->>'create_user')::varchar(40) as create_user,
                (new_data->>'created_at')::timestamp(0) as created_at,
                (new_data->>'id')::varchar(40) as id,
                (new_data->>'currency_key')::varchar(40) as currency_key,
                (new_data->>'customer_key')::varchar(40) as customer_key,
                (new_data->>'description')::varchar(250) as description,
                (new_data->>'doc_date')::timestamp(0) as doc_date,
                (new_data->>'doc_number')::varchar(40) as doc_number,
                (new_data->>'document_type')::varchar(40) as document_type,
                (new_data->>'item_key')::varchar(40) as item_key,
                (new_data->>'item_period')::date as item_period,
                (new_data->>'line_number')::numeric(4) as line_number,
                (new_data->>'organization_key')::varchar(40) as organization_key,
                (new_data->>'rate_nbu')::numeric(15,2) as rate_nbu,
                (new_data->>'recorder_type')::numeric(15,2) as recorder_type,
                (new_data->>'ref_key')::varchar(40) as ref_key,
                (new_data->>'update_user')::varchar(40) as update_user
            FROM 
                t_change_log
            WHERE table_name IN ('{TABLE_NAME}')
                AND id IS NOT NULL
                AND changed_at::date < '02.01.2025'::date
    )
    SELECT DISTINCT
        doc_date,
        doc_number,
        amount,
        amount_usd,
        description,
        document_type,
        item_period,
        recorder_type,
        rate_nbu,
        line_number,
        id,
        ref_key,
        organization_key,
        currency_key,
        customer_key,
        item_key,
        create_user,
        created_at,
        update_user
    FROM (
        SELECT DISTINCT 
            changed_at = max(changed_at) OVER (PARTITION BY ref_key, line_number) AS max_date,
            status = max(status) OVER (PARTITION BY ref_key, line_number) AS max_status,
            log.*
        FROM extract_data_from_log AS log
        ) AS t
    WHERE max_date 
        AND max_status
        AND ref_key IS NOT NULL 
        AND line_number IS NOT NULL 
    ORDER BY
        doc_date,
        doc_number
    ) AS r
    ;

'''


async def save_doc_cash_bank_all():
    await create_views()
    await  upload_data()
    result = await async_save_pg(CREATE_TABLE)
    logger.info(f"{result}, CREATE TABLE {TABLE_NAME}")
    result = await async_save_pg(CREATE_FUNCTION_BEFORE)
    logger.info(f"{result}, CREATE_FUNCTION_BEFORE {TABLE_NAME}")
    result = await async_save_pg(CREATE_TRIGGER_AFTER)
    logger.info(f"{result}, CREATE_TRIGGER_AFTER {TABLE_NAME}")
    result = await async_save_pg(CREATE_TRIGGER_BEFORE)
    logger.info(f"{result}, CREATE_TRIGGER_BEFORE {TABLE_NAME}")
    result = await async_save_pg(INSERT_FROM_CASHIER_VIEW)
    logger.info(f"{result}, INSERT_FROM_CASHIER_VIEW")
    result = await async_save_pg(SQL_UPDATE_ITEMS)
    logger.info(f"{result}, SQL_UPDATE_ITEMS")

    # Восстановление данных из лога
    # result = await async_save_pg(SQL_RESTORE_FROM_LOG)
    # logger.info(f"{result}, SQL_RESTORE_FROM_LOG")


async def upload_data():
    # Catalog_СтатьиДвиженияДенежныхСредств
    await main_cat_cash_flow_items_async()

    # Document_ПлатежноеПоручениеВходящее
    await main_doc_cash_order_receipt_async()

    # Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
    await main_doc_cash_order_receipt_details_async()

    # Document_ПлатежноеПоручениеИсходящее
    await main_doc_cash_order_expense_async()

    # Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
    await main_doc_cash_order_expense_details_async()

    # Document_ПлатежноеПоручениеИсходящее_ВыплатаЗаработнойПлаты
    await main_salary_payment_of_wages_async()

    # Document_РасходныйКассовыйОрдер
    await main_doc_cash_warrant_expense_async()

    # Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
    await main_doc_cash_warrant_expense_details_async()

    # Document_ПриходныйКассовыйОрдер
    await main_doc_cash_warrant_receipt_async()

    # Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
    await main_doc_cash_warrant_receipt_details_async()


if __name__ == '__main__':
    asyncio.run(save_doc_cash_bank_all())
