# reading data from Document_ГТДИмпорт and add to pg in table {TABLE_NAME}
# *********** импортируем данные для подключения к базам
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_debt_correction"
DOCUMENT = "Document_КорректировкаДолга"
SELECT_COLUMNS = (
    "DataVersion,DeletionMark,Number,Date,Posted,ВидОперации,Комментарий,ОтражатьВБухгалтерскомУчете,"
    "Отражать<PERSON>УправленческомУчете,<PERSON><PERSON><PERSON><PERSON><PERSON>окумента,КратностьДокумента,Ref_Key,КонтрагентДебитор_Key,"
    "Подразделение_Key,Организация_Key,Ответственный_Key,КонтрагентКредитор_Key,ВалютаДокумента_Key,"
    "ДоговорКонтрагента_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NULL,  -- DataVersion
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        doc_number varchar(50) NULL,  -- Number
        doc_date timestamp NOT NULL,  -- Date
        posted boolean NOT NULL DEFAULT FALSE,  -- Posted
        operation_type varchar(50) NULL,  -- ВидОперации
        a_comment varchar(300) NULL,  -- Комментарий
        is_accounting boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВБухгалтерскомУчете
        is_management boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВУправленческомУчете
        rate_document numeric(10,4) NOT NULL DEFAULT 0,  -- КурсДокумента
        multiplicity_of_document numeric(10,4) NOT NULL DEFAULT 0,  -- КратностьДокумента
        ref_key varchar(50) NULL,  -- Ref_Key
        department_key varchar(50) NULL,  -- Подразделение_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        responsible_key varchar(50) NULL,  -- Ответственный_Key
        counterparty_debtor_key varchar(50) NULL,  -- КонтрагентДебитор_Key
        counterparty_creditor_key varchar(50) NULL,  -- КонтрагентКредитор_Key
        currency_key varchar(50) NULL,  -- ВалютаДокумента_Key
        counterparty_contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS ' DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS ' DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS ' Number';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS ' Date';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS ' Posted';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS ' ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS ' Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS ' ОтражатьВБухгалтерскомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS ' ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.rate_document IS ' КурсДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_document IS ' КратностьДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_debtor_key IS ' КонтрагентДебитор_Key';
    COMMENT ON COLUMN {TABLE_NAME}.department_key IS ' Подразделение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS ' Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.responsible_key IS ' Ответственный_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_creditor_key IS ' КонтрагентКредитор_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS ' ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_contract_key IS ' ДоговорКонтрагента_Key';

    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;

"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        dataversion, 
        deletion_mark, 
        doc_number, 
        doc_date, 
        posted, 
        operation_type, 
        a_comment, 
        is_accounting, 
        is_management, 
        rate_document, 
        multiplicity_of_document, 
        ref_key, 
        counterparty_debtor_key, 
        department_key, 
        organization_key, 
        responsible_key, 
        counterparty_creditor_key, 
        currency_key, 
        counterparty_contract_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion= EXCLUDED.dataversion,
        deletion_mark= EXCLUDED.deletion_mark,
        doc_number= EXCLUDED.doc_number,
        doc_date= EXCLUDED.doc_date,
        posted= EXCLUDED.posted,
        operation_type= EXCLUDED.operation_type,
        a_comment= EXCLUDED.a_comment,
        is_accounting= EXCLUDED.is_accounting,
        is_management= EXCLUDED.is_management,
        rate_document= EXCLUDED.rate_document,
        multiplicity_of_document= EXCLUDED.multiplicity_of_document,
        ref_key= EXCLUDED.ref_key,
        counterparty_debtor_key= EXCLUDED.counterparty_debtor_key,
        department_key= EXCLUDED.department_key,
        organization_key= EXCLUDED.organization_key,
        responsible_key= EXCLUDED.responsible_key,
        counterparty_creditor_key= EXCLUDED.counterparty_creditor_key,
        currency_key= EXCLUDED.currency_key,
        counterparty_contract_key= EXCLUDED.counterparty_contract_key
    ;    
    """
    return sql.replace("'", "")


async def main_debt_correction_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, "", "", True)
    maket = await create_model_async(19)
    sql = await sql_insert(maket)
    sql = sql.replace("$4,", "to_timestamp($4, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_debt_correction_async())
