import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_warehouses"
DOCUMENT = "Catalog_Склады"
SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Predefined,Parent_Key,IsFolder,Code,Description,Комментарий"
SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NOT NULL,  -- DataVersion
        code numeric(10, 4) NOT NULL DEFAULT 0,  -- Code
        description varchar(50) NOT NULL,  -- Description
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        deletionmark bool NOT NULL DEFAULT false,  -- DeletionMark
        predefined bool NOT NULL DEFAULT false,  -- Predefined
        parent_key varchar(50) NOT NULL,  -- Parent_Key
        isfolder bool NOT NULL DEFAULT false,  -- IsFolder
        a_comment text NULL,  -- Комментарий
        CONSTRAINT t_one_cat_warehouses_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        ref_key,
        dataversion,
        deletionmark,
        predefined,
        parent_key,
        isfolder,
        code,
        description,
        a_comment
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        predefined = EXCLUDED.predefined,
        parent_key = EXCLUDED.parent_key,
        isfolder = EXCLUDED.isfolder,
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        a_comment = EXCLUDED.a_comment
    ;
    '''
    return sql.replace("'", "")


async def main_cat_warehouse_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    # sql = sql.replace("$3,", "to_timestamp($3, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_warehouse_async())
