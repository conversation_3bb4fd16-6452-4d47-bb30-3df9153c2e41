import requests
from datetime import datetime
import pandas as pd


def get_stock_balance(date, url, username, password):
    # Форматируем дату в нужный формат
    date_str = date.strftime('%Y-%m-%d')

    # URL для запроса к OData сервису
    odata_url = (f"{url}/odata/standard.odata/AccumulationRegister_ПартииТоваровНаСкладах_RecordType?$format=json"
                 f"&$filter=Номенклатура_Key eq guid'7e79d65a-5909-11e8-80e1-001dd8b79079' "
                 f"and Period le datetime'{date_str}T00:00:00'")

    # Отправка GET-запроса с базовой аутентификацией
    response = requests.get(odata_url, auth=(username, password))

    # Проверка успешности запроса
    if response.status_code != 200:
        raise Exception(f"Error fetching data: {response.status_code}, {response.text}")

    # Парсинг JSON-ответа
    data = response.json()

    # Извлечение и возвращение данных об остатках
    df = pd.DataFrame(data['value'])
    return df

# Пример использования функции
url = 'http://192.168.1.254/utp_prestige'  # URL до вашего сервера 1С и базы данных
username = 'rasim'  # Ваше имя пользователя для подключения к базе данных
password = '15021972'  # Ваш пароль для подключения к базе данных

date = datetime(2024, 6, 30)  # Укажите нужную дату

stock_balances = get_stock_balance(date, url, username, password)

for balance in stock_balances:
    print(f"Номенклатура: {balance['Номенклатура_Key']}, Остаток: {balance['Количество']}, Склад: {balance['Склад_Key']}")
