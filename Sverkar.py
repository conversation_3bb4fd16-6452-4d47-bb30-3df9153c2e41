import pandas as pd
import re
import os
import warnings
from datetime import datetime
import numpy as np
from dateutil.parser import parse


# Настроим отображение предупреждений
warnings.filterwarnings('ignore')

def process_excel_file(file_path):
    """
    Универсальный обработчик для извлечения данных из Excel файлов с актами сверки на украинском языке
    
    Args:
        file_path: путь к файлу Excel
    
    Returns:
        DataFrame с извлеченными данными
    """
    print(f"Обрабатываем файл: {file_path}")
    
    # Создадим словарь для перевода украинских названий месяцев
    ukrainian_months = {
        'січня': 1, 'лютого': 2, 'березня': 3, 'квітня': 4, 'травня': 5, 'червня': 6,
        'липня': 7, 'серпня': 8, 'вересня': 9, 'жовтня': 10, 'листопада': 11, 'грудня': 12,
        'січень': 1, 'лютий': 2, 'березень': 3, 'квітень': 4, 'травень': 5, 'червень': 6,
        'липень': 7, 'серпень': 8, 'вересень': 9, 'жовтень': 10, 'листопад': 11, 'грудень': 12
    }
    
    # Загрузим файл Excel, отменяя объединение ячеек
    try:
        # Сначала откроем с помощью openpyxl для отмены объединения ячеек
        import openpyxl
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        sheet = workbook.active
        
        # Отменим объединение ячеек
        for merged_cell_range in list(sheet.merged_cells.ranges):
            sheet.unmerge_cells(str(merged_cell_range))
        
        # Сохраним временный файл
        temp_file = file_path + "_temp.xlsx"
        workbook.save(temp_file)
        
        # Теперь загрузим этот файл с pandas
        df_raw = pd.read_excel(temp_file, header=None)
        
        # Удалим временный файл
        try:
            os.remove(temp_file)
        except:
            pass
    except:
        # Если не получилось использовать openpyxl, попробуем прямо открыть файл с pandas
        try:
            df_raw = pd.read_excel(file_path, header=None)
        except Exception as e:
            print(f"Ошибка при чтении файла: {e}")
            return None
    
    # Создадим DataFrame для хранения результатов
    result_df = pd.DataFrame(columns=['date_raw', 'description', 'debt', 'credet', 'date_doc', 'doc_number', 'doc_type'])
    
    # Найдем нужные колонки по заголовкам
    date_col = None
    doc_col = None
    debt_col = None
    credit_col = None
    
    # Проверим каждую строку на наличие заголовков
    for i in range(9, len(df_raw)):
        row = df_raw.iloc[i].tolist()
        
        for j, cell in enumerate(row):
            if isinstance(cell, str) and cell.strip().lower() == "дата" and date_col is None:
                date_col = j
            elif isinstance(cell, str) and cell.strip().lower() == "документ" and doc_col is None:
                doc_col = j
            elif isinstance(cell, str) and cell.strip().lower() == "дебет" and debt_col is None:
                debt_col = j
            elif isinstance(cell, str) and cell.strip().lower() == "кредит" and credit_col is None:
                credit_col = j
        
        # Если нашли все колонки, запомним индекс строки заголовка и прервем поиск
        if all(x is not None for x in [date_col, doc_col, debt_col, credit_col]):
            header_row = i
            break
    
    # Если не нашли все нужные колонки, попробуем найти их по частичному соответствию
    if any(x is None for x in [date_col, doc_col, debt_col, credit_col]):
        for i in range(len(df_raw)):
            row = df_raw.iloc[i].tolist()
            
            for j, cell in enumerate(row):
                if isinstance(cell, str):
                    cell_lower = cell.strip().lower()
                    if date_col is None and "дата" in cell_lower:
                        date_col = j
                    elif doc_col is None and ("документ" in cell_lower or "номер" in cell_lower):
                        doc_col = j
                    elif debt_col is None and "дебет" in cell_lower:
                        debt_col = j
                    elif credit_col is None and "кредит" in cell_lower:
                        credit_col = j
            
            # Если нашли все колонки, запомним индекс строки заголовка и прервем поиск
            if all(x is not None for x in [date_col, doc_col, debt_col, credit_col]):
                header_row = i
                break
    
    # Если не нашли колонки, возвращаем пустой DataFrame
    if any(x is None for x in [date_col, doc_col, debt_col, credit_col]):
        print("Не удалось найти все требуемые колонки в файле.")
        return result_df
    
    print(f"Найдены колонки: Дата ({date_col}), Документ ({doc_col}), Дебет ({debt_col}), Кредит ({credit_col})")
    
    # Функция для проверки, является ли значение датой
    def is_date(text):
        if not isinstance(text, str):
            # Проверим, не является ли значение уже датой в pandas
            if isinstance(text, (pd.Timestamp, datetime)) or parse(text, dayfirst=True):
                return True
            return False
        
        text = text.strip()
        
        # Проверим разные форматы даты (цифровые и текстовые)
        date_patterns = [
            r'\d{2}\.\d{2}\.\d{4}',  # формат ДД.ММ.ГГГГ
            r'\d{2}\.\d{2}\.\d{2}',  # формат ДД.ММ.ГГГГ
            r'\d{2}\-\d{2}\-\d{4}',  # формат ДД-ММ-ГГГГ
            r'\d{2}/\d{2}/\d{4}',    # формат ДД/ММ/ГГГГ
            r'\d{1,2}\s+\w+\s+\d{4}' # формат Д месяц ГГГГ или ДД месяц ГГГГ
        ]
        
        for pattern in date_patterns:
            if re.match(pattern, text):
                return True
        return False
    
    # Функция для обработки даты
    def parse_date(date_str):
        if not isinstance(date_str, str):
            # Проверим, не является ли значение уже датой в pandas
            if isinstance(date_str, (pd.Timestamp, datetime)) or parse(date_str, dayfirst=True):
                return parse(date_str, dayfirst=True).strftime('%d.%m.%Y')
            return None
        
        date_str = date_str.strip()
        # удалим из date_str все символы, кроме цифр, точек, дефисов, переносы строк и слешей
        date_str = re.sub(r'[^0-9\.\-\n/]', '', date_str)
        date_str = date_str.replace('\n', ' ').strip()
        
        # Попробуем разные форматы дат
        try:
            # Для формата ДД.ММ.ГГГГ
            if re.match(r'\d{2}\.\d{2}\.\d{4}', date_str):
                return date_str
            # Для формата ДД.ММ.ГГ
            elif re.match(r'\d{2}\.\d{2}\.\d{2}', date_str):
                return date_str
            
            # Для формата ДД-ММ-ГГГГ
            elif re.match(r'\d{2}\-\d{2}\-\d{4}', date_str):
                dt = datetime.strptime(date_str, '%d-%m-%Y')
                return dt.strftime('%d.%m.%Y')
            
            # Для формата ДД/ММ/ГГГГ
            elif re.match(r'\d{2}/\d{2}/\d{4}', date_str):
                dt = datetime.strptime(date_str, '%d/%m/%Y')
                return dt.strftime('%d.%m.%Y')
            elif parse(date_str, dayfirst=True):
                return parse(date_str, dayfirst=True).strftime('%d.%m.%Y')
            
            # Для формата с текстовым представлением месяца (например, "12 березня 2024")
            elif re.match(r'\d{1,2}\s+\w+\s+\d{4}', date_str):
                parts = date_str.split()
                day = int(parts[0])
                month_name = parts[1].lower()
                year = int(parts[2])
                
                if month_name in ukrainian_months:
                    month_num = ukrainian_months[month_name]
                    dt = datetime(year, month_num, day)
                    return dt.strftime('%d.%m.%Y')
        except:
            return None
        
        return None
    
    # Функция для извлечения даты и номера документа из описания
    def extract_doc_info(description):
        if not isinstance(description, str):
            return None, None
        
        doc_number = None
        doc_date = None
        
        # Ищем номер документа (буквенно-цифровой)
        number_patterns = [
            r'№\s*([A-Za-zА-Яа-яіІїЇєЄ0-9\-/]+)',  # Формат с №
            r'ном[а-яіїє]*\s*([A-Za-zА-Яа-яіІїЇєЄ0-9\-/]+)',  # Формат с "номер"
        ]
        
        for pattern in number_patterns:
            match = re.search(pattern, description)
            if match:
                doc_number = match.group(1).strip()
                break
        
        # Если номер документа не найден, попробуем поискать любой буквенно-цифровой идентификатор
        if not doc_number:
            matches = re.findall(r'[A-Za-zА-Яа-яіІїЇєЄ0-9\-/]+', description)
            for match in matches:
                if re.search(r'[0-9]', match) and len(match) >= 3:  # Номер должен содержать цифры и быть не коротким
                    doc_number = match
                    break
        
        # Ищем дату в разных форматах
        date_patterns = [
            r'від\s+(\d{2}\.\d{2}\.\d{4})',  # ДД.ММ.ГГГГ
            r'від\s+(\d{2}\-\d{2}\-\d{4})',  # ДД-ММ-ГГГГ
            r'від\s+(\d{2}/\d{2}/\d{4})',    # ДД/ММ/ГГГГ
            r'від\s+(\d{1,2}\s+\w+\s+\d{4})' # Д месяц ГГГГ или ДД месяц ГГГГ
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, description)
            if match:
                date_str = match.group(1).strip()
                doc_date = parse_date(date_str)
                break
        
        # Если дата не найдена со словом "від", попробуем поискать любую дату
        if not doc_date:
            # Ищем любую дату в тексте
            date_patterns = [
                r'(\d{2}\.\d{2}\.\d{4})',  # ДД.ММ.ГГГГ
                r'(\d{2}\-\d{2}\-\d{4})',  # ДД-ММ-ГГГГ
                r'(\d{2}/\d{2}/\d{4})',    # ДД/ММ/ГГГГ
                r'(\d{1,2}\s+\w+\s+\d{4})' # Д месяц ГГГГ или ДД месяц ГГГГ
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, description)
                if match:
                    date_str = match.group(1).strip()
                    doc_date = parse_date(date_str)
                    break
        
        return doc_date, doc_number
    
    # Функция для определения типа документа
    def determine_doc_type(description, debt, credit):
        if not isinstance(description, str):
            return None
        
        description_lower = description.lower()
        
        # Определим тип документа по описанию
        if 'надходження' in description_lower:
            return 'РН'
        elif 'поверн' in description_lower:
            return 'ВН'
        elif 'взаємозалік' in description_lower:
            return 'Взаиморасчет'
        elif 'платіжн' in description_lower or 'оплат' in description_lower:
            # Проверим, какая колонка заполнена
            if pd.notna(debt) and (pd.isna(credit) or float(debt) > 0):
                return 'ППВ'
            else:
                return 'ППИ'
        
        return None
    
    # Обработаем данные начиная со строки после заголовка
    data_rows = []
    turnover_row = None
    df_raw = df_raw.iloc[:, [date_col, doc_col, debt_col, credit_col]]
    sum_debt = df_raw[df_raw[1]=='Обороти за період'][debt_col].iloc[0]
    sum_credit = df_raw[df_raw[1]=='Обороти за період'][credit_col].iloc[0]

    # Если это строка с оборотами, сохраним её отдельно
    turnover_row = {
        'date_raw': None,
        'description': None,
        'debt': sum_debt,
        'credet': sum_credit,
        'date_doc': None,
        'doc_number': None,
        'doc_type': None
    }

    
    # Начнем со строки после заголовка
    for i in range(header_row + 1, len(df_raw)):
        row = df_raw.iloc[i]
        
        # Проверим, есть ли в строке значение в колонке с датой
        if pd.isna(row[date_col]):
            continue
        
        date_value = row[date_col]

        # удалим из date_str все символы, кроме цифр, точек, дефисов, переносы строк и слешей
        date_value = date_value.replace('\n', ' ').strip()
        
        # Проверим, является ли значение датой или "Обороти за період"
        is_date_value = is_date(str(date_value)) if not pd.isna(date_value) else False
        is_turnover = isinstance(date_value, str) and "обороти за період" in date_value.lower()
        
        if is_date_value or is_turnover:
            # Получим значения для всех колонок
            doc_value = row[doc_col] if not pd.isna(row[doc_col]) else ""
            
            # Преобразуем значения дебета и кредита в числовой формат
            try:
                debt_value = float(row[debt_col]) if not pd.isna(row[debt_col]) else 0.0
            except:
                debt_value = 0.0
            
            try:
                credit_value = float(row[credit_col]) if not pd.isna(row[credit_col]) else 0.0
            except:
                credit_value = 0.0
            
            # Извлечем дату и номер документа
            date_doc, doc_number = extract_doc_info(str(doc_value))
            
            # Определим тип документа
            doc_type = determine_doc_type(str(doc_value), debt_value, credit_value)
            
            # Добавим строку в список данных
            data_rows.append({
                'date_raw': parse_date(str(date_value)),
                'description': doc_value,
                'debt': debt_value,
                'credet': credit_value,
                'date_doc': date_doc,
                'doc_number': doc_number,
                'doc_type': doc_type
            })
    
    # Создадим DataFrame из собранных данных
    if data_rows:
        result_df = pd.DataFrame(data_rows)
    
    # Удалим строки, где дата не определена
    result_df = result_df.dropna(subset=['date_raw'])
    
    # Проверим суммы
    total_debt = result_df['debt'].sum()
    total_credit = result_df['credet'].sum()
    
    # Извлечем суммы из строки с оборотами, если она есть
    turnover_debt = 0
    turnover_credit = 0
    
    if turnover_row is not None:
        turnover_debt = turnover_row['debt']
        turnover_credit = turnover_row['credet']
    
    # Проверим совпадают ли суммы
    debt_ok = abs(total_debt - turnover_debt) < 0.01
    credit_ok = abs(total_credit - turnover_credit) < 0.01
    
    if not (debt_ok and credit_ok) and turnover_debt > 0 and turnover_credit > 0:
        print(f"ВНИМАНИЕ: Суммы не совпадают!")
        print(f"Расчетный дебет: {total_debt}, из строки оборотов: {turnover_debt}")
        print(f"Расчетный кредит: {total_credit}, из строки оборотов: {turnover_credit}")
        
        # Здесь можно добавить логику перезапуска обработки при несовпадении сумм
    else:
        print(f"Суммы совпадают! Дебет: {total_debt}, Кредит: {total_credit}")
    
    return result_df


def main():
    # Пример использования
    file_path = r"C:\Users\<USER>\Desktop\Сверка\ПП УКРПАЛЕТСИСТЕМ\Акт звірки взаємних розрахунків № ЦБ-3413 від 05.04.2024.xlsx"
    
    if not os.path.exists(file_path):
        print(f"Файл не найден: {file_path}")
        return
    
    # Обработаем файл
    result_df = process_excel_file(file_path)
    
    if result_df is not None and not result_df.empty:
        # Выведем результаты
        print("\nРезультаты обработки:")
        print(result_df)
        
        # Сохраним результаты в CSV-файл
        output_file = os.path.splitext(file_path)[0] + "_processed.csv"
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\nРезультаты сохранены в файл: {output_file}")
    else:
        print("Не удалось извлечь данные из файла.")


if __name__ == "__main__":
    os.system('cls')  # Очистка консоли
    main()
