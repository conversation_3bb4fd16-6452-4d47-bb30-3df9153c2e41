import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_infreg_contact_information"
DOCUMENT = "InformationRegister_КонтактнаяИнформация"
SELECT_COLUMNS = "Объект,Объект_Type,Тип,Вид,Вид_Type,Представление,Поле1,Поле2,Поле3,Поле4,Поле5,Поле6,Поле7,Поле8," \
                 "Поле9,Поле10,Комментарий,ЗначениеПоУмолчанию,ТипДома,ТипКорпуса,ТипКвартиры"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        _type varchar(50) NULL,  -- Тип
        performance varchar(500) NULL,  -- Представление
        field1 varchar(500) NULL,  -- код страны
        field2 varchar(500) NULL,  -- код города
        field3 varchar(500) NULL,  -- номер
        field4 varchar(500) NULL,  -- внутренний
        field5 varchar(500) NULL,  -- Поле5
        field6 varchar(500) NULL,  -- Поле6
        field7 varchar(500) NULL,  -- Поле7
        field8 varchar(500) NULL,  -- Поле8
        field9 varchar(500) NULL,  -- Поле9
        field10 varchar(500) NULL,  -- Поле10
        a_comment varchar(500) NULL,  -- Комментарий
        default_value boolean NOT NULL DEFAULT FALSE,  -- ЗначениеПоУмолчанию
        an_object varchar(50) NULL,  -- Объект
        house_type varchar(50) NULL,  -- ТипДома
        type_of_shell varchar(50) NULL,  -- ТипКорпуса
        apartment_type varchar(50) NULL,  -- ТипКвартиры
        object_type varchar(500) NULL,  -- Объект_Type
        _view varchar(500) NULL,  -- Вид
        kind_type varchar(100) NULL,  -- Вид_Type
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (an_object, _view)
    );
    
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.an_object IS 'Объект';
    COMMENT ON COLUMN {TABLE_NAME}.object_type IS 'Объект_Type';
    COMMENT ON COLUMN {TABLE_NAME}._type IS 'Тип';
    COMMENT ON COLUMN {TABLE_NAME}._view IS 'Вид';
    COMMENT ON COLUMN {TABLE_NAME}.kind_type IS 'Вид_Type';
    COMMENT ON COLUMN {TABLE_NAME}.performance IS 'Представление';
    COMMENT ON COLUMN {TABLE_NAME}.field1 IS 'код страны';
    COMMENT ON COLUMN {TABLE_NAME}.field2 IS 'код города';
    COMMENT ON COLUMN {TABLE_NAME}.field3 IS 'номер';
    COMMENT ON COLUMN {TABLE_NAME}.field4 IS 'внутренний';
    COMMENT ON COLUMN {TABLE_NAME}.field5 IS 'Поле5';
    COMMENT ON COLUMN {TABLE_NAME}.field6 IS 'Поле6';
    COMMENT ON COLUMN {TABLE_NAME}.field7 IS 'Поле7';
    COMMENT ON COLUMN {TABLE_NAME}.field8 IS 'Поле8';
    COMMENT ON COLUMN {TABLE_NAME}.field9 IS 'Поле9';
    COMMENT ON COLUMN {TABLE_NAME}.field10 IS 'Поле10';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.default_value IS 'ЗначениеПоУмолчанию';
    COMMENT ON COLUMN {TABLE_NAME}.house_type IS 'ТипДома';
    COMMENT ON COLUMN {TABLE_NAME}.type_of_shell IS 'ТипКорпуса';
    COMMENT ON COLUMN {TABLE_NAME}.apartment_type IS 'ТипКвартиры';
'''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}(
            an_object,
            object_type,
            _type,
            _view,
            kind_type,
            performance,
            field1,
            field2,
            field3,
            field4,
            field5,
            field6,
            field7,
            field8,
            field9,
            field10,
            a_comment,
            default_value,
            house_type,
            type_of_shell,
            apartment_type
        )
        VALUES {maket}
        ON CONFLICT (an_object, _view)
        DO UPDATE SET
            _type = EXCLUDED._type,
            _view = EXCLUDED._view,
            kind_type = EXCLUDED.kind_type,
            performance = EXCLUDED.performance,
            field1 = EXCLUDED.field1,
            field2 = EXCLUDED.field2,
            field3 = EXCLUDED.field3,
            field4 = EXCLUDED.field4,
            field5 = EXCLUDED.field5,
            field6 = EXCLUDED.field6,
            field7 = EXCLUDED.field7,
            field8 = EXCLUDED.field8,
            field9 = EXCLUDED.field9,
            field10 = EXCLUDED.field10,
            a_comment = EXCLUDED.a_comment,
            default_value = EXCLUDED.default_value,
            house_type = EXCLUDED.house_type,
            type_of_shell = EXCLUDED.type_of_shell,
            apartment_type = EXCLUDED.apartment_type
    ;
    '''
    return sql.replace("'", "")


async def main_infreg_contact_information_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(21)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_infreg_contact_information_async())
