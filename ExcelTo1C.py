# Odata
# 1С, карточка товара, добавляет количество месяцев хранения

# pip install pyxlsb

import pandas as pd

from prestige_authorize import send_request
from configPrestige import URL_CONST

FILE_NAME = r'C:\Rasim\Python\Prestige\Outher\ExceptsClients.xlsx'
DOCUMENT = "Catalog_Контрагенты"


def nomenclature_storage_days():
    df = pd.read_excel(FILE_NAME, sheet_name=0)
    if 'segment_key' in df.columns:
        df = df.dropna(subset=['segment_key']).reset_index(drop=True)
    url_main = URL_CONST + DOCUMENT
    for row in df.itertuples():
        url = "%s(guid'%s')?$format=json" % (url_main, row.client_ref_key)
        data_json = {
            "Сегмент_Key": row.segment_key
        }
        result = send_request(url=url, method='PATCH', data_json=data_json)
        print(row.client_code, row.client_name, result)


if __name__ == '__main__':
    nomenclature_storage_days()
