-- Формирует отчет по дебиторской задолженности по клиентам
-- DROP FUNCTION fn_debt(date, varchar);
CREATE OR REPLACE FUNCTION fn_debt(date_first date, manager_key character varying)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    sql_query TEXT;
    view_name varchar(30);
    date_temp date;
BEGIN
    IF date_first::date > current_date THEN
        date_first = current_date;
        RAISE NOTICE 'Конечная дата (%) позже текущей. Меняем на текущую', date_first::date;
    END IF;

    EXECUTE 'DROP VIEW IF EXISTS v_one_balance_manager CASCADE;';

    sql_query = format('CREATE OR REPLACE VIEW v_one_balance_manager AS
		WITH details AS (
			SELECT
				manager,
				segment,
				customer,
				currency_name,
				is_accounting,
				doc_date,
				last_date,
				receipt,
				expense,
				receipt + expense doc_sum
			FROM (
				SELECT 
					COALESCE(manager,'''') manager,
					COALESCE(segment,'''') segment,
					COALESCE(customer,'''') customer,
					COALESCE(currency_name,'''') currency_name,
					COALESCE(is_accounting,'''') is_accounting,
					doc_date,
					last_date,
					sum(doc_sum) receipt,
					0 expense
				FROM v_one_balance_details
				WHERE
					doc_date::date <= CURRENT_DATE
					AND manager_key = %L
					AND recordtype = ''Receipt''
				GROUP BY 
					COALESCE(manager,''''),
					COALESCE(segment,''''),
					COALESCE(customer,''''),
					COALESCE(currency_name,''''),
					COALESCE(is_accounting,''''),
					doc_date,
					last_date
				UNION ALL 
				SELECT 
					COALESCE(manager,'''') manager,
					COALESCE(segment,'''') segment,
					COALESCE(customer,'''') customer,
					COALESCE(currency_name,'''') currency_name,
					COALESCE(is_accounting,'''') is_accounting,
					doc_date,
					last_date,
					0 receipt,
					sum(doc_sum) expense
				FROM v_one_balance_details
				WHERE
					doc_date::date <= CURRENT_DATE
					AND manager_key = %L
					AND recordtype = ''Expense''
				GROUP BY 
					COALESCE(manager,''''),
					COALESCE(segment,''''),
					COALESCE(customer,''''),
					COALESCE(currency_name,''''),
					COALESCE(is_accounting,''''),
					doc_date,
					last_date
			) AS sub
            ORDER BY
                manager,
                segment,
                customer,
                currency_name,
                is_accounting,
                doc_date,
                last_date
		)
        SELECT *
        FROM details
		;',
		date_first::date, manager_key,  -- details_receipt
		date_first::date, manager_key -- details_expense
		);
    RAISE NOTICE '%', sql_query;
    EXECUTE sql_query;
    
    RETURN;

END;
$function$
;
