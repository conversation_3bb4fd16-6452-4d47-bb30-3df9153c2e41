import asyncio
import os
from pathlib import Path

from async_Postgres import async_save_pg, async_truncate_table, async_sql_create_index_unique
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_cash_assignment_group'
SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        assign_clause varchar(300) NOT NULL,  -- статья/назначение
        clause varchar(300) NULL,  -- статья движения
        cost_item varchar(300) NULL,  -- статьяЗатрат
        assign varchar(300) NULL,  -- назначение
        cash_flow_item varchar(50) NULL,  -- статья_key
        organization varchar(150) NULL,  -- организация
        organization_key varchar(50) NULL,  -- организация_key
        assign_key varchar(50) NULL,  -- назначение_key
        counterparty varchar(150) NULL,  -- контрагент
        counterparty_key varchar(50) NULL,  -- контрагент_key
        cost_item_key varchar(50) NULL,  -- СтатьяЗатрат_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id)
    );

    COMMENT ON COLUMN {TABLE_NAME}.assign_clause IS 'статья/назначение';
    COMMENT ON COLUMN {TABLE_NAME}.clause IS 'статья движения';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item IS 'СтатьяЗатрат';
    COMMENT ON COLUMN {TABLE_NAME}.cost_item_key IS 'СтатьяЗатрат_Key затрат';
    COMMENT ON COLUMN {TABLE_NAME}.assign IS 'назначение';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'статья_key';
    COMMENT ON COLUMN {TABLE_NAME}.assign_key IS 'назначение_key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty IS 'контрагент';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_key IS 'контрагент_key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'организация_key';
    COMMENT ON COLUMN {TABLE_NAME}.organization IS 'организация';
    
    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;

'''

SQL_CREATE_TRIGGER = f'''
    CREATE TRIGGER fn_{TABLE_NAME}_bfr BEFORE
    INSERT
    OR UPDATE
        ON
        {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_{TABLE_NAME}()
    ;
'''


SQL_UPDATE_CLAUSE = f'''
    UPDATE t_cash_assignment_group AS grp
    SET clause = cls.description
    FROM t_one_cat_cash_flow_item AS cls
    WHERE cls.ref_key = grp.cash_flow_item 
    ;
'''
SQL_UPDATE_ASSIGN = f'''    
    UPDATE t_cash_assignment_group AS grp
    SET assign = cls.description
    FROM t_one_cat_cash_assignment AS cls
    WHERE cls.ref_key = grp.assign_key 
    ;
'''
SQL_UPDATE_COST_ITEM = f'''        
    UPDATE t_cash_assignment_group AS grp
    SET cost_item = cls.description
    FROM t_one_cat_cash_cost_item AS cls
    WHERE cls.ref_key = grp.cost_item_key 
    ;
'''
SQL_UPDATE_ORGANIZATION = f'''        
    UPDATE t_cash_assignment_group AS grp
    SET organization = cls.description
    FROM t_one_cat_organizations AS cls
    WHERE cls.ref_key = grp.organization_key 
    ;
'''
SQL_UPDATE_COUNTERPARTY = f'''        
    UPDATE t_cash_assignment_group AS grp
    SET counterparty = cls.description
    FROM t_one_cat_counterparties AS cls
    WHERE cls.ref_key = grp.counterparty_key 
    ;
'''


async def main_cash_assignment_group_async():
    logger.info(f"START")
    path_cur_file = Path(os.path.abspath(__file__)).parent.parent
    with open(os.path.join(path_cur_file,'t_cash_assignment_group_NE_UDALAT.sql'), 'r', encoding='utf-8') as file:
        sql_insert = file.read()
    result = await async_save_pg(SQL_CREATE_TABLE)
    logger.info(f"{result}, CREATE TABLE {TABLE_NAME}")
    result = await async_truncate_table(TABLE_NAME)
    logger.info(f"{result}, TRUNCATE TABLE {TABLE_NAME}")
    result = await async_sql_create_index_unique(TABLE_NAME, "cash_flow_item", "assign_key", "cost_item_key")
    logger.info(f"{result}, CREATE INDEX {TABLE_NAME} cash_flow_item, assign_key, cost_item_key")
    result = await async_save_pg(SQL_UPDATE_ASSIGN)
    logger.info(f"{result}, SQL_UPDATE_ASSIGN")
    result = await async_save_pg(SQL_UPDATE_CLAUSE)
    logger.info(f"{result}, SQL_UPDATE_CLAUSE")
    result = await async_save_pg(SQL_UPDATE_COUNTERPARTY)
    logger.info(f"{result}, SQL_UPDATE_COUNTERPARTY")
    result = await async_save_pg(SQL_UPDATE_COST_ITEM)
    logger.info(f"{result}, SQL_UPDATE_COST_ITEM")
    result = await async_save_pg(SQL_UPDATE_ORGANIZATION)
    logger.info(f"{result}, SQL_UPDATE_ORGANIZATION")
    result = await async_save_pg(sql_insert)
    logger.info(f"{result}, INSERT INTO {TABLE_NAME}")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cash_assignment_group_async())
