# https://www.youtube.com/watch?v=cYwn8Pu5eRg
# in VBA
# Function pythonSum(a <PERSON>, b <PERSON>)
#     pythonSum = VBA.CreateObject("Python.ObjectLibrary").pythonSum(a, b)
# End Function
#
# Function pythonMultiply(a <PERSON>, b <PERSON>)
#     pythonMultiply = VBA.CreateObject("Python.ObjectLibrary").pythonMultiply(a, b)
# End Function
#
# Function addArray(a As Range)
#     addArray = VBA.CreateObject("Python.ObjectLibrary").addArray(a)
# End Function


# import our libraries
import pythoncom
import numpy as np
import requests
import win32com.client

from datetime import datetime, timedelta
from prestige_authorize import change_data_in_table_bl, con_access_prestige


class PythonObjectLibrary:
    c_login = 'rasim'
    c_psw = '15021972'
    chatid_rasim = 490323168
    token_prestige = '5728309503:AAFOMRt9xnvNWNEJ6N71n-NLGlKnQvw1UIg'  # Prestige
    url_const = 'http://*************/Prestige/odata/standard.odata/'
    conac = con_access_prestige()

    # This will create a GUID to register it with Windows, it is unique.
    _reg_clsid_ = pythoncom.CreateGuid()

    # Register the object as an EXE file, the alternative is an DLL file (INPROC_SERVER)
    _reg_clsctx_ = pythoncom.CLSCTX_LOCAL_SERVER

    # the program ID, this is the name of the object library that users will use to create the object.
    _reg_progid_ = "Python.ObjectLibrary"

    # this is a description of our object library.
    _reg_desc_ = "This is our Python object library."

    # a list of strings that indicate the public methods for the object.
    # If they aren't listed they are conisdered private.
    _public_methods_ = ['python_sum', 'python_multiply', 'add_array', 'send_sms', 'con_to_access_prestige', 'get_order',
                        'save_to_db_access']

    # multiply two cell values.
    @staticmethod
    def python_multiply(a, b):
        return a * b

    # add two cell values
    def python_sum(self, x, y):
        return (x + y) * 200

    # send GET request to database 1C
    def get_response(self, url):
        return requests.get(url, auth=('rasim', '15021972')).json()['value']

    # add a range of cell values
    def add_array(self, my_range):
        # create an instance of the range object that is passed through
        rng1 = win32com.client.Dispatch(my_range)

        # Get the values from the range
        rng1val = np.array(list(rng1.Value))

        return rng1val.sum()

    def send_sms(self, msj_out, chat_id=chatid_rasim):
        # отправляем смс
        try:
            if msj_out != "":
                api_url = "https://api.telegram.org/bot{}/sendMessage?chat_id={}&text={}".format(
                    '5728309503:AAFOMRt9xnvNWNEJ6N71n-NLGlKnQvw1UIg',
                    chat_id,
                    str(msj_out))
                requests.get(api_url)

        except Exception as e:
            msj = "send_sms: %s; ERROR: %s" % (msj_out, e)
            print(msj)

    def get_order(self):
        date_on = "'" + (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%dT00:00:00") + "'"
        url = self.url_const + \
              'Document_%D0%97%D0%B0%D0%BA%D0%B0%D0%B7%D0%9F%D0%BE%D0%BA%D1%83%D0%BF%D0%B0%D1%82%D0%B5%D0%BB%D1%8F' \
              '?$format=json' \
              '&$orderby=Date%20desc' \
              f'&$filter=Posted eq true and Date ge datetime{date_on}' \
              '&$select=**' \
              '&$inlinecount=allpages'

        try:
            response = self.get_response(url)
            print(response)
            for i in range(len(response)):
                self.save_to_db_access(response[i])
            return "Data added to the database = %s" % True

        except Exception as e:
            msj = "get_order %s" % e
            print(msj)
            return "Data added to the database = %s" % False

    def save_to_db_access(self, json_row):
        try:

            str_sql = '''
                INSERT INTO t_customer_order (ref_key, doc_date, doc_number, customer_key, organization_key)
                VALUES (?, ?, ?, ?, ?)
                '''
            odata = (
                json_row['Ref_Key']
                , datetime.fromisoformat(json_row['Date'])
                , json_row['Number']
                , json_row['Контрагент_Key']
                , json_row['Организация_Key'])

            result = change_data_in_table_bl(str_sql, self.conac, odata)
            print("Данные занесены в бд access=%s" % result)

        except Exception as e:
            msj = "Ошибка при добавлении/обновлении данных в базу"
            print(msj, str(e))


if __name__ == '__main__':
    # import win32com.server.register
    # win32com.server.register.UseCommandLine(PythonObjectLibrary)
    PythonObjectLibrary().get_order()
    print('OK')
