# Odata
# 1С, карточка товара, добавляет количество месяцев хранения

# pip install pyxlsb
import requests
import pandas as pd

from prestige_authorize import send_request
from configPrestige import URL_CONST

FILE_NAME = r'C:\Users\<USER>\Desktop\Excel\Поступления_Сроки.xlsb'
DOCUMENT = "Catalog_Номенклатура"


def nomenclature_storage_days():
    df = pd.read_excel(FILE_NAME, sheet_name=2)
    url_main = URL_CONST + DOCUMENT
    for row in df.itertuples():
        url = "%s(guid'%s')?$format=json" % (url_main, row.nomenclature_key)
        data_json = {
            "КоличествоМесяцевХранения": row.storage_month
        }
        result = send_request(url=url, method='PATCH', data_json=data_json)
        print(row.sku, result)


if __name__ == '__main__':
    nomenclature_storage_days()
