import pandas as pd
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Функция для классификации по XYZ-анализу
def classify_xyz(cv_value, threshold_x=0.5, threshold_y=1.0):
    if cv_value <= threshold_x:
        return 'X'
    elif cv_value <= threshold_y:
        return 'Y'
    else:
        return 'Z'

def perform_xyz_analysis(df, demand_columns):
    """
    Выполняет XYZ-анализ для заданного DataFrame.
    Добавляет колонки: 'mean', 'std', 'cv' и 'xyz'.
    """
    df['mean'] = df[demand_columns].mean(axis=1)
    df['std'] = df[demand_columns].std(axis=1)
    # Предотвращаем деление на 0
    df['cv'] = np.where(df['mean'] != 0, df['std'] / df['mean'], np.nan)
    df['xyz'] = df['cv'].apply(classify_xyz)
    logger.info("XYZ-анализ выполнен для %d товаров", len(df))
    return df

# Функция для ABC-анализу: рассчитываем суммарные продажи и ранжируем товары
def perform_abc_analysis(df, sales_columns, a_threshold=0.7, b_threshold=0.9):
    """
    Выполняет ABC-анализ для заданного DataFrame.
    Добавляет колонки: 'total_sales', 'cum_pct' и 'abc'.
    a_threshold и b_threshold задают пороги для отнесения к группам A и B.
    """
    df['total_sales'] = df[sales_columns].sum(axis=1)
    df_sorted = df.sort_values(by='total_sales', ascending=False).copy()
    total_sum = df_sorted['total_sales'].sum()
    df_sorted['cum_pct'] = df_sorted['total_sales'].cumsum() / total_sum

    def classify_abc(cum_pct):
        if cum_pct <= a_threshold:
            return 'A'
        elif cum_pct <= b_threshold:
            return 'B'
        else:
            return 'C'
    
    df_sorted['abc'] = df_sorted['cum_pct'].apply(classify_abc)
    logger.info("ABC-анализ выполнен для %d товаров", len(df_sorted))
    # Восстанавливаем исходный порядок
    df_final = df_sorted.sort_index()
    return df_final

# Функция для совмещенного ABC/XYZ-анализа
def perform_abc_xyz_analysis(df, demand_columns, sales_columns):
    """
    Выполняет совмещенный ABC/XYZ-анализ.
    Возвращает DataFrame с колонками: 'abc' и 'xyz' вместе с дополнительными метриками.
    """
    try:
        df_xyz = perform_xyz_analysis(df.copy(), demand_columns)
        df_abc = perform_abc_analysis(df.copy(), sales_columns)
        # Объединяем результаты по индексу (или по уникальному идентификатору, если он есть)
        df_combined = df_xyz.join(df_abc[['abc']], how='left')
        logger.info("ABC/XYZ-анализ успешно объединён")
        return df_combined
    except Exception as e:
        logger.error("Ошибка в совмещённом анализе: %s", e)
        raise e

# Пример данных – предположим, что у нас есть продажи за 4 недели
data = {
    'item': ['Товар A', 'Товар B', 'Товар C', 'Товар D', 'Товар E'],
    'week_1': [100, 20, 5, 80, 1],
    'week_2': [110, 22, 8, 75, 0],
    'week_3': [105, 18, 7, 82, 2],
    'week_4': [98, 25, 6, 78, 1]
}

df = pd.DataFrame(data)
# Для XYZ-анализ используем колонки с данными спроса, для ABC – те же продажи
result_df = perform_abc_xyz_analysis(df, demand_columns=['week_1', 'week_2', 'week_3', 'week_4'], sales_columns=['week_1', 'week_2', 'week_3', 'week_4'])

print(result_df[['item', 'abc', 'mean', 'std', 'cv', 'xyz']])