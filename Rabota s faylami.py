import datetime
import pypyodbc
import shutil  # копирование файлов
import sys
import os
import re
import win32.client as win32
from PIL import Image  # pip install Pillow

dsn = r"Driver={Microsoft Access Driver (*.mdb, *.accdb)};Dbq=f:\Baza\7km\Barkode\Nomenklatura\TableNomenklatura.accdb;"
connaccess = pypyodbc.connect(dsn)
cur = connaccess.cursor()
newPapka = "F:\\Baza\\7km\\Foto\\MK\\Site\\PoSezonam\\"
papkaResize = r'F:\Baza\7km\Foto\MK\Site\PoSezonam\800x600'

# Есть наименование папки Н: BSC003050509W35
# 1. определим сезон Н: 20Y
# 2. в конце папки добавим сезон. Н: BSC003050509W35 20Y
# 3. Все файлы в данной папке переименуем как имя папки + сезон + порядковый номер. Н: BSC003050509W35 20Y 0; BSC003050509W35 20Y 1;...
# 4. Изменим размер фото 800х600
# 5. Создадим папку, (если ее нет) в каталоге papkaResize. Н: papkaResize + '\\' + s_sezon
# 6. Перенесем файл с расширение 800х600 в папку papkaResize + '\\' + s_sezon + '\\'
# 
# 
# 

def tsikPoPapkamIfaylam(pathName):
    global newPapka

    # цикл по папкам и файлам в папке pathName
    for root, dirs, files in os.walk(pathName):
        try:
            papka = os.path.split(root)[-1]
            print('путь ', root)
            print('крайняя папка ', papka)
            print('папки ', dirs)
            print('файлов ', len(files), files)
            print(os.path.split(root)[0])

            # если в папке содержатся файлы
            if len(files) != 0:

                # 1. определяем сезон у papka. papka - наименование артикула
                s_sezon = dfSezon(papka)
                # print(s_sezon != None)

                if s_sezon != None:  # сезон определился

                    # наименование сезона нет в наименовании папки. Т.е еще не переименовывали
                    if not (s_sezon.upper() in papka):

                        # переименовывем файлы в соответствии с наименованием papka (артикулом товара)
                        # наименование файла = наименованию папки = наименованию артикула(15 символов)
                        dfRenameFiles(papka, root, s_sezon) # пункты 2,3,4,5,6 из задания

                        # создаем папку. к Пути newPapka справа добавляем s_sezon (сезон) и в ней papka (артикул товара) + сезон
                        # Н: '20Y\\BSC003050509W35 20Y'
                        newPapka += s_sezon + '\\' + papka + ' ' + s_sezon
                        # os.makedirs(newPapka, exist_ok=True)

                        # переименовываем папку - в конце папки дописываем сезон
                        papkaSezon = os.path.split(
                            root)[0] + '\\' + papka + ' ' + s_sezon

                        os.rename(os.path.split(root)[
                                  0] + '\\' + papka, papkaSezon)

                        # копируем папку (папка = наименованию артикула) в соответствующий сезон
                        shutil.copytree(papkaSezon, newPapka)

        except Exception as e:
            print(str(e))
            if e.errno != 17:  # папка существует
                print('папка %s существует' % papka)
            continue


def dfImageResizer(fileName, s_sezon):
    # https://auth0.com/blog/image-processing-in-python-with-pillow/
    # изменяет размер изображения
    global newPapka

    # меняем размер изображения
    try:
        # Имя файла
        base_name = os.path.basename(fileName)

        image = Image.open(fileName)
        new_image = image.resize((600, 800))
        
        # sNewPath = r'F:\Baza\7km\Foto\MK\Site\PoSezonam\800x600' + '\\' + s_sezon + '\\'
        sNewPath = papkaResize + '\\' + s_sezon + '\\'

        if not os.path.exists(sNewPath):
            # папки нет. Создаем
            os.mkdir(sNewPath)

        # Сохраняем файл в новой папке
        new_image.save(sNewPath + base_name)

    except Exception as e:
        print(str(e))


def dfSezon(filename):
    # подключается к базе и определяет сезон у модели filename
    while True:
        try:
            intab = '~@#$%^&*()=_-+/|?<>,'
            outtab = '                    '
            filename.translate(str.maketrans(intab, outtab))

            # Удаляем пробелы в начале и в конце строки
            filename = filename.strip()

            left15 = filename[:filename.rfind(' ')] if (filename.rfind(
                ' ') < 15) and (filename.rfind(' ') > 0) else filename[:15]

            strSql = '''
                SELECT DISTINCT TOP 1 Barkod.SEZON
                FROM Barkode_model15
                WHERE left15 = '%s';
                ''' % left15

            # print(strSql)
            cur.execute(strSql)
            # for row in cur.description: #наименование колонок
            #     print (row[0])
            row = cur.fetchone()
            if not row:
                break
            print(row[0])

            return row[0].upper()

        except Exception as e:
            print(str(e))


def dfRenameFiles(papka, ImaPapki, s_sezon):
    # в папке ImaPapki переименовывает файлы с расширением ('.jpg', '.png', '.jpeg')
    i = 0
    new_abs_file_name = ''

    for FILE_NAME in sorted(os.listdir(ImaPapki)):

        try:
            # base_name- Имя файла; ext - расширение
            base_name, ext = os.path.splitext(FILE_NAME)

            print(papka[:15].upper(), base_name[:15].upper())

            # # Нужны файлы с расширениями
            # if re.match(r'\w*(.jpg|.png|.jpeg)\b', FILE_NAME):
            #     print(FILE_NAME)  # выводим имя файла

            if ext.lower() not in ['.jpg', '.jpeg', '.png']:
                # Пропускаем файлы, если не соответствуют расширению
                continue
            
            print(FILE_NAME)  # выводим имя файла

            # Полный путь к текущему файлу
            abs_file_name = os.path.join(ImaPapki, FILE_NAME)

            # resize image & copi to folder. Пункты 4,5,6 из задания
            dfImageResizer(abs_file_name, s_sezon)

            # Полный путь к текущему файлу с новым названием
            # Наименование файла = наименованию папки (не путь!) + сезон + i + расширение
            new_abs_file_name = os.path.join(
                ImaPapki + '\\' + papka + ' ' + s_sezon + ' ' + str(i) + ext)

            os.rename(abs_file_name, new_abs_file_name)

            i += 1

        except Exception as e:
            print(str(e))
            continue


def dfXlsToXlsx(sFullPathToXslFile):
    # все файлы xls сохраняет в формате xlsx
    try:
        excel = win32.gencache.EnsureDispatch('Excel.Application')
        wb = excel.Workbooks.Open(sFullPathToXslFile)

        # FileFormat = 51 is for .xlsx extension; #FileFormat = 56 is for .xls extension
        wb.SaveAs(sFullPathToXslFile+"x", FileFormat=51)
    except Exception as e:
        print(str(e))

    finally:
        wb.Close()
        excel.Application.Quit()


try:
    # работаем в папке (создаем в ней папки, файлы...)
    PathDefaultInDisk = r"F:\Baza\7km\Foto\MK\Site\2020.05.23"

    tsikPoPapkamIfaylam(PathDefaultInDisk)

except Exception as e:
    print(str(e))
    # выход с нуля, который обычно интерпретируется как успех. Ненулевые коды обычно рассматриваются как ошибки. По умолчанию выход с нуля.
    sys.exit(0)

finally:
    print('ЗАВЕРШЕНО', datetime.datetime.now())

