import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_sale_of_goods_services"
DOCUMENT = "Document_РеализацияТоваровУслуг"

SELECT_COLUMNS = """Ref_Key,DataVersion,Number,Date,Posted,Организация_Key,ОтражатьВУправленческомУчете,
    ОтражатьВБухгалтерскомУчете,Комментарий,Сделка,Склад_Key,Контрагент_Key,ТипЦен_Key,
    ВалютаДокумента_Key,УчитыватьНД<PERSON>,СуммаВключаетНДС,КурсВзаиморасчетов,СуммаДокумента,КратностьВзаиморасчетов,
    АдресДоставки,СуммаДоставки,ДоговорКонтрагента_Key,ОснованиеВозвратОтПокупателя_Key"""

SQL_CREATE_TABLE = f"""        
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NULL,  -- DataVersion
            doc_number varchar(50) NULL,  -- Number
            doc_date timestamp(0) NULL,  -- Date
            posted bool DEFAULT false NULL,  -- Posted
            is_management bool DEFAULT false NULL,  -- ОтражатьВУправленческомУчете
            is_accounting bool DEFAULT false NULL,  -- ОтражатьВБухгалтерскомУчете
            description text NULL,  -- Комментарий
            consider_vat bool DEFAULT false NULL,  -- УчитыватьНДС
            amount_includes_vat bool DEFAULT false NULL,  -- СуммаВключаетНДС
            rate numeric(10, 4) DEFAULT 0 NOT NULL,  -- Курс за 1 уе
            rate_settlement numeric(10, 4) DEFAULT 0 NOT NULL,  -- КурсВзаиморасчетов
            rate_nbu numeric(10, 4) DEFAULT 0 NOT NULL,  -- курс НБУ
            logistics_costs varchar(10) DEFAULT '0' NOT NULL,  -- Стоимость доставки
            document_amount numeric(15, 4) DEFAULT 0 NOT NULL,  -- СуммаДокумента
            multiplicity_of_mutual_settlements numeric(10, 4) DEFAULT 0 NOT NULL,  -- КратностьВзаиморасчетов
            delivery_address varchar NULL,  -- АдресДоставки
            ref_key varchar(50) NOT NULL,  -- Ref_Key
            warehouse_key varchar(50) NULL,  -- Склад_Key
            organization_key varchar(50) NULL,  -- Организация_Key
            account_key varchar(50) NULL,  -- Контрагент_Key
            contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
            price_type_key varchar(50) NULL,  -- ТипЦен_Key
            currency_key varchar(50) NULL,  -- ВалютаДокумента_Key
            return_owner_key varchar(50) NULL,  -- ОснованиеВозвратОтПокупателя_Key
            deal varchar(50) NULL,  -- Сделка
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
        COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
        COMMENT ON COLUMN {TABLE_NAME}.is_management 
            IS 'ОтражатьВУправленческомУчете';
        COMMENT ON COLUMN {TABLE_NAME}.is_accounting 
            IS 'ОтражатьВБухгалтерскомУчете';
        COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
        COMMENT ON COLUMN {TABLE_NAME}.deal IS 'Сделка';
        COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
        COMMENT ON COLUMN {TABLE_NAME}.logistics_costs IS 'Стоимость доставки';
        COMMENT ON COLUMN {TABLE_NAME}.rate IS 'Курс за 1 уе';
        COMMENT ON COLUMN {TABLE_NAME}.rate_settlement IS 'КурсВзаиморасчетов';
        COMMENT ON COLUMN {TABLE_NAME}.rate_nbu IS 'курс НБУ';
        COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
        COMMENT ON COLUMN {TABLE_NAME}.price_type_key IS 'ТипЦен_Key';
        COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
        COMMENT ON COLUMN {TABLE_NAME}.consider_vat IS 'УчитыватьНДС';
        COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
        COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements 
            IS 'КратностьВзаиморасчетов';
        COMMENT ON COLUMN {TABLE_NAME}.delivery_address IS 'АдресДоставки';
        COMMENT ON COLUMN {TABLE_NAME}.return_owner_key IS 'ОснованиеВозвратОтПокупателя_Key';
    """

SQL_UPDATE_CONTRACT_KEY = """
    -- Документ КорректировкаДолга исправляет указанный неправильно в РН номер договора
    -- от этого зависит кол-во дней отсрочки платежа
    
    UPDATE t_one_doc_sale_of_goods_services AS sale
    SET  contract_key = debt.counterparty_contract_key
    FROM t_one_doc_debt_correction_debt_amount AS sub_debt
        INNER JOIN t_one_doc_debt_correction AS debt
            ON debt.ref_key = sub_debt.ref_key		
    WHERE debt.counterparty_debtor_key = debt.counterparty_creditor_key
        AND operation_type IN ('ПереносЗадолженности')
        AND sale.ref_key = sub_debt.document_key
    ;
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        dataversion,
        doc_number,
        doc_date,
        posted,
        organization_key,
        is_management,
        is_accounting,
        description,
        deal,
        warehouse_key,
        account_key,
        price_type_key,
        currency_key,
        consider_vat,
        amount_includes_vat,
        rate_settlement,
        document_amount,
        multiplicity_of_mutual_settlements,
        delivery_address,
        logistics_costs,
        contract_key,
        return_owner_key
        ) VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_number = EXCLUDED.doc_number,
        doc_date = EXCLUDED.doc_date,
        posted = EXCLUDED.posted,
        organization_key = EXCLUDED.organization_key,
        is_management = EXCLUDED.is_management,
        is_accounting = EXCLUDED.is_accounting,
        description = EXCLUDED.description,
        deal = EXCLUDED.deal,
        warehouse_key = EXCLUDED.warehouse_key,
        account_key = EXCLUDED.account_key,
        price_type_key = EXCLUDED.price_type_key,
        currency_key = EXCLUDED.currency_key,
        consider_vat = EXCLUDED.consider_vat,
        amount_includes_vat = EXCLUDED.amount_includes_vat,
        rate_settlement = EXCLUDED.rate_settlement,
        document_amount = EXCLUDED.document_amount,
        multiplicity_of_mutual_settlements = EXCLUDED.multiplicity_of_mutual_settlements,
        delivery_address = EXCLUDED.delivery_address,
        logistics_costs = EXCLUDED.logistics_costs,
        contract_key = EXCLUDED.contract_key,
        return_owner_key = EXCLUDED.return_owner_key
    ;
    """
    return sql.replace("'", "")


async def main_doc_sale_of_goods_services_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(23)
    sql = await sql_insert(maket)
    sql = sql.replace("$4", "to_timestamp($4, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")
    await async_sql_create_index(TABLE_NAME, "account_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")
    await async_save_pg(SQL_UPDATE_CONTRACT_KEY)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_sale_of_goods_services_async())
