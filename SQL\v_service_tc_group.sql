
    CREATE OR REPLACE VIEW v_service_tc_group
    AS SELECT 
        serv.organizations,
        serv.client,
        serv.serv_date,
        serv.tarih,
        serv.doc_number,
        serv.cur_type AS doviz,
        COALESCE(serv.giris_tutar_usd,0)::numeric(10,4) AS giris_tutar,
        COALESCE(serv.rate_settlement,0)::numeric(10,4) AS kur,
        COALESCE(gtd.gtd_sum_ue,0) AS gtd_sum_ue,
        COALESCE(dop.dop_sum_ue,0)::numeric(10,4) AS dop_sum_ue,
        (COALESCE(gtd.gtd_sum_ue,0) + COALESCE(dop.dop_sum_ue,0))::numeric(10,4) AS toplam_gider,
        ((COALESCE(gtd.gtd_sum_ue,0) + COALESCE(dop.dop_sum_ue,0)) 
            / COALESCE(serv.giris_tutar_usd,0))::numeric(10,4) AS gider_koef,
        serv.batch_document
       FROM v_service_tc serv
         LEFT JOIN ( SELECT batch_document,
                sum(COALESCE(gtd_sum_ue,0)) AS gtd_sum_ue
               FROM v_service_tc_gtd
              GROUP BY batch_document) gtd ON gtd.batch_document::text = serv.batch_document::text
         LEFT JOIN ( SELECT batch_document,
                sum(COALESCE(dop_sum_ue,0)) AS dop_sum_ue
               FROM v_service_tc_dop
              GROUP BY batch_document) dop ON dop.batch_document::text = serv.batch_document::text
      ORDER BY serv.serv_date DESC, serv.doc_number;

    -- Permissions

    ALTER TABLE v_service_tc_group OWNER TO postgres;
    GRANT ALL ON TABLE v_service_tc_group TO postgres;
    GRANT SELECT ON TABLE v_service_tc_group TO user_prestige;
    