    DROP VIEW IF EXISTS v_one_tax_and_sale_amount_different CASCADE;
    CREATE OR REPLACE VIEW v_one_tax_and_sale_amount_different AS
    SELECT  
        client.manager,
        client.segment,
        client.customer,
        t.doc_period,
        t.doc_type,
        round(sum(t.document_amount),2) AS amount
    FROM (
        SELECT
            to_char(doc_date,'YYYY.MM') AS doc_period,
            doc_date,
            doc_number,
            document_amount,
            'taxРК' AS doc_type,
            account_key,
            document_currency_key    
        FROM t_one_doc_tax_appendix_2
        WHERE posted 
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
        UNION ALL
        SELECT
            to_char(doc_date,'YYYY.MM') AS doc_period,
            doc_date,
            doc_number,
            document_amount,
            'taxНН' AS doc_type,
            account_key,
            document_currency_key    
        FROM t_one_doc_tax_sale
        WHERE posted 
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
        UNION ALL
        SELECT
            to_char(doc_date,'YYYY.MM') AS doc_period,
            doc_date,
            doc_number,
            -document_amount,
            'РН' AS doc_type,
            account_key,
            currency_key
        FROM t_one_doc_sale_of_goods_services main
        WHERE posted 
            AND is_accounting 
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
        UNION ALL
        SELECT
            to_char(doc_date,'YYYY.MM') AS doc_period,
            doc_date,
            doc_number,
            document_amount,
            'ВН' AS doc_type,
            account_key,
            currency_key
        FROM t_one_doc_return_of_goods_from_customers main
        WHERE posted 
            AND is_accounting 
            AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'
    ) AS t
    LEFT JOIN 
        (
        SELECT DISTINCT 
            manager,
            segment,
            customer,
            customer_key
        FROM v_one_manager_counterparty_contracts_segments 
        ) AS client
        ON client.customer_key = t.account_key
    GROUP BY 
        client.manager,
        client.segment,
        client.customer,
        t.doc_period,
        t.doc_type
    ORDER BY 
        client.manager,
        client.segment,
        client.customer,
        t.doc_period,
        t.doc_type
    ;

    COMMENT ON VIEW v_one_tax_and_sale_amount_different IS 'расхождение сумм между налоговой накладной и расходной';
    GRANT SELECT ON TABLE  v_one_tax_and_sale_amount_different TO user_prestige;
