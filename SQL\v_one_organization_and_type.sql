
    CREATE OR REPLACE VIEW v_one_organization_and_type AS
    SELECT 
        org.ref_key,
        org.description AS organization,
        (
        SELECT trim(value)
        FROM regexp_split_to_table(STRING_AGG(codes.number_registration, ', '), ',') AS value
        WHERE value != ''
        LIMIT 1)    
        AS number_registration,
        (
        SELECT trim(value)
        FROM regexp_split_to_table(STRING_AGG(codes.inn_okpo, ', '), ',') AS value
        WHERE value != ''
        LIMIT 1)  AS inn_okpo,
        CASE 
            WHEN (SELECT length(trim(value))
                FROM regexp_split_to_table(STRING_AGG(codes.inn_okpo, ', '), ',') AS value
                WHERE value != ''
                LIMIT 1) = 8 THEN True
            ELSE False
        END::boolean AS organization_type
    FROM t_one_cat_organizations AS org
        LEFT JOIN t_one_infreg_organization_codes AS codes
            ON org.ref_key = codes.organization_key 
    GROUP BY org.ref_key, org.description
    ORDER BY org.description
    ;

    COMMENT ON VIEW v_one_organization_and_type IS 'организации с юр типом';

