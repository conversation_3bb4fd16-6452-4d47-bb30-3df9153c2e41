import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_incoming_additional_expenses_goods"
DOCUMENT = "Document_ПоступлениеДопРасходов_Товары"
SELECT_COLUMNS = (
    "Ref_Key,LineNumber,ЕдиницаИзмерения_Key,Номенклатура_Key,ДокументПартии,Количество,Коэффициент,Сумма,"
    "СуммаНДС,СуммаТовара"
)

SQL_CREATE_TABLE = f"""        
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        idt serial4 NOT NULL,
        line_number numeric(7) NOT NULL,  -- LineNumber
        amount numeric(10, 2) NOT NULL DEFAULT 0,  -- Количество
        coefficient numeric(10, 2) NOT NULL DEFAULT 0,  -- Коэффициент
        doc_sum numeric(10, 3) NOT NULL DEFAULT 0,  -- Сумма
        amount_vat numeric(10, 3) NOT NULL DEFAULT 0,  -- СуммаНДС
        amount_of_goods numeric(16, 4) NOT NULL DEFAULT 0,  -- СуммаТовара
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        nomenclature_key varchar(50) NOT NULL,  -- Номенклатура_Key
        unit_of_key varchar(50) NOT NULL,  -- ЕдиницаИзмерения_Key
        batch_document varchar(50) NOT NULL,  -- ДокументПартии
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.doc_sum IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_of_goods IS 'СуммаТовара';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.batch_document IS 'ДокументПартии';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        line_number,
        unit_of_key,
        nomenclature_key,
        batch_document,
        amount,
        coefficient,
        doc_sum,
        amount_vat,
        amount_of_goods
    )
    VALUES{maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        unit_of_key = EXCLUDED.unit_of_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        batch_document = EXCLUDED.batch_document,
        amount = EXCLUDED.amount,
        coefficient = EXCLUDED.coefficient,
        doc_sum = EXCLUDED.doc_sum,
        amount_vat = EXCLUDED.amount_vat,
        amount_of_goods = EXCLUDED.amount_of_goods
    ;
    """
    return sql.replace("'", "")


async def main_incoming_additional_expenses_goods_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "ref_key")
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_incoming_additional_expenses_goods_async())
