# https://www.youtube.com/watch?v=iP1Ko9sFkzs

import win32com.client as client # pip install pywin32 or pip install pypiwin32

# startup outlook instance
outlook = client.Dispatch('Outlook.Application')

# get namespace so that we can access folders
namespace = outlook.GetNameSpace('MAPI')

# get the inbox folder, specifically
inbox = namespace.Folders['Inbox']

# alternatively you could have used the following
inbox = namespace.GetDefaultFolder(6)
