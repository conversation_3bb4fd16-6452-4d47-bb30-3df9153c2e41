import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_cash_warrant_receipt_details"
DOCUMENT = "Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа"
SELECT_COLUMNS = ("LineNumber,КратностьВзаиморасчетов,КурсВзаиморасчетов,СуммаВзаиморасчетов,СуммаНДС,СуммаПлатежа,"
                 "Ref_Key,ДоговорКонтрагента_Key,СтатьяДвиженияДенежныхСредств_Key,СчетУчетаРасчетовСКонтрагентом_Key,"
                  "Сделка,Сделка_Type")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME}(
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        multiplicity_settlements numeric(10, 4) NOT NULL DEFAULT 0,  -- КратностьВзаиморасчетов
        settlement_rate numeric(10, 4) NOT NULL DEFAULT 0,  -- КурсВзаиморасчетов
        amount_of_settlements numeric(15, 4) NULL,  -- СуммаВзаиморасчетов
        amount_vat numeric(15, 4) NOT NULL DEFAULT 0,  -- СуммаНДС
        amount_of_payment numeric(15, 4) NULL,  -- СуммаПлатежа
        ref_key varchar(50) NULL,  -- Ref_Key
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        cash_flow_item varchar(50) NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        counterparty_account_key varchar(50) NULL,  -- СчетУчетаРасчетовСКонтрагентом_Key
        base_key varchar(50) NULL,  -- Сделка
        base_type varchar(50) NULL,  -- Сделка_Type
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
        
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.multiplicity_settlements IS 'КратностьВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_rate IS 'КурсВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.amount_of_settlements IS 'СуммаВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_of_payment IS 'СуммаПлатежа';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.counterparty_account_key IS 'СчетУчетаРасчетовСКонтрагентом_Key';
    COMMENT ON COLUMN {TABLE_NAME}.base_key IS 'Сделка';
    COMMENT ON COLUMN {TABLE_NAME}.base_type IS 'Сделка_Type';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        line_number,
        multiplicity_settlements,
        settlement_rate,
        amount_of_settlements,
        amount_vat,
        amount_of_payment,
        ref_key,
        contract_key,
        cash_flow_item,
        counterparty_account_key,
        base_key,
        base_type
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        multiplicity_settlements = EXCLUDED.multiplicity_settlements,
        settlement_rate = EXCLUDED.settlement_rate,
        amount_of_settlements = EXCLUDED.amount_of_settlements,
        amount_vat = EXCLUDED.amount_vat,
        amount_of_payment = EXCLUDED.amount_of_payment,
        contract_key = EXCLUDED.contract_key,
        cash_flow_item = EXCLUDED.cash_flow_item,
        counterparty_account_key = EXCLUDED.counterparty_account_key,
        base_key = EXCLUDED.base_key,
        base_type = EXCLUDED.base_type
    '''
    return sql.replace("'", "")


async def main_doc_cash_warrant_receipt_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "ref_key")
    await async_sql_create_index(TABLE_NAME, "cash_flow_item")
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_warrant_receipt_details_async())
