DROP VIEW IF EXISTS v_one_cash_order_receipt CASCADE;
CREATE OR REPLACE VIEW v_one_cash_order_receipt AS
    SELECT
        r.doc_date::date AS doc_date,
        r.doc_date AS tarih,
        r.doc_number,
        clients.description AS customer,
        contracts.description AS contract,
        coalesce(rd.amount_of_payment,0) as amount_of_payment,
        coalesce(rd.amount_vat,0) as amount_vat,
        sum(rd.amount_of_payment) OVER(PARTITION BY rd.ref_key) AS amount_of_payment_sum,
        sum(rd.amount_vat) OVER(PARTITION BY rd.ref_key) AS amount_of_vat_sum,
        CASE
            WHEN r.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN --uah
                round(sum(rd.amount_of_payment) OVER(PARTITION BY rd.ref_key) /rate.rate_usd_nbu,3)
            ELSE
                round(sum(rd.amount_of_payment) OVER(PARTITION BY rd.ref_key),3)
        END AS amount_of_payment_sum_usd,
        CASE
            WHEN r.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN --uah
                round(sum(rd.amount_vat) OVER(PARTITION BY rd.ref_key) / rate.rate_usd_nbu,3)
            ELSE
                round(sum(rd.amount_vat) OVER(PARTITION BY rd.ref_key),3)
        END AS amount_of_vat_sum_usd,
        coalesce(contracts.limit_days_credit_ua,0) AS contract_days,
        rate.rate_usd_nbu,
        cur.description AS currency,
        item.description AS main_cash_flow_item_name,
        item_det.description AS det_cash_flow_item_name,
        r.a_comment,
        rd.line_number,
        r.ref_key,
        r.organization_key,
        r.currency_key,
        r.account_key,
        rd.contract_key,
        r.cash_flow_item AS main_cash_flow_item_key,
        rd.cash_flow_item AS det_cash_flow_item_key,
        r.is_management
    FROM t_one_doc_cash_order_receipt AS r
        LEFT JOIN t_one_doc_cash_order_receipt_details AS rd
            USING (ref_key)
        LEFT JOIN t_one_cat_currencies AS cur
            ON r.currency_key = cur.ref_key
        LEFT JOIN t_one_cat_counterparties AS clients
            ON r.account_key = clients.ref_key
        LEFT JOIN t_one_cat_contracts_counterparties AS contracts
            ON rd.contract_key = contracts.ref_key
        LEFT JOIN t_rate_nbu AS rate
            ON rate.rate_date::date = r.doc_date::date
        LEFT JOIN t_one_cat_cash_flow_item AS item
            ON r.cash_flow_item = item.ref_key
        LEFT JOIN t_one_cat_cash_flow_item AS item_det
            ON rd.cash_flow_item = item_det.ref_key
    WHERE r.posted
    ORDER BY r.doc_date, r.doc_number
;

COMMENT ON VIEW v_one_cash_order_receipt IS 'ПлатежноеПоручениеВходящее';

COMMENT ON COLUMN v_one_cash_order_receipt.doc_number IS 'номер документа';
COMMENT ON COLUMN v_one_cash_order_receipt.customer IS 'контрагент';
COMMENT ON COLUMN v_one_cash_order_receipt.contract IS 'договор';
COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_payment IS 'сумма оплаты';
COMMENT ON COLUMN v_one_cash_order_receipt.amount_vat IS 'НДС';
COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_payment_sum IS 'сумма по документу';
COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_vat_sum IS 'НДС по документу';
COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_payment_sum_usd IS 'сумма по документу usd';
COMMENT ON COLUMN v_one_cash_order_receipt.amount_of_vat_sum_usd IS 'НДС по документу usd';
COMMENT ON COLUMN v_one_cash_order_receipt.contract_days IS 'лимит дней задолженности';
COMMENT ON COLUMN v_one_cash_order_receipt.rate_usd_nbu IS 'курс НБУ';
COMMENT ON COLUMN v_one_cash_order_receipt.currency IS 'валюта';
COMMENT ON COLUMN v_one_cash_order_receipt.ref_key IS 'Ref_Key';
COMMENT ON COLUMN v_one_cash_order_receipt.currency_key IS 'Валюта_Key';
COMMENT ON COLUMN v_one_cash_order_receipt.account_key IS 'Контрагент_Key';
COMMENT ON COLUMN v_one_cash_order_receipt.contract_key IS 'ДоговорКонтрагента_Key';
COMMENT ON COLUMN v_one_cash_order_receipt.organization_key IS 'Организация_Key';
COMMENT ON COLUMN v_one_cash_order_receipt.doc_date IS 'датаДок';
COMMENT ON COLUMN v_one_cash_order_receipt.tarih IS 'датаДок';
COMMENT ON COLUMN v_one_cash_order_receipt.a_comment IS 'комментарий';
COMMENT ON COLUMN v_one_cash_order_receipt.line_number IS 'номер строки';
COMMENT ON COLUMN v_one_cash_order_receipt.main_cash_flow_item_name IS 'СтатьяДвиженияДенежныхСредств';
COMMENT ON COLUMN v_one_cash_order_receipt.det_cash_flow_item_name IS 'СтатьяДвиженияДенежныхСредствДет';
COMMENT ON COLUMN v_one_cash_order_receipt.main_cash_flow_item_key IS 'СтатьяДвиженияДенежныхСредств_Key';
COMMENT ON COLUMN v_one_cash_order_receipt.det_cash_flow_item_key IS 'СтатьяДвиженияДенежныхСредствДет_Key';
