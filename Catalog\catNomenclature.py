# получение номенклатуры из 1С и добавление в базу pg {TABLE_NAME}
# 09.09.2022
import asyncio
import inspect
import os
import sys
from pathlib import Path

# Добавляем путь к родительской директории в sys.path
sys.path.append(Path(__file__).parent.parent.__str__())

import asyncpg
import pandas as pd

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table, \
    engine, CONN_PARAMS, add_to_log
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
ISNULL = "00000000-0000-0000-0000-000000000000"

TABLE_NAME = "t_one_cat_nomenclature"
DOCUMENT = "Catalog_Номенклатура"

SELECT_COLUMNS = ("Ref_Key,БазоваяЕдиницаИзмерения_Key,Code,DeletionMark,Description,IsFolder,Predefined,Услуга,"
                 "КоличествоМесяцевХранения,Parent_Key,НоменклатураГТД,ВидНоменклатуры_Key,Артикул,"
                  "ОсновнойПоставщик_Key,Комментарий,ЕдиницаХраненияОстатков_Key,ЕдиницаДляОтчетов_Key")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        article varchar(20) NULL,  -- Артикул
        code bpchar(10),  -- Code
        description varchar(150),  -- Description
        count_of_months_of_storage int default 0,  -- КоличествоМесяцевХранения
        coefficient_report_unit numeric(10, 3) NOT NULL DEFAULT 0,  -- Коэффициент ЕдиницаДляОтчетов
        deletion_mark bool,  -- DeletionMark
        isfolder bool,  -- IsFolder
        predefined bool,  -- Predefined
        service bool,  -- Услуга
        comment varchar(150),  -- Комментарий
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        base_unit_of_key varchar(50),  -- БазоваяЕдиницаИзмерения_Key
        report_unit_key varchar(50),  -- ЕдиницаДляОтчетов_Key
        stock_unit_key varchar(50),  -- ЕдиницаХраненияОстатков_Key для регистров
        parent_key  varchar(50),  -- Parent_Key
        item_type_key varchar(50) NOT NULL,  -- ВидНоменклатуры_Key
        gtd_uktzt_key varchar(50),  -- НоменклатураГТД_key,
        supplier_key varchar(50),  -- ОсновнойПоставщик_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';

    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.base_unit_of_key IS 'БазоваяЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.count_of_months_of_storage IS 'КоличествоМесяцевХранения';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.service IS 'Услуга';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.gtd_uktzt_key IS 'НоменклатураГТД_key';
    COMMENT ON COLUMN {TABLE_NAME}.item_type_key IS 'ВидНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.article IS 'Артикул';
    COMMENT ON COLUMN {TABLE_NAME}.supplier_key IS 'ОсновнойПоставщик_Key';
    COMMENT ON COLUMN {TABLE_NAME}.comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.stock_unit_key IS 'ЕдиницаХраненияОстатков_Key';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient_report_unit IS 'Коэффициент ЕдиницаДляОтчетов';
    COMMENT ON COLUMN {TABLE_NAME}.report_unit_key IS 'ЕдиницаДляОтчетов_Key';

    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;
    '''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}
        (
            ref_key,
            base_unit_of_key,
            code,
            deletion_mark,
            description,
            isfolder,
            predefined,
            service,
            count_of_months_of_storage,
            parent_key,
            gtd_uktzt_key,
            item_type_key,
            article,
            supplier_key,
            comment,
            stock_unit_key,
            report_unit_key            
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            base_unit_of_key = EXCLUDED.base_unit_of_key,
            code = EXCLUDED.code,
            deletion_mark = EXCLUDED.deletion_mark,
            description = EXCLUDED.description,
            isfolder = EXCLUDED.isfolder,
            predefined = EXCLUDED.predefined,
            service = EXCLUDED.service,
            count_of_months_of_storage = EXCLUDED.count_of_months_of_storage,
            parent_key = EXCLUDED.parent_key,
            gtd_uktzt_key = EXCLUDED.gtd_uktzt_key,
            item_type_key = EXCLUDED.item_type_key,
            article = EXCLUDED.article,
            supplier_key = EXCLUDED.supplier_key,
            comment = EXCLUDED.comment,
            stock_unit_key = EXCLUDED.stock_unit_key,
            report_unit_key = EXCLUDED.report_unit_key
    ;
    '''
    return sql.replace("'", "")


ISNULL = "00000000-0000-0000-0000-000000000000"

# обновим ед.хранения отчетов.
SQL_UPDATE_REPORT_UNIT = f"""
    UPDATE t_one_cat_nomenclature main
    SET coefficient_report_unit = unit.coefficient 
    FROM (
        WITH 
        base_unit AS (
            SELECT 
                sub.*
            FROM t_one_cat_nomenclature main
                INNER JOIN t_one_cat_units AS sub
                    ON sub.nomenclature_key = main.ref_key	
            WHERE main.base_unit_of_key = sub.unit_by_classifier_key
        ),
        report_unit AS (
            SELECT 
                sub.*
            FROM t_one_cat_nomenclature main
                INNER JOIN t_one_cat_units AS sub
                    ON sub.nomenclature_key = main.ref_key	
            WHERE main.report_unit_key = sub.ref_key
        )
        SELECT 
            report_unit.coefficient/base_unit.coefficient AS coefficient,
            report_unit.nomenclature_key
        FROM base_unit
            INNER JOIN report_unit
            ON base_unit.nomenclature_key = report_unit.nomenclature_key
    ) AS unit
    WHERE unit.nomenclature_key = main.ref_key
    ;
"""


# Получение ref_key номенклатуры без ref_key поставщика
async def get_nomenclature_key_without_supplier_key_to_df():
    sql = f"""
        SELECT
            description,
            ref_key
        FROM {TABLE_NAME}
        WHERE not isfolder
            AND COALESCE(supplier_key,'{ISNULL}') = '{ISNULL}'
        ORDER BY description
        """
    df = pd.read_sql_query(sql, engine)
    return df


async def get_parent_and_supplier_keys(ref_keys):
    """Получает parent_key и supplier_key для нескольких ref_key за один запрос."""
    if not ref_keys:
        return {}
    
    # Удаляем дубликаты и None значения
    unique_keys = [key for key in set(ref_keys) if key]
    if not unique_keys:
        return {}
    
    placeholders = ",".join(f"'{key}'" for key in unique_keys)
    sql = f"""SELECT ref_key, parent_key, supplier_key
              FROM {TABLE_NAME}
              WHERE ref_key IN ({placeholders})"""
    
    try:
        async with asyncpg.create_pool(**CONN_PARAMS, min_size=1, max_size=10) as pool:
            async with pool.acquire() as conn:
                records = await conn.fetch(sql)
                return {r['ref_key']: (r['parent_key'], r['supplier_key']) for r in records}
    except Exception as e:
        logger.error(f"Ошибка при получении parent_key и supplier_key: {e}")
        return {}


async def find_supplier_key(ref_key, key_cache):
    """Рекурсивно ищет supplier_key, используя кэш для уменьшения запросов."""
    if not ref_key:
        return None, None
        
    if ref_key in key_cache:
        return key_cache.get(ref_key)
    
    # Собираем цепочку ref_keys для проверки
    keys_to_check = [ref_key]
    current_key = ref_key
    visited = {current_key}
    
    while True:
        # Получаем данные для текущего ключа
        keys_data = await get_parent_and_supplier_keys([current_key])
        
        # Проверяем, есть ли ключ в результатах
        if current_key not in keys_data:
            break
            
        parent_key, supplier_key = keys_data[current_key]
        
        if supplier_key and supplier_key != ISNULL:
            # Нашли непустой supplier_key
            key_cache[ref_key] = (parent_key, supplier_key)
            return parent_key, supplier_key
        
        if not parent_key or parent_key in visited:
            # Предотвращение циклов или достигли корня
            break
            
        keys_to_check.append(parent_key)
        current_key = parent_key
        visited.add(current_key)
    
    # Получаем все необходимые ключи за один запрос
    all_keys_data = await get_parent_and_supplier_keys(keys_to_check)
    
    # Находим первый непустой supplier_key, идя от родителя к потомку
    found_supplier = None
    for key in reversed(keys_to_check):
        if key in all_keys_data and all_keys_data[key][1] and all_keys_data[key][1] != ISNULL:
            found_supplier = all_keys_data[key][1]
            break
    
    # Обновляем кэш для исходного ключа
    parent_for_ref = all_keys_data.get(ref_key, (None, None))[0]
    key_cache[ref_key] = (parent_for_ref, found_supplier)
    
    return parent_for_ref, found_supplier


async def update_suppliers_fast():
    """
    Быстрый метод обновления supplier_key для всех записей, 
    использующий всего 3 запроса к БД и обработку в памяти
    """
    logger.info("Начало быстрого обновления supplier_key")
    
    # 1. Получаем ВСЕ записи из таблицы за один запрос (только нужные поля)
    sql_all_records = f"""
        SELECT ref_key, parent_key, supplier_key 
        FROM {TABLE_NAME}
        WHERE NOT isfolder OR parent_key IS NOT NULL
    """
    
    async with asyncpg.create_pool(**CONN_PARAMS, min_size=1, max_size=10) as pool:
        async with pool.acquire() as conn:
            # Выполняем запрос и получаем все записи
            records = await conn.fetch(sql_all_records)
            
            # 2. Строим иерархическую структуру в памяти
            # Словарь с данными каждой записи
            nodes = {r['ref_key']: {
                'parent_key': r['parent_key'], 
                'supplier_key': r['supplier_key'],
                'effective_supplier': None  # Это поле будем заполнять
            } for r in records}
            
            # 3. Находим корневые узлы (записи без родителей или с родителями не из нашего списка)
            root_nodes = {k for k, v in nodes.items() 
                         if not v['parent_key'] or v['parent_key'] not in nodes}
            
            # 4. Строим дерево от родителей к потомкам
            children = {k: [] for k in nodes}
            for ref_key, data in nodes.items():
                parent_key = data['parent_key']
                if parent_key and parent_key in nodes:
                    children[parent_key].append(ref_key)
            
            # 5. Проходим от корня к листьям, наследуя supplier_key
            # Функция для обработки узла и его детей
            def process_node(node_key, inherited_supplier=None):
                if node_key not in nodes:
                    return
                
                node = nodes[node_key]
                
                # Определяем effective_supplier для текущего узла
                if node['supplier_key'] and node['supplier_key'] != ISNULL:
                    # Если у узла есть свой supplier_key - используем его
                    effective_supplier = node['supplier_key']
                else:
                    # Иначе наследуем от родителя
                    effective_supplier = inherited_supplier
                
                # Сохраняем effective_supplier
                node['effective_supplier'] = effective_supplier
                
                # Обрабатываем детей этого узла с унаследованным effective_supplier
                for child_key in children[node_key]:
                    process_node(child_key, effective_supplier)
            
            # Запускаем обработку от корневых узлов
            for root_key in root_nodes:
                process_node(root_key)
            
            # 6. Готовим данные для обновления
            # Только записи без supplier_key но с найденным effective_supplier
            to_update = []
            for ref_key, data in nodes.items():
                if (data['supplier_key'] == ISNULL or not data['supplier_key']) and data['effective_supplier']:
                    to_update.append((data['effective_supplier'], ref_key))
            
            # 7. Выполняем массовое обновление за один запрос, если есть что обновлять
            if to_update:
                logger.info(f"Найдено {len(to_update)} записей для обновления supplier_key")
                
                # Используем более эффективный способ пакетного обновления
                batch_size = 5000  # Увеличиваем размер пакета для большей эффективности
                
                for i in range(0, len(to_update), batch_size):
                    batch = to_update[i:i+batch_size]
                    values_str = ",".join(f"('{supplier}', '{ref_key}')" for supplier, ref_key in batch)
                    
                    update_sql = f"""
                        UPDATE {TABLE_NAME} AS t
                        SET supplier_key = v.supplier
                        FROM (VALUES {values_str}) AS v(supplier, ref_key)
                        WHERE t.ref_key = v.ref_key
                    """
                    
                    await conn.execute(update_sql)
                    logger.info(f"Обновлено {len(batch)} записей (пакет {i//batch_size + 1}/{(len(to_update)-1)//batch_size + 1})")
                
                logger.info(f"Всего обновлено {len(to_update)} записей")
            else:
                logger.info("Нет записей для обновления supplier_key")
    
    logger.info("Завершено быстрое обновление supplier_key")
    return True


async def batch_update(pool, updates):
    """Выполняет пакетное обновление записей."""
    if not updates:
        return
        
    async with pool.acquire() as conn:
        # Подготавливаем запрос для массового обновления
        query = f"""
            UPDATE {TABLE_NAME} 
            SET supplier_key = $1 
            WHERE ref_key = $2
        """
        await conn.executemany(query, updates)

async def execute_with_retry(pool, sql, args=None, retry_count=3):
    """Выполняет SQL с повторными попытками при определенных ошибках."""
    for attempt in range(retry_count):
        try:
            async with pool.acquire() as conn:
                async with conn.transaction():
                    if args:
                        if isinstance(args[0], list):
                            return await conn.executemany(sql, *args)
                        else:
                            return await conn.execute(sql, *args)
                    else:
                        return await conn.execute(sql)
        except (asyncpg.exceptions.ConnectionDoesNotExistError, 
                asyncpg.exceptions.CannotConnectNowError) as e:
            logger.warning(f"Ошибка соединения, повтор {attempt + 1}/{retry_count}: {e}")
            if attempt + 1 == retry_count:
                raise
        except asyncpg.exceptions.DuplicateObjectError:
            return True
        except asyncpg.exceptions.DeadlockDetectedError as e:
            logger.warning(f"Обнаружена взаимная блокировка, повтор {attempt + 1}/{retry_count}: {e}")
            await asyncio.sleep(1 * (attempt + 1))  # Увеличивающаяся задержка
            if attempt + 1 == retry_count:
                raise
        except Exception as e:
            caller_info = get_caller_info()
            logger.error(f"Исключение: {caller_info}: {e}")
            logger.error(f"SQL: {sql}")
            
            await add_to_log(f"caller_info: {caller_info}; error: {str(e)}")
            raise
    
    return False

def get_caller_info():
    """Получает информацию о вызывающей функции и файле."""
    frame = inspect.currentframe()
    frames = inspect.getouterframes(frame)
    
    # Ищем первый фрейм, который не является текущей функцией
    for i, frame_info in enumerate(frames[1:], 1):
        if frame_info.function != get_caller_info.__name__:
            return f"{frame_info.filename}/{frame_info.function}"
    
    return "<unknown>/<unknown>"

async def main_cat_nomenclature_async(is_supplier_update=True):
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(17)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(SQL_UPDATE_REPORT_UNIT)
    await async_sql_create_index(TABLE_NAME, "description")
    if is_supplier_update:
        await update_suppliers_fast()
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_nomenclature_async())
    # asyncio.run(main_cat_nomenclature_async(False))
