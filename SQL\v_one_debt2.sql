--пока нигде не используется

DROP VIEW IF EXISTS v_one_debt2 CASCADE;

CREATE OR REPLACE VIEW v_one_debt2 AS
WITH v_one_debt_source AS (
    SELECT
        manager,
        segment_folder,
        segment,
        customer,
        dtperiod,
        to_char(dtperiod,'YYYY.MM') periods,
        doc_number,
        debt,
        credit,
        amount,
        costs,
        profit,
        currency,
        rate,
        recorder_type AS doc_type,
        recorder
    FROM (
        SELECT
            count(customer) OVER (PARTITION BY recorder) AS customers_count,
            manager,
            segment_folder,
            segment,
            customer,
            dtperiod,
            doc_number,
            sum(coalesce(debt,0)) debt,
            sum(coalesce(credit,0)) credit,
            sum(coalesce(amount,0)) amount,
            sum(coalesce(costs,0)) costs,
            sum(coalesce(amount,0)) + sum(coalesce(costs,0)) profit,
            currency,
            rate,
            CASE
                WHEN recorder_type = 'ВозвратТоваровОтПокупателя' THEN
                    'продажа'
                WHEN recorder_type = 'РеализацияТоваровУслуг' THEN
                    'продажа'
                WHEN recorder_type = 'ПоступлениеТоваровУслуг' THEN
                    'поступление'
                WHEN recorder_type = 'КорректировкаДолга' THEN
                    'взаимозачет'
            END recorder_type,
            -- REPLACE(recorder_type,'ВозвратТоваровОтПокупателя','РеализацияТоваровУслуг') AS recorder_type,
            recorder
        FROM (
            SELECT
                client.manager,
                client.segment_folder,
                client.segment,
                client.customer,
                reg.dtperiod::date dtperiod,
                reg.doc_number,
                COALESCE(CASE
                    WHEN reg.recordtype = 'Receipt' THEN
                        COALESCE(reg.amount,0)
                END,0) debt,
                COALESCE(CASE
                    WHEN reg.recordtype = 'Expense' THEN
                        -COALESCE(reg.amount,0)
                END,0) credit,
                CASE
                    WHEN reg.recordtype = 'Receipt' THEN
                        COALESCE(reg.amount,0)
                    ELSE
                        -COALESCE(reg.amount,0)
                END amount,
                -coalesce(amount_maliyet_uah,0) AS costs,
                cur.description AS currency,
                COALESCE(rate.rate_usd_nbu, rate.rate_1c) AS rate,
                REPLACE(reg.recorder_type,'StandardODATA.Document_','') AS recorder_type,
                reg.recorder
            FROM
                (   SELECT DISTINCT
                        dtperiod,
                        doc_number,
                        recorder_type,
                        sum(amount) as amount,
                        counterparty_key,
                        currency_key,
                        recordtype,
                        organization_key,
                        recorder
                    FROM t_one_doc_acc_reg_reciprocal_settlements_details
                    GROUP BY
                        dtperiod,
                        doc_number,
                        recorder_type,
                        counterparty_key,
                        currency_key,
                        recordtype,
                        organization_key,
                        recorder
                ) 
                 AS reg
                LEFT JOIN
                    (
                    SELECT DISTINCT
                        manager,
                        segment_folder,
                        segment,
                        customer,
                        customer_key
                    FROM v_one_manager_counterparty_contracts_segments
                    ) AS client
                    ON client.customer_key = reg.counterparty_key
                LEFT JOIN t_one_cat_currencies AS cur
                    ON cur.ref_key = reg.currency_key
                LEFT JOIN t_rate_nbu AS rate
                    ON rate.rate_date = reg.dtperiod::date
                LEFT JOIN
                    (SELECT
                        CASE
                            WHEN issale = 1 THEN
                                sum(amount_maliyet_uah)
                            ELSE
                                0
                        END AS amount_maliyet_uah,
                        ref_key
                    FROM t_one_sale
                    GROUP BY
                        ref_key,
                        issale
                    )AS csts
                    ON csts.ref_key = reg.recorder
            WHERE
                dtperiod::date >= '01.01.2024'::date
                AND dtperiod::date <= current_date
                AND organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
                AND counterparty_key <> 'f51a0e88-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
            ) AS t
        GROUP BY
            manager,
            segment_folder,
            segment,
            customer,
            dtperiod,
            doc_number,
            currency,
            rate,
            recorder_type,
            recorder
        ) AS tt
    WHERE customers_count = 1
        -- накладные по переоценке, переброске с фирмы на фирму, не учитываем
        AND recorder NOT IN
            (
                SELECT
                    main.ref_key
                FROM t_one_doc_sale_of_goods_services AS main
                    INNER JOIN
                        (SELECT
                            ref_key,
                            description AS warehouse
                        FROM t_one_cat_warehouses
                        WHERE description ILIKE '%ПЕРЕОЦІНКА%'
                            AND NOT deletionmark) AS depo
                        ON main.warehouse_key = depo.ref_key
                UNION ALL
                SELECT
                    main.ref_key
                FROM t_one_doc_return_of_goods_from_customers AS main
                    INNER JOIN
                        (SELECT
                            ref_key,
                            description AS warehouse
                        FROM t_one_cat_warehouses
                        WHERE description ILIKE '%ПЕРЕОЦІНКА%'
                            AND NOT deletionmark) AS depo
                        ON main.warehouse_key = depo.ref_key
                UNION ALL 
                SELECT 
                	ref_key  -- данные ref_key будут исключены из выборки
                FROM t_one_doc_debt_correction
                WHERE organization_key = 'f51a0e76-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ" 
					OR operation_type = 'ПереносЗадолженности'  -- с фирмы на другую фирму или между договорами не выводим
            )
)
    SELECT
        CASE  -- если услуг (накл поступление) получено больше, чем во взаимозачете кредита - учитываем поступления
            WHEN
                COALESCE(abs(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,'')
                    )),0)
                >
                COALESCE(abs(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,'')
                    )),0)
                    THEN

                        -COALESCE(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,'')
                            ),0)
            ELSE  -- иначе учитываем взаимозачет
                -COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,'')
                    ),0)
        END amount_segment,
        CASE  -- если услуг (накл поступление) получено больше, чем во взаимозачете кредита - учитываем поступления
            WHEN
                COALESCE(abs(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(customer,'')
                    )),0)
                >
                COALESCE(abs(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(customer,'')
                    )),0)
                    THEN

                        -COALESCE(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(customer,'')
                            ),0)
            ELSE  -- иначе учитываем взаимозачет
                -COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(customer,'')
                    ),0)
        END amount_customer,
        CASE -- высчитываем процент возврата
            WHEN
                COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'продажа')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(customer,'')
                    ),0) <> 0 AND doc_type = 'продажа' THEN

                        (100 * COALESCE(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'продажа')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(customer,'')
                            ),0)
                        /
                        COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'продажа')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(customer,'')
                            ),0))::numeric(10,2)
            ELSE
                0
        END return_percent,
        *
    FROM v_one_debt_source
    ORDER BY
        manager,
        segment_folder,
        segment,
        customer,
        dtperiod,
        doc_number,
        currency
    ;

COMMENT ON VIEW v_one_debt2 IS 'Вся дебиторка';
GRANT SELECT ON TABLE v_one_debt2 TO user_prestige;
