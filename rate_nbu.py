import os
import sys
from datetime import datetime, timedelta

import requests

sys.path.insert(0, r'd:\Prestige\Python\Config')
from configPrestige import URL_CONST

from logger_prestige import get_logger
from prestige_authorize import change_data_in_table_returning, con_postgres_psycopg2, get_result_one_column, \
    sql_to_dataframe, send_request

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

date_start = datetime.date(datetime.today() - timedelta(days=2494))
conpg = con_postgres_psycopg2()

sql_create = '''
      -- DROP TABLE IF EXISTS t_rate_nbu CASCADE;
      
      CREATE TABLE IF NOT EXISTS t_rate_nbu (
          id_rate serial4 NOT NULL,
          rate_date date NOT NULL,  -- дата курса
          rate_usd_nbu numeric(10, 7) NULL DEFAULT 0,  -- курс usd НБУ
          rate_euro_nbu numeric(10, 7) NULL DEFAULT 0,  -- курс euro НБУ
          rate_1c numeric(10, 7) NULL DEFAULT 0,  -- курс в 1C     
          rate_usd_for_report numeric(10, 7) NULL DEFAULT 0,  -- курс usd для отчетов
          date_create timestamp NULL DEFAULT now(),  -- дата/время записи
          CONSTRAINT t_rate_nbu_pkey PRIMARY KEY (rate_date)
      );
      
      COMMENT ON TABLE t_rate_nbu IS 'курс нбу и в обменниках';
      
      COMMENT ON COLUMN t_rate_nbu.rate_date IS 'дата курса';
      COMMENT ON COLUMN t_rate_nbu.rate_usd_nbu IS 'КурсНБУ_USD';
      COMMENT ON COLUMN t_rate_nbu.rate_euro_nbu IS 'КурсНБУ_EURO';
      COMMENT ON COLUMN t_rate_nbu.rate_1c IS 'курс в 1C';
      COMMENT ON COLUMN t_rate_nbu.rate_usd_for_report IS 'курс usd для отчетов';
      COMMENT ON COLUMN t_rate_nbu.date_create IS 'дата/время записи';
'''

sql_insert = '''
    INSERT INTO t_rate_nbu (
        rate_date,
        rate_usd_nbu,
        rate_euro_nbu    
    )
    VALUES 
        (%s, %s, %s)
    ON CONFLICT ON CONSTRAINT t_rate_nbu_pkey
    DO UPDATE
    SET rate_usd_nbu = %s,  rate_euro_nbu = %s
    ;           
    '''

sql_update_rate_1c = '''
    update t_rate_nbu
    set rate_1c = r.rate
    from (
        select distinct doc_date::date as tarih, rate_settlement/multiplicity_of_mutual_settlements as rate
        from t_one_doc_receipt_of_goods_services
        where rate_settlement <> 1
        order by doc_date::date
        ) as r
    where r.tarih = t_rate_nbu.rate_date
    '''

CREATE_INDEX = '''CREATE UNIQUE INDEX t_rate_nbu_rate_date_idx ON t_rate_nbu (rate_date);'''


def max_date_in_table():
    sql_select = '''
        SELECT max(rate_date)
        FROM t_rate_nbu
        '''
    return get_result_one_column(sql_select, conpg)


def get_rate_is_zero():
    sql_select = '''
        SELECT rate_date
        FROM t_rate_nbu
        WHERE rate_usd_nbu = 0
        ORDER BY rate_date desc
        ;
        '''
    return sql_to_dataframe(sql_select)


def get_rate_from_nbu(date_rate, rate_type='usd'):
    try:
        date_rate = datetime.strftime(date_rate, '%Y%m%d')
        url = f'https://bank.gov.ua/NBUStatService/v1/statdirectory/exchange?valcode={rate_type}&date={date_rate}&json'
        response = requests.get(url)
        response.raise_for_status()  # Raises stored HTTPError, if one occurred.
        data = response.json()
        return data[0]['rate']
    except requests.HTTPError as http_err:
        logger.info(f'HTTP error occurred: {http_err}')  # Python 3.6
    except Exception as err:
        logger.info(f'Other error occurred: {err}')  # Python 3.6
    return None


def add_rates_to_db(current_date):
    rate_usd = get_rate_from_nbu(current_date, rate_type='usd')
    rate_euro = get_rate_from_nbu(current_date, rate_type='eur')
    odata = (current_date, rate_usd, rate_euro, rate_usd, rate_euro)
    change_data_in_table_returning(sql_insert, conpg, odata, 'add_rates_to_db')
    change_data_in_table_returning(CREATE_INDEX, conpg, '', 'add_rates_to_db')

    # add rate to 1C
    add_rate_nbu(current_date, 'usd', rate_usd)
    add_rate_nbu(current_date, 'euro', rate_euro)


# added rate (usd, euro) to 1C
def add_rate_nbu(period, rate_type, rate):
    try:
        method = "POST"
        result = 'Не определен ref_key валюты'
        period = datetime.strftime(period, '%Y-%m-%dT00:00:00')
        DOCUMENT = 'InformationRegister_КурсыВалют/'
        url = URL_CONST + DOCUMENT
        rate_key = ''

        if rate_type == 'usd':
            rate_key = "3d7e2aca-8ac8-11e6-80c4-c936aa9c817c"
        elif rate_type == 'euro':
            rate_key = "3d7e2acb-8ac8-11e6-80c4-c936aa9c817c"

        if rate_key != '':
            data_json = {
                "Period": period,
                "Валюта_Key": rate_key,
                "Курс": rate,
                "Кратность": 1
            }

            result = send_request(url=url, method=method, data_json=data_json)
        logger.info(f"{result}")
    except Exception as e:
        logger.info(f"Возникла ошибка при добавлении курса в 1C: {e}")


def main_rate_nbu():
    df = get_rate_is_zero()

    if len(df) > 0:
        for row in df.itertuples():
            current_date = row.rate_date
            add_rates_to_db(current_date)

    current_date = max_date_in_table()
    if not current_date:
        current_date = date_start

    while current_date < datetime.date(datetime.today()):
        current_date += timedelta(days=1)
        add_rates_to_db(current_date)

        result = change_data_in_table_returning(sql_update_rate_1c, conpg, '', 'main_rate_nbu')
        logger.info(f"main_rate_nbu {result}")


if __name__ == "__main__":
    main_rate_nbu()
