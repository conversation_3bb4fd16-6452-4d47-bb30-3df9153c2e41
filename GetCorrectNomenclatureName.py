import re


# Вставить пробел перед 'x', если после него идет число
# Удалить двойные пробелы
def process_text(text):

    # 1. Удалить пробелы после числа перед любыми символами
    text = re.sub(r'(\d)\s+(\S)', r'\1\2', text)

    # 2. Вставить пробел после 'x', если после него идет число
    text = re.sub(r'(?<=\d)([*xXхХ]) (?=\d)', r'\1', text)

    # 3. Вставить пробел перед 'x', если после него идет число
    text = re.sub(r'([*xXхХ])(?=\d)', r' \1', text)

    # 4. Заменяем [*xXхХ] на 'х'
    text = re.sub(r'[*xXхХ](?=\d)', r'х', text)

    # 5. Удалить двойные пробелы
    text = re.sub(r'\s{2,}', ' ', text)

    return text


# Перед нечетным ", вставляем пробел, если его нет.
# После нечетного ", удаляем пробел, если он есть.
# Перед четным ", удаляем пробел, если он есть.
# После четного ", вставляем пробел, если его нет.
def process_quotes(text):
    # Найти все кавычки в строке
    quotes = [m.start() for m in re.finditer(r'"', text)]

    # Проверить, что количество кавычек четное
    if len(quotes) % 2 != 0:
        raise ValueError(f"Нечетное количество кавычек в строке {text}")

    # Обработать каждую кавычку
    for i, pos in enumerate(quotes):
        if i % 2 == 0:
            # Нечетная кавычка
            if pos > 0 and text[pos - 1] != ' ':
                text = text[:pos] + ' ' + text[pos:]
                pos += 1
            if pos + 1 < len(text) and text[pos + 1] == ' ':
                text = text[:pos + 1] + text[pos + 2:]
        else:
            # Четная кавычка
            if pos > 0 and text[pos - 1] == ' ':
                text = text[:pos - 1] + text[pos:]
                pos -= 1
            if pos + 1 < len(text) and text[pos + 1] != ' ':
                text = text[:pos + 1] + ' ' + text[pos + 1:]

    # Удалить двойные пробелы
    text = re.sub(r'\s{2,}', ' ', text)

    return text


def convert_to_uppercase_between_quotes(text):
    # Преобразовать все буквы между двумя кавычками в верхний регистр
    def to_upper(match):
        return match.group(0).upper()

    # Проверить, что количество кавычек четное
    if text.count('"') % 2 != 0:
        raise ValueError("Нечетное количество кавычек в строке")

    text = re.sub(r'(?<=")(.*?)(?=")', to_upper, text)
    return text

def convert_to_uppercase_latin_character(text):
    # Преобразовать все латинские буквы в верхний регистр
    text = re.sub(r'[a-z]', lambda x: x.group().upper(), text)
    return text


def get_correct_nomenclature_name(text):
    text = process_text(text)
    text = process_quotes(text)
    # text = convert_to_uppercase_latin_character(text)
    text = convert_to_uppercase_between_quotes(text)
    return text


if __name__ == '__main__':
    #old_text = 'Цукрова пудра з кислинкою"  Soda fruit Bottle sour powder Candy "4грx120x12'
    # old_text = 'Цукерки-драже" Браслет Candy Heart & watch "фруктовый микс 10 грх48х12'
    old_text = 'Цукерки желе "Дракон"  13 гр  х100 X 6'
    text = get_correct_nomenclature_name(old_text)
    print(text)
