import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_salary"
DOCUMENT = "Document_ЗарплатаКВыплатеОрганизаций"
SELECT_COLUMNS = (
    "DataVersion,Date,DeletionMark,Number,Posted,Комментарий,КраткийСоставДокумента,ОтражатьВРеглУчете,"
    "ОтражатьВУпрУчете,ПериодРегистрации,СпособВыплаты,Ref_Key,ВидВыплаты_Key,Организация_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    dataversion varchar(30) NULL,  -- DataVersion
    doc_date timestamp NOT NULL,  -- Date
    deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
    doc_number varchar(15) NULL,  -- Number
    posted boolean NOT NULL DEFAULT FALSE,  -- Posted
    a_comment varchar(250) NULL,  -- Комментарий
    short_content_of_document varchar(250) NULL,  -- КраткийСоставДокумента
    is_regulation boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВРеглУчете
    is_management boolean NOT NULL DEFAULT FALSE,  -- ОтражатьВУпрУчете
    registration_period timestamp NOT NULL,  -- ПериодРегистрации
    payment_method varchar(50) NULL,  -- СпособВыплаты
    ref_key varchar(50) NULL,  -- Ref_Key
    payout_type_key varchar(50) NULL,  -- ВидВыплаты_Key
    organization_key varchar(50) NULL,  -- Организация_Key
    CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.short_content_of_document IS 'КраткийСоставДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.is_regulation IS 'ОтражатьВРеглУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУпрУчете';
    COMMENT ON COLUMN {TABLE_NAME}.registration_period IS 'ПериодРегистрации';
    COMMENT ON COLUMN {TABLE_NAME}.payment_method IS 'СпособВыплаты';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.payout_type_key IS 'ВидВыплаты_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME} (
        dataversion,
        doc_date,
        deletion_mark,
        doc_number,
        posted,
        a_comment,
        short_content_of_document,
        is_regulation,
        is_management,
        registration_period,
        payment_method,
        ref_key,
        payout_type_key,
        organization_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        deletion_mark = EXCLUDED.deletion_mark,
        doc_number = EXCLUDED.doc_number,
        posted = EXCLUDED.posted,
        a_comment = EXCLUDED.a_comment,
        short_content_of_document = EXCLUDED.short_content_of_document,
        is_regulation = EXCLUDED.is_regulation,
        is_management = EXCLUDED.is_management,
        registration_period = EXCLUDED.registration_period,
        payment_method = EXCLUDED.payment_method,
        ref_key = EXCLUDED.ref_key,
        payout_type_key = EXCLUDED.payout_type_key,
        organization_key = EXCLUDED.organization_key
    """
    return sql.replace("'", "")


async def main_doc_salary_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, True, True)
    maket = await create_model_async(14)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$10,", "to_timestamp($10, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_salary_async())
