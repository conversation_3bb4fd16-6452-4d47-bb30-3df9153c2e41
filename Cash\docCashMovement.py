import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_doc_cash_movement'
DOCUMENT = "Document_ВнутреннееПеремещениеНаличныхДенежныхСредств"
SELECT_COLUMNS = "DataVersion,Date,Number,Комментарий,СуммаДокумента,Posted,Оплачено,Ref_Key,ВалютаДокумента_Key," \
                 "Касса_Key,КассаПолучатель_Key,СтатьяДвиженияДенежныхСредств_Key"

SQL_CREATE_TABLE = f'''

    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_date timestamp NOT NULL,  -- Date
        doc_number varchar(15) NOT NULL,  -- Number
        a_comment varchar NULL,  -- Комментарий
        amount numeric(10, 4) NOT NULL DEFAULT 0,  -- СуммаДокумента
        isposted bool NOT NULL DEFAULT false,  -- Posted
        ispaid bool NOT NULL DEFAULT false,  -- Оплачено
        is_management bool NOT NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        dataversion varchar(15) NOT NULL,  -- DataVersion
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        currency_key varchar(50) NOT NULL,  -- ВалютаДокумента_Key
        checkout_key varchar(50) NOT NULL,  -- Касса_Key
        cashier_recipient_key varchar(50) NOT NULL,  -- КассаПолучатель_Key
        cash_flow_item varchar(50) NOT NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.isposted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.ispaid IS 'Оплачено';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.checkout_key IS 'Касса_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cashier_recipient_key IS 'КассаПолучатель_Key';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
'''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}(
            dataversion,
            doc_date,
            doc_number,
            a_comment,
            amount,
            isposted,
            ispaid,
            ref_key,
            currency_key,
            checkout_key,
            cashier_recipient_key,
            cash_flow_item
        )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        doc_date = EXCLUDED.doc_date,
        doc_number = EXCLUDED.doc_number,
        a_comment = EXCLUDED.a_comment,
        amount = EXCLUDED.amount,
        isposted = EXCLUDED.isposted,
        ispaid = EXCLUDED.ispaid,
        currency_key = EXCLUDED.currency_key,
        checkout_key = EXCLUDED.checkout_key,
        cashier_recipient_key = EXCLUDED.cashier_recipient_key,
        cash_flow_item = EXCLUDED.cash_flow_item
    '''
    return sql.replace("'", "")


async def main_doc_cash_movement_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, True)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    sql = sql.replace("$2,", "to_timestamp($2, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, 'doc_date')
    await async_sql_create_index(TABLE_NAME, 'doc_number')
    await async_sql_create_index(TABLE_NAME, 'cash_flow_item')
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_movement_async())
