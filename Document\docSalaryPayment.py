import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_salary_payment"
DOCUMENT = "Document_ЗарплатаКВыплатеОрганизаций_РаботникиОрганизации"
SELECT_COLUMNS = "LineNumber,ВыплаченностьЗарплаты,Сумма,СуммаУпр,Ref_Key,Банк_Key,Сотрудник_Key"

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    idt bigserial NOT NULL,
    line_number numeric(10,4) NOT NULL DEFAULT 0,  -- LineNumber
    paid_salaries varchar(50) NULL,  -- ВыплаченностьЗарплаты
    total numeric(10,4) NOT NULL DEFAULT 0,  -- Сумма
    amount_management numeric(10,4) NOT NULL DEFAULT 0,  -- СуммаУпр
    ref_key varchar(50) NULL,  -- Ref_Key
    bank_key varchar(50) NULL,  -- Банк_Key
    employee_key uuid NULL,  -- Сотрудник_Key
    CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key,line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.paid_salaries IS 'ВыплаченностьЗарплаты';
    COMMENT ON COLUMN {TABLE_NAME}.total IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_management IS 'СуммаУпр';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.bank_key IS 'Банк_Key';
    COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'Сотрудник_Key';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME} (
        line_number,
        paid_salaries,
        total,
        amount_management,
        ref_key,
        bank_key,
        employee_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key,line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        paid_salaries = EXCLUDED.paid_salaries,
        total = EXCLUDED.total,
        amount_management = EXCLUDED.amount_management,
        ref_key = EXCLUDED.ref_key,
        bank_key = EXCLUDED.bank_key,
        employee_key = EXCLUDED.employee_key
    """
    return sql.replace("'", "")


async def main_doc_salary_payment_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(7)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "ref_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_salary_payment_async())
