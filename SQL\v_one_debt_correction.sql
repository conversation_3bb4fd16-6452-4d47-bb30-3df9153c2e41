DROP VIEW IF EXISTS v_one_debt_correction CASCADE;

CREATE OR REPLACE VIEW v_one_debt_correction AS
SELECT
	manager,
	segment_folder,
	segment,
	customer,
	doc_date::date doc_date,
	doc_number,
--  не выводим кредитора, т.к. он всегда = debtor, за исключением operation_type = ПереносЗадолженности
-- в отчет operation_type = ПереносЗадолженности не выводим
--	creditor,
	sum(COALESCE(debt,0)) debt,
	sum(COALESCE(credit,0)) credit,
	sum(COALESCE(debt,0)) - sum(COALESCE(credit,0)) AS diff,
	currency,
	operation_type,
	is_sum_equals,
	is_management,
	ref_key
FROM
	(
	SELECT DISTINCT 
		main.doc_date,
		main.doc_number,
		cust_debt.customer,
		cust_debt.manager,
    	cust_debt.segment_folder,
		cust_debt.segment,
		COALESCE(sum(amount) FILTER (WHERE debt_type = 'Кредиторская') OVER (PARTITION BY main.ref_key),0) AS debt,
		COALESCE(sum(amount) FILTER (WHERE debt_type = 'Дебиторская') OVER (PARTITION BY main.ref_key),0) AS credit,
		cur.description AS currency,
		main.operation_type,
		COALESCE(sum(amount) FILTER (WHERE debt_type = 'Дебиторская') OVER (PARTITION BY main.ref_key, det.counterparty_contract_key),0) = 
			COALESCE(sum(amount) FILTER (WHERE debt_type = 'Кредиторская') OVER (PARTITION BY main.ref_key, det.counterparty_contract_key),0) AS is_sum_equals,
		main.ref_key,
		main.is_management
	FROM t_one_doc_debt_correction AS main
		LEFT JOIN t_one_doc_debt_correction_debt_amount AS det
			using(ref_key)
		LEFT JOIN t_one_cat_currencies AS cur
			ON cur.ref_key = main.currency_key
		LEFT JOIN
		(
			SELECT DISTINCT
				customer,
        	    segment_folder,
				manager,
				segment,
				customer_key
			FROM v_one_manager_counterparty_contracts_segments
		)
		AS cust_debt
			ON main.counterparty_debtor_key = cust_debt.customer_key
	WHERE main.posted
	    AND main.is_management
		AND main.organization_key <> 'f51a0e76-4821-11ec-811d-001dd8b72b55'  -- ТОВ "АЛЬФА БЕСТ"
		AND (main.counterparty_debtor_key = main.counterparty_creditor_key
			OR main.counterparty_creditor_key = '00000000-0000-0000-0000-000000000000')
		-- AND operation_type = 'СписаниеЗадолженности'  -- СписаниеЗадолженности, т.к. в других типах участвуют документы продажа, поступление, которые подтягиваются из других док-в.
	) AS t
WHERE NOT COALESCE(is_sum_equals, FALSE)
GROUP BY
	doc_date,
	doc_number,
	manager,
    segment_folder,
	segment,
	customer,
	currency,
	is_sum_equals,
	operation_type,
	is_management,
	ref_key
ORDER BY
	manager,
    segment_folder,
	segment,
	customer,
	doc_date DESC,
	doc_number,
	currency,
	operation_type
;

COMMENT ON VIEW v_one_debt_correction IS 'корректировка долга';

