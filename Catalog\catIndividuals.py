import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Catalog_ФизическиеЛица"
TABLE_NAME = "t_one_cat_individuals"
SELECT_COLUMNS = "Code,DataVersion,DeletionMark,Description,IsFolder,Predefined,ДатаРождения,КодПоДРФО,Комментарий," \
                 "МестоРождения,Пол,СтраховойНомерПФ,Parent_Key,Ref_Key,ОсновноеИзображение_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15) NOT NULL,  -- Code
        dataversion varchar(50) NOT NULL,  -- DataVersion
        deletionmark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        description varchar(250) NULL,  -- Description
        isfolder boolean NOT NULL DEFAULT FALSE,  -- IsFolder
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        doc_date_of_birth timestamp NULL,  -- ДатаРождения
        code_by_drfo varchar(20) NULL,  -- КодПоДРФО
        a_comment varchar(250) NULL,  -- Комментарий
        place_of_birth varchar(250) NULL,  -- МестоРождения
        gender varchar(50) NULL,  -- Пол
        insurance_doc_number_p_f varchar(50) NULL,  -- СтраховойНомерПФ
        parent_key varchar(50) NULL,  -- Parent_Key
        ref_key varchar(50) NULL,  -- Ref_Key
        main_image_key varchar(50) NULL,  -- ОсновноеИзображение_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_of_birth IS 'ДатаРождения';
    COMMENT ON COLUMN {TABLE_NAME}.code_by_drfo IS 'КодПоДРФО';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.place_of_birth IS 'МестоРождения';
    COMMENT ON COLUMN {TABLE_NAME}.gender IS 'Пол';
    COMMENT ON COLUMN {TABLE_NAME}.insurance_doc_number_p_f IS 'СтраховойНомерПФ';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.main_image_key IS 'ОсновноеИзображение_Key';
    
    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;
'''


async def sql_insert(maket):
    sql = f'''
        INSERT INTO {TABLE_NAME}(
            code,
            dataversion,
            deletionmark,
            description,
            isfolder,
            predefined,
            doc_date_of_birth,
            code_by_drfo,
            a_comment,
            place_of_birth,
            gender,
            insurance_doc_number_p_f,
            parent_key,
            ref_key,
            main_image_key
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            code = EXCLUDED.code,
            dataversion = EXCLUDED.dataversion,
            deletionmark = EXCLUDED.deletionmark,
            description = EXCLUDED.description,
            isfolder = EXCLUDED.isfolder,
            predefined = EXCLUDED.predefined,
            doc_date_of_birth = EXCLUDED.doc_date_of_birth,
            code_by_drfo = EXCLUDED.code_by_drfo,
            a_comment = EXCLUDED.a_comment,
            place_of_birth = EXCLUDED.place_of_birth,
            gender = EXCLUDED.gender,
            insurance_doc_number_p_f = EXCLUDED.insurance_doc_number_p_f,
            parent_key = EXCLUDED.parent_key,
            main_image_key = EXCLUDED.main_image_key
    ;
    '''
    return sql.replace("'", "")


async def main_cat_individuals_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(15)
    sql = await sql_insert(maket)
    sql = sql.replace("$7,", "to_timestamp($7, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_individuals_async())
