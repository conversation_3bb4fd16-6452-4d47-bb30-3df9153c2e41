import asyncpg
from pathlib import Path
import asyncio

DB_CONFIG = {
    'database': 'MEDOC',
    'user': 'postgres',
    'password': 'hfvpfc15',
    'host': '127.0.0.1',
    'port': '5432',
    'server_settings': {
        'zero_damaged_pages': 'on',
        'ignore_checksum_failure': 'on'
    }
}

async def restore_from_bin(file_path, code):
    conn = None
    try:
        # Читаем бинарные данные
        data = Path(file_path).read_bytes()

        # Подключаемся к БД
        conn = await asyncpg.connect(
            host='localhost',
            user='postgres',
            password='hfvpfc15',
            database='MEDOC'
        )

        # Используем прямое преобразование в bytea
        await conn.execute("""
            UPDATE docsign_original
            SET 
                signfile = $1::bytea,
                tmpsign = $1::bytea,
                filename = filename || '_restored'
            WHERE code = $2
        """, data, code)

        return True

    except Exception as e:
        print(f"Ошибка восстановления {file_path}: {str(e)}")
        return False
    finally:
        if conn and not conn.is_closed():
            await conn.close()


async def main():
    files = {
        223724: r'E:\backup_medoc\chunk_1711623.bin',
        507209: r'E:\backup_medoc\chunk_17364864.bin',
        507210: r'E:\backup_medoc\chunk_17364865.bin'
    }

    for code, path in files.items():
        success = await restore_from_bin(path, code)
        print(f"Код {code}: {'Успех' if success else 'Ошибка'}")


async def process_file(file_path: str, code: int):
    conn = await asyncpg.connect(**DB_CONFIG)
    try:
        # Удаляем старую таблицу
        await conn.execute("""
            DROP TABLE IF EXISTS temp_restore;
        """)

        # Создаем новую
        await conn.execute("""
            CREATE TEMP TABLE temp_restore (
                code INTEGER,
                data BYTEA
            ) ON COMMIT DROP;
        """)

        # Импорт данных
        with open(file_path, 'rb') as f:
            await conn.copy_into_table(
                'temp_restore',
                source=f,
                format='binary',
                columns=('code', 'data'))

            # Обновление данных
            await conn.execute("""
            UPDATE docsign_original
            SET 
                signfile = t.data,
                tmpsign = t.data
            FROM temp_restore t
            WHERE code = $1
        """, code)

    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(main())