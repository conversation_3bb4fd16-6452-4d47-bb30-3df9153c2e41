DROP VIEW IF EXISTS v_one_debt CASCADE;

CREATE OR REPLACE VIEW v_one_debt AS
    WITH v_one_debt_source AS (
        SELECT
            manager,
            segment_folder,
            segment,
            customer,
            doc_date::date doc_date,
            doc_number,
            coalesce(debt,0) debt,
            coalesce(credit,0) credit,
            coalesce(debt,0) - coalesce(credit,0) diff,
            0 costs,
            coalesce(debt,0) - coalesce(credit,0) profit_costs,
            currency,
            operation_type,
            'взаимозачет' doc_type
        FROM v_one_debt_correction
        WHERE is_management
        UNION ALL
        SELECT
            manager,
            segment_folder,
            segment,
            customer,
            doc_date::date doc_date,
            doc_number,
            coalesce(debt,0) debt,
            coalesce(credit,0) credit,
            coalesce(debt,0) - coalesce(credit,0) diff,
            0 costs,
            coalesce(debt,0) - coalesce(credit,0) profit_costs,
            currency,
            operation_type,
            'касса' doc_type
        FROM v_one_debt_checkout
--        WHERE is_management
        UNION ALL
        SELECT
            manager,
            segment_folder,
            segment,
            customer,
            doc_date::date doc_date,
            doc_number,
            coalesce(debt,0) debt,
            coalesce(credit,0) credit,
            coalesce(debt,0) - coalesce(credit,0) diff,
            0 costs,
            coalesce(debt,0) - coalesce(credit,0) profit_costs,
            currency,
            operation_type,
            'банк' doc_type
        FROM v_one_debt_bank
--        WHERE is_management
        UNION ALL
        SELECT
            manager,
            segment_folder,
            segment,
            customer,
            doc_date::date doc_date,
            doc_number,
            coalesce(debt,0) debt,
            coalesce(credit,0) credit,
            coalesce(debt,0) - coalesce(credit,0) diff,
            coalesce(costs,0) costs,
            coalesce(debt,0) - coalesce(credit,0) - coalesce(costs,0) profit_costs,
            currency,
            operation_type,
            'продажа' doc_type
        FROM v_one_debt_sale_salereturn
        UNION ALL
        SELECT
            manager,
            segment_folder,
            segment,
            customer,
            doc_date::date doc_date,
            doc_number,
            coalesce(debt,0) debt,
            coalesce(credit,0) credit,
            coalesce(debt,0) - coalesce(credit,0) diff,
            0 costs,
            coalesce(debt,0) - coalesce(credit,0) profit_costs,
            currency,
            operation_type,
            'поступление' doc_type
        FROM v_one_debt_receipt_receipt_return
    )
    SELECT
        CASE  -- если услуг (накл поступление) получено больше, чем во взаимозачете кредита - учитываем поступления
            WHEN
                COALESCE(abs(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),'')
                    )),0)
                >
                COALESCE(abs(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),'')
                    )),0)
                    THEN

                        -COALESCE(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(to_char(doc_date,'YYYY.MM'),'')
                            ),0)
            ELSE  -- иначе учитываем взаимозачет
                -COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),'')
                    ),0)
        END amount_segment,
        CASE  -- если услуг (накл поступление) получено больше, чем во взаимозачете кредита - учитываем поступления
            WHEN
                COALESCE(abs(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),''),
                        coalesce(customer,'')
                    )),0)
                >
                COALESCE(abs(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),''),
                        coalesce(customer,'')
                    )),0)
                    THEN

                        -COALESCE(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'поступление')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(to_char(doc_date,'YYYY.MM'),''),
                                coalesce(customer,'')
                            ),0)
            ELSE  -- иначе учитываем взаимозачет
                -COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'взаимозачет')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),''),
                        coalesce(customer,'')
                    ),0)
        END amount_customer,
        CASE -- высчитываем процент возврата
            WHEN
                COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'продажа')
                    OVER (PARTITION BY
                        coalesce(manager,''),
                        coalesce(segment_folder,''),
                        coalesce(segment,''),
                        coalesce(to_char(doc_date,'YYYY.MM'),''),
                        coalesce(customer,'')
                    ),0) <> 0 AND doc_type = 'продажа' THEN

                        (100 * COALESCE(sum(COALESCE(credit,0)) FILTER (WHERE doc_type = 'продажа')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(to_char(doc_date,'YYYY.MM'),''),
                                coalesce(customer,'')
                            ),0)
                        /
                        COALESCE(sum(COALESCE(debt,0)) FILTER (WHERE doc_type = 'продажа')
                            OVER (PARTITION BY
                                coalesce(manager,''),
                                coalesce(segment_folder,''),
                                coalesce(segment,''),
                                coalesce(to_char(doc_date,'YYYY.MM'),''),
                                coalesce(customer,'')
                            ),0))::numeric(10,2)
            ELSE
                0
        END return_percent,
        to_char(doc_date,'YYYY.MM') periods,
        CASE
            WHEN coalesce(debt,0) = 0 THEN
                coalesce(credit,0)
            WHEN coalesce(credit,0) = 0 THEN
                coalesce(debt,0)
            ELSE
                0
        END AS amount,
        *
    FROM v_one_debt_source
    ORDER BY
        manager NULLS LAST,
        segment_folder NULLS LAST,
        segment NULLS LAST,
        customer NULLS LAST,
        doc_date,
        doc_number
    ;

COMMENT ON VIEW v_one_debt IS 'Вся дебиторка';
GRANT SELECT ON TABLE v_one_debt TO user_prestige;
