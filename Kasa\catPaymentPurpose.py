import asyncio
import os
import sys

sys.path.append(r'D:\Prestige\Python\Prestige')
from cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_cash_payment_purpose"
DOCUMENT = "назначение платежа access"
SQL_CREATE_TABLE = f"""
    -- таб НЕ УДАЛЯТЬ. ИНФ сначала сохранить!!!
    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        item_id uuid NOT NULL,  -- статья
        purpose_name varchar(35) NULL,  -- назначение
        purpose_name_tr varchar(35) NULL,  -- назначение транслит
        description varchar(250) NULL,  -- примечание
        is_periodic bool DEFAULT false NOT NULL,  -- периодический
        id_doc_type uuid NULL,
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_unq UNIQUE (purpose_name),
        CONSTRAINT {TABLE_NAME}_fk FOREIGN KEY (item_id) REFERENCES t_cash_item(id) ON DELETE CASCADE,
        CONSTRAINT {TABLE_NAME}_doc_type_fk FOREIGN KEY (id_doc_type) REFERENCES t_cash_doc_type(id) ON UPDATE CASCADE,
        CONSTRAINT {TABLE_NAME}_t_cash_doc_types_fk FOREIGN KEY (id_doc_type) REFERENCES t_cash_doc_types(id) ON UPDATE CASCADE        
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.item_id IS 'статья';
    COMMENT ON COLUMN {TABLE_NAME}.purpose_name IS 'назначение';
    COMMENT ON COLUMN {TABLE_NAME}.purpose_name_tr IS 'назначение транслит';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'примечание';
    COMMENT ON COLUMN {TABLE_NAME}.is_periodic IS 'периодический';
    COMMENT ON COLUMN {TABLE_NAME}.id_doc_type IS 'тип документа';
    GRANT ALL ON TABLE {TABLE_NAME} TO {COMPANY_CASHIER}; --{DOCUMENT}

"""


async def main_t_cash_payment_purpose_async():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_TABLE)
    logger.info(f"{result}, SQL_CREATE_TABLE")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_t_cash_payment_purpose_async())
