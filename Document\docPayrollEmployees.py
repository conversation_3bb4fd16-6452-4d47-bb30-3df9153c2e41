import asyncio
import os

from async_Postgres import async_truncate_table, async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_payroll_employees"
DOCUMENT = "Document_НачислениеЗарплатыРаботникамОрганизаций_Начисления"
SELECT_COLUMNS = (
    "Ref_Key,LineN<PERSON>ber,Сотрудник_Key,Назначение_Key,ВидРасчета_Key,ДатаНачала,ДатаОкончания,"
    "БазовыйПериодНачало,БазовыйПериодКонец,Результат,РезультатУпр,ОплаченоДнейЧасов,ОтработаноДней,"
    "КалендарныеДни,НормаДнейЗаМесяц,Показатель1,Авторасчет,Сторно,СторнируемыйДокумент"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        idt bigserial NOT NULL,
        line_number numeric(10,4) NOT NULL DEFAULT 0,
        start_doc_date date NOT NULL,
        expiration_doc_date date NOT NULL,
        base_period_beginning date NOT NULL,
        base_period_end date NOT NULL,
        amount numeric(10,4) NOT NULL DEFAULT 0,
        amount_control numeric(10,4) NOT NULL DEFAULT 0,
        paid_days_hours numeric(10,4) NOT NULL DEFAULT 0,
        days_worked numeric(10,4) NOT NULL DEFAULT 0,
        calendar_days numeric(10,4) NOT NULL DEFAULT 0,
        norm_days_per_month numeric(10,4) NOT NULL DEFAULT 0,
        indicator1 numeric(10,4) NOT NULL DEFAULT 0,
        autocalculation boolean NOT NULL DEFAULT FALSE,
        reversible boolean NOT NULL DEFAULT FALSE,
        cancellable_document varchar(50) NOT NULL,
        ref_key varchar(50) NOT NULL,
        employee_key uuid NOT NULL,
        purpose_key varchar(50) NOT NULL,
        type_of_calculation_key varchar(50) NOT NULL,
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'Сотрудник_Key';
    COMMENT ON COLUMN {TABLE_NAME}.purpose_key IS 'Назначение_Key';
    COMMENT ON COLUMN {TABLE_NAME}.type_of_calculation_key IS 'ВидРасчета_Key';
    COMMENT ON COLUMN {TABLE_NAME}.start_doc_date IS 'ДатаНачала';
    COMMENT ON COLUMN {TABLE_NAME}.expiration_doc_date IS 'ДатаОкончания';
    COMMENT ON COLUMN {TABLE_NAME}.base_period_beginning IS 'БазовыйПериодНачало';
    COMMENT ON COLUMN {TABLE_NAME}.base_period_end IS 'БазовыйПериодКонец';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Результат';
    COMMENT ON COLUMN {TABLE_NAME}.amount_control IS 'РезультатУпр';
    COMMENT ON COLUMN {TABLE_NAME}.paid_days_hours IS 'ОплаченоДнейЧасов';
    COMMENT ON COLUMN {TABLE_NAME}.days_worked IS 'ОтработаноДней';
    COMMENT ON COLUMN {TABLE_NAME}.calendar_days IS 'КалендарныеДни';
    COMMENT ON COLUMN {TABLE_NAME}.norm_days_per_month IS 'НормаДнейЗаМесяц';
    COMMENT ON COLUMN {TABLE_NAME}.indicator1 IS 'Показатель1';
    COMMENT ON COLUMN {TABLE_NAME}.autocalculation IS 'Авторасчет';
    COMMENT ON COLUMN {TABLE_NAME}.reversible IS 'Сторно';
    COMMENT ON COLUMN {TABLE_NAME}.cancellable_document IS 'СторнируемыйДокумент';

"""


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}
        (
            ref_key,
            line_number,
            employee_key,
            purpose_key,
            type_of_calculation_key,
            start_doc_date,
            expiration_doc_date,
            base_period_beginning,
            base_period_end,
            amount,
            amount_control,
            paid_days_hours,
            days_worked,
            calendar_days,
            norm_days_per_month,
            indicator1,
            autocalculation,
            reversible,
            cancellable_document
        )
        VALUES {maket}
        ON CONFLICT (ref_key, line_number)
        DO UPDATE SET
            line_number = EXCLUDED.line_number,
            employee_key = EXCLUDED.employee_key,
            purpose_key = EXCLUDED.purpose_key,
            type_of_calculation_key = EXCLUDED.type_of_calculation_key,
            start_doc_date = EXCLUDED.start_doc_date,
            expiration_doc_date = EXCLUDED.expiration_doc_date,
            base_period_beginning = EXCLUDED.base_period_beginning,
            base_period_end = EXCLUDED.base_period_end,
            amount = EXCLUDED.amount,
            amount_control = EXCLUDED.amount_control,
            paid_days_hours = EXCLUDED.paid_days_hours,
            days_worked = EXCLUDED.days_worked,
            calendar_days = EXCLUDED.calendar_days,
            norm_days_per_month = EXCLUDED.norm_days_per_month,
            indicator1 = EXCLUDED.indicator1,
            autocalculation = EXCLUDED.autocalculation,
            reversible = EXCLUDED.reversible,
            cancellable_document = EXCLUDED.cancellable_document
    ;
    """
    return sql.replace("'", "")


async def main_doc_payroll_employees_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(19)
    sql = await sql_insert(maket)
    sql = sql.replace("$6", "to_timestamp($6, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$7", "to_timestamp($7, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$8", "to_timestamp($8, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$9", "to_timestamp($9, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_payroll_employees_async())
