-- 20250407 1026
-- задолженность клиентов.
-- выводим только те накладные, которые НЕ были оплачены

CREATE OR REPLACE VIEW v_one_customer_balance_no_date AS
WITH
balance1 AS (
  SELECT
   det.segment,
   det.manager,
   det.customer,
   det.organization,
   det.currency_name,
   det.doc_date,
   sum(det.doc_sum) doc_sum,
   CASE
    WHEN det.recorder_type NOT IN ('ПлатежноеПоручениеВходящее', 'ПлатежноеПоручениеИсходящее', 'ПриходныйКассовыйОрдер', 'РасходныйКассовыйОрдер', 'КорректировкаДолга') THEN
     sum(det.doc_sum)
    ELSE
     0
   END sale,
   CASE
    WHEN det.recorder_type IN ('ПлатежноеПоручениеВходящее', 'ПлатежноеПоручениеИсходящее', 'ПриходныйКассовыйОрдер', 'РасходныйКассовыйОрдер', 'КорректировкаДолга') THEN
     sum(det.doc_sum)
    ELSE
     0
   END pay,
   det.last_date,
   CASE
    WHEN det.recorder_type = 'ВозвратТоваровОтПокупателя' THEN
     concat(det.doc_number,'/',COALESCE(rtrn.customer_number,'б/н'))
    ELSE
     det.doc_number
   END doc_number,
   det.contract_type,
   det.contract_name,
   det.recorder_type,
    det.manager_key
  FROM mt_one_balance_details AS det
   LEFT JOIN t_one_doc_return_of_goods_from_customers AS rtrn
    ON det.recorder = rtrn.ref_key
  WHERE det.organization NOT IN ('ТОВ "АЛЬФА БЕСТ"','ТОВ "ПРЕСТИЖ ПРОДУКТ-К"','ТОВ "А-НОВА"')
      -- НЕ учитываем образцы.
      -- ТК если в док РН только образцы, то при расчете дней ПДЗ,
      -- они тоже будут участовать, что ни есть правильно. т.к. они даются бесплатно
   AND abs(det.doc_sum) >= 0.1  -- надо учесть, что некоторые позиции продаем просрочнику по 0,2
  GROUP BY
   det.doc_date::date,
   det.last_date::date,
   det.recordtype,
   det.recorder_type,
   det.contract_type,
   det.contract_name,
   det.segment,
   det.manager,
   det.customer,
   det.organization,
   det.currency_name,
   det.manager_key,
   CASE
    WHEN det.recorder_type = 'ВозвратТоваровОтПокупателя' THEN
     concat(det.doc_number,'/',COALESCE(rtrn.customer_number,'б/н'))
    ELSE
     det.doc_number
   END
),
balance2 AS (
 SELECT
  row_number() OVER (ORDER BY
    segment,
    manager,
    customer,
    organization,
    currency_name,
    contract_type,
    contract_name,
    last_date,
    doc_date,
    doc_number
   )
  AS row_no,
  -- задолженность для расчета только дней ПДЗ!!!
  -- берем всю поставку до начала периода и вычитаем ВСЕ оплаты.
  -- так мы получим даты оплаченных накладных
  sum(doc_sum) FILTER (WHERE doc_sum > 0) OVER (PARTITION BY
   segment,
   manager,
   customer,
   organization,
   currency_name,
   contract_type,
   contract_name
   ORDER BY last_date, doc_date, doc_number
   )
   + -- + т.к оплаты со знаком -
  sum(doc_sum) FILTER (WHERE doc_sum < 0) OVER (PARTITION BY
   segment,
   manager,
   customer,
   organization,
   currency_name,
   contract_type,
   contract_name
  ) AS customer_sum_for_day,
  *
 FROM balance1
),
balance3 AS (
 SELECT
  row_no = max(row_no) OVER (PARTITION BY
   segment,
   manager,
   customer,
   organization,
   currency_name,
   contract_type,
   contract_name
  )
  AS max_row_no,
  -- нам нужна дата первой продажи после даты закрытия долга. (customer_sum_for_day <= 1)
  -- От нее будет отсчитывать дни задолженности (ПДЗ)
  max(
   CASE -- извлекаем последнюю дату, когда закрывали долг. customer_sum_for_day <= 1
--    WHEN customer_sum_for_day <= 1 AND last_date <= current_date AND contract_type = 'СПокупателем' THEN
    WHEN customer_sum_for_day <= 1 AND last_date <= current_date THEN
     last_date
    ELSE -- нет дня, когда долг клиента был <= 1 или это новый клиент - первая запись
     NULL::date
   END
  ) OVER (PARTITION BY
   segment,
   manager,
   customer,
   organization,
   currency_name,
   contract_type,
   contract_name
  )
  AS max_negative_customer_date,
  -- извлечем и дату первой продажи, на случай max_negative_customer_date is null
  min(
   CASE
    WHEN customer_sum_for_day > 1 AND last_date <= current_date AND contract_type = 'СПокупателем' AND recorder_type = 'РеализацияТоваровУслуг' THEN
     last_date
    ELSE
     NULL::date
   END
   ) OVER (PARTITION BY
    segment,
    manager,
    customer,
    organization,
    currency_name,
    contract_type,
    contract_name
   )
  AS min_positive_customer_date,
  *
 FROM balance2
),
balance_det AS (
 SELECT  
  sum(doc_sum) OVER (PARTITION BY
    segment,
    manager,
    customer,
    organization,
    currency_name,
    contract_type,
    contract_name
    ORDER BY last_date, doc_date, doc_number
   )
  AS customer_sum,
  CASE
   WHEN max_negative_customer_date IS NULL AND  min_positive_customer_date IS NOT NULL THEN
    -- если max_negative_customer_date = NULL (т.е долг не закрывал или первая запись нового клиента)
    -- тогда берем минимальную дату продажи
    min_positive_customer_date
   ELSE
    -- max_negative_customer_date - есть дата, когда клиент закрывал долг.
    -- извлекаем дату первой продажи после даты закрытия долга
    min(
     CASE
      WHEN last_date >= max_negative_customer_date
       AND customer_sum_for_day > 1
--       AND contract_type = 'СПокупателем'
       AND recorder_type = 'РеализацияТоваровУслуг' THEN
        last_date
      ELSE
       NULL::date
     END
     ) OVER (PARTITION BY
      segment,
      manager,
      customer,
      organization,
      currency_name,
      contract_type,
      contract_name
     )
  END date_for_count_days,
  *
 FROM balance3
),
balance AS (
 SELECT
  row_no,
  max_row_no,
  min_positive_customer_date,
  max_negative_customer_date,
  CASE
   -- выводим строки по маркетингу, если у последней строки сумма долга значится > 1
   WHEN max_row_no AND contract_type = 'СПоставщиком' AND abs(customer_sum) < 1 THEN
    NULL::date
   WHEN contract_type = 'СПоставщиком' AND abs(customer_sum) >= 1 THEN
    max(
     CASE
      WHEN abs(customer_sum) <= 1 AND contract_type = 'СПоставщиком' THEN
       last_date
      ELSE
       NULL::date
     END
    ) OVER (PARTITION BY
     segment,
     manager,
     customer,
     organization,
     currency_name,
     contract_type,
     contract_name
    )
   ELSE
    NULL::date
  END AS min_marketing_date,
  CASE
    WHEN last_date::date > current_date THEN
        current_date - last_date::date
    ELSE
        current_date - date_for_count_days::date
  END AS  leave_days,
  date_for_count_days,
  customer_sum,
  segment, manager, customer, currency_name, doc_date, last_date, customer_sum_for_day, -- doc_sum,
  sale, pay, doc_number, organization, contract_type, contract_name, recorder_type, manager_key
 FROM balance_det
)
--  SELECT
-- 	  0 AS sort, row_no, leave_days, customer_sum, segment, manager, customer, doc_date, last_date, 
-- 	  'начальный остаток' doc_number, 0 sale, 0 pay, organization, currency_name, contract_type, contract_name, NULL::recorder_type, manager_key
--  FROM balance
--  WHERE max_row_no 
-- 		-- AND doc_date < '01.04.2025'::date 
--  UNION ALL 
--  SELECT
-- 	  1 AS sort, row_no, leave_days, customer_sum, segment, manager, customer, doc_date, last_date, 
-- 	  doc_number, sale, pay, organization, currency_name, contract_type, contract_name, recorder_type, manager_key
--  FROM balance
-- WHERE doc_date >= '01.04.2025'::date
SELECT * 
FROM balance
ORDER BY
 row_no
;