import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_tax_receipt"
DOCUMENT = "Document_РегистрацияВходящегоНалоговогоДокумента"
SELECT_COLUMNS = (
    "Ref_Key,DataVersion,DeletionMark,Number,Date,Posted,ВидОперации,Комментарий,Организация_Key,"
    "Контрагент_Key,ДоговорКонтрагента_Key,ДокументОснование,ДокументОснование_Type,"
    "НомерВходящегоДокумента,ДатаВходящегоДокумента,СуммаДокумента,СуммаНДСДокумента"
)

SQL_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            ref_key varchar(50) NULL,  -- Ref_Key
            dataversion varchar(25) NULL,  -- DataVersion
            deletionmark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
            doc_number varchar(25) NULL,  -- Number
            doc_date timestamp NOT NULL,  -- Date
            posted boolean NOT NULL DEFAULT FALSE,  -- Posted
            operation_type varchar(50) NULL,  -- ВидОперации
            a_comment varchar(350) NULL,  -- Комментарий
            organization_key varchar(50) NULL,  -- Организация_Key
            account_key varchar(50) NULL,  -- Контрагент_Key
            counterparty_agreement_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
            document_base varchar(50) NULL,  -- ДокументОснование
            document_base_type varchar(100) NULL,  -- ДокументОснование_Type
            doc_number_of_incoming_document varchar(25) NULL,  -- НомерВходящегоДокумента
            incoming_document_doc_date timestamp NOT NULL,  -- ДатаВходящегоДокумента
            document_amount numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаДокумента
            amount_vat_document numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаНДСДокумента
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
        COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
        COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
        COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
        COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
        COMMENT ON COLUMN {TABLE_NAME}.counterparty_agreement_key IS 'ДоговорКонтрагента_Key';
        COMMENT ON COLUMN {TABLE_NAME}.document_base IS 'ДокументОснование';
        COMMENT ON COLUMN {TABLE_NAME}.document_base_type IS 'ДокументОснование_Type';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number_of_incoming_document IS 'НомерВходящегоДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.incoming_document_doc_date IS 'ДатаВходящегоДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.amount_vat_document IS 'СуммаНДСДокумента';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        dataversion,
        deletionmark,
        doc_number,
        doc_date,
        posted,
        operation_type,
        a_comment,
        organization_key,
        account_key,
        counterparty_agreement_key,
        document_base,
        document_base_type,
        doc_number_of_incoming_document,
        incoming_document_doc_date,
        document_amount,
        amount_vat_document
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        doc_number = EXCLUDED.doc_number,
        doc_date = EXCLUDED.doc_date,
        posted = EXCLUDED.posted,
        operation_type = EXCLUDED.operation_type,
        a_comment = EXCLUDED.a_comment,
        organization_key = EXCLUDED.organization_key,
        account_key = EXCLUDED.account_key,
        counterparty_agreement_key = EXCLUDED.counterparty_agreement_key,
        document_base = EXCLUDED.document_base,
        document_base_type = EXCLUDED.document_base_type,
        doc_number_of_incoming_document = EXCLUDED.doc_number_of_incoming_document,
        incoming_document_doc_date = EXCLUDED.incoming_document_doc_date,
        document_amount = EXCLUDED.document_amount,
        amount_vat_document = EXCLUDED.amount_vat_document
    ;
    """
    return sql.replace("'", "")


async def main_doc_tax_receipt_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, False, False, True)
    maket = await create_model_async(17)
    sql = await sql_insert(maket)
    sql = sql.replace("$5,", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$15,", "to_timestamp($15, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_tax_receipt_async())
