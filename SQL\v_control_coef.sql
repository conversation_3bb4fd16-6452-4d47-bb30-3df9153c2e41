
    CREATE OR REPLACE VIEW v_control_coef
    AS SELECT DISTINCT sale.sku,
        mlt.serv_date,
        mlt.tarih,
        mlt.doc_number,
        sale.inbox AS "ед в крб по отчету",
        mlt.coef_doc AS "ед в крб по док.поступл"
       FROM t_one_sale sale
         JOIN v_service_tc_maliyet mlt ON mlt.nomenclature_key::text = sale.nomenclature_key::text
      WHERE sale.inbox <> mlt.coef_doc
      ORDER BY sale.sku;
    