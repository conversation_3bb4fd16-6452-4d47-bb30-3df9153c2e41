from prestige_authorize import con_postgres_psycopg2, change_data_in_table_bl

conpg = con_postgres_psycopg2()

sql_create_table_t_authorize = '''
    -- DROP TABLE IF EXISTS t_authorize CASCADE;

    CREATE TABLE IF NOT EXISTS t_authorize (
        id smallserial NOT NULL,
        edrpou numeric(10) NOT NULL,
        isim varchar(200) NOT NULL,
        ids varchar(200) NOT NULL,
        tokens varchar(500) NOT NULL,
        CONSTRAINT t_authorize_pk PRIMARY KEY (id)
    );
    COMMENT ON TABLE t_authorize IS 'Банк Авторизация';
'''

sql_create_table_t_telegram = '''
    -- DROP TABLE IF EXISTS t_telegram CASCADE;

    CREATE TABLE IF NOT EXISTS t_telegram (
        id serial4 NOT NULL,
        chat_id numeric(25) NOT NULL,
        first_name varchar(25) NULL,
        last_name varchar(25) NULL,
        username varchar(25) NULL,
        msj text NULL,
        den date NULL DEFAULT CURRENT_DATE,
        tlgdate timestamp NULL,
        tarih timestamp NULL DEFAULT CURRENT_TIMESTAMP
        CONSTRAINT t_telegram_pkey PRIMARY KEY (id)
    );
'''


sql_create_table_t_telegram_policy = '''
    -- DROP TABLE IF EXISTS t_telegram_policy CASCADE;

    CREATE TABLE IF NOT EXISTS t_telegram_policy (
        id smallserial NOT NULL,
        idchat numeric(25) NOT NULL,
        idfop varchar(10) NOT NULL,
        chatowner varchar(50) NOT NULL,
        fopowner varchar(50) NOT NULL,
        description varchar(255) NULL,
        CONSTRAINT t_telegram_policy_pkey PRIMARY KEY (id),
        CONSTRAINT t_telegram_policy_fk FOREIGN KEY (idfop) 
        REFERENCES t_authorize(id) ON DELETE CASCADE ON UPDATE CASCADE        
    );
'''

sql_create_table_t_pb = '''
   -- DROP TABLE IF EXISTS t_pb CASCADE;

    CREATE TABLE IF NOT EXISTS t_pb (
        idt serial4 NOT NULL,
        idklienta varchar NULL,
        aut_my_crf varchar(200) NULL,
        aut_my_mfo varchar(200) NULL,
        aut_my_acc varchar(200) NULL,
        aut_my_nam varchar(200) NULL,
        aut_my_mfo_name varchar(200) NULL,
        aut_my_mfo_city varchar(200) NULL,
        aut_cntr_crf varchar(200) NULL,
        aut_cntr_mfo varchar(200) NULL,
        aut_cntr_acc varchar(200) NULL,
        aut_cntr_nam varchar(200) NULL,
        aut_cntr_mfo_name varchar(200) NULL,
        aut_cntr_mfo_city varchar(200) NULL,
        ccy varchar(200) NULL,
        fl_real varchar(200) NULL,
        pr_pr varchar(200) NULL,
        doc_typ varchar(200) NULL,
        num_doc varchar(200) NULL,
        dat_kl date NULL,
        dat_od date NULL,
        osnd varchar(200) NULL,
        sum numeric(10, 2) NULL,
        sum_e numeric(10, 2) NULL,
        "ref" varchar(200) NULL,
        refn varchar(200) NULL,
        tim_p varchar(10) NULL,
        date_time_dat_od_tim_p timestamp NULL,
        id varchar(200) NULL,
        trantype varchar(200) NULL,
        dlr varchar(200) NULL,
        technical_transaction_id varchar(200) NULL,
        adet numeric(10, 2) NULL DEFAULT 0,
        dblsumbrutto numeric(10, 2) NULL DEFAULT 0,
        dblkomissiya numeric(10, 2) NULL DEFAULT 0,
        dblsumnetto numeric(10, 2) NULL,
        tlg bool NULL DEFAULT false,
        nhareketid numeric NULL,
        idnebim varchar(15) NULL,
        idbazi numeric NULL,
        dblusd numeric(10, 2) NULL DEFAULT 0,
        dblkurs numeric(10, 2) NULL DEFAULT 0,
        strtip varchar(25) NULL,
        dtcreated timestamp NULL DEFAULT now(),
        dtchange timestamp NULL DEFAULT now(),
        unqkey varchar(50) NOT NULL,
        idkasa numeric NULL,
        idinternet numeric NULL,
        idchata numeric(15) NULL,
        fop varchar(30) NULL,
        tlg_all int2 NULL DEFAULT 0,  -- otpravleno sms: 1- mne; 2-klientu; 3 - klientu i mne
        iade numeric(10, 2) NULL,  -- возврат средст клиенту по эквайрингу
        payer_ultmt_nceo varchar NULL,
        payer_ultmt_name varchar NULL,
        CONSTRAINT t_pb_pkey PRIMARY KEY (idt),
        CONSTRAINT t_pb_nhareketid_key UNIQUE (nhareketid),
        CONSTRAINT t_pb_ref_refn_unq UNIQUE (ref, refn, trantype),
        CONSTRAINT t_pb_unqkey_key UNIQUE (unqkey)
    );
'''

def main_authorize():
    change_data_in_table_bl(sql_create_table_t_authorize, conpg)
    change_data_in_table_bl(sql_create_table_t_telegram, conpg)
    change_data_in_table_bl(sql_create_table_t_telegram_policy, conpg)
    change_data_in_table_bl(sql_create_table_t_pb, conpg)

    print("main_authorize OK")


if __name__ == '__main__':
    main_authorize()
