import os
import shutil
import datetime
import asyncio
import aiohttp
import aiosqlite
import pandas as pd
from abc import ABC, abstractmethod
# from pathlib import Path  # Не используется
import win32com.client
import time
from typing import List, Dict, Any

URL_CONST = "http://195.128.227.141/utp_prestige/odata/standard.odata/"

# --- Интерфейсы (Абстрактные Базовые Классы) ---

class FileCopier(ABC):
    @abstractmethod
    def copy_file(self, source: str, destination: str) -> str:
        pass

class DatabaseManager(ABC):
    @abstractmethod
    async def create_table(self, db):
        pass

    @abstractmethod
    async def insert_data(self, db, data: List[tuple]):
        pass

class ApiClient(ABC):
    @abstractmethod
    async def fetch_data(self, url: str) -> Dict[str, Any]:
        pass

class ExcelProcessor(ABC):
    @abstractmethod
    def process_file(self, file_path: str, photo_folder: str):
        pass

# --- Конкретные Реализации ---

class SimpleFileCopier(FileCopier):
    def copy_file(self, source: str, destination: str) -> str:
        shutil.copy2(source, destination)
        return destination

class SQLiteStockDatabaseManager(DatabaseManager):
    async def create_table(self, db):
        await db.execute('DROP TABLE IF EXISTS stock')
        await db.execute('''
            CREATE TABLE IF NOT EXISTS stock (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                Склад_Key TEXT,
                Номенклатура_Key TEXT,
                СерияНоменклатуры_Key TEXT,
                Количество DECIMAL(10,3)
            )
        ''')
        await db.commit()

    async def insert_data(self, db, data: List[tuple]):
        await db.executemany(
            "INSERT INTO stock (Склад_Key, Номенклатура_Key, СерияНоменклатуры_Key, Количество) VALUES (?, ?, ?, ?)",
            data
        )
        await db.commit()

class SQLiteNomenclatureDatabaseManager(DatabaseManager):
    async def create_table(self, db):
        await db.execute('DROP TABLE IF EXISTS nomenclature')
        await db.execute('''
            CREATE TABLE IF NOT EXISTS nomenclature (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ref_key TEXT,
                code TEXT,
                description TEXT,
                is_folder BOOLEAN
            )
        ''')
        await db.commit()

    async def insert_data(self, db, data: List[tuple]):
        await db.executemany(
            "INSERT INTO nomenclature (ref_key, code, description, is_folder) VALUES (?, ?, ?, ?)",
            data
        )
        await db.commit()

class SQLiteWarehauseDatabaseManager(DatabaseManager):
    async def create_table(self, db):
        await db.execute('DROP TABLE IF EXISTS warehause')
        await db.execute('''
            CREATE TABLE IF NOT EXISTS warehause (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ref_key TEXT,
                code TEXT,
                description TEXT,
                is_folder BOOLEAN
            )
        ''')
        await db.commit()

    async def insert_data(self, db, data: List[tuple]):
        await db.executemany(
            "INSERT INTO warehause (ref_key, code, description, is_folder) VALUES (?, ?, ?, ?)",
            data
        )
        await db.commit()

class HttpApiClient(ApiClient):
    def __init__(self, auth: tuple):
        self.auth = auth

    async def fetch_data(self, url: str) -> Dict[str, Any]:
        print(f"\nЗапрос к API: {url}")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, auth=aiohttp.BasicAuth(*self.auth)) as response:
                    if response.status != 200:
                        print(f"Ошибка при запросе: {response.status}")
                        text = await response.text()
                        print(f"Текст ошибки: {text}")
                        return {"value": []}

                    data = await response.json()
                    print(f"Запрос выполнен успешно, получено записей: {len(data.get('value', []))}")
                    return data
        except Exception as e:
            print(f"Ошибка при выполнении запроса: {e}")
            return {"value": []}

class Win32ExcelProcessor(ExcelProcessor):
    def __init__(self):
        self.excel = win32com.client.Dispatch("Excel.Application")
        self.excel.Visible = False

    def process_file(self, file_path: str, photo_folder: str):
        # Открытие рабочей книги
        workbook = self.excel.Workbooks.Open(file_path)
        sheet = workbook.Sheets(1)

        # Получение файлов фотографий
        photo_files = [f for f in os.listdir(photo_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]

        # Установка размеров столбцов и строк
        for i in range(1, len(photo_files) + 1):
            sheet.Rows(i).RowHeight = 100
        sheet.Columns("A:A").ColumnWidth = 18
        sheet.Columns("B:B").ColumnWidth = 0.5

        # Вставка фотографий и метаданных
        for i, photo_file in enumerate(photo_files, 1):
            photo_path = os.path.join(photo_folder, photo_file)
            cell = sheet.Cells(i, 1)

            # Вставка изображения
            picture = sheet.Shapes.AddPicture(
                Filename=photo_path,
                LinkToFile=False,
                SaveWithDocument=True,
                Left=cell.Left,
                Top=cell.Top,
                Width=100,
                Height=100
            )

            # Настройка изображения
            picture.Width = 100
            picture.Height = 100
            picture.OnAction = "ToggleImageSize"
            picture.Placement = 1  # xlMoveAndSize

            # Запись имени файла в столбец C
            file_name_without_ext = os.path.splitext(photo_file)[0]
            sheet.Cells(i, 3).NumberFormat = "@"
            sheet.Cells(i, 3).Value = str(file_name_without_ext)

            time.sleep(0.1)

        # Сохранение и закрытие
        workbook.Save()
        workbook.Close()
        self.excel.Quit()

# --- Логика Приложения ---

class PrestigeApp:
    # комментарий
    def __init__(
        self,
        file_copier: FileCopier,
        stock_db_manager: DatabaseManager,
        nomenclature_db_manager: DatabaseManager,
        warehause_db_manager: DatabaseManager,
        api_client: ApiClient,
        excel_processor: ExcelProcessor
    ):
        self.file_copier = file_copier
        self.stock_db_manager = stock_db_manager
        self.nomenclature_db_manager = nomenclature_db_manager
        self.warehause_db_manager = warehause_db_manager
        self.api_client = api_client
        self.excel_processor = excel_processor

    async def run(self, photo_folder: str):
        current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M")
        # db_name = f"prestige_{current_datetime}.db"
        db_name = f"prestige.db"
        excel_file_name = f"Price_{current_datetime}.xls"
        # stock_file_name = f"AccumulationRegister_ТоварыНаСкладах_RecordType_{current_datetime}.xlsx"

        # Копирование файла Excel
        source_file = "PricePrestigeWithPhoto.xls"
        self.file_copier.copy_file(source_file, excel_file_name)
        full_path = os.path.abspath(excel_file_name)

        # Обработка файла Excel с фотографиями
        self.excel_processor.process_file(full_path, photo_folder)

        # Создание и заполнение базы данных
        async with aiosqlite.connect(db_name) as db:
            # Создание таблиц
            await self.stock_db_manager.create_table(db)
            await self.nomenclature_db_manager.create_table(db)
            await self.warehause_db_manager.create_table(db)

            warehause_url = (URL_CONST + "Catalog_Склады?"
                "$format=json&$inlinecount=allpages"
                "&$filter=IsFolder eq false"
                "&$select=Ref_Key,Code,Description,IsFolder"
            )
            warehause_data = await self.api_client.fetch_data(warehause_url)
            warehause_records = [(item['Ref_Key'], item['Code'], item['Description'], item['IsFolder']) for item in warehause_data.get('value', [])]
            await self.warehause_db_manager.insert_data(db, warehause_records)

            # Получение и обработка данных номенклатуры
            # Фильтрация на стороне сервера не работает с полем ВидНоменклатуры_Key
            # Используем фильтрацию на стороне клиента
            target_type_guid = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'

            # Запрашиваем все данные с необходимыми полями
            # Используем $top=5000 для ограничения количества записей
            nomenclature_url = f"{URL_CONST}Catalog_Номенклатура?$format=json&$select=Ref_Key,Code,Description,IsFolder,ВидНоменклатуры_Key"
            nomenclature_data = await self.api_client.fetch_data(nomenclature_url)

            # Фильтрация на стороне клиента
            print(f"Фильтрация по виду номенклатуры '{target_type_guid}'...")
            filtered_items = [item for item in nomenclature_data.get('value', [])
                             if item.get('ВидНоменклатуры_Key') == target_type_guid]

            print(f"Всего получено записей: {len(nomenclature_data.get('value', []))}")
            print(f"После фильтрации: {len(filtered_items)}")

            # Если есть записи, показываем пример
            if filtered_items:
                example = filtered_items[0]
                print(f"Пример записи: {example['Description']} (Code: {example['Code']})")

            nomenclature_records = [(item['Ref_Key'], item['Code'], item['Description'], item['IsFolder']) for item in filtered_items]
            await self.nomenclature_db_manager.insert_data(db, nomenclature_records)

            # Получение и обработка данных о запасах
            select_column = "Period,RecordType,Склад_Key,Номенклатура_Key,СерияНоменклатуры_Key,Количество"
            stock_url = (URL_CONST + f"AccumulationRegister_ТоварыНаСкладах_RecordType?$format=json&$inlinecount=allpages"
                f"&$select={select_column}"
            )
            stock_data = await self.api_client.fetch_data(stock_url)

            df_stock = pd.DataFrame(stock_data.get('value', []))
            if not df_stock.empty and 'RecordType' in df_stock.columns and 'Количество' in df_stock.columns:
                df_stock['Количество'] = df_stock.apply(
                    lambda row: -row['Количество'] if row['RecordType'] == 'Expense' else row['Количество'],
                    axis=1
                )
                df_stock = df_stock.groupby(
                    ['Склад_Key', 'Номенклатура_Key', 'СерияНоменклатуры_Key']
                ).agg({'Количество': 'sum'}).reset_index()

                records = df_stock[['Склад_Key', 'Номенклатура_Key', 'СерияНоменклатуры_Key', 'Количество']].to_records(index=False)
                data_to_insert = [(r[0], r[1], r[2], r[3]) for r in records]
                await self.stock_db_manager.insert_data(db, data_to_insert)

        # Очистка базы данных
        # if os.path.exists(db_name):
        #     os.remove(db_name)

# --- Точка Входа ---

def clear_console():
    # На Windows используем 'cls', на других ОС - 'clear'
    os.system('cls' if os.name == 'nt' else 'clear')

if __name__ == "__main__":
    clear_console()
    print(f"Start: {datetime.datetime.now()}")

    # Внедрение Зависимостей
    app = PrestigeApp(
        file_copier=SimpleFileCopier(),
        stock_db_manager=SQLiteStockDatabaseManager(),
        nomenclature_db_manager=SQLiteNomenclatureDatabaseManager(),
        warehause_db_manager=SQLiteWarehauseDatabaseManager(),
        api_client=HttpApiClient(auth=('rasim', '15021972')),
        excel_processor=Win32ExcelProcessor()
    )

    # Запуск приложения
    photo_folder = r"C:\Rasim\Python\Prestige"
    asyncio.run(app.run(photo_folder))

    print(f"End: {datetime.datetime.now()}")