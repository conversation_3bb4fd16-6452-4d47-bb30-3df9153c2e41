import asyncio
import os

from async_Postgres import async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_cash"
DOCUMENT = "Catalog_Кассы"
SELECT_COLUMNS = 'Code,DataVersion,DeletionMark,Description,IsFolder,Predefined,Owner_Key,Parent_Key,Ref_Key,ВалютаДенежныхСредств_Key'

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15) NOT NULL DEFAULT 0,  -- Code
        dataversion varchar(15) NOT NULL,  -- DataVersion
        deletionmark bool NOT NULL DEFAULT false,  -- DeletionMark
        description varchar(200) NOT NULL,  -- Description
        isfolder bool NOT NULL DEFAULT false,  -- Is<PERSON><PERSON><PERSON>
        predefined bool NOT NULL DEFAULT false,  -- Predefined
        organization_key varchar(50) NOT NULL,  -- organization_key
        parent_key varchar(50) NOT NULL,  -- Parent_Key
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        currency_key varchar(50) NOT NULL,  -- ВалютаДенежныхСредств_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'organization_key';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДенежныхСредств_Key';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        code,
        dataversion,
        deletionmark,
        description,
        isfolder,
        predefined,
        organization_key,
        parent_key,
        ref_key,
        currency_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        code = EXCLUDED.code,
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        description = EXCLUDED.description,
        isfolder = EXCLUDED.isfolder,
        predefined = EXCLUDED.predefined,
        organization_key = EXCLUDED.organization_key,
        parent_key = EXCLUDED.parent_key,
        ref_key = EXCLUDED.ref_key,
        currency_key = EXCLUDED.currency_key
    '''
    return sql.replace("'", "")


async def main_cat_cash_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_cash_async())
