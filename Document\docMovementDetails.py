# док перемещение между складами
import asyncio
import os

from async_Postgres import async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

TABLE_NAME = "t_one_doc_movement_details"
DOCUMENT = "Document_ПеремещениеТоваров_Товары"

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

SELECT_COLUMNS = "Ref_Key,LineNumber,Номенклатура_Key,ЕдиницаИзмерения_Key,Коэффициент,Количество,СерияНоменклатуры_Key"

SQL_CREATE_TABLE = f"""    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial not null,
        line_number numeric(10, 4) NOT NULL DEFAULT 0,  -- LineNumber
        quantity numeric(10, 4) NOT NULL DEFAULT 0,  -- Количество
        coefficient numeric(10, 4) NOT NULL DEFAULT 0,  -- Коэффициент
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        nomenclature_key varchar(50) NOT NULL,  -- Номенклатура_Key
        nomenclature_series_key varchar(40) NULL,  -- СерияНоменклатуры_Key
        unit_of_key varchar(50) NOT NULL,  -- ЕдиницаИзмерения_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
        
"""

async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        line_number,
        nomenclature_key,
        unit_of_key,
        coefficient,
        quantity,
        nomenclature_series_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        nomenclature_key = EXCLUDED.nomenclature_key,
        unit_of_key = EXCLUDED.unit_of_key,
        coefficient = EXCLUDED.coefficient,
        quantity = EXCLUDED.quantity,
        nomenclature_series_key = EXCLUDED.nomenclature_series_key
    ;
    """
    return sql.replace("'", "")


async def main_doc_movement_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(7)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
        
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_movement_details_async())
