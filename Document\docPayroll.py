import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_payroll"
DOCUMENT = "Document_НачислениеЗарплатыРаботникамОрганизаций"
SELECT_COLUMNS = (
    "Ref_Key,DataVersion,DeletionMark,Number,Date,Posted,ПериодРегистрации,Организация_Key,Комментарий,"
    "КраткийСоставДокумента,ОтражатьВУпрУчете,ОтражатьВРеглУчете"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NOT NULL,
        deletion_mark boolean NOT NULL DEFAULT FALSE,
        doc_number varchar(20) NOT NULL,
        doc_date timestamp NOT NULL,
        posted boolean NOT NULL DEFAULT FALSE,
        registration_period timestamp NOT NULL,
        a_comment varchar(300) NOT NULL,
        brief_structure_of_the_document varchar(300) NOT NULL,
        is_management boolean NOT NULL DEFAULT FALSE,
        is_accounting boolean NOT NULL DEFAULT FALSE,
        ref_key varchar(50) NOT NULL,
        organization_key varchar(50) NOT NULL,
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.registration_period IS 'ПериодРегистрации';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.brief_structure_of_the_document IS 'КраткийСоставДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУпрУчете';
    COMMENT ON COLUMN {TABLE_NAME}.is_accounting IS 'ОтражатьВРеглУчете';

"""


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME}
        (
            ref_key,
            dataversion,
            deletion_mark,
            doc_number,
            doc_date,
            posted,
            registration_period,
            organization_key,
            a_comment,
            brief_structure_of_the_document,
            is_management,
            is_accounting
        )
        VALUES {maket}
        ON CONFLICT (ref_key)
        DO UPDATE SET
            dataversion = EXCLUDED.dataversion,
            deletion_mark = EXCLUDED.deletion_mark,
            doc_number = EXCLUDED.doc_number,
            doc_date = EXCLUDED.doc_date,
            posted = EXCLUDED.posted,
            registration_period = EXCLUDED.registration_period,
            organization_key = EXCLUDED.organization_key,
            a_comment = EXCLUDED.a_comment,
            brief_structure_of_the_document = EXCLUDED.brief_structure_of_the_document,
            is_management = EXCLUDED.is_management,
            is_accounting = EXCLUDED.is_accounting
    ;
    """
    return sql.replace("'", "")


async def main_doc_payroll_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    sql = sql.replace("$5", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    sql = sql.replace("$7", "to_timestamp($7, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_payroll_async())
