
    CREATE OR REPLACE VIEW v_stickers AS 
    SELECT 
        idchat,
        username сотрудник,
        datesticker дата,
        sku_code as sku,
        x.quantity AS количество,
        x.amount AS сумма
    FROM t_stickers x
        LEFT JOIN
        (SELECT DISTINCT chat_id , username
        FROM t_telegram) AS t
        ON chat_id = idchat
--    GROUP BY username, datesticker, idchat
    ORDER BY datesticker, username
    ;

    GRANT SELECT ON TABLE v_stickers TO user_prestige;
