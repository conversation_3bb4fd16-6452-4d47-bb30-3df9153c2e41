import asyncpg
import asyncio
from datetime import datetime
import pandas as pd
from sqlalchemy import create_engine

DB_CONFIG = {
    'database': 'MEDOC',
    'user': 'postgres',
    'password': 'hfvpfc15',
    'host': '127.0.0.1',
    'port': '5432',
    'server_settings': {
        'zero_damaged_pages': 'on',
        'ignore_checksum_failure': 'on'
    }
}
DB_CONFIG_SYNC = {
    'dbname': 'MEDOC',
    'user': 'postgres',
    'password': 'hfvpfc15',
    'host': '127.0.0.1',
    'port': '5432'
}


async def insert_data(conn, code):
    """
    Вставка данных из таблицы docsign в таблицу docsign_fixed.
    """
    sql = """
        INSERT INTO docsign_fixed (
            code, cardcode, filetype, state, crc, signfile, tmpsign,
            filename, signtype, govfilenm, signnum, tspocsp, hrefwassend
        )
        SELECT 
            code, cardcode, filetype, state, crc, signfile, tmpsign,
            filename, signtype, govfilenm, signnum, tspocsp, hrefwassend
        FROM public.docsign
        WHERE code = $1
    """
    try:
        await conn.execute(sql, code)
        return None
    except asyncpg.DataCorruptedError as e:
        return f"TOAST Error: {str(e)}; code: {code}"
    except Exception as e:
        return f"General Error: {str(e)}; code: {code}"


async def create_error_log_table(conn):
    """
    Создание таблицы для логирования ошибок.
    """
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS restore_errors (
            id SERIAL PRIMARY KEY,
            error_time TIMESTAMP DEFAULT NOW(),
            code INTEGER,
            error_message TEXT
        )
    """)


async def log_error(conn, code, message):
    """
    Логирование ошибки в таблицу restore_errors.
    """
    await conn.execute("""
        INSERT INTO restore_errors(code, error_message)
        VALUES($1, $2)
    """, code, message)


async def process_batch(conn, codes):
    """
    Обработка пакета кодов.
    """
    results = []
    for code in codes:
        error = await insert_data(conn, code)
        if error:
            await log_error(conn, code, error)
            results.append(error)
    return results


async def quick_check():
    """
    Основная функция для восстановления данных.
    """
    conn = None
    try:
        # Подключение к базе данных
        conn = await asyncpg.connect(**DB_CONFIG)
        print("Connected to database")
        # engine = create_engine(
        #     f"postgresql+psycopg2://{DB_CONFIG_SYNC['user']}:{DB_CONFIG_SYNC['password']}@"
        #     f"{DB_CONFIG_SYNC['host']}:{DB_CONFIG_SYNC['port']}/{DB_CONFIG_SYNC['dbname']}")
        #
        # df = pd.read_sql("SELECT * FROM docsign_original", engine)
        # print(df.head())
        await conn.execute("DROP TABLE IF EXISTS docsign_fixed")
        await conn.execute("DROP TABLE IF EXISTS restore_errors")

        # Создание таблицы для логирования ошибок
        await create_error_log_table(conn)

        # Проверка существования таблицы docsign
        exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 
                FROM pg_tables 
                WHERE schemaname = 'public' 
                AND tablename = 'docsign'
            )
        """)

        if not exists:
            return "Таблица docsign не найдена"

        await conn.execute("""
            CREATE TABLE docsign_fixed (
                code integer PRIMARY KEY,
                cardcode integer,
                filetype integer,
                state integer,
                crc integer,
                signfile bytea,
                tmpsign bytea,
                filename varchar(104),
                signtype integer,
                govfilenm varchar(104),
                signnum smallint,
                tspocsp smallint,
                hrefwassend varchar(10)
            )
        """)

        # Получение списка всех кодов из таблицы docsign
        codes = await conn.fetch("""
            SELECT code 
            FROM docsign 
            ORDER BY code
        """)
        codes = [r['code'] for r in codes]

        # Пакетная обработка по 100 записей
        batch_size = 100
        total = len(codes)
        errors = []

        for i in range(0, total, batch_size):
            batch = codes[i:i + batch_size]
            print(f"Processing batch {i // batch_size + 1}/{(total // batch_size) + 1}")
            batch_errors = await process_batch(conn, batch)
            errors.extend(batch_errors)

            if batch_errors:
                print(f"Найдено ошибок в пакете: {len(batch_errors)}")

        # Переименование таблиц
        await conn.execute("""
            ALTER TABLE IF EXISTS docsign 
            RENAME TO docsign_corrupted_backup            
        """)
        await conn.execute("""
            ALTER TABLE docsign_fixed 
            RENAME TO docsign
        """)

        return f"Успешно завершено. Ошибок: {len(errors)}"

    except Exception as e:
        return f"Критическая ошибка: {str(e)}"
    finally:
        # Закрытие соединения с базой данных
        if conn and not conn.is_closed():
            await conn.close()




def analyze_bin(file_path):
    from pathlib import Path
    data = Path(file_path).read_bytes()
    print(f"Анализ файла: {file_path}")
    print(f"Размер данных: {len(data):,} байт")
    print(f"Первые 16 байт в HEX: {data[:16].hex()}")
    print(f"Строковые данные: {data[:100].decode(errors='replace')}\n")


import sys
from pgcopy import CopyManager
from io import BytesIO
import asyncpg
from pathlib import Path


async def restore_from_bin(file_path, code):
    # Чтение бинарных данных
    data = Path(file_path).read_bytes()

    # Создаем буфер с заголовком PGCOPY
    buf = BytesIO(data)
    buf.seek(0)

    # Подключаемся к БД
    conn = await asyncpg.connect(
        host='localhost',
        user='postgres',
        password='hfvpfc15',
        database='MEDOC'
    )

    try:
        # Создаем временную таблицу
        await conn.execute("""
            CREATE TEMP TABLE IF NOT EXISTS temp_toast_restore (
                id serial,
                data bytea
            )
        """)

        # Используем менеджер копирования
        mgr = CopyManager(conn, 'temp_toast_restore', ['data'])
        with buf:
            mgr.copy(buf)

        # Извлекаем восстановленные данные
        restored = await conn.fetchval("""
            SELECT data FROM temp_toast_restore ORDER BY id LIMIT 1
        """)

        # Обновляем оригинальную запись
        await conn.execute("""
            UPDATE docsign_original
            SET signfile = $1, 
                tmpsign = $1,
                filename = filename || '_restored'
            WHERE code = $2
        """, restored, code)

        return True
    except Exception as e:
        print(f"Ошибка восстановления {file_path}: {str(e)}")
        return False
    finally:
        await conn.close()


# Использование
async def main():
    files = {
        223724: r'E:\backup_medoc\chunk_1711623.bin',
        507209: r'E:\backup_medoc\chunk_17364864.bin',
        507210: r'E:\backup_medoc\chunk_17364865.bin'
    }

    for code, path in files.items():
        success = await restore_from_bin(path, code)
        print(f"Код {code}: {'Успех' if success else 'Ошибка'}")


if __name__ == "__main__":
    import asyncio

    # asyncio.run(main())
#     analyze_bin(r'E:\backup_medoc\chunk_1711623.bin')
#     analyze_bin(r'E:\backup_medoc\chunk_17364864.bin')
#     analyze_bin(r'E:\backup_medoc\chunk_17364865.bin')
    print("Начало работы:", datetime.now())
    result = asyncio.run(quick_check())
    print("\nРезультат:", result)
    print("Конец работы:", datetime.now())