DROP VIEW IF EXISTS v_one_cash_warrant_expense CASCADE;
CREATE OR REPLACE VIEW v_one_cash_warrant_expense AS
SELECT
    main.dataversion,
    main.doc_date,
    main.doc_number,
    main.operation_type,
    main.base,
    clients.description AS client,
    cur2.description AS document_currency,
    main.a_comment,
    main.application,
  - main.amount as document_amount,
    main.vat_rate,
    cur1.description AS worker_settlement_currency,
    cash.description AS checkout,
    assign.description AS assignment_of_money,
    org.description AS organization,
    main_item.description AS main_cash_flow_item_name,
    det_item.description AS det_cash_flow_item_name,
    contract.description AS contract,
    main.ref_key,
    main.organization_key,
    main.cash_flow_item main_cash_flow_item_key,
    detail.cash_flow_item det_cash_flow_item_key,
    main.currency_key,
    main.contractor_key as customer_key,
    detail.line_number,
    main.is_management
FROM t_one_doc_cash_warrant_expense AS main
    LEFT JOIN t_one_doc_cash_warrant_expense_details AS detail
        USING(ref_key)
    LEFT JOIN t_one_cat_counterparties AS clients
        ON clients.ref_key = main.contractor_key
    LEFT JOIN t_one_cat_currencies AS cur1
        ON cur1.ref_key = main.worker_settlement_currency_key
    LEFT JOIN t_one_cat_currencies AS cur2
        ON cur2.ref_key = main.currency_key
    LEFT JOIN t_one_cat_cash AS cash
        ON cash.ref_key = main.checkout_key
    LEFT JOIN t_one_cat_cash_assignment AS assign
        ON assign.ref_key = main.assignment_key
    LEFT JOIN t_one_cat_organizations AS org
        ON org.ref_key = main.organization_key
    LEFT JOIN t_one_cat_cash_flow_item AS main_item
        ON main_item.ref_key = main.cash_flow_item
    LEFT JOIN t_one_cat_contracts_counterparties AS contract
        ON contract.ref_key = detail.contract_key
    LEFT JOIN t_one_cat_cash_flow_item AS det_item
        ON det_item.ref_key = detail.cash_flow_item
WHERE main.posted
ORDER BY main.doc_date DESC
;

COMMENT ON VIEW v_one_cash_warrant_expense IS 'Расходный кассовый ордер';

