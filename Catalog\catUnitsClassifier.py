import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_units_classifier"
DOCUMENT = "Catalog_КлассификаторЕдиницИзмерения"
SELECT_COLUMNS = "Ref_Key,Code,DeletionMark,Description,Predefined,МеждународноеСокращение,НаименованиеПолное"

SQL_CREATE_TABLE = f'''
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key varchar(50) NOT NULL,
        code varchar(15) NOT NULL DEFAULT 0,
        description varchar(50) NOT NULL,
        international_abbreviation varchar(50) NOT NULL,
        name_full varchar(50) NOT null,
        deletion_mark bool NOT NULL,
        predefined bool NOT NULL,
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)    
        );        
        
        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
        COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
        COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
        COMMENT ON COLUMN {TABLE_NAME}.international_abbreviation IS 'МеждународноеСокращение';
        COMMENT ON COLUMN {TABLE_NAME}.name_full IS 'НаименованиеПолное';
    '''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}( 
        ref_key,
        code,
        deletion_mark,
        description,
        predefined,
        international_abbreviation,
        name_full
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        code = EXCLUDED.code,
        deletion_mark = EXCLUDED.deletion_mark,
        description = EXCLUDED.description,
        predefined = EXCLUDED.predefined,
        international_abbreviation = EXCLUDED.international_abbreviation,
        name_full = EXCLUDED.name_full
    ;
    '''
    return sql.replace("'", "")


sql_delete_not_posted = f'DELETE FROM t_one_cat_units WHERE deletion_mark'


async def main_cat_units_classifier_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(7)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(sql_delete_not_posted)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_units_classifier_async())
