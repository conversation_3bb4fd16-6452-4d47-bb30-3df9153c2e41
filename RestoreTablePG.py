import asyncpg
import psycopg2
import asyncio
from datetime import datetime, date
import pandas as pd
from sqlalchemy import create_engine
from async_Postgres import hostname_public

DB_CONFIG = {
    'database': 'MEDOC',
    'user': 'postgres',
    'password': 'hfvpfc15',
    'host': hostname_public,
    'port': '5432'
}


async def restore_table(conn, code):
    sql = f"""
        UPDATE docsign_fixed df
        SET signfile = d.signfile,
            tmpsign = d.tmpsign
        FROM public.docsign d
        WHERE df.code = d.code
            AND d.code = $1
    """
    try:
        async with conn.transaction():
            await conn.execute(sql, code)

    except Exception as e:
        return f"Error: {str(e)}; code: {code}"


async def quick_check():
    conn = None
    cur = None
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        print("Connected to database")

        # Check if we can access the database
        sql = "SELECT current_database(), current_user;"
        result = await conn.fetch(sql)
        print(f"{result}")

        # Check if the original table exists
        exists = await conn.fetch("""
            SELECT count(*)
            FROM pg_tables
            WHERE schemaname = 'public' 
            AND tablename = 'docsign';
        """)
        exists = exists[0]['count']
        print(exists)

        if exists:
            # Get some basic info about the table
            count = await conn.fetch("""
                SELECT count(*) 
                FROM public.docsign 
                WHERE code NOT IN (507353, 507355, 507354);
            """)
            count = count[0]['count']
            print(f"Количество строк в таблице docsign: {count}")

            # 1. Create new table
            await conn.execute("""TRUNCATE TABLE public.docsign_fixed;""")
            print("Удалена таблица docsign_fixed", datetime.now())

            await conn.execute("""
            CREATE TABLE IF NOT EXISTS docsign_fixed (
                code integer PRIMARY KEY,
                cardcode integer,
                filetype integer,
                state integer,
                crc integer,
                signfile bytea,
                tmpsign bytea,
                filename varchar(104),
                signtype integer,
                govfilenm varchar(104),
                signnum smallint,
                tspocsp smallint,
                hrefwassend varchar(10)
            );
            """)
            fix_count = await conn.fetch("""
                SELECT count(*) as count
                FROM pg_tables
                WHERE schemaname = 'public'
                AND tablename = 'docsign_fixed';
            """)
            fix_count = fix_count[0]['count']
            print(fix_count, "Создана новая таблица docsign_fixed", datetime.now())

            # 2. Copy non-TOAST data
            await conn.execute("""
                INSERT INTO docsign_fixed (
                code, cardcode, filetype, state, crc,
                filename, signtype, govfilenm, signnum,
                tspocsp, hrefwassend
            )
            SELECT
                code, cardcode, filetype, state, crc,
                filename, signtype, govfilenm, signnum,
                tspocsp, hrefwassend
            FROM public.docsign
            WHERE code NOT IN (507353, 507355, 507354);
            """)
            print("Скопированы некоррумпированные данные", datetime.now())

            for i in range(1, count+1):
                print(f"Restoring row {i} of {count}")
                await restore_table(conn, i)

            # 4. When complete:
            await conn.execute("""
            BEGIN;
            ALTER TABLE docsign RENAME TO docsign_corrupted_backup;
            ALTER TABLE docsign_fixed RENAME TO docsign;
            COMMIT;
            """)
            print("Переименованы таблицы", datetime.now())

            await conn.execute("""VACUUM FULL public.docsign;""")
            print("Выполнена операция VACUUM FULL", datetime.now())
        else:
            return "Could not find the docsign table. Please verify the table name."

    except Exception as e:
        return f"Database error: {str(e)}"
    finally:
        if conn and not conn.is_closed():
            await conn.close()


if __name__ == "__main__":
    print("Начало работы:", datetime.now())
    result = asyncio.run(quick_check())
    print("\nResult:", result)
    print("Конец работы:", datetime.now())