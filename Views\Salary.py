# дебеторская задолженность с учетом сроков отсрочки платежа согласно Договора
import asyncio
import os
import sys

from CreateAndRunSQLScripts import create_views

# Добавляем корневой каталог проекта в sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from Cash.docCashCorrectionOfRegister import main_doc_cash_correction_of_register_async
from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderWithdrawalOfFunds import main_doc_cash_payment_order_withdrawal_of_funds_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Catalog.catCounterparties import main_cat_counterparties_async
from Catalog.catCounterpartySegment import main_cat_counterparty_segment_async
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async
from Document.docDebtCorrection import main_debt_correction_async
from Document.docReturnOfGoodsFromCustomers import main_doc_return_of_goods_from_customers_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
from async_Postgres import async_save_pg, drop_function
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

VIEW_BALANCE_DETAILS = 'v_one_balance_details'
FUNCTION_SALARY_NAME = 'fn_salary_bonus_manager'  # расчет бонусов менеджерам по месяцам
FUNCTION_FETCH_SALARY_DATA_NAME = 'fetch_salary_data'  # получение данных по бонусам менеджеров

SQL_CREATE_FUNCTION_SALARY = f"""
    CREATE OR REPLACE FUNCTION {FUNCTION_SALARY_NAME}(start_date DATE, end_date DATE)
    RETURNS SETOF refcursor AS $$
    DECLARE
        columns TEXT;
        query TEXT;
        ref refcursor;
    BEGIN
        -- Получаем уникальные месяцы в виде строки для crosstab
        SELECT string_agg(DISTINCT format('"%s" TEXT', TO_CHAR(doc_date, 'YYYY-MM')), ', ')
        INTO columns
        FROM {VIEW_BALANCE_DETAILS}
        WHERE recorder_type IN (
                'ПлатежноеПоручениеВходящее',
                'ПриходныйКассовыйОрдер',
                'РасходныйКассовыйОрдер',
                'ПлатежноеПоручениеИсходящее',
                'КорректировкаДолга',
                'КорректировкаЗаписейРегистров')
          AND contract_type = 'СПокупателем'
          AND doc_date >= start_date
          AND doc_date < end_date;
    
        -- Формируем динамический запрос
        query := format(
            'SELECT * FROM crosstab(
                $q$
                SELECT
                    manager,
                    TO_CHAR(doc_date, ''YYYY-MM'') AS month,
                    SUM(doc_sum) AS amount
                FROM {VIEW_BALANCE_DETAILS}
                WHERE recorder_type IN (''ПлатежноеПоручениеВходящее'',
                                        ''ПриходныйКассовыйОрдер'',
                                        ''РасходныйКассовыйОрдер'',
                                        ''ПлатежноеПоручениеИсходящее'',
                                        ''КорректировкаДолга'',
                                        ''КорректировкаЗаписейРегистров'')
                  AND contract_type = ''СПокупателем''
                  AND doc_date >= %L
                  AND doc_date < %L
                  AND recorder NOT IN
                  (
                    SELECT recorder
                    FROM {VIEW_BALANCE_DETAILS} AS req
                    WHERE recorder_type = ''КорректировкаДолга''
                        AND contract_type = ''СПокупателем''
                        AND doc_date >= %L
                        AND doc_date < %L
                    GROUP BY recorder
                    HAVING count(recorder) > 1 AND sum(doc_sum) = 0
                  )
                GROUP BY manager, TO_CHAR(doc_date, ''YYYY-MM'')
                ORDER BY manager,  month
                $q$,
                $q$
                SELECT DISTINCT TO_CHAR(doc_date, ''YYYY-MM'')
                FROM {VIEW_BALANCE_DETAILS}
                WHERE doc_date >= %L
                  AND doc_date < %L
                  AND recorder NOT IN
                  (
                    SELECT  recorder
                    FROM {VIEW_BALANCE_DETAILS} AS req
                    WHERE recorder_type = ''КорректировкаДолга''
                        AND contract_type = ''СПокупателем''
                        AND doc_date >= %L
                        AND doc_date < %L
                    GROUP BY recorder
                    HAVING count(recorder) > 1 AND sum(doc_sum) = 0
                  )
                ORDER BY 1
                $q$
            ) AS ct (manager TEXT, %s)',
                    start_date, end_date, start_date, end_date, start_date, end_date, start_date, end_date, columns
        );
    
        -- Открываем курсор
        OPEN ref FOR EXECUTE query;
    
        -- Возвращаем курсор
        RETURN NEXT ref;
    END;
    $$ LANGUAGE plpgsql;
    
    COMMENT ON FUNCTION {FUNCTION_SALARY_NAME}(date, date) IS 'расчет бонус менеджерам по месяцам';
"""

SQL_FUNCTION_FETCH_SALARY_DATA = f"""
    CREATE OR REPLACE FUNCTION {FUNCTION_FETCH_SALARY_DATA_NAME}(start_date DATE, end_date DATE)
    RETURNS TABLE (manager TEXT, month TEXT, amount NUMERIC) AS $$
    DECLARE
        ref refcursor;
        row record;
    BEGIN
        -- Вызываем функцию
        SELECT * INTO ref FROM {FUNCTION_SALARY_NAME}(start_date, end_date);
    
        -- Извлекаем строки из курсора
        LOOP
            FETCH NEXT FROM ref INTO row;
            EXIT WHEN NOT FOUND;
            -- Выводим строки
            manager := row.manager;
            month := row.month;
            amount := row.amount;
            RETURN NEXT;
        END LOOP;
        CLOSE ref;
    END;
    $$ LANGUAGE plpgsql;
"""


async def main_view_salary_async():
    function_args = ['DATE', 'DATE']  # аргументы(!), не значения функции

    result = await drop_function(FUNCTION_FETCH_SALARY_DATA_NAME, function_args)
    logger.info(f"{result}, drop {FUNCTION_FETCH_SALARY_DATA_NAME}")

    result = await drop_function(FUNCTION_SALARY_NAME, function_args)
    logger.info(f"{result}, drop {FUNCTION_SALARY_NAME}")

    # расчет бонусов менеджерам по месяцам
    result = await async_save_pg(SQL_CREATE_FUNCTION_SALARY)
    logger.info(f"{result}, SQL_CREATE_FUNCTION_SALARY")

    # получение данных по бонусам менеджеров
    result = await async_save_pg(SQL_FUNCTION_FETCH_SALARY_DATA)
    logger.info(f"{result}, SQL_FUNCTION_FETCH_SALARY_DATA")


async def load_data():
    tasks = [
        main_cat_counterparties_async(),  # "Catalog_Контрагенты"
        main_cat_counterparty_segment_async(),  # "Catalog_СегментыКонтрагентов"
        main_doc_sale_of_goods_services_async(),  # "Document_РеализацияТоваровУслуг"
        main_doc_return_of_goods_from_customers_async(),  # "Document_ВозвратТоваровОтПокупателя"
        main_debt_correction_async(),  # "Document_КорректировкаДолга"
        main_doc_cash_correction_of_register_async(),  # "Document_КорректировкаРегистра"
        main_doc_cash_order_receipt_async(),  # "Document_ПлатежноеПоручениеВходящее"
        main_doc_cash_order_expense_async(),  # "Document_ПлатежноеПоручениеИсходящее"
        main_doc_cash_payment_order_withdrawal_of_funds_async(),  # "Document_ПлатежноеПоручениеСнятиеСредств"
        main_doc_cash_warrant_receipt_async(),  # "Document_ПриходныйКассовыйОрдер"
        main_doc_cash_warrant_expense_async(),  # "Document_РасходныйКассовыйОрдер"
    ]
    await asyncio.gather(*tasks)
    await main_doc_reciprocal_settlements_details_async()
    await create_views()


if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(load_data())
    asyncio.run(main_view_salary_async())
    logger.info(f"FINISH")
