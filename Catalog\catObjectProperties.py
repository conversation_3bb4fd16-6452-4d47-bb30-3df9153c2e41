import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_object_properties"
DOCUMENT = "ChartOfCharacteristicTypes_СвойстваОбъектов"
SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Predefined,Code,Description,НазначениеСвойства_Key,Категория"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        dataversion varchar(50) NOT NULL,  -- DataVersion
        deletionmark bool NOT NULL DEFAULT false,  -- DeletionMark
        predefined bool NOT NULL DEFAULT false,  -- Predefined
        code varchar(50) NOT NULL,  -- Code
        description varchar(150) NOT NULL,  -- Description
        property_key varchar(50) NOT NULL,  -- НазначениеСвойства_Key
        category bool NOT NULL,  -- Категория
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.property_key IS 'НазначениеСвойства_Key';
    COMMENT ON COLUMN {TABLE_NAME}.category IS 'Категория';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        ref_key,
        dataversion,
        deletionmark,
        predefined,
        code,
        description,
        property_key,
        category
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        predefined = EXCLUDED.predefined,
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        property_key = EXCLUDED.property_key,
        category = EXCLUDED.category
    ;
    '''
    return sql.replace("'", "")


async def main_cat_object_properties_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(8)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_object_properties_async())
