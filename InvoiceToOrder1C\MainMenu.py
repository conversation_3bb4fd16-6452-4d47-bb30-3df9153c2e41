# pip install openpyxl
# pip install auto-py-to-exe

import os
import pandas as pd
import asyncio
from tkinter import *
from tkinter import ttk
from tkinter.messagebox import showinfo, OK, WARNING
from datetime import datetime, date, timedelta
from Customers import main_customers_async
from DocumentDetails import main_document_details_async
from ExtraExpenses import main_extra_expenses_async
from GTD import main_gtd_async
from Order import main_create_sale_async
from listOfDocuments import main_list_doc_async

pd.set_option('float_format', '{:.2f}'.format)
pd.options.display.float_format = '${:,.2f}'.format
root = Tk()
root.title("выгрузка в excel")
root.geometry("300x200")
combobox_df = pd.DataFrame()
head_df = pd.DataFrame()


def selected(event):
    global ref_key, customer_key, combobox_df, head_df
    # получаем выделенный в раскрывающемся списке элемент
    selection = combobox.get().split(" ")
    combobox_df['date'] = combobox_df['date'].astype(str)
    head_df = combobox_df.query(f"(date == '{selection[0]}') and (number == '{selection[1]}')")
    return head_df


async def main_menu_async():
    # список документов в раскрывающемся списке
    # the list of documents that will be displayed in ComboboxSelected
    return await main_list_doc_async()


def add_expenses_to_table_df(table_df: pd.DataFrame(), extra_expenses_df: pd.DataFrame()):
    last_column_df = table_df['Сумма_с_НДС']
    for i, row in extra_expenses_df.iterrows():
        doc = f"{row.Контрагент}\n({row.Number})\n{round(row.суммаUAH, 2)} грн"
        table_df[doc] = ((row.суммаUAH / last_column_df.sum()) + 1) * last_column_df
        last_column_df = table_df.iloc[:, len(table_df.columns) - 1]

    table_df['с-сть'] = last_column_df / table_df['Количество']
    table_df['ПолнаяСумма'] = last_column_df
    table_df['с-сть'] = table_df['с-сть'].map('{:.2f}'.format).astype(float)
    table_df['ПолнаяСумма'] = table_df['ПолнаяСумма'].map('{:.2f}'.format).astype(float)
    return table_df


def grouping(table_df):
    result = table_df.groupby(['Номенклатура', 'Номенклатура_Key', 'Цена'])[['Количество', 'сумма']].apply(
        sum).reset_index()
    result = result[['Номенклатура', 'Количество', 'Цена', 'сумма', 'Номенклатура_Key']]
    return result


def get_document_details_and_customer():
    global head_df
    label_text = ''
    ref_key = head_df['ref_key'].values.item() if len(head_df['ref_key']) != 0 else ''
    if ref_key:
        customer_key = head_df['customer_key'].values.item()
        customers_df = asyncio.run(main_customers_async(customer_key))
        foreign_key = head_df['foreign_key'].values.item()
        print("document_key", ref_key)
        head_df_new = pd.merge(head_df, customers_df, on="customer_key")
        sum_doc_uah = head_df_new['суммадокумента'].values.item() * head_df_new[
            'курсвзаиморасчетов'].values.item()
        gtd = asyncio.run(main_gtd_async(ref_key))
        table_df = asyncio.run(main_document_details_async(ref_key))
        table_df['Цена грн'] = table_df['Цена'] * head_df_new['курсвзаиморасчетов'].values.item()
        table_df['Сумма грн'] = table_df['Цена грн'] * table_df['Количество']
        if len(gtd) != 0:
            head_df_new = pd.merge(head_df_new, gtd, on="ref_key")
            table_df['пошлина'] = (head_df_new['пошлина'].values.item() / sum_doc_uah) * table_df[
                'Цена грн'] * table_df['Количество']
            table_df['НДС'] = (head_df_new['НДС'].values.item() / sum_doc_uah) * table_df['Цена грн'] * \
                              table_df['Количество']
        else:
            table_df['пошлина'] = 0
            table_df['НДС'] = 0
            label_text += "НЕ НАШЕЛ ДОКУМЕНТ ГТД\n"

        table_df['цена с пошлиной'] = table_df['Цена грн'] + table_df['пошлина'] / table_df['Количество']
        table_df['сумма с пошлиной'] = table_df['цена с пошлиной'] * table_df['Количество']
        table_df['Цена_с_НДС'] = table_df['цена с пошлиной'] + table_df['НДС'] / table_df['Количество']
        table_df['Сумма_с_НДС'] = table_df['Цена_с_НДС'] * table_df['Количество']
        table_df['с-сть'] = table_df['Цена_с_НДС']
        table_df['ПолнаяСумма'] = table_df['Сумма_с_НДС']
        FILE_NAME = head_df_new['number'].iloc[0]
        file_date = datetime.strptime(head_df_new['date'].values.item(), "%Y-%m-%d").date()
        file_date = datetime.strftime(file_date, "%Y.%m.%d")
        extra_expenses_df = asyncio.run(main_extra_expenses_async(ref_key, customer_key, foreign_key))
        if len(extra_expenses_df) != 0:
            table_df = add_expenses_to_table_df(table_df, extra_expenses_df).reset_index(drop=True)
        else:
            label_text += "НЕ НАШЕЛ ДОКУМЕНТ 'ДОПРАСХОДЫ'\n"

        folder = create_folder_on_desctop()
        path_file = os.path.join(folder, f'{file_date} {FILE_NAME}.xlsx')

        result_add_to_1c = asyncio.run(main_create_sale_async(head_df_new, table_df))
        if result_add_to_1c:
            label_text += f"в 1С создан док 'реализация товаров и услуг': {result_add_to_1c['Number']} " \
                          f"от {result_add_to_1c['Date']}\n\n"
        else:
            label_text += "ДОКУМЕНТ ЗАКАЗ НЕ СОЗДАН\n"

        label_text += f"расчеты сохранены в : {path_file}\n\n"

        with pd.ExcelWriter(path_file) as writer:
            table_df.to_excel(writer, sheet_name='себестоимость', index=False)
            extra_expenses_df.to_excel(writer, sheet_name='допРасходы', index=False)
            gtd.to_excel(writer, sheet_name='ГТД', index=False)
            head_df_new.to_excel(writer, sheet_name='док', index=False)

        showinfo(title="Предупреждение", message=label_text, icon=WARNING, default=OK)


def create_folder_on_desctop():
    folder = os.path.join(os.path.join(os.environ['USERPROFILE']), 'Desktop', 'Bot')
    if not os.path.exists(folder):
        os.makedirs(folder)
    return folder


if __name__ == "__main__":
    data = asyncio.run(main_list_doc_async())
    combobox_df = pd.json_normalize(data)
    combobox_df.columns = [x.lower() for x in combobox_df.columns.values.tolist()]
    combobox_df = combobox_df.rename({'контрагент_key': 'customer_key'}, axis='columns')
    combobox_df = combobox_df.drop(
        combobox_df[
            combobox_df[
                'организация_key'] != 'f51a0e76-4821-11ec-811d-001dd8b72b55'].index).reset_index(
        drop=True)
    combobox_df['date'] = combobox_df['date'].astype('datetime64[ns]')
    combobox_df['date'] = combobox_df['date'].dt.date
    dt = date.today() - timedelta(days=200)
    mask = ((combobox_df['date'] > dt) & (
            combobox_df['организация_key'] == 'f51a0e76-4821-11ec-811d-001dd8b72b55'))
    combobox_df = combobox_df.loc[mask]
    if len(combobox_df) != []:
        combobox_df = combobox_df.sort_values(by=['date'], ascending=False, ignore_index=True)
        for_checkbox = combobox_df[['date', 'number']].values.tolist()
        combobox = ttk.Combobox(values=for_checkbox, state="readonly")
        combobox.pack(anchor=NW, fill=X, padx=5, pady=5)
        combobox.bind("<<ComboboxSelected>>", selected)
        # btn = ttk.Button(text="сформировать", command=lambda: get_document_details_and_customer("See this worked!"))
        btn = ttk.Button(text="сформировать", command=get_document_details_and_customer)
        btn.pack()
    root.mainloop()
