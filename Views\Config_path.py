import os
import sys


def get_path():
    if os.environ['COMPUTERNAME'] == 'PRESTIGEPRODUCT':
        AUTHORIZE_PATH = r"D:\Prestige\Python\Prestige"
        CONFIG_PATH = r"D:\Prestige\Python\Config"
        IMAGE_CONFIG_PATH = r"d:\Prestige\Python\ImageToPDF"
        CONFIG_PATH_VACATION = r"D:\Prestige\Python\VacationApplication"
    else:
        AUTHORIZE_PATH = r"C:\Rasim\Python"
        CONFIG_PATH = "C:\Rasim\Python\Config"
        IMAGE_CONFIG_PATH = r"C:\Rasim\Python\ImageToPDF"
        CONFIG_PATH_VACATION = r"C:\Rasim\Python\VacationApplication"

    sys.path.append(os.path.abspath(CONFIG_PATH))
    sys.path.append(os.path.abspath(AUTHORIZE_PATH))
    sys.path.append(os.path.abspath(IMAGE_CONFIG_PATH))
    sys.path.append(os.path.abspath(CONFIG_PATH_VACATION))


if __name__ == "__main__":
    get_path()
    print(sys.path)
