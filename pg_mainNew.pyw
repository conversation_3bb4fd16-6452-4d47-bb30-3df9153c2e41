# запуск всех скриптов
# 09.09.2022
import asyncio
import os
import time

from Cash.CashAssignmentGroup import main_cash_assignment_group_async
from Cash.docCashAccRegRecordtype import main_doc_cash_acc_reg_recordtype_async
from Cash.docCashCorrectionOfRegister import main_doc_cash_correction_of_register_async
from Cash.docCashMoneyCheck import main_doc_money_check_async
from Cash.docCashMovement import main_doc_cash_movement_async
from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Cash.docCashPaymentOrderExpenseDetails import main_doc_cash_order_expense_details_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderReceiptDetails import main_doc_cash_order_receipt_details_async
from Cash.docCashPaymentOrderWithdrawalOfFunds import main_doc_cash_payment_order_withdrawal_of_funds_async
from Cash.docCashPaymentOrderWithdrawalOfFundsDetails import \
    main_doc_cash_payment_order_withdrawal_of_funds_details_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashWarrantExpenseDetails import main_doc_cash_warrant_expense_details_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Cash.docCashWarrantReceiptDetails import main_doc_cash_warrant_receipt_details_async
from Catalog.InfRegBarcodes import main_info_reg_barcodes_async
from Catalog.InfRegContactInformation import main_infreg_contact_information_async
from Catalog.InfRegDocumentBarcode import main_info_reg_document_barcode_async
from Catalog.InfReg_ObjectPropertyValues import main_cat_object_property_values_async
from Catalog.accRegGoodInWarehausesRecordType import main_accreg_good_in_warehauses_recordtype_async
from Catalog.catBanks import main_cat_banks_async
from Catalog.catCash import main_cat_cash_async
from Catalog.catCashAssignment import main_cat_cash_assignment_async
from Catalog.catCashBankAccounts import main_cat_cash_bank_accounts_async
from Catalog.catCashFlowItem import main_cat_cash_flow_items_async
from Catalog.catContactPersonsOfCounterparties import main_cat_contact_persons_of_counterparties_async
from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catContractsCounterpartiesManagers import main_cat_counterparties_managers_async
from Catalog.catCounterparties import main_cat_counterparties_async
from Catalog.catCounterpartySegment import main_cat_counterparty_segment_async
from Catalog.catCurrency import main_cat_currencies_async
from Catalog.catEmployees import main_cat_employee_async
from Catalog.catIndividuals import main_cat_individuals_async
from Catalog.catNomenclature import main_cat_nomenclature_async
from Catalog.catNomenclatureCounterparty import main_cat_nomenclature_counterparty_async
from Catalog.catNomenclatureGTD import main_cat_nomenclature_gtd_async
from Catalog.catNomenclatureSeries import main_cat_nomenclature_series_async
from Catalog.catObjectProperties import main_cat_object_properties_async
from Catalog.catOrganizationCodes import main_cat_organization_codes_async
from Catalog.catOrganizations import main_cat_organizations_async
from Catalog.catTaxSchema import main_cat_tax_schema_async
from Catalog.catTaxSchemaCounterparty import main_cat_tax_schema_counterparty_async
from Catalog.catUKTVED import main_cat_uktved_async
from Catalog.catUnits import main_cat_units_async
from Catalog.catUnitsClassifier import main_cat_units_classifier_async
from Catalog.catUsers import main_cat_users_async
from Catalog.catWarehouse import main_cat_warehouse_async
from CreateAndRunSQLScripts import create_views
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async
from Document.docBuyerOrder import main_doc_buyer_order_async
from Document.docCalculationVFSS import main_doc_calculation_VFSS_async
from Document.docCalculationVFSSMaternityBenefits import main_doc_calculation_VFSS_maternity_benefits_async
from Document.docCalculationVFSSsick import main_doc_calculation_VFSS_sick_async
from Document.docComplectation import main_doc_complectation_async
from Document.docComplectationDetails import main_doc_complectation_details_async
from Document.docCorrectionSeries import main_doc_correction_series_async
from Document.docCorrectionSeriesDetails import main_doc_correction_series_details_async
from Document.docDebtCorrection import main_debt_correction_async
from Document.docDebtCorrectionDebtAmounts import main_debt_correction_debt_amount_async
from Document.docGtdImport import main_gtd_import_async
from Document.docGtdImportGoods import main_gtd_import_goods_async
from Document.docGtdImportPartitions import main_gtd_import_partitions_async
from Document.docIncomingAdditionalExpenses import main_incoming_additional_expenses_async
from Document.docIncomingAdditionalExpensesGoods import main_incoming_additional_expenses_goods_async
from Document.docInventory import main_doc_inventory_async
from Document.docMovement import main_doc_movement_async
from Document.docMovementDetails import main_doc_movement_details_async
from Document.docPayroll import main_doc_payroll_async
from Document.docPayrollEmployees import main_doc_payroll_employees_async
from Document.docPostingGoods import main_doc_posting_goods_async
from Document.docPostingOfGoodsDetails import main_doc_posting_of_goods_details_async
from Document.docPriceDetails import main_doc_price_details_async
from Document.docReceiptOfGoodsServices import main_receipt_of_goods_services_async
from Document.docReceiptOfGoodsServicesGoods import main_receipt_of_goods_services_goods_async
from Document.docRequirementInvoice import main_doc_requirement_invoice_async
from Document.docRequirementInvoiceMaterials import main_doc_requirement_invoice_materials_async
from Document.docReturnOfGoodsFromCustomers import main_doc_return_of_goods_from_customers_async
from Document.docReturnOfGoodsFromCustomersGoods import main_doc_return_of_goods_from_customers_goods_async
from Document.docReturnOfGoodsToSupplier import main_doc_return_of_goods_to_supplier_async
from Document.docReturnOfGoodsToSupplierDetails import main_doc_return_of_goods_to_supplier_details_async
from Document.docSalary import main_doc_salary_async
from Document.docSalaryPayment import main_doc_salary_payment_async
from Document.docSalaryPaymentOfWages import main_salary_payment_of_wages_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async
from Document.docSaleOfGoodsServicesGoods import main_doc_sale_of_goods_services_goods_async
from Document.docTaxAppendix2 import main_doc_tax_appendix2_async
from Document.docTaxAppendix2Details import main_doc_tax_appendix2_details_async
from Document.docTaxReceipt import main_doc_tax_receipt_async
from Document.docTaxReceiptDetails import main_doc_tax_receipt_details_async
from Document.docTaxSale import main_doc_tax_sale_async
from Document.docTaxSaleDetails import main_doc_tax_details_sale_async
from Document.docVacationOfficial import main_doc_vacation_official_async
from Document.docVacationOfficialDetails import main_doc_vacation_official_details_async
from Document.docVacationOfficialPeriod import main_doc_vacation_official_period_async
from Document.docWriteOffOfGoods import main_doc_write_off_of_goods_async
from Document.docWriteOffOfGoodsDetails import main_doc_write_off_of_goods_details_async
from Kasa.catCoworker import main_t_coworker_async
from Kasa.catEmployee import main_v_cat_employee_async
from Kasa.docCashBankAll import save_doc_cash_bank_all
# from ClientsContact import main_clients_contact # NP
# from NewPost import main_ttn
# from t_one_sale_logistics import main_one_sale_logistics
from Views.AccountsReceivable import main_accounts_receivable_async
# from prestige_authorize import CONFIG_PATH, CONFIG_PATH_NOVAPOSHTA
from async_Postgres import full_vacuum_pg_async
from createUser import main_create_user_async
from logger_prestige import get_logger
from rate_nbu import main_rate_nbu
from t_one_sale import main_sale_async
from t_one_stock import main_t_one_stock_async
from t_one_stock_short import main_t_one_stock_short_async
from tabAdditionalExpenses import main_tab_additional_expenses_async

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

os.system('cls' if os.name == 'nt' else 'clear')


async def pg_main():
    # restart_postgresql_service()
    time_start = time.time()
    logger.info("START")
    logger.info(int(time.time() - time_start))

    # курс нбу
    main_rate_nbu()
    logger.info(int(time.time() - time_start))

    # # ****************************************************
    # #     Получите список всех функций в текущем модуле
    # all_functions = inspect.getmembers(sys.modules[__name__], inspect.isfunction)
    # # Отфильтруйте функции, чтобы оставить только те, которые начинаются с 'main_cat'
    # main_cat_functions = [f for name, f in all_functions if name.startswith('main_cat')]
    main_cat_functions = [main_cat_users_async, main_cat_nomenclature_async, main_cat_nomenclature_gtd_async,
                          main_cat_nomenclature_series_async, main_info_reg_barcodes_async,
                          main_cat_counterparties_managers_async, main_cat_counterparties_async,
                          main_infreg_contact_information_async, main_cat_contact_persons_of_counterparties_async,
                          main_cat_individuals_async, main_cat_organizations_async, main_cat_organization_codes_async,
                          main_cat_currencies_async, main_cat_employee_async, main_cat_units_async,
                          main_cat_units_classifier_async, main_cat_warehouse_async,
                          main_cat_contracts_counterparties_async, main_cat_cash_async,
                          main_cat_cash_bank_accounts_async, main_cat_banks_async, main_cat_counterparty_segment_async,
                          main_cat_cash_assignment_async, main_cat_uktved_async, main_t_coworker_async,
                          main_doc_money_check_async, main_doc_cash_movement_async,
                          main_doc_cash_order_expense_details_async, main_doc_cash_order_expense_async,
                          main_doc_cash_order_receipt_details_async, main_doc_cash_order_receipt_async,
                          main_doc_cash_warrant_expense_details_async, main_doc_cash_warrant_expense_async,
                          main_doc_cash_warrant_receipt_details_async, main_doc_cash_warrant_receipt_async,
                          main_doc_salary_async, main_doc_salary_payment_async, main_salary_payment_of_wages_async,
                          main_doc_tax_appendix2_details_async, main_doc_tax_appendix2_async,
                          main_doc_tax_details_sale_async, main_doc_tax_sale_async, main_doc_tax_receipt_details_async,
                          main_doc_tax_receipt_async, main_doc_buyer_order_async, main_cat_object_properties_async,
                          main_doc_correction_series_details_async, main_doc_correction_series_async,
                          main_doc_posting_of_goods_details_async, main_doc_posting_goods_async,
                          main_doc_write_off_of_goods_details_async, main_doc_write_off_of_goods_async,
                          main_doc_inventory_async, main_doc_requirement_invoice_materials_async,
                          main_doc_requirement_invoice_async, main_doc_return_of_goods_from_customers_async,
                          main_doc_movement_details_async, main_doc_movement_async,
                          main_doc_return_of_goods_to_supplier_async, main_receipt_of_goods_services_goods_async,
                          main_doc_return_of_goods_from_customers_goods_async, main_doc_sale_of_goods_services_async,
                          main_receipt_of_goods_services_async, main_gtd_import_async, main_gtd_import_partitions_async,
                          main_incoming_additional_expenses_async, main_incoming_additional_expenses_goods_async,
                          main_doc_payroll_async, main_doc_payroll_employees_async, main_cat_tax_schema_async,
                          main_doc_cash_acc_reg_recordtype_async, main_debt_correction_async,
                          main_debt_correction_debt_amount_async, main_doc_cash_payment_order_withdrawal_of_funds_async,
                          main_doc_cash_payment_order_withdrawal_of_funds_details_async,
                          main_doc_cash_correction_of_register_async, main_doc_vacation_official_async,
                          main_doc_vacation_official_period_async, main_doc_vacation_official_details_async,
                          main_doc_reciprocal_settlements_details_async, main_accounts_receivable_async,
                          main_info_reg_document_barcode_async, main_doc_price_details_async, main_v_cat_employee_async,
                          main_doc_complectation_async, main_doc_complectation_details_async,
                          main_cat_nomenclature_counterparty_async, main_cat_tax_schema_counterparty_async,
                          main_cat_object_property_values_async, main_doc_calculation_VFSS_sick_async,
                          main_doc_calculation_VFSS_async, main_doc_calculation_VFSS_maternity_benefits_async,
                          main_cat_cash_flow_items_async, save_doc_cash_bank_all]

    # main_cat_functions.append(main_doc_self_financing_details_async)

    # # Запустите все функции 'main_cat' параллельно
    # await asyncio.gather(*(f() for f in main_cat_functions))

    # Разделите список функций на группы по 10
    groups = [main_cat_functions[n:n + 10] for n in range(0, len(main_cat_functions), 10)]

    # Запустите каждую группу функций
    for group in groups:
        await asyncio.gather(*(f() for f in group))

    await main_doc_return_of_goods_to_supplier_details_async()
    logger.info(int(time.time() - time_start))

    # ****************
    # Document_РеализацияТоваровУслуг_Товары
    await main_doc_sale_of_goods_services_goods_async()  # run single
    logger.info(int(time.time() - time_start))

    # AccumulationRegister_ТоварыНаСкладах_RecordType
    await main_accreg_good_in_warehauses_recordtype_async()  # run single
    logger.info(int(time.time() - time_start))

    # Document_ГТДИмпорт_Товары
    await main_gtd_import_goods_async()  # run single
    logger.info(int(time.time() - time_start))

    # change ref_key and update additional expenses. After ПоступлениеТоваровУслуг
    await main_tab_additional_expenses_async()
    logger.info(int(time.time() - time_start))

    await main_cash_assignment_group_async()
    logger.info(int(time.time() - time_start))

    # остатки на складе
    await main_t_one_stock_async()
    logger.info(int(time.time() - time_start))

    # отчет по продажам (после пересоздание views)
    await main_sale_async()
    logger.info(int(time.time() - time_start))

    await main_t_one_stock_short_async()
    logger.info(int(time.time() - time_start))

    # создание пользователей postgresql + привелегии
    await main_create_user_async()
    logger.info(int(time.time() - time_start))
    #
    # # ******************** Nova Poshta
    # await main_ttn()
    # await main_one_sale_logistics()
    # # ********************

    # await main_balance_async()

    # пересоздание views
    await create_views()
    logger.info(int(time.time() - time_start))

    await full_vacuum_pg_async()

    logger.info('ALL FINISH')


if __name__ == '__main__':
    # asyncio.run(pg_main())
    asyncio.get_event_loop().run_until_complete(pg_main())
