import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_price_type"
DOCUMENT = "Catalog_ТипыЦенНоменклатуры"
SELECT_COLUMNS = ("Ref_Key,DataVersion,DeletionMark,Predefined,Code,Description,ВалютаЦены_Key,БазовыйТипЦен_Key,"
                  "ЦенаВключаетНДС,Комментарий")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        dataversion varchar(50) NULL,  -- DataVersion
        code numeric(10,4) NOT NULL DEFAULT 0,  -- Code
        description varchar(50) NULL,  -- Description
        is_includes_vat boolean NOT NULL DEFAULT FALSE,  -- ЦенаВключаетНДС
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        a_comment varchar(50) NULL,  -- Комментарий
        ref_key varchar(50) NULL,  -- Ref_Key
        currency_prices_key varchar(50) NULL,  -- ВалютаЦены_Key
        basic_price_type_key varchar(50) NULL,  -- БазовыйТипЦен_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS ' DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS ' DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS ' Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.code IS ' Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS ' Description';
    COMMENT ON COLUMN {TABLE_NAME}.currency_prices_key IS ' ВалютаЦены_Key';
    COMMENT ON COLUMN {TABLE_NAME}.basic_price_type_key IS ' БазовыйТипЦен_Key';
    COMMENT ON COLUMN {TABLE_NAME}.is_includes_vat IS ' ЦенаВключаетНДС';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS ' Комментарий';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        ref_key,
        dataversion,
        deletion_mark,
        predefined,
        code,
        description,
        currency_prices_key,
        basic_price_type_key,
        is_includes_vat,
        a_comment
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        predefined = EXCLUDED.predefined,
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        currency_prices_key = EXCLUDED.currency_prices_key,
        basic_price_type_key = EXCLUDED.basic_price_type_key,
        is_includes_vat = EXCLUDED.is_includes_vat,
        a_comment = EXCLUDED.a_comment
    ;
    '''
    return sql.replace("'", "")


async def main_cat_price_type_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(10)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_price_type_async())
