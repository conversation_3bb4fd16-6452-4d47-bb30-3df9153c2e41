import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_contact_person"
DOCUMENT = "Catalog_КонтактныеЛица"
SELECT_COLUMNS = ("Code,DataVersion,DeletionMark,Description,Predefined,ДатаРождения,Имя,Описание,Отчество,Пол,Фамилия,"
                  "Ref_Key")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15) NOT NULL DEFAULT 0,  -- Code
        dataversion varchar(50) NULL,  -- DataVersion
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        description varchar(50) NULL,  -- Description
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        doc_date_of_birth timestamp NOT NULL,  -- ДатаРождения
        last_name varchar(50) NULL,  -- Фамилия
        first_name varchar(50) NULL,  -- Имя
        middle_name varchar(50) NULL,  -- Отчество
        comment varchar(250) NULL,  -- Описание
        gender varchar(50) NULL,  -- Пол
        ref_key varchar(50) NULL,  -- Ref_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date_of_birth IS 'ДатаРождения';
    COMMENT ON COLUMN {TABLE_NAME}.last_name IS 'Фамилия';
    COMMENT ON COLUMN {TABLE_NAME}.first_name IS 'Имя';
    COMMENT ON COLUMN {TABLE_NAME}.middle_name IS 'Отчество';
    COMMENT ON COLUMN {TABLE_NAME}.comment IS 'Описание';
    COMMENT ON COLUMN {TABLE_NAME}.gender IS 'Пол';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        code,
        dataversion,
        deletion_mark,
        description,
        predefined,
        doc_date_of_birth,
        first_name,
        comment,
        middle_name,
        gender,
        last_name,
        ref_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        code = EXCLUDED.code,
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        description = EXCLUDED.description,
        predefined = EXCLUDED.predefined,
        doc_date_of_birth = EXCLUDED.doc_date_of_birth,
        first_name = EXCLUDED.first_name,
        comment = EXCLUDED.comment,
        middle_name = EXCLUDED.middle_name,
        gender = EXCLUDED.gender,
        last_name = EXCLUDED.last_name
    ;
    '''
    return sql.replace("'", "")


async def main_cat_contact_persons_of_counterparties_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    sql = sql.replace("$6,", "to_timestamp($6, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_contact_persons_of_counterparties_async())
