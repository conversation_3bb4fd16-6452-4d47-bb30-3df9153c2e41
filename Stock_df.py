import os

import pandas as pd
from dateutil.parser import parse

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

excel_file_path = r"Book2.xlsx"
date_str = '2023-11-03'
date_start = parse(date_str).date()  # datetime.strptime(date_str, '%Y-%m-%d').date()


def add_column_to_df(df):
    df['days_calc'] = 0
    df['is_previos_stock'] = 0
    df['date'] = pd.to_datetime(df['doc_date'].dt.strftime("%d.%m.%Y"), dayfirst=True)
    df['is_row_next'] = df['sku_row'].shift(-1) == 0
    df['is_previos_date'] = df['date'].shift()
    df['is_previos_sku'] = (df['sku'] == df['sku'].shift())
    for i, row in df.iterrows():

        if row['sku_row'] == 0:
            df.at[i, 'days_calc'] = 0
            continue
        else:
            max_row_current_date = df[(df['sku'] == row['sku']) & (df['date'].dt.date == row['date'].date())][
                'sku_row'].max()
            if row['sku_row'] < max_row_current_date:
                df.at[i, 'days_calc'] = 0
                continue
            else:
                max_row_previos_date_lst = df[(df['sku'] == row['sku']) & (df['date'].dt.date < row['date'].date())][
                    'sku_row'].tolist()
                if len(max_row_previos_date_lst) == 0:
                    df.at[i, 'days_calc'] = 0
                    max_row_previos_date = row['sku_row']
                else:
                    max_row_previos_date = max(max_row_previos_date_lst)

        if (df[df['sku'] == row['sku']].count().values[0] == 2) and (df.at[i - 1, 'stock'] > 2) and row['is_row_next']:
            df.at[i, 'is_previos_stock'] = df[(df['sku'] == row['sku']) & (df['date'] < row['date'])][
                'date'].max().date()
            df.at[i, 'days_calc'] = (date_start - df.at[i - 1, 'date'].date()).days
        elif row['is_row_next']:
            last_stock_previos_date = df[(df['sku'] == row['sku']) & (df['sku_row'] == max_row_previos_date)][
                'stock'].tolist()
            if len(last_stock_previos_date) == 0:
                df.at[i, 'days_calc'] = 0
                continue
            else:
                last_stock_previos_date = last_stock_previos_date[0]
                if last_stock_previos_date < 2 or row['stock'] < 2:
                    df.at[i, 'days_calc'] = 0
                elif row['date'].date() < date_start:
                    try:
                        previos_max_date = pd.to_datetime(
                            df[(df['sku'] == row['sku']) & (df['sku_row'] == max_row_previos_date)]['date']).values[0]
                        previos_max_date = parse(previos_max_date.astype(str)).date()
                        df.at[i, 'days_calc'] = (row['date'].date() - previos_max_date).days
                    except Exception as e:
                        logger.error(f"Error: {e}")
        elif row['old_stock'] > 2 and row['is_previos_sku']:
            if row['date'] == df.at[i, 'is_previos_date']:
                df.at[i, 'days_calc'] = 0
            else:
                df.at[i, 'days_calc'] = (row['date'] - df.at[i, 'is_previos_date']).days

    df['doc_date'] = df['doc_date'].dt.strftime("%d.%m.%Y %H:%M:%S")
    df['date'] = df['date'].astype(str)
    df['fark'] = df['days_calc'] - df['days']
    df['is_previos_date'] = df['is_previos_date'].astype(str)
    return df


def stock_df_main():
    df = pd.read_excel(excel_file_path)
    df = add_column_to_df(df)

    # with pd.ExcelWriter(excel_file_path, mode='a') as writer:
    excel_file_path1 = excel_file_path.replace("Book2", "Book3")
    df.to_excel(excel_file_path1, sheet_name="df", index=False)
    # with pd.ExcelWriter(excel_file_path1,) as writer:
    #     df.to_excel(writer, sheet_name='df')


if __name__ == '__main__':
    stock_df_main()
