import os
import pandas as pd
import asyncio

from multiThread import get_json_from_url

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Document_ГТДИмпорт"
SELECT_COLUMNS = "Number,Date,Posted,Разделы"


async def get_response(document_key: str):
    url = f"http://192.168.1.254/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"/?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    filter_ref_key = f"&$filter=(Товары/any(d: d/ДокументПартии eq cast(guid'{document_key}','Document_ПоступлениеТоваровУслуг')))"
    url += filter_ref_key
    response = await get_json_from_url(url)
    return response


async def main_gtd_async(document_key, foreign_key=''):
    df = pd.DataFrame()
    json_data = await get_response(document_key)
    if json_data.get('value'):
        df = pd.json_normalize(json_data['value'][0]['Разделы'][0])
        df = df[['СуммаПошлины', 'СуммаНДС']]
        df['ref_key'] = document_key
        df['date'] = json_data['value'][0]['Date']
        df['number'] = json_data['value'][0]['Number']
        df.columns = ['пошлина', 'НДС', 'ref_key', 'gtd_date', 'gtd_number']
        df = df[['gtd_date', 'gtd_number', 'пошлина', 'НДС', 'ref_key']]

    return df


if __name__ == "__main__":
    df = asyncio.run(main_gtd_async("ac9806e7-63aa-11ef-81bc-001dd8b740bc"))
    print(df)
