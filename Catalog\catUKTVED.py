import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_cat_uktved"
DOCUMENT = "Catalog_КлассификаторУКТВЭД"
SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Predefined,Code,Description,НаименованиеПолное"

SQL_CREATE_TABLE = f'''
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NULL,  -- DataVersion
            deletionmark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
            predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
            code varchar(15) NOT NULL,  -- Code
            description varchar(500) NULL,  -- Description
            name_full varchar(500) NULL,  -- НаименованиеПолное
            ref_key varchar(50) NULL,  -- Ref_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );

        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
        COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
        COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
        COMMENT ON COLUMN {TABLE_NAME}.name_full IS 'НаименованиеПолное';
    '''

create_trigger = f'''
    CREATE TRIGGER bfr_t_one_cat_uktved BEFORE
    INSERT
        OR
    UPDATE
        ON
    {TABLE_NAME} FOR EACH ROW EXECUTE FUNCTION fn_update_uktved()
    '''

CREATE_FUNCTION = '''
    CREATE OR REPLACE FUNCTION fn_update_uktved()
     RETURNS trigger
     LANGUAGE plpgsql
    AS $function$
        BEGIN
            NEW.code = REPLACE(NEW.code,' ','');
            RETURN NEW;                  
        END;
    
      $function$
    ;
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}(
        ref_key,
        dataversion,
        deletionmark,
        predefined,
        code,
        description,
        name_full
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        predefined = EXCLUDED.predefined,
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        name_full = EXCLUDED.name_full
    ;
    '''
    return sql.replace("'", "")


async def main_cat_uktved_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    await async_save_pg(CREATE_FUNCTION)
    await async_save_pg(create_trigger)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(7)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_uktved_async())
