    CREATE OR REPLACE VIEW v_one_return_control_double AS
    SELECT
        data_all.doc_date AS ВН_дата,
        data_all.doc_number AS ВН_номер,
        data_all.customer_date ВН_датаКлиента,
        data_all.customer_number ВН_номерКлиента,
        sell.doc_date AS РН_дата,
        sell.doc_number	AS РН_номер,
        date_create датаСозданияДок,
        counterparties.description клиент,
        count(*) OVER (PARTITION BY data_all.customer_date, data_all.customer_number, sell.doc_date
            ORDER BY data_all.customer_date, data_all.customer_number, sell.doc_date) as количествоПовторов
    FROM (
        SELECT DISTINCT main.*, sub.base_doc_key
        FROM t_one_doc_return_of_goods_from_customers_goods sub
            INNER JOIN t_one_doc_return_of_goods_from_customers AS main
                ON main.ref_key = sub.ref_key
        ) AS data_all
        INNER JOIN
        (SELECT customer_date, customer_number, deal_key, base_doc_key, count(*)
        FROM (
            SELECT DISTINCT main.ref_key, main.customer_date, main.customer_number,
                main.deal_key, sub.base_doc_key
            FROM t_one_doc_return_of_goods_from_customers_goods sub
                INNER JOIN t_one_doc_return_of_goods_from_customers AS main
                    ON main.ref_key = sub.ref_key
            WHERE (main.user_key = '091d06f1-e92d-11eb-810e-001dd8b72b55' -- Света Лужанская
                    OR  main.responsible_key = '091d06f1-e92d-11eb-810e-001dd8b72b55')
                AND main.posted
                AND main.customer_date <> '0001-01-01'::date
            ) AS ta
        GROUP BY customer_date, customer_number, deal_key, base_doc_key
        HAVING count(*) > 1
        ) AS dbl
        ON data_all.customer_date = dbl.customer_date
            AND data_all.customer_number = dbl.customer_number
            AND data_all.deal_key = dbl.deal_key
            AND data_all.base_doc_key = dbl.base_doc_key
        INNER JOIN t_one_doc_sale_of_goods_services AS sell
            ON sell.ref_key = dbl.base_doc_key
        INNER JOIN t_one_cat_counterparties counterparties
            ON counterparties.ref_key = sell.account_key
    WHERE data_all.doc_date::date >= '01.01.2023'
    ORDER BY data_all.doc_date::date DESC, ВН_номерКлиента, ВН_датаКлиента, dbl.base_doc_key
    ;

    COMMENT ON VIEW v_one_return_control_double IS 'список задвоенных накл';
    GRANT SELECT ON TABLE v_one_return_control_double TO user_prestige;
