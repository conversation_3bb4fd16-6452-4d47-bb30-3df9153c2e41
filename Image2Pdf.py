# pip install Pillow
import os

from PIL import Image

image_path_source = r'C:\Users\<USER>\Documents\Scanned Documents'


def cycle_on_directory_files():
    # цикл по папкам и файлам в папке pathName
    for root, dirs, files in os.walk(image_path_source):
        try:
            papka = os.path.split(root)[-1]
            # print('путь ', root)
            # print('крайняя папка ', papka)
            # print('папки ', dirs)
            # print('файлов ', len(files), files)
            # print(os.path.split(root)[0])

            for i, name in enumerate(files):
                filename, file_extension = os.path.splitext(name.lower())
                if file_extension in ['.jpg', '.png', '.bmp'] and filename[:2] in ['рн', 'тт']:
                    print(name)
                    image_resize_and_save_2_pdf(image_path_source + r'\\' + name)

        except Exception as e:
            print(str(e))
            continue


def image_resize_and_save_2_pdf(file_name):
    # https://auth0.com/blog/image-processing-in-python-with-pillow/
    # изменяет размер изображения
    try:
        # Имя файла
        base_name = os.path.basename(file_name)
        image = Image.open(file_name)
        new_image = image.resize((1000, 1400))
        new_path = os.path.join(image_path_source, 'PDF')
        if not os.path.exists(new_path):
            # папки нет. Создаем
            os.mkdir(new_path)

        new_file_path = os.path.join(image_path_source, 'PDF',base_name)


        # Сохраняем файл в новой папке
        # new_image.save(new_path)
        # image_1 = Image.open(new_path)
        # im_1 = image_1.convert('RGB')
        im_1 = new_image.convert('RGB')
        im_1.save(new_file_path.replace("jpg", "pdf"))

    except Exception as e:
        print(str(e))


if __name__ == '__main__':
    cycle_on_directory_files()
