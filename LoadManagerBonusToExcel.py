import asyncio
from multiprocessing import managers
import os
import shutil
import sys
from datetime import datetime

import xlwings as xw
from dateutil.parser import parse

from Cash.docCashCorrectionOfRegister import main_doc_cash_correction_of_register_async
from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderWithdrawalOfFunds import main_doc_cash_payment_order_withdrawal_of_funds_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from CreateAndRunSQLScripts import create_views
from Document.docDebtCorrection import main_debt_correction_async
from async_Postgres import sql_to_dataframe_async, get_result_one_column
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
CUR_DIR = os.path.dirname(os.path.abspath(__file__))

managers_chatid_name = {
    "5273124197": "001 Поночовный Олег",
    "5221016109": "002 Мороз Валера",
    "5307415579": "004 Попов Максим",
    "1319016293": "009 Боркута Юлія",
    "455320369": "010 Довголенко Е.",
    "11": "011 Мерцалов Александр",
    "275082436": "013 Будко Олександр",
    "490323168": "001 Поночовный Олег",
    "688283456": "014 Пилипяк Богдан",
}

managers_name_uuid = {
    '001 Поночовный Олег': '5002b988-9831-11e6-80c4-c936aa9c817c',
    '002 Мороз Валера': 'fb0c69e4-96c8-11e6-80c4-c936aa9c817c',
    '004 Попов Максим': '355ef365-bd22-11ea-80f8-001dd8b79079',
    '005 Кузьминский Игорь (Химия)': 'c2dc6ba9-3b79-11ea-80ee-001dd8b79079',
    '006 Яновский Юрий': '995b1c22-4d2e-11ec-811e-001dd8b72b55',
    '007 Кичук Юрий ': 'cd33834b-c08c-11e8-80e7-001dd8b79079',
    '008 Грубая Илона': 'cebb50d1-76c2-11e8-80e2-001dd8b79079',
    '009 Боркута Юлія': '0294036f-7766-11ec-8126-001dd8b72b55',
    '010 Довголенко Е.': '0363fd89-410f-11e7-80cd-001dd8b79079',
    '011 Мерцалов Александр': '0363fd85-410f-11e7-80cd-001dd8b79079',
    '012 Токмаков М.В.': '0363fd8a-410f-11e7-80cd-001dd8b79079',
    '013 Будко Олександр': 'fead7a54-0d3f-11eb-80fb-001dd8b72b55',
    '014 Пилипяк Богдан': 'c6cc6fdc-8bb6-11e7-80d4-001dd8b79079',
    '018 Жуковский Ю. (ранее Аполонов С.)': '0363fd87-410f-11e7-80cd-001dd8b79079',
    'МЕНЕДЖЕРЬІ ВЬІБЬІВШИЕ': 'c6a0df91-ebe1-11ec-8135-001dd8b740bc',
    'Продажи сотрудникам': 'f979197e-b087-11e6-80c4-c936aa9c817c',
    'Прямые продажи': 'f979197d-b087-11e6-80c4-c936aa9c817c'
}


async def load_data():
    tasks = [
        # Document_КорректировкаДолга
        asyncio.create_task(main_debt_correction_async()),
        # Document_КорректировкаЗаписейРегистров
        asyncio.create_task(main_doc_cash_correction_of_register_async()),
        # Document_ПлатежноеПоручениеИсходящее
        asyncio.create_task(main_doc_cash_order_expense_async()),
        # Document_ПлатежноеПоручениеВходящее
        asyncio.create_task(main_doc_cash_order_receipt_async()),
        # Document_ПлатежныйОрдерСписаниеДенежныхСредств
        asyncio.create_task(main_doc_cash_payment_order_withdrawal_of_funds_async()),
        # Document_РасходныйКассовыйОрдер
        asyncio.create_task(main_doc_cash_warrant_expense_async()),
        # Document_ПриходныйКассовыйОрдер
        asyncio.create_task(main_doc_cash_warrant_receipt_async()),
    ]

    # Запускаем все задачи и ждем их завершения
    await asyncio.gather(*tasks)
    await create_views()


async def check_view_exists(view_name):
    query = f"SELECT to_regclass('{view_name}');"
    result = await get_result_one_column(query)
    return result is not None


async def copy_file(manager):
    """
    Копирует файл "ManagerBonus.xlsx", добавляя текущую дату и время в название файла.
    Возвращает путь к новому файлу.
    """
    try:
        input_file = os.path.join(CUR_DIR, "ManagerBonus.xlsx")
        output_file = os.path.join(CUR_DIR,
                                   f"ManagerBonus_{manager}_{datetime.strftime(datetime.now(), '%Y%m%d_%H%M%S')}.xlsx")
        shutil.copyfile(input_file, output_file)
        return output_file
    except Exception as e:
        logger.info(f"Error copying file: {e}")
        return None


async def load_data_to_df(*params):
    """
    Загружает данные из базы данных в DataFrame.
    """
    try:
        sql = "SELECT * FROM fn_salary_bonus_manager('{}'::date, '{}'::date, '{}')".format(*params)
        df = await sql_to_dataframe_async(sql)
        if df is None:
            return None
        df["сумма"] = df["сумма"].astype(float)
        df.sort_values(by=["клиент", "тип_документа", "датаДок", "номерДок"], inplace=True)
        logger.info(df)
        return df
    except Exception as e:
        logger.info(f"Error loading data to DataFrame: {e}")
        return None


def refresh_all_pivot_tables(wb):
    """
    Обновляет все сводные таблицы в рабочей книге Excel.
    """
    try:
        for ws in wb.sheets:
            pivot_tables_count = ws.api.PivotTables().Count
            for i in range(1, pivot_tables_count + 1):
                pt = ws.api.PivotTables(i)
                pt.RefreshTable()

                # Автонастройка ширины столбцов
                ws.cells.columns.autofit()

    except Exception as e:
        logger.error(f"Error refreshing pivot tables: {e}")


def update_workbook(output_file, df, sht_name):
    """
    Обновляет рабочую книгу Excel данными из DataFrame.
    """
    try:
        app = xw.App(visible=False)
        wb = xw.Book(output_file)
        ws = wb.sheets[sht_name]

        last_row = ws.range('A' + str(ws.cells.last_cell.row)).end('up').row - 1
        ws.range('A' + str(last_row + 1)).options(index=False).value = df.values

        refresh_all_pivot_tables(wb)

        wb.save(output_file)
        wb.close()
        app.quit()
    except Exception as e:
        logger.info(f"Error updating workbook: {e}")


async def manager_key_validation(args):
    """
    Проверка ключа менеджера.
    """
    logger.info(f"args[2]={args[2]}")
    logger.info(f"len(args[2])={len(args[2])}")
    if len(args[2]) != 36:
        logger.info(f"Error: Invalid number of arguments.{args}, len={len(args)}")
        return False
    return True


async def args_count_validation(args):
    """
    Проверка количества аргументов.
    """
    if len(args) != 3:
        logger.info(f"Error: Invalid number of arguments.{args}, len={len(args)}")
        return False
    return True


async def period_validation(args):
    if args[0] > args[1]:
        logger.info(f"Error: Invalid date range: {args[0]} > {args[1]}")
        return False
    return True


async def is_date(args):
    try:
        if parse(args[0]) and parse(args[1]):
            return True
        else:
            return False

    except ValueError:
        return False


async def all_data_validation(args):
    """
    Проверка данных в базе данных.
    """
    result = await args_count_validation(args)
    if not result:
        msg = f"Error: Количество аргументов должно быть равно 3."
        return False

    result = await is_date(args)
    if not result:
        msg = f"Error: Неверный формат даты."
        return False

    result = await period_validation(args)
    if not result:
        msg = f"Error: Неверный период даты."
        return False

    result = await manager_key_validation(args)
    if not result:
        msg = f"Error: Неверный ключ менеджера."
        return False

    return True


async def load_manager_bonus_to_excel(*args):
    try:
        args = args[0]
        result = await all_data_validation(args)
        if not result:
            return False

        await load_data()

        i = 0
        view_exists = False
        while not view_exists or i < 3:
            view_exists = await check_view_exists('v_one_balance_details')
            if not view_exists:
                await create_views()
            else:
                view_exists = True

            i += 1

        date_start = args[0]
        date_end = args[1]
        manager_key = args[2]
        logger.info(f"manager_key={manager_key}, date_start={date_start}, date_end={date_end}")
        df = await load_data_to_df(date_start, date_end, manager_key)

        if df is None:
            logger.info("Error: Unable to load data to DataFrame.")
            return

        sht_name = "source"
        output_file = await copy_file(df["менеджер"].iloc[0])

        if output_file is None:
            logger.info("Error: Unable to copy file.")
            return

        update_workbook(output_file, df, sht_name)

    except Exception as e:
        logger.info(f"Error in main function: {e}")


if __name__ == '__main__':
    # Симуляция передачи аргументов в командную строку
    sys.argv = [
        r"D:\Prestige\Python\Prestige\LoadManagerBonusToExcel.py",
        "01.11.2024",
        datetime.strftime(datetime.now(), "%d.%m.%Y"),
        managers_name_uuid["010 Довголенко Е."],
    ]
    logger.info(sys.argv[1:])

    args = []
    if sys.argv[1:]:
        args = sys.argv[1:]
    asyncio.run(load_manager_bonus_to_excel(args))
