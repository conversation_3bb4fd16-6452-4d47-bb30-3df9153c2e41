import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_cat_cash_cost_item"
DOCUMENT = "Catalog_СтатьиЗатрат"
SELECT_COLUMNS = ("Ref_Key,DataVersion,DeletionMark,Predefined,Parent_Key,IsFolder,Code,Description,ВидЗатрат,"
                  "ХарактерЗатрат,ПостояннаяЗатрата")

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key varchar(50) NULL,  -- Ref_Key
        dataversion varchar(50) NULL,  -- DataVersion
        deletion_mark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
        predefined boolean NOT NULL DEFAULT FALSE,  -- Predefined
        parent_key varchar(50) NULL,  -- Parent_Key
        isfolder boolean NOT NULL DEFAULT FALSE,  -- IsFolder
        code varchar(15) NOT NULL DEFAULT FALSE,  -- Code
        description varchar(300) NULL,  -- Description
        type_of_costs varchar(50) NULL,  -- ВидЗатрат
        character_of_costs varchar(50) NULL,  -- ХарактерЗатрат
        fixed_cost boolean NULL DEFAULT FALSE,  -- ПостояннаяЗатрата
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.type_of_costs IS 'ВидЗатрат';
    COMMENT ON COLUMN {TABLE_NAME}.character_of_costs IS 'ХарактерЗатрат';
    COMMENT ON COLUMN {TABLE_NAME}.fixed_cost IS 'ПостояннаяЗатрата';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        ref_key,
        dataversion,
        deletion_mark,
        predefined,
        parent_key,
        isfolder,
        code,
        description,
        type_of_costs,
        character_of_costs,
        fixed_cost
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        predefined = EXCLUDED.predefined,
        parent_key = EXCLUDED.parent_key,
        isfolder = EXCLUDED.isfolder,
        code = EXCLUDED.code,
        description = EXCLUDED.description,
        type_of_costs = EXCLUDED.type_of_costs,
        character_of_costs = EXCLUDED.character_of_costs,
        fixed_cost = EXCLUDED.fixed_cost
    ;
    '''
    return sql.replace("'", "")


async def main_cat_cash_clause_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(11)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_cat_cash_clause_async())
