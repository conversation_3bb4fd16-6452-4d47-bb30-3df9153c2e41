import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_receipt_of_goods_services_goods"
DOCUMENT = "Document_ПоступлениеТоваровУслуг_Товары"
SELECT_COLUMNS = (
    "LineNumber,ЕдиницаИзмерения_Key,Номенклатура_Key,Ref_Key,Количество,Коэффициент,Сумма,СуммаНДС,Цена,"
    "fiyat,Склад_Key,СерияНоменклатуры_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id serial4 NOT NULL,
        line_number numeric(7) NOT NULL,  -- LineNumber
        coefficient numeric(10, 3) NOT NULL DEFAULT 0,  -- Коэффициент
        quantity numeric(10, 3) NOT NULL DEFAULT 0,  -- Количество
        price numeric(10, 3) NOT NULL DEFAULT 0,  -- Цена
        amount numeric(15, 3) NOT NULL DEFAULT 0,  -- Сумма
        amount_vat numeric(15, 3) NOT NULL DEFAULT 0,  -- СуммаНДС
        price_tr numeric(10, 3) NOT NULL DEFAULT 0,  -- ЦенаTr
        amount_tr numeric(15, 3) NOT NULL DEFAULT 0,  -- СуммаTr
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        nomenclature_key varchar(50) NOT NULL,  -- Номенклатура_Key
        nomenclature_series_key varchar(40) NULL,  -- СерияНоменклатуры_Key
        unit_of_key varchar(50) NOT NULL,  -- ЕдиницаИзмерения_Key
        warehouse_key varchar(50) NOT NULL,  -- Склад_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );

    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';    
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.price_tr IS 'ЦенаTr';    
    COMMENT ON COLUMN {TABLE_NAME}.amount_tr IS 'СуммаTr';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}
        (
        line_number,
        unit_of_key,
        nomenclature_key,
        ref_key,
        quantity,
        coefficient,
        amount,
        amount_vat,
        price,
        price_tr,
        warehouse_key,
        nomenclature_series_key
        )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        unit_of_key = EXCLUDED.unit_of_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        ref_key = EXCLUDED.ref_key,
        quantity = EXCLUDED.quantity,
        coefficient = EXCLUDED.coefficient,
        amount = EXCLUDED.amount,
        amount_vat = EXCLUDED.amount_vat,
        price = EXCLUDED.price,
        price_tr = EXCLUDED.price_tr,
        warehouse_key = EXCLUDED.warehouse_key,
        nomenclature_series_key = EXCLUDED.nomenclature_series_key
    """
    return sql.replace("'", "")


sql_update_amount_tr = f"""
    UPDATE {TABLE_NAME}
    SET amount_tr = price_tr * quantity; 
"""


async def main_receipt_of_goods_services_goods_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(12)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_save_pg(sql_update_amount_tr)
    await async_sql_create_index(TABLE_NAME, "ref_key")
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_receipt_of_goods_services_goods_async())
