# Description: Сохраняет данные о задолженности в Excel.
# отбираются документы за указанный период


import os
import sys
import time

from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catCounterparties import main_cat_counterparties_async
from CreateAndRunSQLScripts import create_views
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async

sys.path.append(os.path.dirname(__file__))
import asyncio
import aiohttp
import aiofiles
import json
from aiohttp import FormData
from datetime import datetime, date
from dateutil.parser import parse
from pathvalidate import sanitize_filename
from decimal import Decimal
import pandas as pd
import xlsxwriter
from dateutil import parser
from LoadManagerBonusToExcel import managers_chatid_name, managers_name_uuid
from logger_prestige import get_logger
from async_Postgres import (
    sql_to_dataframe_async,
    send_telegram,
    TELEGRAM_TOKEN,
    chatid_rasim,
)

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
DATE_FIRST = parser.parse("01.05.2024", dayfirst=True).date()
SAVE_TO_FILE = "Config.xlsx"

# Установите опцию отображения максимального количества столбцов
pd.set_option('display.max_columns', None)


def head_format(wb):
    return wb.add_format({"bg_color": "#778899", "bold": True, "align": "center"})


def segment_format(wb):
    return wb.add_format(
        {
            "bg_color": "#808080",  # color - gray
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 14,
        }
    )


def customer_format(wb):
    return wb.add_format(
        {
            "bg_color": "#C0C0C0",  # color - silver
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 13,
        }
    )


def organization_format(wb):
    return wb.add_format(
        {
            "bg_color": "#f2ebf1",  # color - lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 11,
        }
    )


def date_format(wb):
    return wb.add_format({"num_format": "dd.mm.yyyy"})


def number_format(wb):
    return wb.add_format({"num_format": "# ### ##0.00;[Red]# ### ##0.00"})


def int_format(wb):
    return wb.add_format({"num_format": "# ### ##0;[Red]# ### ##0"})


def date_to_str(period):
    return datetime.strftime(period, "%Y%m%d")


# отправка файла в телеграм
async def telegram_bot_send_document(path_doc, chat_id):
    chat_id = str(chat_id)
    logger.info(f"файл для отправки: {path_doc}")
    async with aiofiles.open(path_doc, "rb") as doc:
        data = FormData()
        data.add_field("chat_id", chat_id)
        data.add_field("document", doc, filename=path_doc)

        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendDocument"
            async with session.post(url, data=data) as response:
                logger.info(response.status)
                content = await response.text()
                result = json.loads(content)
                filename = {result.get("result").get("document").get("file_name")}
                logger.info(f"filename: {filename}")
                await send_telegram(f"Вам отправлен файл: {filename}", chat_id)

    if chat_id != str(chatid_rasim):
        await telegram_bot_send_document(os.path.abspath(path_doc), str(chatid_rasim))

    return response.status


def cell_address(row, col):
    # Преобразуем номер колонки в букву
    column_letter = ""
    while col >= 0:
        column_letter = chr(col % 26 + ord("A")) + column_letter
        col = col // 26 - 1
    # Возвращаем адрес ячейки в формате Excel
    return f"{column_letter}{row + 1}"


def set_column_width_cm(worksheet, col_num, width_in_cm):
    cm_to_pixels = 3.78
    width_in_pixels = width_in_cm * cm_to_pixels
    worksheet.set_column(col_num, col_num, width_in_pixels)


async def colored_range(ws, row, cell_format):
    # Закрашиваем диапазон ячеек
    for col in range(6):
        cell = cell_address(row, col)
        ws.write_blank(cell, None, cell_format)


# получение данных из представления v_one_balance_details
# и запись их в DataFrame
async def get_data_from_view(manager_key: str, date_first: date = None):
    if date_first is None:
        date_first = DATE_FIRST
    sql = f'''
        WITH 
        balance_first AS (
            SELECT 
                segment,
                manager,
                customer,
                organization,
                currency_name,
                LAST_VALUE(doc_sum) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) doc_sum,
                LAST_VALUE(customer_pdz) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) customer_pdz,
                LAST_VALUE(customer_sum_pdz) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) customer_sum_pdz,
                doc_date = max(doc_date) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) is_max_date,
                $2::date - 1 doc_date,
                $2::date - 1 last_date,
                LAST_VALUE(leave_days) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) leave_days,
                edrpou,
                manager_key
            FROM mt_one_balance
            WHERE manager_key = $1
                AND last_date < $2
        ),
        balance_middle AS (
            SELECT 
                CASE 
                    WHEN  abs(customer_sum_pdz) < 10 
                        AND 
                            last_date = min(last_date)
                                OVER (PARTITION BY 
                                    segment,
                                    manager,
                                    customer,
                                    organization,
                                    currency_name
                                    )
                        AND 
                            last_date = max(last_date)
                                OVER (PARTITION BY 
                                    segment,
                                    manager,
                                    customer,
                                    organization,
                                    currency_name
                                    )
                        AND 
                            1 = count(last_date)
                                OVER (PARTITION BY 
                                    segment,
                                    manager,
                                    customer,
                                    organization,
                                    currency_name
                                    )
                                    THEN
                            TRUE 
                    ELSE
                        FALSE 
                END
                AS is_empty,
                CASE
                    WHEN doc_sum >= 0 THEN
                        'Receipt'
                    ELSE
                        'Expense'
                END AS recordtype,
                *
            FROM mt_one_balance
            WHERE manager_key = $1
                AND last_date >= $2
        ),
        balance_last AS (
            SELECT 
                segment,
                manager,
                customer,
                organization,
                currency_name,
                LAST_VALUE(doc_sum) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) doc_sum,
                LAST_VALUE(customer_pdz) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) customer_pdz,
                LAST_VALUE(customer_sum_pdz) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                )customer_sum_pdz,
                doc_date = max(doc_date) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) is_max_date,
                current_date doc_date,
                current_date last_date,
                LAST_VALUE(leave_days) OVER (PARTITION BY         
                    segment,
                    manager,
                    customer,
                    organization,
                    currency_name
                ) leave_days,
                edrpou,
                manager_key
            FROM mt_one_balance
            WHERE manager_key = $1
        ),
        balance AS (
        SELECT DISTINCT 
            CASE
                WHEN customer_pdz >= 0 THEN
                    'Receipt'
                ELSE
                    'Expense'
            END AS recordtype,
            segment,
            manager,
            customer,
            organization,
            currency_name,
            doc_sum,
            customer_pdz,
            customer_sum_pdz,
            doc_date,
            last_date,
            leave_days,
            edrpou,
            manager_key
        FROM balance_first
        WHERE is_max_date
            AND (abs(customer_pdz) + abs(customer_sum_pdz)) > 1
        UNION ALL 
        SELECT DISTINCT 
            recordtype,
            segment,
            manager,
            customer,
            organization,
            currency_name,
            doc_sum,
            customer_pdz,
            customer_sum_pdz,
            doc_date,
            last_date,
            leave_days,
            edrpou,
            manager_key
        FROM balance_middle
        WHERE NOT is_empty
        UNION ALL 
        SELECT DISTINCT
            CASE
                WHEN customer_pdz >= 0 THEN
                    'Receipt'
                ELSE
                    'Expense'
            END AS recordtype,
            segment,
            manager,
            customer,
            organization,
            currency_name,
            doc_sum,
            customer_pdz,
            customer_sum_pdz,
            doc_date,
            last_date,
            leave_days,
            edrpou,
            manager_key
        FROM balance_last
        WHERE is_max_date
            AND (abs(customer_pdz) + abs(customer_sum_pdz)) > 1
        )
        SELECT 
            *
        FROM balance 
            LEFT JOIN LATERAL (
                SELECT 
                	sum("сумма") AS block_amount_nn,
                	sum("НДС") AS block_vat_nn
                FROM v_court_erpn
                WHERE ОКПО_Покуп::VARCHAR(40) = balance.edrpou::VARCHAR(40)
                    AND дата_НН_РК::date <= balance.last_date::date
					AND типСтатуса IN (
							1,  -- реєстрацiю зупинено
							2,  -- вiдмовлено за рiшенням Комiсiї
							13  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"'
						)
                    AND "0-НН;1-РК;" = 0  -- НН
                LIMIT 1
            ) AS t ON TRUE
            LEFT JOIN LATERAL (
                SELECT 
                	sum("НДС") * 6 AS block_amount_rk,
                	sum("НДС") AS block_vat_rk
                FROM v_court_erpn
                WHERE ОКПО_Покуп::VARCHAR(40) = balance.edrpou::VARCHAR(40)
                    AND дата_НН_РК::date <= balance.last_date::date
					AND типСтатуса IN (
							1,  -- реєстрацiю зупинено
							2,  -- вiдмовлено за рiшенням Комiсiї
							13  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"'
						)
                    AND "0-НН;1-РК;" = 1  -- РК
                LIMIT 1
            ) AS tt ON TRUE
        ;
    '''
    df = await sql_to_dataframe_async(sql, manager_key, date_first)

    # Преобразование колонки 'doc_date' в формат даты
    df["doc_date"] = pd.to_datetime(df["doc_date"])
    df["last_date"] = pd.to_datetime(df["last_date"])
    return df


async def get_sum_form_df(df, column_name, filter_column=None, filter_value=None):
    """
    Parameters:
    df - DataFrame,
    column_name - название столбца/ов для группировки.
        По нему будет считаться сумма. Может быть несколько столбцов(список),
    filter_column - название столбца для фильтрации,
    filter_value - значение для фильтрации
    """
    df.loc[:, column_name] = df[column_name].fillna("")
    if filter_column:
        df = df[
            df[filter_column] == filter_value
            ].copy()  # Используем .copy() для создания копии DataFrame

    # Группировка и суммирование doc_sum
    group_df = df.groupby([*column_name])["doc_sum"].sum().reset_index()

    # Получение последнего значения customer_pdz в каждой группе
    df.loc[:, "last_customer_pdz"] = df.groupby([*column_name])[
        "customer_pdz"
    ].transform("last")

    # Добавление последнего значения customer_pdz в group_df
    group_df["last_customer_pdz"] = df.drop_duplicates(subset=column_name)[
        "last_customer_pdz"
    ].values

    return group_df


# сумма по статьям до текущей даты
async def get_data_before(df, columns_by_group):
    df_sum = await get_sum_form_df(df, columns_by_group)
    return df_sum


# сумма по статьям между датами
async def get_data_between(df, columns_by_group):
    df_before = df[
        (df["last_date"].dt.date >= DATE_FIRST)
        & (df["last_date"].dt.date <= date.today())
        ]
    df_before_receipt = df_before[df_before["recordtype"] == "Receipt"]
    segment_df_receipt = await get_sum_form_df(df_before_receipt, columns_by_group)
    segment_df_receipt.rename(columns={"doc_sum": "receipt"}, inplace=True)

    df_before_expense = df_before[df_before["recordtype"] == "Expense"]
    segment_df_expense = await get_sum_form_df(df_before_expense, columns_by_group)
    segment_df_expense.rename(columns={"doc_sum": "expense"}, inplace=True)
    merged_df = segment_df_receipt.merge(
        segment_df_expense, on=[*columns_by_group], how="outer"
    )
    # Удаление дублирующихся колонок
    merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]
    return merged_df


async def get_data_by_section(df, columns):
    section_sum = await get_data_before(df, columns)
    section_period = await get_data_between(df, columns)
    section_merge = section_sum.merge(section_period, on=columns, how="outer")

    # Заменяем NaN на 0
    section_merge = section_merge.fillna(0)
    section_not_zero = section_merge[
        (section_merge["doc_sum"] != 0)
        | (section_merge["receipt"] != 0)
        | (section_merge["expense"] != 0)
        ]
    section_not_zero.reset_index(drop=True, inplace=True)
    # logger.info(section_not_zero)
    return section_not_zero


async def add_head_to_excel(wb, sheet):
    # Создаём формат с переносом текста
    sheet.set_row(0, 40)  # установка высоты строки
    combined_format = head_format(wb)
    combined_format.set_text_wrap()
    sheet.write("A1", "сегмент/клиент/организация", head_format(wb))
    sheet.write("B1", "валюта", head_format(wb))
    sheet.write("C1", "датаДок", head_format(wb))
    sheet.write("D1", "датаСрока", head_format(wb))
    sheet.write("E1", "продажа/возврат", head_format(wb))
    sheet.write("F1", "оплата", head_format(wb))
    sheet.write("G1", "ОбщийДолг", head_format(wb))
    sheet.write("H1", "+прошлоДней\n-наступит", combined_format)
    sheet.write("I1", "блокНН", head_format(wb))
    sheet.write("J1", "блокНН/НДС", head_format(wb))
    sheet.write("K1", "блокРК", head_format(wb))
    sheet.write("L1", "блокРК/НДС", head_format(wb))


async def create_excel(df):
    try:
        df.to_excel('output.xlsx', index=False)
        df = df.fillna("")
        # Создаем новый Excel файл и добавляем лист
        # wb = xlsxwriter.Workbook(SAVE_TO_FILE)
        wb = xlsxwriter.Workbook(SAVE_TO_FILE, {'nan_inf_to_errors': True})
        sheet = wb.add_worksheet()
        sheet.set_column("H:H", 15)

        # создаем шапку
        await add_head_to_excel(wb, sheet)
        df = df.sort_values(
            by=["segment", "customer", "organization", "currency_name", "last_date", "doc_date"])

        # Остаток суммы по всем сегментам + валюте по текущую дату включительно
        columns = ["segment", "currency_name"]
        segment_sum = await get_data_by_section(df, columns)
        segment_sum = segment_sum.loc[
            (
                    abs(segment_sum["receipt"])
                    + abs(segment_sum["expense"])
                    + abs(segment_sum["doc_sum"])
            )
            > 5
            ]
        row_number = 2  # Начинаем с 2 строки, т.к. 1 строка - наименование столбцов
        for index, row in segment_sum.iterrows():
            sheet.write(f"A{row_number}", row.get("segment"), segment_format(wb))
            sheet.write(f"B{row_number}", row.get("currency_name"), segment_format(wb))
            sheet.write_blank(f"C{row_number}", None, segment_format(wb))
            sheet.write_blank(f"D{row_number}", None, segment_format(wb))
            sheet.write(f"E{row_number}", row.get("receipt"), segment_format(wb))
            sheet.write(f"F{row_number}", row.get("expense"), segment_format(wb))
            sheet.write(f"G{row_number}", row.get("doc_sum"), segment_format(wb))
            sheet.write(f"I{row_number}", row.get("block_amount_nn"), segment_format(wb))
            sheet.write(f"J{row_number}", row.get("block_vat_nn"), segment_format(wb))
            sheet.write(f"K{row_number}", row.get("block_amount_rk"), segment_format(wb))
            sheet.write(f"L{row_number}", row.get("block_vat_rk"), segment_format(wb))
            segment_row = row_number
            segment_total = 0
            segment_total_receipt = 0  # сумма по товарам отгрузка/возврат
            segment_total_expense = 0  # сумма по оплатам
            row_number += 1  # уровень сегмента

            columns = ["segment", "customer", "currency_name"]
            customer_df = df[
                (df["segment"] == row.get("segment"))
                & (df["currency_name"] == row.get("currency_name"))
                ]
            customer_sum_df = await get_data_by_section(customer_df, columns)
            customer_sum_df = customer_sum_df.loc[
                (
                        abs(customer_sum_df["receipt"])
                        + abs(customer_sum_df["expense"])
                        + abs(customer_sum_df["doc_sum"])
                )
                > 5
                ]
            for ind1, row1 in customer_sum_df.iterrows():
                sheet.write(f"A{row_number}", row1.get("customer"), customer_format(wb))
                sheet.write(f"B{row_number}", row1.get("currency_name"), customer_format(wb))
                sheet.write_blank(f"C{row_number}", None, customer_format(wb))
                sheet.write_blank(f"D{row_number}", None, customer_format(wb))
                sheet.write(f"E{row_number}", row1.get("receipt"), customer_format(wb))
                sheet.write(f"F{row_number}", row1.get("expense"), customer_format(wb))
                # sheet.write(f"G{row_number}", row1.get("doc_sum"), customer_format(wb))
                sheet.write(f"G{row_number}", row1.get("last_customer_pdz"), customer_format(wb))
                sheet.write(f"I{row_number}", row1.get("block_amount_nn"), customer_format(wb))
                sheet.write(f"J{row_number}", row1.get("block_vat_nn"), customer_format(wb))
                sheet.write(f"K{row_number}", row1.get("block_amount_rk"), customer_format(wb))
                sheet.write(f"L{row_number}", row1.get("block_vat_rk"), customer_format(wb))
                sheet.set_row(row_number - 1, None, None, {"level": 1})
                customer_row = row_number
                customer_total = 0
                customer_receipt = 0
                customer_expense = 0

                row_number += 1  # уровень клиента

                columns = ["segment", "customer", "organization", "currency_name"]
                organization_df = df[
                    (df["segment"] == row1.get("segment"))
                    & (df["customer"] == row1.get("customer"))
                    & (df["currency_name"] == row1.get("currency_name"))
                    ]
                organization_sum_df = await get_data_by_section(organization_df, columns)
                organization_sum_df = organization_sum_df.loc[
                    (
                            abs(organization_sum_df["receipt"])
                            + abs(organization_sum_df["expense"])
                            + abs(organization_sum_df["doc_sum"])
                    )
                    > 5
                    ]
                for ind2, row2 in organization_sum_df.iterrows():
                    sheet.write(f"A{row_number}", row2.get("organization"), organization_format(wb))
                    sheet.write(f"B{row_number}", row2.get("currency_name"), organization_format(wb))
                    sheet.write_blank(f"C{row_number}", None, organization_format(wb))
                    sheet.write_blank(f"D{row_number}", None, organization_format(wb))
                    sheet.write(f"E{row_number}", row2.get("receipt"), organization_format(wb))
                    sheet.write(f"F{row_number}", row2.get("expense"), organization_format(wb))
                    sheet.write(f"G{row_number}", row2.get("doc_sum"), organization_format(wb))
                    sheet.write(f"I{row_number}", row2.get("block_amount_nn"), organization_format(wb))
                    sheet.write(f"J{row_number}", row2.get("block_vat_nn"), organization_format(wb))
                    sheet.write(f"K{row_number}", row2.get("block_amount_rk"), organization_format(wb))
                    sheet.write(f"L{row_number}", row2.get("block_vat_rk"), organization_format(wb))
                    sheet.set_row(row_number - 1, None, None, {"level": 1})  # группировка строк уровень сегмента
                    sheet.set_row(row_number - 1, None, None, {"level": 2})  # группировка строк уровень клиента
                    organization_row = row_number
                    organization_receipt = 0 # сумма по товарам отгрузка/возврат. Клиент+валюта+организация
                    organization_expense = 0 # сумма по оплатам. Клиент+валюта+организация

                    row_number += 1  # уровень организации
                    columns = ["segment", "customer", "organization", "doc_date", "last_date", "currency_name"]
                    df['doc_sum'] = df['doc_sum'].fillna(0).astype(float)
                    df['customer_pdz'] = df['customer_pdz'].fillna(0).astype(float)

                    details_df = df[
                          (df["segment"] == row2.get("segment"))
                        & (df["customer"] == row2.get("customer"))
                        & (df["currency_name"] == row2.get("currency_name"))
                        & (df["organization"] == row2.get("organization"))
                        & (df["last_date"].dt.date >= DATE_FIRST)
                        ].reset_index(drop=True)

                    details_sum_df = await get_data_by_section(details_df, columns)
                    details_sum_df["receipt"] = details_sum_df["receipt"].astype(float)
                    details_sum_df["expense"] = details_sum_df["expense"].astype(float)
                    columns_sort = ["segment", "customer", "organization", "currency_name", "last_date", "doc_date"]
                    details_df = details_df.sort_values(by=columns_sort)
                    for ind3, row3 in details_df.iterrows():
                        sheet.write(f"B{row_number}", row3.get("currency_name"))
                        sheet.write(f"C{row_number}", row3.get("doc_date"), date_format(wb))
                        sheet.write(f"D{row_number}", row3.get("last_date"), date_format(wb))

                        if row3.get("recordtype") == "Receipt":
                            organization_receipt += Decimal(row3.get("doc_sum"))
                            sheet.write(f"E{row_number}", row3.get("doc_sum"), number_format(wb))
                        else:
                            organization_expense += Decimal(row3.get("doc_sum"))
                            sheet.write(f"F{row_number}", row3.get("doc_sum"), number_format(wb))

                        sheet.write(f"G{row_number}", row3.get("customer_pdz"), number_format(wb))
                        sheet.write(f"H{row_number}", row3.get("leave_days"), int_format(wb))
                        sheet.write(f"I{row_number}", row3.get("block_amount_nn"), number_format(wb))
                        sheet.write(f"J{row_number}", row3.get("block_vat_nn"), number_format(wb))
                        sheet.write(f"K{row_number}", row3.get("block_amount_rk"), number_format(wb))
                        sheet.write(f"L{row_number}", row3.get("block_vat_rk"), number_format(wb))

                        # row_number - 1 - т.к. индексация начинается с 0
                        sheet.set_row(row_number - 1, None, None, {"level": 1})  # группировка строк уровень сегмента
                        sheet.set_row(row_number - 1, None, None, {"level": 2})  # группировка строк уровень клиента
                        sheet.set_row(row_number - 1, None, None, {"level": 3})  # группировка строк уровень организации
                        row_number += 1  # уровень детализации

                    sheet.write(f"G{organization_row}", row2.get("last_customer_pdz"), organization_format(wb))
                    sheet.write(f"I{organization_row}", row2.get("block_amount_nn"), organization_format(wb))
                    sheet.write(f"E{organization_row}", organization_receipt, organization_format(wb))
                    sheet.write(f"F{organization_row}", organization_expense, organization_format(wb))
                    sheet.write_blank(f"H{organization_row}", None, organization_format(wb))
                    customer_total += Decimal(row2.get("last_customer_pdz"))  # organization_total
                    customer_receipt += organization_receipt
                    customer_expense += organization_expense

                sheet.write(f"G{customer_row}", customer_total, customer_format(wb))
                sheet.write(f"E{customer_row}", customer_receipt, customer_format(wb))
                sheet.write(f"F{customer_row}", customer_expense, customer_format(wb))
                sheet.write_blank(f"H{customer_row}", None, customer_format(wb))
                segment_total += customer_total
                segment_total_receipt += customer_receipt
                segment_total_expense += customer_expense

            sheet.write(f"G{segment_row}", segment_total, segment_format(wb))
            sheet.write(f"E{segment_row}", segment_total_receipt, segment_format(wb))
            sheet.write(f"F{segment_row}", segment_total_expense, segment_format(wb))
            sheet.write_blank(f"H{segment_row}", None, segment_format(wb))

            row_number += 1  # Пустая строка между сегментами

        sheet.freeze_panes(1, 0)  # Закрепление первой строки
        sheet.autofit()  # Автоподбор ширины столбцов
        sheet.hide_zero()  # Скрытие нулевых значений
        sheet.outline_settings(symbols_below=False)
        wb.close()  # Закрываем и сохраняем файл

        logger.info(f"Файл {SAVE_TO_FILE} создан.")
        return True
    except Exception as e:
        logger.error(f"Error in create_excel function: {e}")
        return False


# парсинг и контроль аргументов командной строки
async def control_args():
    global DATE_FIRST
    DATE_FIRST = await is_date(sys.argv[1])
    if not DATE_FIRST:
        return {"ERROR": "Не определана дата"}

    chatid = sys.argv[2]
    manager_name = await is_manager(chatid)
    manager_key = managers_name_uuid.get(manager_name)
    if not manager_key:
        return {"ERROR": "Вы не зарегистрированы"}

    result = {
        "date_first": DATE_FIRST,
        "manager_key": manager_key,
        "chatid": chatid,
    }
    logger.info(f"result: {result}")
    return result


async def is_manager(chatid):
    managers_name = managers_chatid_name.get(chatid)
    logger.info(f"managers_name: {managers_name}")
    return managers_name if managers_name else None


# contol date_text is date
async def is_date(parsed_date):
    try:
        if isinstance(parsed_date, date):
            return parsed_date
        else:
            return parse(parsed_date, dayfirst=True).date()
    except ValueError:
        return False


async def load_data(date_first, manager_key):
    global SAVE_TO_FILE
    try:
        await send_telegram("Ожидайте. Вам будет выслан файл.", sys.argv[-1])

        await main_cat_counterparties_async()

        # Catalog_ДоговорыКонтрагентов
        await main_cat_contracts_counterparties_async()

        # AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
        await main_doc_reciprocal_settlements_details_async()
        await create_views()

        df = await get_data_from_view(manager_key, date_first)
        if df.empty:
            logger.info("Error: Unable to load data to DataFrame.")
            return None

        manager_name = df.get("manager").iloc[0]
        if manager_name is None:
            await send_telegram("Error: Unable to get manager name.", sys.argv[-1])
            return None

        SAVE_TO_FILE = f"{manager_name}_{date_to_str(date_first)}_{date_to_str(datetime.today())}_{time.strftime('%H%M')}.xlsx"
        SAVE_TO_FILE = sanitize_filename(SAVE_TO_FILE)
        SAVE_TO_FILE = os.path.join(os.path.dirname(__file__), SAVE_TO_FILE)
        return await create_excel(df)

    except Exception as e:
        logger.error(f"Error in load_data function: {e}")

    return None


async def main_debt_to_excel_xlsx_writer_async():
    global DATE_FIRST
    # добавим аргументы для запуска функции
    logger.info("Start DebtToExcelXlsxWriter.py")
    cur_dir = os.path.dirname(__file__)
    dir_to_python = os.path.join(cur_dir, ".venv", "Scripts", "python.exe")
    dir_to_script = os.path.join(cur_dir, "DebtToExcelXlsxWriter.py")
    if len(sys.argv) == 1:
        sys.argv = [f"{dir_to_python} {dir_to_script}", "01.04.2025", "490323168"]
    args = await control_args()
    err = args.get("ERROR")
    if err:
        logger.info(err)
        await send_telegram(err, sys.argv[-1])
        sys.exit(0)
    DATE_FIRST = args.get("date_first")
    result = await load_data(DATE_FIRST, args.get("manager_key"))

    if result:
        await telegram_bot_send_document(SAVE_TO_FILE, sys.argv[2])

    logger.info(f"End DebtToExcelXlsxWriter.py: {result}")


def run_main():
    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    loop.run_until_complete(main_debt_to_excel_xlsx_writer_async())


if __name__ == '__main__':
    logger.info(f"Start {datetime.now()}")
    run_main()
    logger.info(f"End {datetime.now()}")
