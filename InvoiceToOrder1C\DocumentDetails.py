import os
import pandas as pd
import asyncio

from InvoiceToOrder1C.Nomenclature import main_nomenclature_async
from multiThread import get_json_from_url

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Document_ПоступлениеТоваровУслуг_Товары"
SELECT_COLUMNS = "Количество,Цена,Сумма,Коэффициент,Номенклатура_Key,СерияНоменклатуры_Key"


async def get_response(ref_key: str):
    url = f"http://192.168.1.254/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"/?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    filter_ref_key = f"&$filter=Ref_Key eq guid'{ref_key}'"
    url += filter_ref_key
    response = await get_json_from_url(url)
    return response


async def main_document_details_async(document_key):
    df_table = pd.DataFrame()
    json_data = await get_response(document_key)
    if ('value' in json_data) and (json_data['value'] != []):
        df_table = pd.json_normalize(json_data['value'])
        df_sku = await create_sku_df(df_table)
        df_sku['Цена'] = df_sku['Сумма'] / df_sku['Количество']
        df_table = df_sku[['Номенклатура', 'Количество', 'Цена', 'Сумма', 'Коэффициент', 'Номенклатура_Key',
                           'СерияНоменклатуры_Key']]  # move column position

    return df_table


async def create_sku_df(dtl):
    dfsku = pd.DataFrame()
    for i, sku in dtl.iterrows():
        sku_data = await main_nomenclature_async(sku['Номенклатура_Key'])
        dfsku = pd.concat([dfsku, sku_data], axis='rows', ignore_index=True)

    dfsku = dfsku.rename({'Ref_Key': 'Номенклатура_Key'}, axis='columns')
    df = pd.concat([dfsku, dtl], axis='columns', join="inner")
    df = df.T.drop_duplicates().T  # drop dublicates columnname

    return df


if __name__ == "__main__":
    asyncio.run(main_document_details_async("d9c3c214-e9b3-11ed-8182-001dd8b740bc"))
