# Description: Сохраняет данные о задолженности в Excel.
# отбираются все не оплаченные документы
# без колонки датаСрока

import os
import sys
import numpy as np
from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catCounterparties import main_cat_counterparties_async
from CreateAndRunSQLScripts import create_views
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async
# для отлавливания номера строки ошибки
import traceback
sys.path.append(os.path.dirname(__file__))
import asyncio
import aiohttp
import aiofiles
import json
from aiohttp import FormData
from datetime import datetime, date
from dateutil.parser import parse
from pathvalidate import sanitize_filename
from decimal import Decimal
import pandas as pd
import xlsxwriter
from dateutil import parser
from time import strftime
from LoadManagerBonusToExcel import managers_chatid_name, managers_name_uuid
from logger_prestige import get_logger
from async_Postgres import (
    sql_to_dataframe_async,
    send_telegram,
    TELEGRAM_TOKEN,
    chatid_rasim,
)

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
DATE_FIRST = parser.parse("01.04.2025", dayfirst=True).date()
SAVE_TO_FILE = "Config.xlsx"

# Установите опцию отображения максимального количества столбцов
pd.set_option('display.max_columns', None)


def head_format(wb):
    return wb.add_format({"bg_color": "#778899", "bold": True, "align": "center"})


def segment_format(wb):
    return wb.add_format(
        {
            "bg_color": "#808080",  # color - gray
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 14,
        }
    )


def customer_format(wb):
    return wb.add_format(
        {
            "bg_color": "#C0C0C0",  # color - silver
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 13,
        }
    )


def organization_format(wb):
    return wb.add_format(
        {
            "bg_color": "#f2ebf1",  # color - lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 11,
        }
    )


def contract_format(wb):
    return wb.add_format(
        {
            "bg_color": "#e6e6fa",  # color - light lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 10,
        }
    )


def date_format(wb):
    return wb.add_format({"num_format": "dd.mm.yyyy"})


def number_format(wb):
    return wb.add_format({"num_format": "# ### ##0.00;[Red]# ### ##0.00"})


def int_format(wb):
    return wb.add_format({"num_format": "# ### ##0;[Red]# ### ##0"})


def date_to_str(period):
    return datetime.strftime(period, "%Y%m%d")


# отправка файла в телеграм
async def telegram_bot_send_document(path_doc, chat_id):
    chat_id = str(chat_id)
    logger.info(f"файл для отправки: {path_doc}")
    async with aiofiles.open(path_doc, "rb") as doc:
        data = FormData()
        data.add_field("chat_id", chat_id)
        data.add_field("document", doc, filename=path_doc)

        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendDocument"
            async with session.post(url, data=data) as response:
                logger.info(response.status)
                content = await response.text()
                result = json.loads(content)
                filename = {result.get("result").get("document").get("file_name")}
                logger.info(f"filename: {filename}")
                await send_telegram(f"Вам отправлен файл: {filename}", chat_id)

    if chat_id != str(chatid_rasim):
        await telegram_bot_send_document(os.path.abspath(path_doc), str(chatid_rasim))

    return response.status


def cell_address(row, col):
    # Преобразуем номер колонки в букву
    column_letter = ""
    while col >= 0:
        column_letter = chr(col % 26 + ord("A")) + column_letter
        col = col // 26 - 1
    # Возвращаем адрес ячейки в формате Excel
    return f"{column_letter}{row + 1}"


def set_column_width_cm(worksheet, col_num, width_in_cm):
    cm_to_pixels = 3.78
    width_in_pixels = width_in_cm * cm_to_pixels
    worksheet.set_column(col_num, col_num, width_in_pixels)


async def colored_range(ws, row, cell_format):
    # Закрашиваем диапазон ячеек
    for col in range(6):
        cell = cell_address(row, col)
        ws.write_blank(cell, None, cell_format)


# получение данных из представления v_one_customer_balance_no_date
# и запись их в DataFrame
async def get_data_from_view(manager_key: str, date_first: date=None):
    sql = f'''
            SELECT
                0 AS sort, row_no, max_row_no, leave_days, customer_sum, segment, manager, customer, doc_date, last_date,
                'начальный остаток' doc_number, 0 sale, 0 pay, organization, currency_name, contract_type,
                contract_name, recorder_type, manager_key
            FROM v_one_customer_balance_no_date
            WHERE max_row_no
                AND manager_key = $1::text
                {'AND doc_date < $2::date' if DATE_FIRST else ''}
            UNION ALL
            SELECT
                1 AS sort, row_no, max_row_no, leave_days, customer_sum, segment, manager, customer, doc_date, last_date,
                doc_number, sale, pay, organization, currency_name, contract_type, contract_name, recorder_type, manager_key
            FROM v_one_customer_balance_no_date
            WHERE manager_key = $1::text
                {'AND doc_date >= $2::date' if DATE_FIRST else ''}
            ORDER BY
                sort, row_no
        '''
    try:
        df = await sql_to_dataframe_async(sql, manager_key, DATE_FIRST)

        # Преобразование колонок в нужные форматы
        df["doc_date"] = pd.to_datetime(df["doc_date"], errors="coerce")  # дата документа
        # df["last_date"] = pd.to_datetime(df["last_date"], errors="coerce")  # дата срока оплаты
        df["customer_sum"] = df["customer_sum"].astype(float)  # сумма долга по клиенту
        df["sale"] = df["sale"].astype(float)  # поставка/возврат
        df["pay"] = df["pay"].astype(float)  # оплата
        # Error in load_data function: Cannot convert non-finite values (NA or inf) to integer/ исправим
        df["leave_days"] = df["leave_days"].replace([np.nan, np.inf, -np.inf], 0)
        df["leave_days"] = df["leave_days"].astype(int)  # остаток дней
        df = df.fillna("")
        return df
    except Exception as e:
        logger.error(f"Error in get_data_from_view function: {e}\n{traceback.format_exc()}")
        return pd.DataFrame()


async def get_sum_form_df(df, column_name, filter_column=None, filter_value=None):
    """
    Parameters:
    df - DataFrame,
    column_name - название столбца/ов для группировки.
        По нему будет считаться сумма. Может быть несколько столбцов(список),
    filter_column - название столбца для фильтрации,
    filter_value - значение для фильтрации
    """
    df.loc[:, column_name] = df[column_name].fillna("")  # Заполняем пустые значения в column_name пустой строкой
    if filter_column:
        df = df[
            df[filter_column] == filter_value
            ].copy()  # Используем .copy() для создания копии DataFrame

    # Группировка и суммирование
    group_df = df[df['max_row_no']== True].groupby([*column_name])["customer_sum"].sum().reset_index()
    # group_df = df.groupby([*column_name])["sale"].sum().reset_index()
    # group_df = df.groupby([*column_name])["pay"].sum().reset_index()

    # Получение последнего значения customer_sum в каждой группе
    # df.loc[:, "customer_sum"] = df.groupby([*column_name])["customer_sum"].transform("last")

    # Добавление последнего значения customer_sum в group_df
    # group_df["customer_sum"] = df.drop_duplicates(subset=column_name)["customer_sum"].values

    return group_df


# сумма по статьям до текущей даты
async def get_data_before(df, columns_by_group):
    df_sum = await get_sum_form_df(df, columns_by_group)
    return df_sum


# сумма по статьям между датами
async def get_data_between(df, columns_by_group):
    # Разделяем данные на продажи и оплаты
    df_sale = df[df["sale"] != 0].copy()
    segment_df_sale = await get_sum_form_df(df_sale, columns_by_group)
    segment_df_sale.rename(columns={"customer_sum": "sale"}, inplace=True)

    df_pay = df[df["pay"] != 0].copy()
    segment_df_pay = await get_sum_form_df(df_pay, columns_by_group)
    segment_df_pay.rename(columns={"customer_sum": "pay"}, inplace=True)

    merged_df = segment_df_sale.merge(segment_df_pay, on=[*columns_by_group], how="outer")
    # Удаление дублирующихся колонок
    merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]
    return merged_df


async def get_data_by_section(df, columns):
    section_sum = await get_data_before(df, columns)
    section_period = await get_data_between(df, columns)
    section_merge = section_sum.merge(section_period, on=columns, how="outer")

    # Заменяем NaN на 0
    section_merge = section_merge.fillna(0)
    section_not_zero = section_merge[
        (section_merge["customer_sum"] != 0)
        | (section_merge["sale"] != 0)
        | (section_merge["pay"] != 0)
        ]



    section_not_zero.reset_index(drop=True, inplace=True)
    return section_not_zero


async def add_head_to_excel(wb, sheet):
    # Создаём формат с переносом текста
    sheet.set_row(0, 40)  # установка высоты строки
    combined_format = head_format(wb)
    combined_format.set_text_wrap()
    sheet.write("A1", "сегмент/клиент/организация", head_format(wb))
    sheet.write("B1", "договор/валюта", head_format(wb))
    sheet.write("C1", "датаДок", head_format(wb))
    sheet.write("D1", "продажа/возврат", head_format(wb))
    sheet.write("E1", "оплата", head_format(wb))
    sheet.write("F1", "ОбщийДолг", head_format(wb))
    sheet.write("G1", "Дней просрочки", combined_format)


# оставляем записи по которым сумма > 1
async def extract_correct_sum(df,columns):
    # return df if df[columns].count() > 0 else pd.DataFrame()
    return True if df[columns].count() > 0 else False


async def create_excel(df):
    try:
        # Создаем новый Excel файл и добавляем лист
        wb = xlsxwriter.Workbook(SAVE_TO_FILE, {'nan_inf_to_errors': True})
        sheet = wb.add_worksheet()
        sheet.set_column("H:H", 15)
        empty_df = pd.DataFrame()

        # создаем шапку
        await add_head_to_excel(wb, sheet)

        # Сортировка данных
        df = df.sort_values(
            by=["segment", "customer", "organization", "currency_name", "contract_name", "doc_date", "doc_number",
                "last_date"])

        # Остаток суммы по всем сегментам + валюте по текущую дату включительно
        columns = ["segment"]
        segment_sum = await get_data_by_section(df, columns)
        row_number = 2  # Начинаем с 2 строки, т.к. 1 строка - наименование столбцов
        for index, row in segment_sum.iterrows():
            sheet.write(f"A{row_number}", row.get("segment"), segment_format(wb))
            sheet.write(f"B{row_number}", row.get("currency_name"), segment_format(wb))
            sheet.write_blank(f"C{row_number}", None, segment_format(wb))
            sheet.write(f"D{row_number}", row.get("sale"), segment_format(wb))
            sheet.write(f"E{row_number}", row.get("pay"), segment_format(wb))
            # Здесь мы пока просто выводим значение, а потом обновим его после расчета итогов
            sheet.write(f"F{row_number}", row.get("customer_sum"), segment_format(wb))
            sheet.write_blank(f"G{row_number}", None, segment_format(wb))
            segment_row = row_number
            row_number += 1  # уровень сегмента

            # First level - segment, customer
            columns = ["segment", "customer"]
            customer_df = df[(df["segment"] == row.get("segment"))]
            customer_sum_df = await get_data_by_section(customer_df, columns)
            for ind1, row1 in customer_sum_df.iterrows():
                sheet.write(f"A{row_number}", row1.get("customer"), customer_format(wb))
                sheet.write_blank(f"B{row_number}", None, customer_format(wb))
                sheet.write_blank(f"C{row_number}", None, customer_format(wb))
                sheet.write(f"D{row_number}", row1.get("sale"), customer_format(wb))
                sheet.write(f"E{row_number}", row1.get("pay"), customer_format(wb))
                sheet.write(f"F{row_number}", row1.get("customer_sum"), customer_format(wb))
                sheet.write_blank(f"G{row_number}", None, customer_format(wb))
                # Устанавливаем уровень группировки для клиента
                sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": False})
                customer_row = row_number
                row_number += 1  # уровень клиента

                # Second level - segment, customer, organization
                columns = ["segment", "customer", "organization"]
                organization_df = df[(df["segment"] == row1.get("segment"))& (df["customer"] == row1.get("customer"))]
                organization_sum_df = await get_data_by_section(organization_df, columns)
                for ind2, row2 in organization_sum_df.iterrows():
                    sheet.write(f"A{row_number}", row2.get("organization"), organization_format(wb))
                    sheet.write(f"B{row_number}", "", organization_format(wb))
                    sheet.write_blank(f"C{row_number}", None, organization_format(wb))
                    sheet.write(f"D{row_number}", row2.get("sale"), organization_format(wb))
                    sheet.write(f"E{row_number}", row2.get("pay"), organization_format(wb))
                    sheet.write(f"F{row_number}", row2.get("customer_sum"), organization_format(wb))
                    sheet.write_blank(f"G{row_number}", None, organization_format(wb))
                    # Устанавливаем уровни группировки для организации
                    sheet.set_row(row_number - 1, None, None, {"level": 2, "collapsed": False})  # группировка строк уровень организации
                    organization_row = row_number
                    organization_sale = 0 # сумма по товарам отгрузка/возврат. Клиент+валюта+организация
                    organization_pay = 0 # сумма по оплатам. Клиент+валюта+организация
                    organization_total = 0

                    row_number += 1  # уровень организации

                    # Добавляем уровень контракта
                    columns = ["segment", "customer", "organization", "contract_name", "currency_name"]
                    contract_df = df[
                        (df["segment"] == row2.get("segment"))
                        & (df["customer"] == row2.get("customer"))
                        & (df["organization"] == row2.get("organization"))
                        ]
                    contract_sum_df = await get_data_by_section(contract_df, columns)
                    # contract_sum_df = await extract_correct_sum(contract_sum_df)

                    for ind3, row3 in contract_sum_df.iterrows():
                        contract_display = f"{row3.get('contract_name')} / {row3.get('currency_name')}"

                        # Детализация по документам - получаем данные до их использования
                        details_df = df[
                            (df["segment"] == row3.get("segment"))
                            & (df["customer"] == row3.get("customer"))
                            & (df["organization"] == row3.get("organization"))
                            & (df["contract_name"] == row3.get("contract_name"))
                            & (df["currency_name"] == row3.get("currency_name"))
                            & (df["doc_number"] != row3.get("doc_number"))
#                            & ((df["sale"] != 0) | (df["pay"] != 0))
                            ].reset_index(drop=True)

                        details_df = details_df.sort_values(by=["doc_date", "doc_number", "row_no"])

                        # Используем сумму последнего документа в детализации
                        contract_total = 0
                        max_leave_days = None
                        if len(details_df) > 0:
                            contract_total = Decimal(details_df.iloc[-1].get("customer_sum"))
                            # Находим максимальное положительное значение leave_days из всех документов договора
                            if 'leave_days' in details_df.columns and not details_df['leave_days'].empty:
                                # Фильтруем только положительные значения
                                positive_days = details_df[details_df['leave_days'] >= 0]['leave_days']
                                if not positive_days.empty:
                                    max_leave_days = positive_days.max()

                        sheet.write(f"A{row_number}", "", contract_format(wb))
                        sheet.write(f"B{row_number}", contract_display, contract_format(wb))
                        sheet.write_blank(f"C{row_number}", None, contract_format(wb))
                        sheet.write(f"D{row_number}", row3.get("sale"), contract_format(wb))
                        sheet.write(f"E{row_number}", row3.get("pay"), contract_format(wb))
                        sheet.write(f"F{row_number}", contract_total, contract_format(wb))
                        # Устанавливаем уровень группировки для контракта
                        sheet.set_row(row_number - 1, None, None, {"level": 3, "collapsed": False})  # группировка строк уровень контракта
                        contract_row = row_number
                        contract_sale = 0
                        contract_pay = 0
                        contract_total = 0

                        row_number += 1  # уровень контракта

                        # Проверяем, что есть детализация для отображения
                        if not details_df.empty:
                            for ind4, row4 in details_df.iterrows():
                                doc_info = f"{row4.get('recorder_type')} {row4.get('doc_number')}"
                                sheet.write(f"B{row_number}", doc_info)
                                sheet.write(f"C{row_number}", row4.get("doc_date"), date_format(wb))

                                if row4.get("sale") != 0:
                                    contract_sale += Decimal(row4.get("sale"))
                                    sheet.write(f"D{row_number}", row4.get("sale"), number_format(wb))

                                if row4.get("pay") != 0:
                                    contract_pay += Decimal(row4.get("pay"))
                                    sheet.write(f"E{row_number}", row4.get("pay"), number_format(wb))

                                sheet.write(f"F{row_number}", row4.get("customer_sum"), number_format(wb))  # здесь оставляем customer_sum, так как это детализация
                                sheet.write(f"G{row_number}", row4.get("leave_days"), number_format(wb))

                                # Группировка строк для детализации
                                sheet.set_row(row_number - 1, None, None, {"level": 4, "collapsed": False})  # уровень детализации
                                contract_total += Decimal(row4.get("sale")) + Decimal(row4.get("pay"))
                                row_number += 1  # уровень детализации

                        # Обновляем итоги по контракту
                        sheet.write(f"D{contract_row}", contract_sale, contract_format(wb))
                        sheet.write(f"E{contract_row}", contract_pay, contract_format(wb))

                        # Записываем максимальное положительное значение leave_days для уровня договора
                        if max_leave_days is not None:
                            sheet.write(f"G{contract_row}", max_leave_days, contract_format(wb))

                        # Добавляем к итогам организации
                        organization_sale += contract_sale
                        organization_pay += contract_pay
                        # Добавляем значение contract_total к итогам организации
                        organization_total += contract_total

                    # Обновляем итоги по организации
                    sheet.write(f"D{organization_row}", organization_sale, organization_format(wb))
                    sheet.write(f"E{organization_row}", organization_pay, organization_format(wb))
                    sheet.write(f"F{organization_row}", organization_total, organization_format(wb))
                    sheet.write_blank(f"G{organization_row}", None, organization_format(wb))

                sheet.write_blank(f"G{customer_row}", None, customer_format(wb))
            sheet.write_blank(f"G{segment_row}", None, segment_format(wb))

            row_number += 1  # Пустая строка между сегментами

        sheet.freeze_panes(1, 0)  # Закрепление первой строки

        # Устанавливаем ширину столбцов вместо autofit (который не поддерживается в xlsxwriter)
        sheet.set_column('A:A', 30)  # Сегмент/клиент/организация
        sheet.set_column('B:B', 25)  # Договор/валюта
        sheet.set_column('C:C', 12)  # ДатаДок
        sheet.set_column('D:E', 15)  # Продажа/возврат и Оплата
        sheet.set_column('F:F', 15)  # ОбщийДолг
        sheet.set_column('G:G', 15)  # Прошло дней

        sheet.hide_zero()  # Скрытие нулевых значений
        # Настройки группировки
        sheet.outline_settings(symbols_below=False)

        # Развернуть все уровни по умолчанию
        sheet.set_default_row(hide_unused_rows=False)

        # Устанавливаем уровни группировки которые будут развернуты
        sheet.set_vba_name("Sheet1")
        wb.close()  # Закрываем и сохраняем файл

        logger.info(f"Файл {SAVE_TO_FILE} создан.")
        return True
    except Exception as e:
        logger.error(f"Error in create_excel function: {e}\n{traceback.format_exc()}")
        return False


# Функция control_args удалена, так как в этой версии используется только фильтр по manager_key


async def is_manager(chatid):
    managers_name = managers_chatid_name.get(chatid)
    logger.info(f"managers_name: {managers_name}")
    return managers_name if managers_name else None


# contol date_text is date
async def is_date(parsed_date):
    try:
        if not parsed_date:
            return None
        if isinstance(parsed_date, date):
            return parsed_date
        else:
            return parse(parsed_date, dayfirst=True).date()
    except ValueError:
        return None


async def load_data(manager_key, date_first=None):
    global SAVE_TO_FILE
    try:
        await send_telegram("Ожидайте. Вам будет выслан файл с балансом по клиентам.", sys.argv[-1])

        await main_cat_counterparties_async()

        # Catalog_ДоговорыКонтрагентов
        await main_cat_contracts_counterparties_async()

        # AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
        await main_doc_reciprocal_settlements_details_async()
        await create_views()

        # Используем только фильтр по manager_key без фильтрации по дате
        df = await get_data_from_view(manager_key, date_first)
        if df.empty:
            logger.info("Error: Unable to load data to DataFrame.")
            await send_telegram("Ошибка: Не удалось загрузить данные.", sys.argv[-1])
            return None

        manager_name = df.get("manager").iloc[0]
        if manager_name is None:
            await send_telegram("Error: Unable to get manager name.", sys.argv[-1])
            return None

        SAVE_TO_FILE = f"{manager_name}_balance_{date_to_str(datetime.today())}_{strftime('%H%M')}.xlsx"
        SAVE_TO_FILE = sanitize_filename(SAVE_TO_FILE)
        SAVE_TO_FILE = os.path.join(os.path.dirname(__file__), SAVE_TO_FILE)
        return await create_excel(df)

    except Exception as e:
        logger.error(f"Error in load_data function: {e}")
        await send_telegram(f"Произошла ошибка: {e}", sys.argv[-1])

    return None


async def main_debt_to_excel_xlsx_writer_no_date_async():
    # добавим аргументы для запуска функции
    logger.info("Start DebtToExcelXlsxWriterNotPayDocsWithDate.py")
    cur_dir = os.path.dirname(__file__)
    dir_to_python = os.path.join(cur_dir, ".venv", "Scripts", "python.exe")
    dir_to_script = os.path.join(cur_dir, "DebtToExcelXlsxWriterNotPayDocsWithDate.py")
    if len(sys.argv) == 1:
        # sys.argv = [f"{dir_to_python} {dir_to_script}", None, "490323168"]
        sys.argv = [f"{dir_to_python} {dir_to_script}", '01.03.2025', "490323168"]

    logger.info(sys.argv[1:]) # добавим аргументы для запуска функции
    # Проверяем, что передан ID менеджера
    if len(sys.argv) < 2:
        err = "Необходимо указать ID менеджера"
        logger.info(err)
        await send_telegram(err, sys.argv[-1] if len(sys.argv) > 1 else "490323168")
        sys.exit(0)

    date_first = await is_date(sys.argv[1])
    chatid = sys.argv[2]
    manager_name = await is_manager(chatid)
    manager_key = managers_name_uuid.get(manager_name)
    if not manager_key:
        err = "Вы не зарегистрированы"
        logger.info(err)
        await send_telegram(err, chatid)
        sys.exit(0)

    result = await load_data(manager_key, date_first)

    if result:
        await telegram_bot_send_document(SAVE_TO_FILE, chatid)

    logger.info(f"End DebtToExcelXlsxWriterNotPayDocsWithDate.py: {result}")


def run_main():
    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    loop.run_until_complete(main_debt_to_excel_xlsx_writer_no_date_async())


if __name__ == '__main__':
    logger.info(f"Start {datetime.now()}")
    run_main()
    logger.info(f"End {datetime.now()}")
