# запуск всех скриптов
# 09.09.2022
import asyncio
import os
import sys

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))
from createUser import main_create_user_async
from Cash.docCashAccRegRecordtype import main_doc_cash_acc_reg_recordtype_async
from Cash.docCashCorrectionOfRegister import main_doc_cash_correction_of_register_async
from Cash.docCashMoneyCheck import main_doc_money_check_async
from Cash.docCashPaymentOrderExpenseDetails import main_doc_cash_order_expense_details_async
from Cash.docCashPaymentOrderReceiptDetails import main_doc_cash_order_receipt_details_async
from Cash.docCashPaymentOrderWithdrawalOfFunds import main_doc_cash_payment_order_withdrawal_of_funds_async
from Cash.docCashPaymentOrderWithdrawalOfFundsDetails import \
    main_doc_cash_payment_order_withdrawal_of_funds_details_async
from Cash.docCashWarrantExpenseDetails import main_doc_cash_warrant_expense_details_async
from Cash.docCashWarrantReceiptDetails import main_doc_cash_warrant_receipt_details_async
from Cash.docCashWarrantReceipt import main_doc_cash_warrant_receipt_async
from Cash.docCashWarrantExpense import main_doc_cash_warrant_expense_async
from Cash.docCashMovement import main_doc_cash_movement_async
from Cash.docCashPaymentOrderReceipt import main_doc_cash_order_receipt_async
from Cash.docCashPaymentOrderExpense import main_doc_cash_order_expense_async
from Catalog.catBanks import main_cat_banks_async
from Catalog.catCashBankAccounts import main_cat_cash_bank_accounts_async
from Catalog.catCashFlowItem import main_cat_cash_clause_async
from Catalog.catCounterparties import main_cat_counterparties_async
from Catalog.catCurrency import main_cat_currencies_async
from Catalog.catOrganizations import main_cat_organizations_async
from Catalog.catUsers import main_cat_users_async
from Views.Offsettings import main_offsets_async
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)


async def pg_main_cash():

    # Catalog_Организации
    await main_cat_organizations_async()
    logger.info("main_cat_organizations_async")

    # Catalog_Пользователи
    await main_cat_users_async()
    logger.info("main_cat_users_async")

    # Catalog_Контрагенты
    await main_cat_counterparties_async()
    logger.info("main_cat_counterparties_async")

    # Catalog_Валюты
    await main_cat_currencies_async()
    logger.info("main_cat_currencies_async")

    # Catalog_СтатьиДвиженияДенежныхСредств
    await main_cat_cash_clause_async()
    logger.info("main_cat_cash_clause_async")

    # Catalog_БанковскиеСчета
    await main_cat_cash_bank_accounts_async()
    logger.info("main_cat_cash_bank_accounts_async")

    # Catalog_Банки
    await main_cat_banks_async()
    logger.info("main_cat_banks_async")

    # ***** касса *****
    # Document_ПриходныйКассовыйОрдер
    await main_doc_cash_warrant_receipt_async()
    logger.info("main_doc_cash_warrant_receipt_async")
    # Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
    await main_doc_cash_warrant_receipt_details_async()
    logger.info("main_doc_cash_warrant_receipt_details_async")

    # Document_РасходныйКассовыйОрдер
    await main_doc_cash_warrant_expense_async()
    logger.info("main_doc_cash_warrant_expense_async")
    # Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
    await main_doc_cash_warrant_expense_details_async()
    logger.info("main_doc_cash_warrant_expense_details_async")

    # Document_ПлатежноеПоручениеВходящее
    await main_doc_cash_order_receipt_async()
    logger.info("main_doc_cash_order_receipt_async")
    # Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
    await main_doc_cash_order_receipt_details_async()
    logger.info("main_doc_cash_order_receipt_details_async")

    # Document_ПлатежноеПоручениеИсходящее
    await main_doc_cash_order_expense_async()
    logger.info("main_doc_cash_order_expense_async")
    # Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
    await main_doc_cash_order_expense_details_async()
    logger.info("main_doc_cash_order_expense_details_async")

    # Document_ВнутреннееПеремещениеНаличныхДенежныхСредств
    await main_doc_cash_movement_async()
    logger.info("main_doc_cash_movement_async")

    # Document_ДенежныйЧек
    await main_doc_money_check_async()
    logger.info("main_doc_money_check_async")

    # AccumulationRegister_ДенежныеСредства_RecordType
    await main_doc_cash_acc_reg_recordtype_async()
    logger.info("main_doc_cash_acc_reg_recordtype_async")

    # Document_ПлатежныйОрдерСписаниеДенежныхСредств
    await main_doc_cash_payment_order_withdrawal_of_funds_async()
    logger.info("main_doc_cash_payment_order_withdrawal_of_funds_async")
    # Document_ПлатежныйОрдерСписаниеДенежныхСредств_РасшифровкаПлатежа
    await main_doc_cash_payment_order_withdrawal_of_funds_details_async()
    logger.info("main_doc_cash_payment_order_withdrawal_of_funds_details_async")

    # Document_КорректировкаЗаписейРегистров
    await main_doc_cash_correction_of_register_async()
    logger.info("main_doc_cash_correction_of_register_async")

    # Взаимозачеты
    await main_offsets_async()
    logger.info("main_offsets_async")
    # ***** касса *****

    # пользователи postgresql и права пользователям
    await main_create_user_async()
    logger.info("main_create_user_async")



if __name__ == '__main__':
    logger.info("START")
    # пересоздание views
    # asyncio.run(main_views_pg_async())
    asyncio.run(pg_main_cash())
    logger.info("FINISH")
