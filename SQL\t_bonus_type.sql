--типы бонусов предоставляемых клиентам
-- DROP TABLE IF EXISTS t_bonus_type;

CREATE TABLE IF NOT EXISTS t_bonus_type (
	id uuid DEFAULT uuid_generate_v4() NOT NULL,  -- идентификатор
	description varchar(30) NOT NULL,  -- тип бонуса
	is_percent bool DEFAULT false NULL,  -- true - процент, false - ставка
	CONSTRAINT t_bonus_type_pkey PRIMARY KEY (id),
	CONSTRAINT t_bonus_type_unq UNIQUE (description)
);

COMMENT ON TABLE t_bonus_type IS 'типы бонусов';
COMMENT ON COLUMN t_bonus_type.description IS 'тип бонуса';
COMMENT ON COLUMN t_bonus_type.is_percent IS 'true - процент, false - ставка';