    CREATE OR REPLACE VIEW v_one_return_control_counterparty_number AS
    SELECT DISTINCT t2.*
    FROM (
        SELECT counterparty Клиент, customer_date КлиентДата, count(DISTINCT lenght_number) количествоЗнаковВНомере
        FROM (
            SELECT customer_date, trim(customer_number) AS customer_number, counterparties.description counterparty,
                LENGTH(trim(customer_number)) AS lenght_number
            FROM t_one_doc_return_of_goods_from_customers AS main
                INNER JOIN t_one_cat_counterparties AS counterparties
                    ON main.account_key = counterparties.ref_key
            WHERE customer_date >= '01.01.2023'::date
            ) AS t
        GROUP BY Клиент, КлиентДата
        HAVING count(DISTINCT lenght_number) > 1
        ) AS t1
        INNER JOIN (
            SELECT customer_date, trim(customer_number) AS customer_number, counterparties.description counterparty,
                LENGTH(trim(customer_number)) AS lenght_number
            FROM t_one_doc_return_of_goods_from_customers AS main
                INNER JOIN t_one_cat_counterparties AS counterparties
                    ON main.account_key = counterparties.ref_key
        ) AS t2
        ON t1.Клиент = t2.counterparty AND t1.КлиентДата = t2.customer_date
        ORDER BY t2.counterparty, t2.customer_date DESC
    ;

    GRANT SELECT ON TABLE v_one_return_control_counterparty_number TO user_prestige;
