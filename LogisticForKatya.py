import asyncio

from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catCounterparties import main_cat_counterparties_async
from Catalog.catNomenclatureSeries import main_cat_nomenclature_series_async
from Document.docSaleOfGoodsServices import main_doc_sale_of_goods_services_async


async def load_data_from_logistic():
    await main_doc_sale_of_goods_services_async()
    await main_cat_counterparties_async()
    await main_cat_contracts_counterparties_async()
    await main_cat_nomenclature_series_async()


if __name__ == "__main__":
    asyncio.run(load_data_from_logistic())