
    CREATE OR REPLACE VIEW v_one_stock_maliyet_rapor_warehouse AS
    SELECT 
        warehouse,
        supplier,
        sku,
        last_maliyet AS maliyet,
        t.last_ostatok_ed AS ed,
        round(last_ostatok_krb,2) AS tobox,
        round(last_ostatok_krb * last_maliyet,3) AS amount_maliyet,
        warehouse_key
    FROM (
        SELECT 
            warehouse,
            supplier,
            sku,
            nomenclature_key,
            tarih,
            LAST_VALUE(maliyet) 
            OVER(
            PARTITION BY nomenclature_key
                ORDER BY tarih
                RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            ) last_maliyet,
            LAST_VALUE(ostatok_krb) 
            OVER(
            PARTITION BY warehouse, nomenclature_key
                ORDER BY tarih
                RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            ) last_ostatok_krb, 
            LAST_VALUE(ostatok_ed) 
            OVER(
            PARTITION BY warehouse, nomenclature_key
                ORDER BY tarih
                RANGE BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            ) last_ostatok_ed,
            warehouse_key            
        FROM 
            v_one_stock_maliyet
        WHERE date_max::date < current_date
            AND warehouse_key IN (
                'a26219b3-8fba-11e6-80c4-c936aa9c817c',  -- Основной склад товаров
                '4b40b865-6d2f-11ec-8125-001dd8b72b55'   -- Основной склад товаров 2022
                )
            AND ostatok_ed <> 0
    ) AS t
    GROUP BY
        warehouse_key,
        warehouse,
        supplier,
        sku,
        last_maliyet,
        nomenclature_key,
        last_ostatok_krb,
        last_ostatok_ed
    ORDER BY sku 
    ;

    GRANT SELECT ON TABLE v_one_stock_maliyet_rapor_warehouse TO user_prestige;

