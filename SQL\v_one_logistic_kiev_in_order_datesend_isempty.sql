--
--    -- the date send is empty but logistic is inserted
--    CREATE OR REPLACE VIEW v_one_logistic_kiev_in_order_datesend_isempty AS
--    SELECT *
--    FROM t_one_sale_logistics
--    WHERE (doc_date >= '01.03.2023')
--        AND (date_send = '0001-01-01')
--        AND (add_to_delivery_address IS NOT NULL)
--        AND (add_to_delivery_address <> '')
--        AND (carrier IS NOT NULL)
--        AND (carrier <> '')
--    ;
--    GRANT SELECT ON TABLE v_one_logistic_kiev_in_order_datesend_isempty TO user_prestige;
