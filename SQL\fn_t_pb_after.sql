CREATE OR REPLACE FUNCTION fn_t_pb_after()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
	DECLARE pattern TEXT;
	BEGIN

		INSERT INTO t_cash_bank(
			doc_date,
			doc_number,
			amount,
			amount_total,
			description,
			organization_key,
			currency_key
			)
		SELECT
			date_time_dat_od_tim_p AS doc_date,
			unqkey AS doc_number,
			t_pb.sum AS amount,
			t_pb.total_sum,
			concat(t_pb.aut_cntr_nam, '; ', t_pb.osnd) AS description,
			acc.organization_key,
			acc.currency_key
		FROM t_pb
			LEFT JOIN t_one_cat_cash_bank_accounts AS acc
				ON trim(acc.account_number) = trim(t_pb.aut_my_acc)
			LEFT JOIN t_one_cat_organizations AS org
				ON org.ref_key = acc.organization_key
			LEFT JOIN t_one_cat_currencies AS cur
				ON cur.ref_key = acc.currency_key
		WHERE length(aut_my_crf)=10
			AND date_time_dat_od_tim_p::Date >='01.07.2024'::Date
		ON CONFLICT (doc_number) DO NOTHING;

		RETURN NEW;
    END;
    $function$
;