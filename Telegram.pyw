# проверяет входящие смс
# отправляет баланс по запросу
# 20210428_0042

# -*- coding: utf-8 -*-

import re
import sys
import requests
import json
import pandas as pd
from json.decoder import JSONDecodeError
from error_log import add_to_log, send_sms
from datetime import datetime, timedelta
from prestige_authorize import con_postgres_psycopg2
from configPrestige import TELEGRAM_TOKEN

token = TELEGRAM_TOKEN
conpg = con_postgres_psycopg2()


def regex(pattern, str_text):
    result = ''
    try:
        # транслитируем смс

        # intab = '~@#$%^&*()=_-+/|?<>,.'
        # outtab = '---------------------'
        # if type(str_text) != 'float':
        #     str_text = str_text.translate(str.maketrans(intab, outtab))

        result = re.search(pattern, str_text)
        if (result is not None) and (result != ''):
            result = result.upper()

    except Exception as e:
        msj = 'Telegram:dfRegex %s' % e
        add_to_log(msj)

    finally:
        return result


def get_api_telegram():
    # проверяем, есть-ли входщие смс
    msj_out = ''
    data = ''
    try:
        api_url = "https://api.telegram.org/bot{}/".format(token)
        method = 'getUpdates'
        params = {'timeout': 0}
        resp = requests.get(api_url + method, params)
        print(resp.text)
        data = json.loads(resp.text)

    except JSONDecodeError as e:
        msj_out = ("ERROR: telegram: : fnSupplierOrderList: ошибка в декодировании" + str(e))

    except TypeError as e:
        msj_out = ("ERROR: telegram: : fnSupplierOrderList: ошибка в значении" + str(e))

    except requests.exceptions.ConnectionError as errc:
        msj_out = ("ERROR: telegram: : Не получен отклик от ссервера", errc)

    except requests.exceptions.Timeout as errt:
        msj_out = ("ERROR: telegram: : Timeout Error:", errt)

    except Exception as e:
        msj_out = 'ERROR: telegram: :BalansToTelegram:dReadMsj'

    finally:
        if 'ERROR: telegram: ' in str(msj_out):
            add_to_log(msj_out)
        return data


def post_update_id(update_id):
    # отмечаем смс прочитанным
    try:
        api_url = "https://api.telegram.org/bot{}/getUpdates?offset={}".format(token, update_id + 1)
        requests.get(api_url)

    except Exception as e:
        print(str(e))
        add_to_log(str(e))


def insert_pg(list_data):
    # заносим в pg входяшие смс
    try:
        str_sql = '''
      INSERT INTO t_telegram
            (
              chat_id,
              is_bot,
              first_name,
              last_name,
              username,
              msj,
              tlgdate,
              croup_chat_id,
              group_name,
              message_id,
              reply_to_message_id
            ) 
          VALUES(%s,%s,%s, %s,%s,%s, %s,%s,%s ,%s,%s)
          '''

        chat_id = list_data['chat_id']
        first_name = list_data['first_name']
        last_name = list_data['last_name']
        username = list_data['username']
        date = list_data['date']
        text_sms = list_data['text_sms']
        is_bot = list_data['is_bot']
        croup_chat_id = list_data['croup_chat_id']
        group_name = list_data['group_name']
        message_id = list_data['message_id']
        reply_to_message_id = list_data['reply_to_message_id']

        odata = (
            chat_id,
            is_bot,
            first_name,
            last_name,
            username,
            text_sms,
            date,
            croup_chat_id,
            group_name,
            message_id,
            reply_to_message_id
        )

        with conpg:
            with conpg.cursor() as curpg:
                curpg.execute(str_sql, odata)
                conpg.commit()

    except Exception as e:
        print(str(e))
        conpg.rollback()
        add_to_log(str(e))


def chatid_policy_firm(chat_id):
    # отбираем фирмы к которым есть права у данного chat_id
    df = pd.DataFrame()
    try:
        str_sql = '''
          SELECT idfop, fopowner
          FROM t_telegram_policy
          WHERE idchat = %s
          ORDER BY fopowner;
          ''' % chat_id

        df = pd.read_sql(str_sql, conpg)

    except Exception as e:
        print(str(e))
        add_to_log(str(e))

    finally:
        return df


def parse_json_tlg(json_data):
    # парсим входящие смс (НЕ сам текст смс!!!)
    try:
        chat = []
        message = ''
        for i in range(len(json_data['result'])):
            result = json_data['result'][i]
            update_id = result['update_id']
            croup_chat_id = None
            group_name = None
            # если в бот поступают смс из группы
            if 'my_chat_member' in result:
                # добавили бот в группу
                my_chat_member = result['my_chat_member']
                date = datetime.utcfromtimestamp(my_chat_member['date'])
                croup_chat_id = my_chat_member['chat']['id']
                group_name = my_chat_member['chat']['title'] if 'title' in my_chat_member else ''
                is_bot = my_chat_member['from']['is_bot']

                if 'from' in my_chat_member:
                    chat = my_chat_member['from']
            elif 'edited_message' in result:
                edited_message = result['edited_message']
                chat = edited_message['from']
                is_bot = edited_message['from']['is_bot']
                message = edited_message['chat']
                date = datetime.utcfromtimestamp(edited_message['edit_date'])
            else:
                message = result['message']
                chat = message['chat']
                is_bot = message['from']['is_bot']
                date = datetime.utcfromtimestamp(message['date'])

            chat_id = chat['id'] if 'id' in chat else 0
            first_name = chat['first_name'] if 'first_name' in chat else ''
            last_name = chat['last_name'] if 'last_name' in chat else ''
            username = chat['username'] if 'username' in chat else ''
            text_sms = message['text'].lower() if 'my_chat_member' not in \
                                                  json_data['result'][i] and 'text' in message else ''  # м.б. photo
            message_id = json_data['result'][i]['message']['message_id'] if 'message' in json_data['result'][i] else 0

            if ('message' in json_data['result'][i]) and ('reply_to_message' in json_data['result'][i]['message']):
                reply_to_message_id = json_data['result'][i]['message']['reply_to_message']['message_id']
            else:
                reply_to_message_id = None
            # text_sms = message['text'].lower() if 'text' in message else '' # м.б. photo

            if username == '':
                username = (first_name + ' ' + last_name).strip()

            dict_data = {
                'chat_id': chat_id,
                'first_name': first_name,
                'last_name': last_name,
                'username': username,
                'date': date,
                'text_sms': text_sms,
                'is_bot': is_bot,
                'croup_chat_id': croup_chat_id,
                'group_name': group_name,
                'message_id': message_id,
                'reply_to_message_id': reply_to_message_id
            }

            # заносим в pg входяшие смс
            insert_pg(dict_data)

            # отмечаем смс прочитанным
            post_update_id(update_id)

            print(text_sms)
            parse_text_sms(chat_id, text_sms, username)


    except Exception as e:
        print(str(e))
        add_to_log(str(e))


def get_api_bank_firm(pb_id, pb_token):
    # api firm
    data = ''
    try:
        url = 'https://acp.privatbank.ua/api/proxy/rest/today?'
        headers = {'user-agent': 'my-own-user-agent/0.0.1', 'id': pb_id, 'token': pb_token}

        req = requests.get(url, headers=headers)
        data = req.json()  # Выделяем данные из ответа сервера

    except Exception as e:
        print(str(e))
        add_to_log(str(e))

    finally:
        return data


def parse_firm_json(json):
    # ответ банка по фирме
    total = 0
    sms = ''
    try:
        balanceResponse = json['balanceResponse']
        for account in balanceResponse:
            total += float(account[list(account.keys())[0]][
                               'balanceOutEq'])  # вариант 2 account[next(iter(account))]['balanceOutEq']

        sms = str("{total:.2f}") if total > 0 else str("{total:.2f}") + ' (EKSI)'

    except Exception as e:
        print(str(e))
        add_to_log(str(e))

    finally:
        return sms


def chatid_policy_kasa(chat_id):
    # отбираем кассы к которым есть права у данного chat_id
    df = pd.DataFrame()
    try:
        str_sql = '''
            SELECT fopowner
            FROM t_telegram_policy
            WHERE (idchat='%')
            ORDER BY t_telegram_policy.fopowner;
            ''' % chat_id

        df = pd.read_sql(str_sql, conpg)

    except Exception as e:
        print(str(e))
        add_to_log(str(e))

    finally:
        return df


def sum_kasa_convert_sms(df):
    # df остатков по кассе перевели в смс
    sms = '\n'
    try:

        for index, row in df.iterrows():
            usd = row.usd
            grn = row.grn
            fop = row.fop

            sms += fop + '\n'

            if usd > 0:
                sms += 'USD: ' + str("{usd:,}") + '\n'
            elif usd < 0:
                sms += 'USD: ' + str("{usd:,}") + ' (EKSI)' + '\n'

            if grn > 0:
                sms += 'GRN: ' + str("{grn:,}")
            elif grn < 0:
                sms += 'GRN: ' + str("{grn:,}") + ' (EKSI)'

            sms += '\n\n'

    except Exception as e:
        print(str(e))
        add_to_log(str(e))

    finally:
        return sms


def parse_text_sms(chat_id, text_sms, fio):
    # парсим сам текст сообщения
    sms = ''
    try:
        date_time = datetime.now().strftime("%d-%m-%Y %H:%M") + '\n\n'

        idnebim = regex(r"\d{6}[a-zA-Z]\d{2}", text_sms)

        if text_sms in ['kasa', 'касса']:
            pass
            # sms = balans(chat_id, fio)

        elif text_sms in ['kur', 'курс']:
            pass
            # sms = kur_minfin()

        else:
            sms = 'ERROR'

        sms = sms.strip() if sms else 'ERROR'

        sms = date_time + sms.strip()
        send_sms(sms, chat_id)

    except Exception as e:
        print(str(e))
        add_to_log(str(e))

    finally:
        return sms


if __name__ == '__main__':

    current_dt = datetime.now() + timedelta(seconds=295)
    while current_dt > datetime.now():
        updateid = 0

        try:

            # читаем входящее смс.
            data = get_api_telegram()

            # принимаем входящее смс. False - смс нет
            if ('result' in data) and data['result']:
                parse_json_tlg(data)

            print("FINISH")

        except Exception as e:
            msj_out = 'Telegram:main'
            msj_out = add_to_log(msj_out)
            sys.exit(0)

    else:
        if conpg:
            conpg.close()

        sys.exit(0)
