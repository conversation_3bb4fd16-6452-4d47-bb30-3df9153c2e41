-- используется в триггере t_cash_register_bfr табл t_cash_register

CREATE OR REPLACE FUNCTION fn_t_cash_register_bfr()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    big_amount numeric(15);
    small_amount numeric(15);
	rate varchar(20);
	clear_description varchar;
BEGIN
    -- если обмен, в коммент ставим курс
	IF COALESCE(NEW.amount_sub,0) <> 0 THEN
    	IF COALESCE(NEW.amount,0) = 0 THEN
            RAISE EXCEPTION 'Заполните сумму';
        END IF;

		NEW.recorder_type = -1;
        NEW.item_id = '4c61bd07-58cb-11e7-80cf-001dd8b79079'; -- обмен
        NEW.payment_purpose_id = '72327884-78a6-11e9-80ea-001dd8b79079'; -- обмен
        NEw.organization_key = '3d7e2ad1-8ac8-11e6-80c4-c936aa9c817c';

	    IF abs(NEW.amount) > abs(NEW.amount_sub) THEN
	        big_amount = NEW.amount;
	        small_amount = NEW.amount_sub;
	    ELSE
	        big_amount = NEW.amount_sub;
	        small_amount = NEW.amount;
	    END IF;

		rate = concat('; курс: ',round(big_amount/small_amount, 2));
		clear_description = (SELECT TRIM(BOTH ';' FROM REGEXP_REPLACE(REGEXP_REPLACE(NEW.description,'[; ]?курс: \d+(\.\d+)?;?','','g'),';*','','g')));
		NEW.description = concat(clear_description, rate);


	END IF ;
	-- ************off exchane

    -- Проверка на существование и заполнение периода и период не заполнен
	IF EXISTS(SELECT * FROM t_cash_payment_purpose WHERE is_periodic AND id = NEW.payment_purpose_id) AND NEW.item_period IS NULL THEN
        RAISE EXCEPTION 'заполните период';
    ELSEIF NOT EXISTS(SELECT * FROM t_cash_payment_purpose WHERE is_periodic AND id = NEW.payment_purpose_id) THEN
        NEW.item_period = current_date;
    END IF;

    -- Перемещение
    IF NEW.organization_key_sub IS NOT NULL THEN
--        NEW.recorder_type = -1;
        NEW.item_id = 'E204DE7A-F24B-486B-973B-0F1FB73512DE'; -- перемещение
        NEW.payment_purpose_id = '92229A9B-48F1-4090-AE74-6C2A55857B22'; -- перемещение
    END IF;

    -- Обновление пользователя при UPDATE
    IF TG_OP = 'UPDATE' THEN
        NEW.update_user := current_user;
    END IF;

    -- Корректировка суммы
    NEW.amount = abs(NEW.amount) * NEW.recorder_type;
    RETURN NEW;
END;
$function$
;

-- Permissions

ALTER FUNCTION public.fn_t_cash_register_bfr() OWNER TO anna;
GRANT ALL ON FUNCTION public.fn_t_cash_register_bfr() TO public;
GRANT ALL ON FUNCTION public.fn_t_cash_register_bfr() TO anna;
