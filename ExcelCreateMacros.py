# 20250413
# Скрипт для создания VBA макроса в Excel

import os
import sys
import traceback

def create_excel_with_macro():
    print("Creating Excel file with ViewPicture macro...")

    try:
        # Try xlwings first (usually easier to use)
        import xlwings as xw
        use_xlwings(xw)
    except ImportError:
        print("xlwings not found. Trying win32com...")
        try:
            import win32com.client
            use_win32com(win32com.client)
        except ImportError:
            print("Error: Neither xlwings nor win32com.client is available.")
            print("Please install one of these packages:")
            print("pip install xlwings")
            print("or")
            print("pip install pywin32")
            return


def use_xlwings(xw):
    try:
        # Start Excel and create a new workbook
        app = xw.App(visible=False)
        wb = app.books.add()

        # Add VBA module
        vba_module = wb.api.VBProject.VBComponents.Add(1)  # 1 = vbext_ct_StdModule

        # Add the macro code
        vba_module.CodeModule.AddFromString(get_macro_code())

        # Save as .xls format
        file_path = os.path.abspath("prise.xls")
        wb.save(file_path)

        # Close workbook and quit Excel
        wb.close()
        app.quit()

        print(f"Success! Excel file with macro created at: {file_path}")
    except Exception as e:
        print(f"Error with xlwings method: {e}")
        print(traceback.format_exc())
        raise


def use_win32com(win32com_client):
    try:
        # Create a new Excel application
        excel = win32com_client.Dispatch("Excel.Application")
        excel.Visible = False  # Run in background

        # Create a new workbook
        workbook = excel.Workbooks.Add()

        # Access the VBA project
        vba_module = workbook.VBProject.VBComponents.Add(1)  # 1 = vbext_ct_StdModule

        # Add the VBA code
        vba_module.CodeModule.AddFromString(get_macro_code())

        # Save the workbook as .xls (Excel 97-2003) format
        file_path = os.path.abspath("PrisePhotoMacros.xls")
        workbook.SaveAs(file_path, FileFormat=56)  # 56 = xls format

        # Close workbook and quit Excel
        workbook.Close(SaveChanges=False)
        excel.Quit()

        print(f"Success! Excel file with macro created at: {file_path}")
    except Exception as e:
        print(f"Error with win32com method: {e}")
        print(traceback.format_exc())
        raise


def get_macro_code():
    return  """
        Sub ToggleImageSize()
            Dim shp As Shape
            
            ' Получаем объект, по которому был клик
            Set shp = ActiveSheet.Shapes(Application.Caller)
            
            ' Выводим изображение на передний план
            shp.ZOrder msoBringToFront
            
            ' Проверяем текущий размер и меняем на противоположный
            If shp.Height = 100 And shp.Width = 100 Then
                ' Увеличиваем до 150мм x 150мм
                ' Конвертируем миллиметры в пункты (1 мм ≈ 2.83465 пунктов)
                shp.Height = 150 * 2.83465
                shp.Width = 150 * 2.83465
            Else
                ' Устанавливаем указанные размеры ячейки
                shp.Height = 100
                shp.Width = 100
            End If
        End Sub
    """


if __name__ == "__main__":
    try:
        create_excel_with_macro()
    except Exception as e:
        print("\nError creating Excel file with macro:")
        print(f"{type(e).__name__}: {e}")
        print("\nThis script requires that 'Trust access to the VBA project object model' is enabled.")
        print("To enable it in Excel:")
        print("1. Open Excel")
        print("2. Go to File > Options > Trust Center > Trust Center Settings")
        print("3. Select Macro Settings")
        print("4. Check 'Trust access to the VBA project object model'")
        print("5. Click OK and restart Excel")
        sys.exit(1)