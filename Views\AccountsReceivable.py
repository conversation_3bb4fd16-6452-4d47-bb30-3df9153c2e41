# дебеторская задолженность
import asyncio
import os
import sys

from prestige_authorize import CONFIG_PATH

sys.path.append(os.path.abspath(CONFIG_PATH))
from CreateAndRunSQLScripts import create_views
from async_Postgres import async_save_pg
from logger_prestige import get_logger
FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

VIEW_NAME_SOURCE = 'v_one_accounts_receivable_source'
VIEW_NAME_MAIN = 'v_one_accounts_receivable_period'
FUNCTION_NAME = 'fn_v_one_accounts_receivable'

SQL_CREATE_FUNCTION = f"""
    DROP FUNCTION IF EXISTS {FUNCTION_NAME}(date_last character);
    
    CREATE OR REPLACE FUNCTION {FUNCTION_NAME}(date_last character)
     RETURNS void
     LANGUAGE plpgsql
    AS $function$
        DECLARE 
            sql_query TEXT;
        BEGIN
        
             -- ***************************************
             sql_query = '
                -- DROP VIEW IF EXISTS {VIEW_NAME_SOURCE} CASCADE;
                CREATE OR REPLACE VIEW {VIEW_NAME_SOURCE} AS
                SELECT manager, customer, doc_date, last_date, 
                    contract_days::int as contract_days, 
                    amount_sale, amount_pay, is_accounting,
                sum(
                    CASE
                        WHEN last_date < ''' || date_last || ''' THEN
                            COALESCE(amount_sale,0) - COALESCE(amount_pay,0)
                        ELSE
                            0
                    END
                ) OVER (PARTITION BY contract_key ORDER BY last_date) AS sum_total,
                contract_key
                FROM (
                    -- продажи/возвраты клиенту
                    SELECT manager, customer, doc_date, last_date, 
                        contract_days::int, sum(amount_sale) as amount_sale,
                        sum(amount_pay) as amount_pay, is_accounting, contract_key
                    FROM (
                        SELECT voc.manager, voc.customer, sale.doc_date::date AS doc_date, 
                            CASE 
                                WHEN issale = 1 THEN 
                                    (sale.doc_date::date + INTERVAL ''1 days'' * COALESCE(contract_days::int,0))::date 
                                ELSE sale.doc_date::date
                            END AS last_date,
                            voc.contract_days::int,
                            sum(amount) AS amount_sale,
                            0 AS amount_pay,
                            sale.is_accounting,
                            voc.contract_key
                        FROM v_one_manager_counterparty_contracts_segments voc 
                            INNER JOIN t_one_sale AS sale
                                ON voc.contract_key = sale.contract_key
                        GROUP BY voc.manager, voc.customer, sale.doc_date::date, voc.contract_days::int,
                            sale.is_accounting, issale, voc.contract_key, sale.ref_key
                        UNION ALL -- оплаты клиента
                        SELECT voc.manager, voc.customer, doc_pay.dtperiod::date AS doc_date, 
                            doc_pay.dtperiod::date AS last_date,
                            voc.contract_days::int,
                            0 AS amount_sale,
                            sum(doc_pay.amount_control) AS amount_pay,
                            doc_pay.is_accounting,
                            voc.contract_key
                        FROM v_one_manager_counterparty_contracts_segments voc 
                            INNER JOIN t_one_accreg_cash_recordtype AS doc_pay
                                ON voc.contract_key = doc_pay.contract_key
                            INNER JOIN (SELECT DISTINCT contract_key FROM t_one_sale) as doc_sale 
                                ON voc.contract_key = doc_sale.contract_key 
                        GROUP BY voc.manager, voc.customer, doc_pay.dtperiod::date, 
                            voc.contract_days::int, doc_pay.is_accounting, 
                            voc.contract_key, doc_pay.recorder_key
                        UNION ALL -- корректировка долга (списание с долга клиента бонусов)
                        SELECT 
                            sub_contract.manager,
                            sub_contract.customer,
                            doc_date::date doc_date,
                            doc_date::date last_date,
                            0 contract_days,
                            debt_sub.amount_uah amount_sale,
                            0 AS amount_pay,
                            debt.is_accounting,
                            sub_contract.contract_key
                        FROM t_one_doc_debt_correction AS debt
                            LEFT JOIN 
                                (
                                SELECT 
                                    counterparty_type, 
                                    debt_type,
                                    document_key,				
                                    CASE 
                                        WHEN debt_type = ''Дебиторская'' THEN 
                                            round((rate / COALESCE(multiplicity)) * amount,3)
                                        ELSE 
                                            - round((rate / COALESCE(multiplicity)) * amount,3)
                                    END AS amount_uah,
                                    ref_key,
                                    counterparty_contract_key
                                FROM t_one_doc_debt_correction_debt_amount 
                                ) AS debt_sub
                                ON debt.ref_key = debt_sub.ref_key 
                            LEFT JOIN v_one_manager_counterparty_contracts_segments AS sub_contract
                                ON sub_contract.contract_key = debt_sub.counterparty_contract_key
                        WHERE debt.posted 
                            AND debt.operation_type IN (''СписаниеЗадолженности'')
                            AND counterparty_type <> ''Поставщик''
                        
                    ) AS t
                    GROUP BY manager, customer, doc_date, last_date, contract_days::int, is_accounting, contract_key
                ) as tt
                ORDER BY manager, customer, last_date, doc_date
                ;
                
                COMMENT ON VIEW {VIEW_NAME_SOURCE} IS ''задолженность клиентов'';
            ';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;
             -- ***************************************

        
            RAISE NOTICE '%', (date_last::date > current_date);
        
            EXECUTE '-- DROP VIEW IF EXISTS {VIEW_NAME_MAIN} CASCADE';
           
            sql_query = '
                CREATE OR REPLACE VIEW {VIEW_NAME_MAIN} AS 
                SELECT manager, customer, doc_date, last_date, contract_days::int as contract_days,
                    COALESCE(vac.amount_sale,0) AS amount_sale, COALESCE(vac.amount_pay,0) AS amount_pay, 
                    SUM(COALESCE(vac.amount_sale,0) - COALESCE(vac.amount_pay,0)) 
                        OVER (PARTITION BY doc_sale.customer_key ORDER BY vac.doc_date) sum_total,
                    CASE
                        WHEN vac.is_accounting AND org_type.organization_type THEN ''Ф1''
                        ELSE ''Ф2''
                    END AS is_accounting,
                    doc_sale.customer_key
                FROM (SELECT DISTINCT customer_key, contract_key, organization_key FROM t_one_sale) as doc_sale 
                    LEFT JOIN v_one_organization_and_type org_type 
                        ON org_type.ref_key = doc_sale.organization_key
                    INNER JOIN 
                    {VIEW_NAME_SOURCE} as vac
                        ON doc_sale.contract_key = vac.contract_key
                    LEFT JOIN
                    (
                    SELECT contract_key, max(last_date::date) as max_date
                    FROM {VIEW_NAME_SOURCE}
                    WHERE COALESCE(sum_total,0) <= 0
                        AND last_date::date < ''' || date_last || '''::date
                    GROUP BY contract_key
                    ) as last_balance_date
                    ON last_balance_date.max_date < vac.last_date
                        AND last_balance_date.contract_key = vac.contract_key
                ORDER BY manager, customer, doc_date
            ';
            RAISE NOTICE '%', sql_query;
            EXECUTE sql_query;
        
            EXECUTE 'ALTER TABLE {VIEW_NAME_SOURCE} OWNER TO user_prestige;';
            EXECUTE 'ALTER TABLE {VIEW_NAME_MAIN} OWNER TO postgres;';
            -- EXECUTE 'ALTER TABLE {VIEW_NAME_MAIN} OWNER TO user_prestige;';
            GRANT SELECT ON TABLE {VIEW_NAME_SOURCE} TO postgres;
            GRANT SELECT ON TABLE {VIEW_NAME_MAIN} TO postgres;

            RETURN;
        
        END;
        $function$
    ;

    ALTER FUNCTION {FUNCTION_NAME}(character) OWNER TO postgres;
    GRANT EXECUTE ON FUNCTION {FUNCTION_NAME}(character) TO user_prestige;
    GRANT ALL ON FUNCTION {FUNCTION_NAME}(character) TO postgres;

"""


async def main_accounts_receivable_async():
    await create_views()
    logger.info(f"create_views")

    result = await async_save_pg(SQL_CREATE_FUNCTION)
    logger.info(f"{result}, SQL_CREATE_FUNCTION")

    result = await async_save_pg('SELECT fn_v_one_accounts_receivable(current_date::text);')
    logger.info(f"{result}, fn_v_one_accounts_receivable")


if __name__ == '__main__':
    logger.info(f"START")
    asyncio.run(main_accounts_receivable_async())
    logger.info(f"FINSIH")
