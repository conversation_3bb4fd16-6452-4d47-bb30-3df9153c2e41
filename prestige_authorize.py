# pip install psycopg2
# pip install pyodbc
# pip install requests
# pip install sqlalchemy

import os
import sys
import traceback

import pandas as pd
import psycopg2
import pyodbc
import requests

if os.environ['COMPUTERNAME'] == 'PRESTIGEPRODUCT':
    CONFIG_PATH = r"d:\Prestige\Python\Config"
    CONFIG_PATH_NOVAPOSHTA = r"D:\Prestige\Python\NovaPoshta"
else:
    CONFIG_PATH = r"C:\Rasim\Python\Config"
    CONFIG_PATH_NOVAPOSHTA = r"c:\Rasim\Python\NovaPoshta"

sys.path.append(os.path.abspath(CONFIG_PATH))
sys.path.append(os.path.abspath(CONFIG_PATH_NOVAPOSHTA))

from datetime import datetime
from psycopg2 import IntegrityError
from sqlalchemy import create_engine
from configPrestige import username, psw, hostname_public, port, basename, URL_CONST, chatid_rasim, DATA_AUTH, schema
# from error_log import send_sms
import sqlalchemy as sa

engine = create_engine('postgresql://**:**@**:**/**' % (username, psw, hostname_public, port, basename), pool_pre_ping=True,
                       connect_args={
                           "keepalives": 1,
                           "keepalives_idle": 30,
                           "keepalives_interval": 10,
                           "keepalives_count": 5,
                       })


def add_path():
    sys.path.append(os.path.abspath(CONFIG_PATH))
    sys.path.append(os.path.abspath(CONFIG_PATH_NOVAPOSHTA))


def send_sms(self, msj_out, chat_id=chatid_rasim):
    # отправляем смс
    try:
        if msj_out != "":
            api_url = "https://api.telegram.org/bot{}/sendMessage?chat_id={}&text={}".format(
                '5728309503:AAFOMRt9xnvNWNEJ6N71n-NLGlKnQvw1UIg',
                chat_id,
                str(msj_out))
            requests.get(api_url)

    except Exception as e:
        msj = "send_sms: **; ERROR: **" % (msj_out, e)
        print(msj)


def full_vacuum_pg():
    result = True
    query = "VACUUM FULL;"
    try:
        conpg = con_postgres_psycopg2()
        old_isolation_level = conpg.isolation_level
        conpg.set_isolation_level(0)
        conpg.cursor().execute(query)
        conpg.set_isolation_level(old_isolation_level)

    except Exception as e:
        result = False
        sms = "table_vacuum_pg: **" % e
        print(sms)
        # # await add_to_log(sms)

    finally:
        print(query, datetime.now())
        return result


def table_vacuum_pg(TABLE_NAME):
    result = True
    query = "VACUUM (ANALYZE) " + TABLE_NAME
    try:
        with con_postgres_psycopg2() as conpg:
            old_isolation_level = conpg.isolation_level
            conpg.set_isolation_level(0)
            conpg.cursor().execute(query)
            conpg.set_isolation_level(old_isolation_level)

    except Exception as e:
        result = False
        sms = "table_vacuum_pg: **" % e
        print(sms)
        # # await add_to_log(sms)

    finally:
        print(query, datetime.now())
        return result


def url_count(DOCUMENT):
    return URL_CONST + DOCUMENT + "/$count"


def url_details(DOCUMENT):
    return URL_CONST + DOCUMENT + "/?$top=**&$skip=**&$format=json"


def url_accumulate(DOCUMENT):
    return URL_CONST + DOCUMENT + "/?$top=**&$skip=**&$format=json&$filter=Active eq true"


def url_main(DOCUMENT):
    return url_details(DOCUMENT) + "&$filter=Posted eq true and ОтражатьВУправленческомУчете eq true"


def url_main_select(DOCUMENT):
    return url_main(DOCUMENT) + "&$select=**"


def url_details_select(DOCUMENT):
    return url_details(DOCUMENT) + "&$select=**"


# отправляет файл телеграм бот
def send_file(doc, chat_id=chatid_rasim, sms=''):
    try:
        with open(doc, 'rb') as file:
            post_data = {'chat_id': chat_id, "caption": sms}
            post_file = {'DOCUMENT': file}
            r = requests.post('https://api.telegram.org/bot{token}/sendDocument', data=post_data, files=post_file)
            print(r.text)
    except Exception as e:
        sms = "send_file **" % e
        print(sms)
        # await add_to_log(sms)


# send GET request to database 1C
def get_response(url):
    result = ''
    try:
        response = requests.get(url, auth=DATA_AUTH)
        if (response.status_code < 200) \
                or (response.status_code >= 300):
            sms = "ERROR url: **. **" % (url, response.json()['odata.error']['message']['value'])
            print(sms)
            # await add_to_log(sms)
            send_sms(sms)

        else:
            result = response.json()['value']  # requests.get(url, auth=(c_login, c_psw)).json()['value']

    except Exception as e:
        sms = "ERROR:get_response: **" % e
        print(sms)
        # await add_to_log(sms)
        send_sms(sms)

    finally:
        return result


# connection to access database prestige
def con_access_prestige():
    path_to_db = r"c:\Rasim\VB\Inventarization\Inventarization.accdb"
    try:
        user = "admin"
        psw_acc = "hfvpfc15"
        dns = r'''DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=**;Uid=**;Pwd=**;'''
        return pyodbc.connect(dns % (path_to_db, user, psw_acc))

    except Exception as e:
        sms = "ERROR:con_access_prestige:**, **" % (path_to_db, e)
        print(sms)
        # await add_to_log(sms)
        send_sms(sms)
        return ''


# обработчик, для получения из базы одного значения
def get_result_one_column(str_sql, con, odata=''):
    result = ''
    try:
        with con_postgres_psycopg2() as con:
            with con.cursor() as cur:
                if odata == '':
                    cur.execute(str_sql)
                else:
                    cur.execute(str_sql, odata)
                result = cur.fetchone()
                result = result[0] if result else ''

    except Exception as e:
        sms = "ERROR:get_result_one_column: ** " % e
        print(sms)
        # await add_to_log(sms)

    finally:
        return result


def con_postgres_psycopg2():
    conpg = ''

    try:
        conpg = psycopg2.connect(dbname=basename, user=username,
                                 password=psw, host=hostname_public, port=port)
        conpg.set_client_encoding('UNICODE')

    except Exception as e:
        sms = "ERROR:con_postgres_psycopg2: **" % e
        print(sms)
        # await add_to_log(sms)
        send_sms(sms)
        return ''

    finally:
        return conpg


def connect_to_pg():
    try:
        conn = psycopg2.connect(host=hostname_public, database=basename, user=username, password=psw, port=port)

    except psycopg2.OperationalError as e:
        # await add_to_log(e)
        raise e

    else:
        print('Connected!')
        return conn


# insert, update, delete data in table
def change_data_in_table_bl(str_sql, con='', odata=''):
    """
    :param str_sql:
    :param con:
    :param odata:
    :param comment:
    :return:
    """
    result = True
    try:
        with con_postgres_psycopg2() as con:
            with con.cursor() as cur:

                if odata == '':
                    cur.execute(str_sql)
                else:
                    cur.execute(str_sql, odata)

                con.commit()

    except Exception as e:
        con.rollback()
        sms = str(sys.exc_info()[0])
        if ('23505' not in sms) and ('23505' not in e.pgcode) \
                and ('invalid token' not in sms) \
                and ('ForeignKeyViolation' not in sms) \
                and ('DuplicateTable' not in sms) \
                and ('DuplicateObject' not in sms):
            result = False
            sms = "Ошибка при добавлении/обновлении данных в базу: **; str_sql: **; odata: **" % (e, str_sql, odata)
            # await add_to_log(sms)

    finally:
        return result


def change_data_in_table_returning(str_sql, con, odata='', comment=''):
    result = ''
    try:
        with con:
            with con.cursor() as cur:

                if odata == '':
                    result = cur.execute(str_sql)
                else:
                    result = cur.execute(str_sql, odata)

                con.commit()
    except IntegrityError as err:
        con.rollback()
        sms = traceback.print_exc()
        # await add_to_log(sms)


    except Exception as e:
        result = False
        con.rollback()
        if e.pgcode not in ['23503', '23505', '42P07', '42710']:  # double in Unique
            sms = "**. Ошибка при добавлении/обновлении данных в базу: **; str_sql: **; odata: **" % (
                comment, e, str_sql, odata)
            print(sms)
            # await add_to_log(sms)
            send_sms(sms)

    finally:
        return result


def read_sql_to_dataframe(sql_str, conn=''):
    with con_postgres_psycopg2() as conn:
        sql_query = pd.read_sql_query(sql_str, conn)
        df = pd.DataFrame(sql_query)  # , columns=['product_id', 'product_name', 'price'])
    return df


def sql_to_dataframe(sql_query, odata=''):
    # conname - тип соединения. Результат запроса переводит в pandas DataFrame
    sms = ''
    df = ''
    try:
        with engine.begin() as conn:
            if odata == '':
                df = pd.read_sql(sa.text(sql_query), conn)
            else:
                df = pd.read_sql_query(sa.text(sql_query), conn, params=odata)

    except Exception as e:
        sms = traceback.print_exc()
        print(str(sms))
        sms = "ERROR:ConnectToBase:dfExtract: **" % sms
        # # await add_to_log(sms)


    finally:
        return df


def pg_sqlalchemy_df_tosql(df, tablename):
    try:

        # работать только через APPEND, replace удаляет ВСЕ записи
        # df.to_sql(name=tablename, con=engine, index=False, if_exists='append', schema="public")

        with engine.connect() as conn:
            # работать только через APPEND, replace удаляет ВСЕ записи!!!
            # df.to_sql(name=tablename, con=conn, index=False, if_exists='append', method='multi',schema="public")
            df.to_sql(name=tablename, con=conn, index=False, if_exists='append',
                      schema="public")  # работать только через APPEND, replace удаляет ВСЕ записи

    except Exception as e:
        sms = "pg_sqlalchemy_df_tosql **" % e
        if ('23505' not in sms) and ('gkpj' not in e.code) \
                and ('invalid token' not in sms) \
                and ('ForeignKeyViolation' not in sms) \
                and ('DuplicateTable' not in sms) \
                and ('DuplicateObject' not in sms):
            sms = "Ошибка при добавлении/обновлении df приватБанка в базу: **" % e
            # # await add_to_log(sms)


def pg_sqlalchemy_table_to_df(tablename):
    try:
        df = pd.DataFrame()
        with engine.connect() as conn:
            df = pd.read_sql_table(tablename,
                                   con=conn,
                                   schema=schema
                                   )

    except Exception as e:
        sms = "pg_sqlalchemy_df_tosql **" % e
        print(sms)
        # # await add_to_log(sms)

    finally:
        conn.close()
        return df


# send a request to the server
def send_request(**kwargs):
    try:
        header = ''
        data_json = ''
        if 'url' in kwargs:
            url = kwargs['url']
        else:
            print('ОТСУТСТВУЕТ ОБЯЗАТЕЛЬНЫЙ ПРАМЕТР url')
            sys.exit(0)

        if 'method' in kwargs:
            method = kwargs['method']
        else:
            print('ОТСУТСТВУЕТ ОБЯЗАТЕЛЬНЫЙ ПРАМЕТР method')
            sys.exit(0)

        if method in ['POST', 'PATCH']:

            if 'data_json' not in kwargs and 're_posted' not in kwargs:  # re_posted - перепроведение
                print("для запросов 'POST'/'PATCH' отсутствует обязательный параметр data_json")
                sys.exit(0)

            else:

                data_json = kwargs['data_json']

                header = {'Accept': 'application/json',
                          'Accept-Charset': 'UTF-8',
                          'User-Agent': 'Fiddler',
                          'Content-Type': 'application/json'
                          }

        elif method != 'GET':
            print("поддерживаются только типы запросов 'POST','PATCH','GET'. Ваш запрос: ", method)
            sys.exit(0)

        if method == 'POST':
            if 're_posted' not in kwargs:
                data_json = kwargs['data_json']

            return requests.post(url, headers=header, json=data_json, auth=DATA_AUTH)

        elif method == 'PATCH':
            if 're_posted' not in kwargs:
                data_json = kwargs['data_json']
            return requests.patch(url, headers=header, json=data_json, auth=DATA_AUTH)

        elif method == 'GET':
            response = requests.get(url, auth=DATA_AUTH).json()['value']

            if len(response) > 0:
                response = response[0].get(list(response[0])[0])
            else:
                response = ''

            return response

    except Exception as e:
        sms = "send_request **" % e
        print(sms)
        # # await add_to_log(sms)


def json_to_dataframe(json_data):
    df = pd.json_normalize(json_data)
    return df


# control the count of rows in the json response and the count of rows in the table
def get_result_json_and_table(url, TABLE_NAME):
    """
    :param url: 
    :param TABLE_NAME:
    """
    response = requests.get(url + "/?$format=json"
                                  "&$inlinecount=allpages"
                                  "&$select=**&$top=1"
                                  "&$filter=Posted eq true and ОтражатьВУправленческомУчете eq true",
                            auth=DATA_AUTH)
    if response.status_code == 200:
        count = int(response.json()['odata.count'])
        intable = int(get_result_one_column("SELECT count(*) FROM **" % TABLE_NAME, con_postgres_psycopg2()))
        if count != intable:
            sms = "Count: **: json=**; intable=**" % (TABLE_NAME, count, intable)
            print(sms)


def json_to_sql(df):
    # creating column list for insertion
    cols = ",".join([str(i) for i in df.columns.tolist()])

    # Insert DataFrame recrds one by one.
    for i, row in df.iterrows():
        sql_insert = "INSERT INTO t_one_doc_return_of_goods_from_customers (" + cols + ") VALUES (" + "**," * (
                len(row) - 1) + "**)"
        change_data_in_table_returning(sql_insert, con_postgres_psycopg2(), tuple(row),
                                       'main_doc_return_of_goods_from_customers')


if __name__ == "__main__":
    df = pg_sqlalchemy_table_to_df('t_one_cat_units_classifier')
    print(df)
    # full_vacuum_pg()
