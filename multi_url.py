import grequests
import requests

from prestige_authorize import get_response, URL_CONST, c_login, c_psw

urls = [
    'http://www.heroku.com',
    'http://python-tablib.org',
    'http://httpbin.org',
    'http://python-requests.org',
    'http://fakedomain/',
    'http://kennethreitz.com'
]


DOCUMENT = "Catalog_Номенклатура"
url_count = URL_CONST + DOCUMENT + "/$count"
response = requests.get(url_count, auth=(c_login, c_psw))
if response.status_code == 200:
    count = int(response.text)
    top = 1000
    skip = 0
    url_list = []

    while skip <= count:
        url = URL_CONST + DOCUMENT + "/?$top=%s&$skip=%s" \
                                     "&$format=json" % (top, skip)

        url_list.append(url)
        skip += top

    # print(url_list)
    rs = (grequests.get(u) for u in url_list)
    print(grequests.map(rs))
