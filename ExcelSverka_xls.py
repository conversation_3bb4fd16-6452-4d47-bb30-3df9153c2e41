# из Excel файла извлекает данные по сверке (бухгалтерия) и сохраняет в Excel
import pandas as pd
import re
import os
from datetime import datetime


def process_excel_file(file_path):
    # Имя файла для добавления в колонку
    file_name = "Акт звірки.xls"
    # Проверим существование файла
    if not os.path.exists(file_path):
        print(f"Файл не найден: {file_path}")
        return pd.DataFrame()
    else:
        print(f"Файл найден: {file_path}")

    # Чтение Excel файла без объединения ячеек
    try:
        print("Пытаемся открыть Excel файл...")
        # Чтение файла без объединения ячеек
        df_raw = pd.read_excel(file_path, sheet_name=0, header=None)
        print("Файл успешно загружен")

        # Найдем строку с заголовками (Дата, Документ, Дебет, Кредит)
        header_row = None
        for i, row in df_raw.iterrows():
            # Преобразуем все значения в строки для поиска
            row_str = row.astype(str)
            # Проверяем наличие всех необходимых слов в строке
            has_date = any('дата' in str(val).lower() for val in row_str)
            has_doc = any('документ' in str(val).lower() for val in row_str)
            has_debet = any('дебет' in str(val).lower() for val in row_str)
            has_kredit = any('кредит' in str(val).lower() for val in row_str)

            if has_date and has_doc and has_debet and has_kredit:
                header_row = i
                print(f"Found header row at index {i} with all required columns")
                break

        if header_row is None:
            print("Could not find a row with all required columns")
            return pd.DataFrame()

        # Теперь создадим словарь для переименования колонок
        header_values = df_raw.iloc[header_row]
        column_names = {}

        # Создаем словарь для переименования колонок
        for i, val in enumerate(header_values):
            if pd.isna(val):
                column_names[i] = f'Column_{i}'
            elif 'дата' in str(val).lower():
                column_names[i] = 'Дата'
            elif 'документ' in str(val).lower():
                column_names[i] = 'Документ'
            elif 'дебет' in str(val).lower():
                column_names[i] = 'Дебет'
            elif 'кредит' in str(val).lower():
                column_names[i] = 'Кредит'
            else:
                column_names[i] = str(val)

        # Проверяем, есть ли дублирующиеся имена колонок
        seen_names = set()
        for i, name in list(column_names.items()):
            if name in seen_names:
                column_names[i] = f"{name}_{i}"
            else:
                seen_names.add(name)

        # Читаем данные снова, но теперь с нашими заголовками
        # Используем header_row + 1, чтобы пропустить строку с заголовками
        df = pd.read_excel(file_path, sheet_name=0, header=None, skiprows=header_row+1)

        # Переименовываем колонки
        df.columns = list(column_names.values())

        # Показываем колонки и первые строки данных
        print(f"Columns: {df.columns.tolist()}")
        print("\nFirst 5 rows of data:")
        print(df.head())

        # Function to check if a value could be a date
        def is_date(value):
            # Check if it's already a datetime
            if isinstance(value, (datetime, pd.Timestamp)):
                return True

            # If it's a string, try to parse it as a date
            if isinstance(value, str):
                # Common date patterns in Ukrainian/Russian format
                date_patterns = [
                    r'\d{2}\.\d{2}\.\d{4}',  # DD.MM.YYYY
                    r'\d{2}/\d{2}/\d{4}',  # DD/MM/YYYY
                    r'\d{2}-\d{2}-\d{4}',  # DD-MM-YYYY
                    r'\d{2}\.\d{2}\.\d{2}',  # DD.MM.YY
                    r'\d{2}/\d{2}/\d{2}',  # DD/MM/YY
                    r'\d{2}-\d{2}-\d{2}'   # DD-MM-YY
                ]

                return any(re.match(pattern, value) for pattern in date_patterns)

            return False

        # Найдем колонку с датами
        date_col = None
        for col in df.columns:
            if col == 'Дата':
                date_col = col
                print(f"Found date column: {col}")
                break

        if date_col is None:
            print("Could not find a column with 'Дата'")
            return pd.DataFrame()

        # Функция для проверки, является ли значение датой
        def is_date_format(value):
            if pd.isna(value):
                return False

            # Если это строка, проверяем на соответствие формату даты
            if isinstance(value, str):
                # Проверяем формат DD.MM.YY
                if re.match(r'\d{2}\.\d{2}\.\d{2}', value):
                    return True
                # Проверяем формат DD.MM.YYYY
                if re.match(r'\d{2}\.\d{2}\.\d{4}', value):
                    return True

            return False

        # Фильтруем строки, где в колонке с датами есть значения в формате даты
        date_mask = df[date_col].apply(is_date_format)
        filtered_df = df[date_mask]
        print(f"Количество строк с датами в колонке '{date_col}': {len(filtered_df)}")

        # Найдем колонки Документ, Дебет, Кредит
        document_col = None
        debet_col = None
        kredit_col = None

        for col in df.columns:
            if col == 'Документ':
                document_col = col
            elif col == 'Дебет':
                debet_col = col
            elif col == 'Кредит':
                kredit_col = col

        # Выбираем только нужные колонки
        columns_to_keep = []
        if date_col:
            columns_to_keep.append(date_col)
        if document_col:
            columns_to_keep.append(document_col)
        if debet_col:
            columns_to_keep.append(debet_col)
        if kredit_col:
            columns_to_keep.append(kredit_col)

        # Создаем новый DataFrame только с нужными колонками
        result_df = filtered_df[columns_to_keep].copy()
        print(f"Выбранные колонки: {columns_to_keep}")

        # Добавляем колонку "Номер"
        result_df['Номер'] = ''

        # Извлекаем номер документа из колонки "Документ"
        if document_col in result_df.columns:
            # Функция для извлечения номера документа
            def extract_document_number(doc_text):
                if pd.isna(doc_text) or not isinstance(doc_text, str):
                    return ''

                # Ищем номер в скобках вида (№123 от 01.01.2020) или (123 від 01.01.2020)
                match = re.search(r'\((?:№)?([\d-]+)\s+(?:от|від)\s+[\d\.]+\)', doc_text)
                if match:
                    return match.group(1)

                # Ищем номер в формате №123
                match = re.search(r'№([\d-]+)', doc_text)
                if match:
                    return match.group(1)

                # Ищем любые цифры в тексте
                match = re.search(r'\d+', doc_text)
                if match:
                    return match.group(0)

                return ''

            # Применяем функцию к каждой строке
            result_df['Номер'] = result_df[document_col].apply(extract_document_number)
            print("Добавлена колонка 'Номер' с извлеченными номерами документов")

        # Преобразуем форматы данных
        # Преобразуем Дату в формат datetime
        if date_col in result_df.columns:
            # Сначала преобразуем в строку, чтобы избежать ошибок
            result_df[date_col] = result_df[date_col].astype(str)
            # Преобразуем в дату
            result_df[date_col] = pd.to_datetime(result_df[date_col], format='%d.%m.%y', errors='coerce')
            print(f"Колонка '{date_col}' преобразована в формат даты")

        # Преобразуем Дебет и Кредит в float с значением по умолчанию 0
        if debet_col in result_df.columns:
            result_df[debet_col] = pd.to_numeric(result_df[debet_col], errors='coerce').fillna(0)
            print(f"Колонка '{debet_col}' преобразована в числовой формат со значением по умолчанию 0")

        if kredit_col in result_df.columns:
            result_df[kredit_col] = pd.to_numeric(result_df[kredit_col], errors='coerce').fillna(0)
            print(f"Колонка '{kredit_col}' преобразована в числовой формат со значением по умолчанию 0")

        if not result_df.empty:
            print("Первые несколько строк отфильтрованных данных:")
            print(result_df.head())

            # Добавляем колонку с именем файла
            result_df['Файл'] = file_name
            return result_df
        else:
            print("Не найдено строк с датами")
            return pd.DataFrame()

    except Exception as e:
        print(f"Ошибка при обработке файла: {e}")

        # Альтернативный подход для проблемных файлов
        try:
            # Пробуем читать с помощью другого движка
            df = pd.read_excel(file_path, engine='xlrd')

            # Остальная обработка остается похожей...
            # Найти строки с 'Дебет' и 'Кредит'
            # Это упрощенный код для демонстрации
            headers_row = None
            for i in range(len(df)):
                row_values = df.iloc[i].astype(str).values
                if any('дебет' in str(val).lower() for val in row_values) and \
                        any('кредит' in str(val).lower() for val in row_values):
                    headers_row = i
                    break

            if headers_row is not None:
                # Extract data after headers
                data_df = pd.read_excel(file_path, header=headers_row, engine='xlrd')

                # Filter date rows and add filename
                # Simplified version
                first_col = data_df.columns[0]
                result_df = data_df[data_df[first_col].apply(is_date)]
                result_df['Файл'] = file_name
                return result_df

        except Exception as e:
            print(f"Альтернативный подход не удался: {e}")

    return pd.DataFrame()  # Возвращаем пустой DataFrame если обработка не удалась


# Использование
# file_path = r"c:\Users\<USER>\Desktop\Акт звірки.xls"   # Путь к Excel файлу
file_path = r"c:\Users\<USER>\Desktop\4 Акт звірки взаємних розрахунків № ЦБ-3413 від 05.04.2024.xlsx"    # Путь к Excel файлу
df = process_excel_file(file_path)

# Отображение результата
print(df)

# Сохранение в Excel
if not df.empty:
    from datetime import datetime
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"extracted_sverka_data_{current_time}.xlsx"
    try:
        df.to_excel(output_file, index=False)
        print(f"\nДанные сохранены в файл {output_file}")
    except PermissionError:
        print(f"\nОшибка: Нет доступа к файлу {output_file}. Возможно, он открыт в другой программе.")
    except Exception as e:
        print(f"\nОшибка при сохранении файла: {e}")
