
CREATE OR REPLACE VIEW v_one_correction
AS SELECT
    goods.id, 
    serv.doc_date::date AS doc_date,
    serv.doc_date AS tarih,
    serv.doc_number,
    nom.sku,
    nom.inbox,
    goods.quantity,
    goods.coefficient,
    goods.quantity * goods.coefficient AS ed,
    goods.quantity * goods.coefficient / nom.inbox AS tobox,
    tocw_out.description AS warehouse,
    'корректировка'::text AS doc_type,
    nom.supplier,
    org.description AS organization,
    serv.ref_key AS document_key,
    nom.nomenclature_key,
    goods.nomenclature_series_key,
    goods.nomenclature_series_new_key,
    nom.supplier_key,
    serv.organization_key,
    goods.unit_of_key,
    serv.warehouse_key,
    goods.line_number
   FROM t_one_doc_correction_series serv
     JOIN t_one_doc_correction_series_details goods USING (ref_key)
     LEFT JOIN v_one_nomenclature_inbox_supplier nom ON nom.nomenclature_key::text = goods.nomenclature_key::text
     LEFT JOIN t_one_cat_warehouses tocw_out ON tocw_out.ref_key::text = serv.warehouse_key::text
     LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text;

    COMMENT ON VIEW v_one_correction IS 'корректировка';
    COMMENT ON COLUMN v_one_correction.doc_number IS 'номер док';
    COMMENT ON COLUMN v_one_correction.sku IS 'номенклатура';
    COMMENT ON COLUMN v_one_correction.inbox IS 'в коробке';
    COMMENT ON COLUMN v_one_correction.quantity IS 'количество';
    COMMENT ON COLUMN v_one_correction.coefficient IS 'коэффициент';
    COMMENT ON COLUMN v_one_correction.ed IS 'количество*коэффициент';
    COMMENT ON COLUMN v_one_correction.tobox IS 'количество*коэффициент/в коробке';
    COMMENT ON COLUMN v_one_correction.warehouse IS 'склад';
    COMMENT ON COLUMN v_one_correction.doc_type IS 'тип док';
    COMMENT ON COLUMN v_one_correction.supplier IS 'поставщик товара';
    COMMENT ON COLUMN v_one_correction.organization IS 'организация';

