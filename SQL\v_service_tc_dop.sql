CREATE OR REPLACE VIEW v_service_tc_dop
AS SELECT DISTINCT
    inc.doc_date::date AS doc_date,
    inc.doc_date AS tarih,
    inc.doc_number,
    org.description AS organization,
    clients.description AS client,
    cur.description AS cur_type,
    serv.rate_settlement,
    inc.document_amount,
    inc.amount_vat,
    round(CASE
        WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- USD
            inc.document_amount
        WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- UAH
            inc.document_amount / (SELECT rate_usd_nbu
                                FROM t_rate_nbu
                                WHERE rate_date = serv.serv_date)
        WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- EURO
            (inc.document_amount /
            (SELECT rate_usd_nbu
            FROM t_rate_nbu
            WHERE rate_date = serv.serv_date)) *
            (SELECT rate_euro_nbu
            FROM t_rate_nbu
            WHERE rate_date = serv.serv_date)
    END,3) AS dop_amount_ue,
    round(CASE
        WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- USD
            inc.amount_vat
        WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- UAH
            inc.amount_vat / (SELECT rate_usd_nbu
                                FROM t_rate_nbu
                                WHERE rate_date = serv.serv_date)
        WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text THEN -- EURO
            (inc.amount_vat /
            (SELECT rate_usd_nbu
            FROM t_rate_nbu
            WHERE rate_date = serv.serv_date)) *
            (SELECT rate_euro_nbu
            FROM t_rate_nbu
            WHERE rate_date = serv.serv_date)
    END,3) AS dop_vat_ue,
    round(CASE
            WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = TRUE THEN -- USD
                inc.document_amount
             WHEN cur.ref_key::text = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = FALSE THEN
                inc.document_amount + inc.amount_vat
            WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = TRUE THEN -- UAH
                (inc.document_amount) / (SELECT rate_usd_nbu
                                    FROM t_rate_nbu
                                    WHERE rate_date = serv.serv_date)
            WHEN cur.ref_key::text = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = FALSE THEN -- UAH
                (inc.document_amount + inc.amount_vat) / (SELECT rate_usd_nbu
                                                            FROM t_rate_nbu
                                                            WHERE rate_date = serv.serv_date)
            WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = TRUE THEN -- EURO
                (inc.document_amount /
                (SELECT rate_usd_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)) *
                (SELECT rate_euro_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)
            WHEN cur.ref_key::text = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c'::text and amount_includes_vat = FALSE THEN -- EURO
                ((inc.document_amount + inc.amount_vat) /
                (SELECT rate_usd_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)) *
                (SELECT rate_euro_nbu
                FROM t_rate_nbu
                WHERE rate_date = serv.serv_date)
    END,3) AS dop_sum_ue,
    inc.ref_key,
    goods.batch_document,
    inc.currency_key,
    inc.organization_key
   FROM t_one_doc_incoming_additional_expenses inc
     LEFT JOIN t_one_doc_incoming_additional_expenses_goods goods ON inc.ref_key::text = goods.ref_key::text
     LEFT JOIN t_one_cat_currencies cur ON cur.ref_key::text = inc.currency_key::text
     LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = inc.organization_key::text
     LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = inc.account_key::text
     JOIN v_service_tc serv ON serv.batch_document::text = goods.batch_document::text
   WHERE inc.posted = true AND NOT (clients.ref_key::text IN ( SELECT DISTINCT v_service_tc.account_key
           FROM v_service_tc))
   ORDER BY (inc.doc_date::date) DESC;
