    CREATE OR REPLACE VIEW v_one_tax_return_and_medoc_amount_different AS
    SELECT
        sell.is_accounting БухУчет,
        sell.edrpou ОКПО,
        sell.customer Контрагент,
        sell.tax_doc_date РК_дата,
        sell.tax_doc_number РК_Номер,
        sell.tax_amount РК_Сумма,
        medoc.docsum AS Medoc_Сумма,
        medoc.vatsum AS Medoc_СуммаНДС,
        sell.tax_amount - medoc.docsum РазницаСумм,
        sell.tax_amount_vat - medoc.vatsum РазницаСуммНДС,
        medoc.doc_date Medoc_Дата,
        medoc.docname Medoc_Наименование,
        medoc.sendsttname Medoc_Статус
    FROM
        (
        SELECT *
        FROM (
            SELECT
                reestr.docsum::numeric(15,2) docsum,
                reestr.vatsum::numeric(15,2) vatsum,
                reestr.doc_date,
                reestr.doc_num,
                info.sendsttname,
                info.lastupdate = max(info.lastupdate) OVER (PARTITION BY doc_id) AS last_updated,
                info.docname,
                reestr.partner_edrpou
            FROM medoc_reestr AS reestr
                LEFT JOIN medoc_doc_info AS info
                    ON reestr.doc_id = info.docid
            ) AS t
        WHERE t.last_updated
        ) AS medoc
        INNER JOIN
        (
        SELECT DISTINCT
            organization_type = COALESCE(sale.is_accounting,FALSE) AS is_accounting,
            client.customer,
            tax_sale.doc_date tax_doc_date,
            tax_sale.doc_number tax_doc_number,
            tax_sale.doc_number_short tax_doc_number_short,
            tax_sale.document_amount tax_amount,
            sale.document_amount sale_amount,
            tax_sale.amount_vat_document tax_amount_vat,
            sale.doc_date sale_doc_date,
            sale.doc_number sale_number,
            sale.doc_number_short sale_doc_number_short,
            client.edrpou
        FROM
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,10)::int::text doc_number_short,
                CASE
                    WHEN posted THEN
                        document_amount
                    ELSE
                        0
                END document_amount,
                CASE
                    WHEN posted THEN
                        amount_vat_document
                    ELSE
                        0
                END amount_vat_document,
                CASE
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        document_base_key
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        deal_key
                END base_key,
                CASE
                    WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(document_base_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        document_base_type
                    WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
                        AND COALESCE(deal_key, '********-0000-0000-0000-************') <> '********-0000-0000-0000-************' THEN
                        deal_type
                END base_type,
                account_key,
                organization_key
            FROM t_one_doc_tax_appendix_2
            WHERE doc_date >= '01.01.2022'::date
            ) AS tax_sale
            LEFT JOIN
            (
            SELECT
                doc_date::date AS doc_date,
                doc_number,
                right(doc_number,8)::int::text doc_number_short,
                document_amount::numeric(15,2) AS document_amount,
                ref_key,
                organization_key,
                is_accounting
            FROM t_one_doc_return_of_goods_from_customers
            ) AS sale
            ON tax_sale.base_key = sale.ref_key
            INNER JOIN v_one_manager_counterparty_contracts_segments AS client
                ON tax_sale.account_key = client.customer_key
            INNER JOIN v_one_organization_and_type AS org
                ON org.ref_key = tax_sale.organization_key
        )
        AS sell
        ON 	medoc.doc_num = sell.tax_doc_number_short
            AND medoc.doc_date = sell.tax_doc_date
                AND medoc.partner_edrpou = sell.edrpou
    WHERE  abs(sell.tax_amount - medoc.docsum::numeric(15,2)) > 0.2
    ORDER BY sell.customer, medoc.doc_date DESC
    ;

    COMMENT ON VIEW v_one_tax_return_and_medoc_amount_different IS 'расхождение сумм между РК и medoc';
    GRANT SELECT ON TABLE  v_one_tax_return_and_medoc_amount_different TO user_prestige;
