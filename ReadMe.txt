
# обновить все пакеты в виртуальном окружении. Перед запуском скрипта необходимо активировать виртуальное окружение
# OS: Windows
.venv\Scripts\python.exe -m pip list --outdated | Select-String -Pattern "(\S+)" -AllMatches | ForEach-Object { $_.Matches.Groups[1].Value } | ForEach-Object { .venv\Scripts\python.exe -m pip install -U $_ }

# Обновить requirements.txt
D:\Prestige\Python\Prestige\.venv\Scripts\python.exe -m pip freeze > requirements.txt