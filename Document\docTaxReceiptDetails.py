import asyncio
import os
import sys

sys.path.append(r'D:\Prestige\Python\Prestige')
from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_tax_receipt_details"
DOCUMENT = "Document_РегистрацияВходящегоНалоговогоДокумента_Товары"
SELECT_COLUMNS = "Ref_Key,LineNumber,НаименованиеТовара,Количество,Цена,Сумма,СтавкаНДС,СуммаВзаиморасчетов,СуммаНДС"

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        ref_key varchar(50) NULL,  -- Ref_Key
        line_number numeric(6) NOT NULL DEFAULT 0,  -- LineNumber
        name_of_product varchar(250) NULL,  -- НаименованиеТовара
        quantity numeric(15,3) NOT NULL DEFAULT 0,  -- Количество
        price numeric(10,3) NOT NULL DEFAULT 0,  -- Цена
        total numeric(15,3) NOT NULL DEFAULT 0,  -- Сумма
        vat_rate varchar(25) NULL,  -- СтавкаНДС
        amount_of_settlements numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаВзаиморасчетов
        amount_vat numeric(15,3) NOT NULL DEFAULT 0,  -- СуммаНДС
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS ' LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.name_of_product IS ' НаименованиеТовара';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS ' Количество';
    COMMENT ON COLUMN {TABLE_NAME}.price IS ' Цена';
    COMMENT ON COLUMN {TABLE_NAME}.total IS ' Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.vat_rate IS ' СтавкаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.amount_of_settlements IS ' СуммаВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS ' СуммаНДС';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        line_number,
        name_of_product,
        quantity,
        price,
        total,
        vat_rate,
        amount_of_settlements,
        amount_vat
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        name_of_product = EXCLUDED.name_of_product,
        quantity = EXCLUDED.quantity,
        price = EXCLUDED.price,
        total = EXCLUDED.total,
        vat_rate = EXCLUDED.vat_rate,
        amount_of_settlements = EXCLUDED.amount_of_settlements,
        amount_vat = EXCLUDED.amount_vat
    ;
    """
    return sql.replace("'", "")


async def main_doc_tax_receipt_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_tax_receipt_details_async())
