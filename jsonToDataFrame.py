from pandas import json_normalize

from prestige_authorize import get_response, URL_CONST

url = URL_CONST + "Document_ПоступлениеТоваровУслуг/?$format=json" \
                  "&$filter=Ref_Key eq guid'f6e7671b-4bba-11ed-8148-001dd8b72b55'"


def main_jsonToDataFrame():
    response = get_response(url)
    df = json_normalize(response[0]['Товары'])
    print(df.columns)
    print(df)


if __name__ == '__main__':
    main_jsonToDataFrame()
