import os

import psutil


def get_cpu_cores():
    return os.cpu_count()


def get_cpu_info():
    cpu_info = psutil.cpu_times()
    return cpu_info


def get_disk_info():
    disk_info = psutil.disk_usage('/')
    return disk_info


def get_ram_info():
    ram_info = psutil.virtual_memory()
    return ram_info


print(f"get_disk_info: {get_disk_info()}")
print(f"get_ram_info: {get_ram_info()}")
print(f"get_cpu_info: {get_cpu_info()}")
print(f"get_cpu_cores: {get_cpu_cores()}")
