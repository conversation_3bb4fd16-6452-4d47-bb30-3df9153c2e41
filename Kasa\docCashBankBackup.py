import asyncio
import os
import sys
from pathlib import Path

cur_dir = Path(__file__).resolve().parent
sys.path.append(str(cur_dir))
sys.path.append(str(cur_dir.parent))
from Kasa.cashConfig import COMPANY_CASHIER
from async_Postgres import async_save_pg
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_cash_bank_backup"
DOCUMENT = "банк access"
SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id uuid DEFAULT uuid_generate_v4() NOT NULL,
        doc_date timestamp NOT NULL,  -- ДатаДок
        doc_number varchar(20) NOT NULL,  -- Номер
        amount numeric(15, 2) NOT NULL,  -- сумма
        amount_total numeric(15, 2) DEFAULT 0 NOT NULL,  -- сумма с накоплением
        payment_purpose_id uuid NULL,  -- назначение платежа
        item_id uuid NULL,  -- статья
        organization_key varchar(36) NOT NULL,  -- организация
        currency_key varchar(36) NOT NULL,  -- валюта
        customer_key varchar(36) NULL,  -- клиент
        employee_key uuid NULL,  -- сотрудник
        description varchar NULL,  -- примечание
        document_type varchar(25) NULL,  -- тип документа
        create_user varchar(30) DEFAULT CURRENT_USER NOT NULL,
        update_user varchar(30) NULL,
        recorder_type numeric(3) DEFAULT '-1'::integer NULL,  -- 1- приход; '-1' - расход
        createdat timestamp DEFAULT now() NOT NULL,
        item_period date NULL,  -- период
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
        CONSTRAINT {TABLE_NAME}_number_unique UNIQUE (id, doc_number) -- добавлено уникальное ограничение
    );
    
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'ДатаДок';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_total IS 'суммаИтоговая';
    COMMENT ON COLUMN {TABLE_NAME}.payment_purpose_id IS 'назначение платежа';
    COMMENT ON COLUMN {TABLE_NAME}.item_id IS 'статья';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'организация';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'валюта';
    COMMENT ON COLUMN {TABLE_NAME}.customer_key IS 'клиент';
    COMMENT ON COLUMN {TABLE_NAME}.employee_key IS 'сотрудник';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'примечание';
    COMMENT ON COLUMN {TABLE_NAME}.document_type IS 'тип документа';
    COMMENT ON COLUMN {TABLE_NAME}.recorder_type IS '1- приход; -1 - расход';
    COMMENT ON COLUMN {TABLE_NAME}.item_period IS 'период';

    ALTER TABLE {TABLE_NAME} OWNER TO postgres;
    GRANT ALL ON TABLE {TABLE_NAME} TO postgres;
    GRANT ALL ON TABLE {TABLE_NAME} TO {COMPANY_CASHIER};
"""

SQL_CREATE_FN = f"""
CREATE OR REPLACE FUNCTION fn_{TABLE_NAME}()
RETURNS void AS $$
BEGIN
    INSERT INTO {TABLE_NAME} (
        id, doc_date, doc_number, amount, amount_total, payment_purpose_id, item_id, 
        organization_key, currency_key, customer_key, employee_key, description, 
        document_type, create_user, update_user, recorder_type, createdat, item_period
    )
    SELECT 
        id, doc_date, doc_number, amount, amount_total, payment_purpose_id, item_id, 
        organization_key, currency_key, customer_key, employee_key, description, 
        document_type, create_user, update_user, recorder_type, createdat, item_period
    FROM t_cash_bank
    ORDER BY createdat desc
    ON CONFLICT (id, doc_number) 
    DO UPDATE SET 
        doc_date = EXCLUDED.doc_date,
        doc_number = EXCLUDED.doc_number,
        amount = EXCLUDED.amount,
        amount_total = EXCLUDED.amount_total,
        payment_purpose_id = EXCLUDED.payment_purpose_id,
        item_id = EXCLUDED.item_id,
        organization_key = EXCLUDED.organization_key,
        currency_key = EXCLUDED.currency_key,
        customer_key = EXCLUDED.customer_key,
        employee_key = EXCLUDED.employee_key,
        description = EXCLUDED.description,
        document_type = EXCLUDED.document_type,
        create_user = EXCLUDED.create_user,
        update_user = EXCLUDED.update_user,
        recorder_type = EXCLUDED.recorder_type,
        createdat = EXCLUDED.createdat,
        item_period = EXCLUDED.item_period
    ;
END;
$$ LANGUAGE plpgsql;
"""

SQL_RUN_FUNCTION = f"""
    SELECT fn_{TABLE_NAME}();
"""


async def main_doc_cash_bank_backup_async():
    logger.info(f"START")
    result = await async_save_pg(SQL_CREATE_TABLE)  # создавать таблицу триггера
    logger.info(f"{result}, SQL_CREATE_TABLE")
    result = await async_save_pg(SQL_CREATE_FN)  # создавать таблицу до триггера
    logger.info(f"{result}, SQL_CREATE_FN")
    result = await async_save_pg(SQL_RUN_FUNCTION)  # создавать таблицу до триггера
    logger.info(f"{result}, SQL_RUN_FUNCTION")

    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_doc_cash_bank_backup_async())
