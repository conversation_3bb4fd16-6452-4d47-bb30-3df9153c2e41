# Description: Сохраняет данные о задолженности в Excel.
# отбираются все не оплаченные документы
# без колонки датаСрока
# Исправлены уровни группировки: сегмент (0), клиент (1), организация (2), контракт (3), детализация (4)

import os
import sys
import numpy as np
from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catCounterparties import main_cat_counterparties_async
from CreateAndRunSQLScripts import create_views
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async
# для отлавливания номера строки ошибки
import traceback
sys.path.append(os.path.dirname(__file__))
import asyncio
import aiohttp
import aiofiles
import json
from aiohttp import FormData
from datetime import datetime, date
from dateutil.parser import parse
from pathvalidate import sanitize_filename
from decimal import Decimal
import pandas as pd
import xlsxwriter
from dateutil import parser
from time import strftime
from LoadManagerBonusToExcel import managers_chatid_name, managers_name_uuid
from logger_prestige import get_logger
from async_Postgres import (
    sql_to_dataframe_async,
    send_telegram,
    TELEGRAM_TOKEN,
    chatid_rasim,
)

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
DATE_FIRST = parser.parse("01.04.2025", dayfirst=True).date()
SAVE_TO_FILE = "Config.xlsx"

# Установите опцию отображения максимального количества столбцов
pd.set_option('display.max_columns', None)


def head_format(wb):
    return wb.add_format({"bg_color": "#778899", "bold": True, "align": "center"})


def segment_format(wb):
    return wb.add_format(
        {
            "bg_color": "#808080",  # color - gray
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 14,
        }
    )


def customer_format(wb):
    return wb.add_format(
        {
            "bg_color": "#C0C0C0",  # color - silver
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 13,
        }
    )


def organization_format(wb):
    return wb.add_format(
        {
            "bg_color": "#f2ebf1",  # color - lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 11,
        }
    )


def contract_format(wb):
    return wb.add_format(
        {
            "bg_color": "#e6e6fa",  # color - light lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 10,
        }
    )


def date_format(wb):
    return wb.add_format({"num_format": "dd.mm.yyyy"})


def number_format(wb):
    return wb.add_format({"num_format": "# ### ##0.00;[Red]# ### ##0.00"})


def int_format(wb):
    return wb.add_format({"num_format": "# ### ##0;[Red]# ### ##0"})


def date_to_str(period):
    return datetime.strftime(period, "%Y%m%d")


# отправка файла в телеграм
async def telegram_bot_send_document(path_doc, chat_id):
    chat_id = str(chat_id)
    logger.info(f"файл для отправки: {path_doc}")
    async with aiofiles.open(path_doc, "rb") as doc:
        data = FormData()
        data.add_field("chat_id", chat_id)
        data.add_field("document", doc, filename=path_doc)

        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendDocument"
            async with session.post(url, data=data) as response:
                logger.info(response.status)
                content = await response.text()
                result = json.loads(content)
                filename = {result.get("result").get("document").get("file_name")}
                logger.info(f"filename: {filename}")
                await send_telegram(f"Вам отправлен файл: {filename}", chat_id)

    if chat_id != str(chatid_rasim):
        await telegram_bot_send_document(os.path.abspath(path_doc), str(chatid_rasim))

    return response.status


def cell_address(row, col):
    # Преобразуем номер колонки в букву
    column_letter = ""
    while col >= 0:
        column_letter = chr(col % 26 + ord("A")) + column_letter
        col = col // 26 - 1
    # Возвращаем адрес ячейки в формате Excel
    return f"{column_letter}{row + 1}"


def set_column_width_cm(worksheet, col_num, width_in_cm):
    cm_to_pixels = 3.78
    width_in_pixels = width_in_cm * cm_to_pixels
    worksheet.set_column(col_num, col_num, width_in_pixels)


async def colored_range(ws, row, cell_format):
    # Закрашиваем диапазон ячеек
    for col in range(6):
        cell = cell_address(row, col)
        ws.write_blank(cell, None, cell_format)


async def get_sql(date_first: date = None):
    return f"""
        WITH 
        -- документы перемещения денежных средств. в отчет не выводим, чтобы не увеличивал сумму по оплатам
        -- не используем, т.к. 
        -- сумма указывается в колонке оплата как приход по одному договор и расход по другому договору, итого = 0 
        except_doc_move
        AS (  
            SELECT 
                doc.ref_key
            FROM(
                SELECT 
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_order_receipt main  -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                    INNER JOIN t_one_doc_cash_order_receipt_details AS sub
                    ON main.ref_key = sub.ref_key
                WHERE 
                    '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
                UNION ALL
                SELECT 
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_order_expense main  -- Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
                    INNER JOIN t_one_doc_cash_order_expense_details sub
                    ON main.ref_key = sub.ref_key
                WHERE 
                    '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
                UNION ALL
                SELECT 
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_warrant_receipt main  -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                    INNER JOIN t_one_doc_cash_warrant_receipt_details sub
                        ON main.ref_key = sub.ref_key
                WHERE 
                    '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
                UNION ALL
                SELECT 
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_warrant_expense main
                    INNER JOIN t_one_doc_cash_warrant_expense_details  sub -- Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
                        ON main.ref_key = sub.ref_key
                WHERE 
                    '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
            ) doc
            INNER JOIN v_one_manager_counterparty_contracts_segments s
                ON doc.contract_key = s.contract_key
        ),
        register as(
            SELECT
                dtperiod,
                dtperiod::date AS doc_date,
                doc_number,
                CASE 
                    WHEN recordtype = 'Receipt' THEN 
                        sum(amount)
                    ELSE
                        -sum(amount)
                END doc_sum,
               CASE
                    -- продажа
                    WHEN req.recorder_type NOT IN (
                            'StandardODATA.Document_ПлатежноеПоручениеВходящее', 
                            'StandardODATA.Document_ПлатежноеПоручениеИсходящее', 
                            'StandardODATA.Document_ПриходныйКассовыйОрдер', 
                            'StandardODATA.Document_РасходныйКассовыйОрдер', 
                            'StandardODATA.Document_КорректировкаДолга') THEN
                        sum(req.amount)
                    ELSE
                     0
               END sale,
               CASE
                    -- оплата
                    WHEN req.recorder_type IN (
                            'StandardODATA.Document_ПлатежноеПоручениеВходящее', 
                            'StandardODATA.Document_ПлатежноеПоручениеИсходящее', 
                            'StandardODATA.Document_ПриходныйКассовыйОрдер', 
                            'StandardODATA.Document_РасходныйКассовыйОрдер', 
                            'StandardODATA.Document_КорректировкаДолга') THEN
                        -sum(req.amount)
                    ELSE
                     0
               END pay,
               REPLACE(recorder_type, 'StandardODATA.Document_', '') recorder_type,
               contract_key,
               organization_key,
               counterparty_key,
               currency_key
            FROM (
                SELECT 
                    *
                FROM t_one_doc_acc_reg_reciprocal_settlements_details
                WHERE organization_key NOT IN (
                    '92a5f889-1eef-11ed-8143-001dd8b72b55',  -- ТОВ "А-НОВА"
                    'b8a54612-9351-11ed-815e-001dd8b72b55',  -- ТОВ "ГК ПРЕСТИЖ"
                    '936f461a-48de-11eb-80fd-001dd8b72b55',  -- ТОВ "КЕНТОН УКРАИНА"
                    '3d7e2ad1-8ac8-11e6-80c4-c936aa9c817c'  -- ТОВ "ПРЕСТИЖ ПРОДУКТ-К"
                    )
                ) AS req  -- AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
            WHERE recorder NOT IN  -- NOT IN - документы которые не извлекать
                (
                    SELECT
                        main.ref_key
                    FROM t_one_doc_debt_correction AS main
                        INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                            ON main.ref_key = sub.ref_key
                    WHERE main.posted
                        -- берем только УправленческийУчет (NOT is_management, т.к. WHERE recorder NOT IN)
                        AND NOT main.is_management  
                        OR main.operation_type = 'ПроведениеВзаимозачета'
                    GROUP BY
                        main.ref_key,
                        sub.counterparty_contract_key
                    HAVING
                        sum(CASE
                            WHEN sub.debt_type = 'Дебиторская' THEN
                                sub.amount
                            ELSE
                                -sub.amount
                        END) = 0
                    -- UNION ALL
                    -- документы перемещения денежных средств. в отчет не выводим, чтобы не увеличивал сумму по оплатам
                    -- не используем, т.к. 
                    -- сумма указывается в колонке оплата как приход по одному договор и расход по другому договору, итого = 0 
                    -- SELECT ref_key 
                    -- FROM except_doc_move
                    )
            GROUP BY 
                dtperiod,
                doc_number,
                recordtype,
                recorder_type,
                contract_key,
                organization_key,
                counterparty_key,
                currency_key
            HAVING 
                abs(CASE 
                    WHEN recordtype = 'Receipt' THEN 
                        sum(amount)
                    ELSE
                        -sum(amount)
                END) >= 1
        ),
        segment AS (
            SELECT 
                COALESCE(trim(segment),'') segment,
                COALESCE(trim(manager),'') manager,
                COALESCE(trim(customer),'') customer,
                COALESCE(trim(contract_name),'') contract_name,
                COALESCE(trim(currency_name),'') currency_name,
                contract_days,
                customer_key,
                contract_key,
                currency_key,
                manager_key
            FROM v_one_manager_counterparty_contracts_segments
        ),
        organization AS (
            SELECT
                organization,
                ref_key
            FROM v_one_organization_and_type
        ),
        add_name AS (
            SELECT
                ROW_NUMBER() OVER (ORDER BY 
                        seg.segment, 
                        seg.manager, 
                        seg.customer, 
                        org.organization, 
                        seg.currency_name,
                        seg.contract_name, 
                        req.dtperiod, 
                        req.doc_number
                ) AS row_no,
                sum(doc_sum) OVER (PARTITION BY 
                            seg.manager, 
                            seg.segment,
                            seg.currency_name,
                            seg.customer, 
                            org.organization, 
                            seg.contract_name
                        ORDER BY 
                            req.dtperiod, 
                            req.doc_number
                    )
                AS customer_sum,
                *
            FROM register AS req
                LEFT JOIN segment AS seg
                    ON req.contract_key = seg.contract_key
                LEFT JOIN organization AS org
                    ON org.ref_key = req.organization_key
        ),
        balance_first AS (
            SELECT 
                0 sort,
                row_no = max(row_no) OVER (ORDER BY 
                        segment, 
                        manager, 
                        customer, 
                        organization, 
                        currency_name,
                        contract_name
                ) AS is_max_row,
                row_no, segment, currency_name, manager, customer,dtperiod, doc_date, doc_number, 
                NULL recorder_type, sale, pay, customer_sum, organization, contract_name, contract_days, manager_key 
            FROM add_name 
            WHERE 
                manager_key = $1::text
                AND doc_date < $2::date
        ),
        balance_middle AS (
            SELECT 
                1 sort,
                row_no = max(row_no) OVER (ORDER BY 
                        segment, 
                        manager, 
                        customer, 
                        organization, 
                        currency_name,
                        contract_name
                ) AS is_max_row,
                row_no, segment, currency_name, manager, customer,dtperiod, doc_date, doc_number, 
                recorder_type, sale, pay, customer_sum, organization, contract_name, contract_days, manager_key 
            FROM add_name 
            WHERE 
                manager_key = $1::text
                AND doc_date >= $2::date
        ),
        balance AS (
            SELECT
                sort, row_no, is_max_row, segment, currency_name, manager, customer,   
                doc_date, 'остаток' doc_number, 0 sale, 0 pay, customer_sum, recorder_type,
                organization, contract_name, manager_key 
            FROM balance_first
            WHERE is_max_row AND abs(customer_sum) > 1
            UNION ALL 
            SELECT 
                sort, row_no, is_max_row, segment, currency_name, manager, customer,   
                doc_date, doc_number, sale, pay, customer_sum, recorder_type,
                organization, contract_name, manager_key 
            FROM balance_middle
        )
        SELECT 
            CASE 
                WHEN is_max_row AND sort = max(sort)
                    OVER (PARTITION BY
                        segment, 
                        manager, 
                        customer, 
                        organization, 
                        currency_name,
                        contract_name)
                    THEN 
                    customer_sum
                ELSE 
                    0
            END last_customer_sum,            
            *
        FROM balance
        ORDER BY 
            sort, row_no 
"""


# получение данных из представления v_one_customer_balance_no_date
# и запись их в DataFrame
async def get_data_from_view(manager_key: str, date_first: date):
    try:
        sql = await get_sql(date_first)
        args = [manager_key, date_first]
        args = [arg for arg in args if arg is not None]
        df = await sql_to_dataframe_async(sql, *args)
        if df.empty:
            logger.info(f"Данные не найдены: {args}")
            return df

        # Преобразование колонок в нужные форматы
        df["doc_date"] = pd.to_datetime(df["doc_date"], errors="coerce")  # дата документа
        df["customer_sum"] = df["customer_sum"].astype(float)  # сумма долга по клиенту
        df["sale"] = df["sale"].astype(float)  # поставка/возврат
        df["pay"] = df["pay"].astype(float)  # оплата
        df = df.fillna("")
        return df
    except Exception as e:
        logger.error(f"Error in get_data_from_view function: {e}\n{traceback.format_exc()}")
        return pd.DataFrame()


async def get_sum_form_df(df, column_name, filter_column=None, filter_value=None):
    """
    Parameters:
    df - DataFrame,
    column_name - название столбца/ов для группировки.
        По нему будет считаться сумма. Может быть несколько столбцов(список),
    filter_column - название столбца для фильтрации,
    filter_value - значение для фильтрации
    """
    df.loc[:, column_name] = df[column_name].fillna("")  # Заполняем пустые значения в column_name пустой строкой
    if filter_column:
        df = df[
            df[filter_column] == filter_value
            ].copy()  # Используем .copy() для создания копии DataFrame

    # Группировка и суммирование
    group_df = df[df['is_max_row']== True].groupby([*column_name])["customer_sum"].sum().reset_index()
    return group_df


# сумма по статьям до текущей даты
async def get_data_before(df, columns_by_group):
    df_sum = await get_sum_form_df(df, columns_by_group)
    return df_sum


# сумма по статьям между датами
async def get_data_between(df, columns_by_group):
    # Разделяем данные на продажи и оплаты
    df_sale = df[df["sale"] != 0].copy()
    segment_df_sale = await get_sum_form_df(df_sale, columns_by_group)
    segment_df_sale.rename(columns={"customer_sum": "sale"}, inplace=True)

    df_pay = df[df["pay"] != 0].copy()
    segment_df_pay = await get_sum_form_df(df_pay, columns_by_group)
    segment_df_pay.rename(columns={"customer_sum": "pay"}, inplace=True)

    merged_df = segment_df_sale.merge(segment_df_pay, on=[*columns_by_group], how="outer")
    # Удаление дублирующихся колонок
    merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]
    return merged_df


async def add_head_to_excel(wb, sheet):
    # Создаём формат с переносом текста
    sheet.set_row(0, 40)  # установка высоты строки
    combined_format = head_format(wb)
    combined_format.set_text_wrap()
    sheet.write("A1", "сегмент/клиент/организация", head_format(wb))
    sheet.write("B1", "договор/валюта", head_format(wb))
    sheet.write("C1", "датаДок", head_format(wb))
    sheet.write("D1", "продажа/возврат", head_format(wb))
    sheet.write("E1", "оплата", head_format(wb))
    sheet.write("F1", "ОбщийДолг", head_format(wb))


async def group_and_sum(df, columns:list, filter_value:bool = False):
    # Фильтруем строки для customer_sum, но не для sale и pay
    filtered_df = df.copy()

    # Для customer_sum используем только строки с is_max_row == True
    customer_sum_df = df.groupby(columns).agg({'last_customer_sum': 'sum'})
    customer_sum_df.rename(columns={"last_customer_sum": "customer_sum"}, inplace=True)

    # Для sale и pay используем все строки
    sale_pay_df = filtered_df.groupby(columns).agg({
        'sale': 'sum',
        'pay': 'sum'
    })

    # Объединяем результаты
    result = pd.merge(customer_sum_df, sale_pay_df, left_index=True, right_index=True)
    result = result.reset_index()
    return result

async def create_excel(df):
    try:
        # Создаем новый Excel файл и добавляем лист
        wb = xlsxwriter.Workbook(SAVE_TO_FILE, {'nan_inf_to_errors': True})
        sheet = wb.add_worksheet()
        sheet.set_column("H:H", 15)

        # создаем шапку
        await add_head_to_excel(wb, sheet)
        columns = ['segment', 'currency_name']
        segment_df = await group_and_sum(df, columns, True)
        segment_df = segment_df.sort_values(by=['segment'])
        row_number = 2  # Начинаем с 2 строки, т.к. 1 строка - наименование столбцов
        for index, row in segment_df.iterrows():
            segment = row.get("segment")
            currency = row.get("currency_name")
            sheet.write(f"A{row_number}", segment, segment_format(wb))
            sheet.write(f"B{row_number}", currency, segment_format(wb))
            sheet.write_blank(f"C{row_number}", None, segment_format(wb))
            sheet.write(f"D{row_number}", row.get("sale"), segment_format(wb))
            sheet.write(f"E{row_number}", row.get("pay"), segment_format(wb))
            sheet.write(f"F{row_number}", row.get("customer_sum"), segment_format(wb))

            row_number += 1  # уровень сегмента
            columns = ['segment', 'currency_name', 'customer']
            customer_df = await group_and_sum(df, columns)
            filter_customer_df = customer_df[(customer_df['segment'] == segment)
                                             & (customer_df['currency_name'] == currency)]
            for ind1, row1 in filter_customer_df.iterrows():
                customer = row1.get("customer")
                sheet.write(f"A{row_number}", customer, customer_format(wb))
                sheet.write_blank(f"B{row_number}", currency, customer_format(wb))
                sheet.write_blank(f"C{row_number}", None, customer_format(wb))
                sheet.write(f"D{row_number}", row1.get("sale"), customer_format(wb))
                sheet.write(f"E{row_number}", row1.get("pay"), customer_format(wb))
                sheet.write(f"F{row_number}", row1.get("customer_sum"), customer_format(wb))

                # Устанавливаем уровень группировки для клиента.
                # "collapsed": False - не сворачивать группу по умолчанию
                sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": False})
                row_number += 1  # уровень клиента

                columns = ['segment', 'currency_name', 'customer', 'organization']
                organization_df =  await group_and_sum(df, columns)
                filter_organization_df = organization_df[(organization_df['segment'] == segment)
                                                    & (organization_df['currency_name'] == currency)
                                                    & (organization_df['customer'] == customer)
                ]
                for ind2, row2 in filter_organization_df.iterrows():
                    organization = row2.get("organization")
                    sheet.write(f"A{row_number}", organization, organization_format(wb))
                    sheet.write(f"B{row_number}", currency, organization_format(wb))
                    sheet.write_blank(f"C{row_number}", None, organization_format(wb))
                    sheet.write(f"D{row_number}", row2.get("sale"), organization_format(wb))
                    sheet.write(f"E{row_number}", row2.get("pay"), organization_format(wb))
                    sheet.write(f"F{row_number}", row2.get("customer_sum"), organization_format(wb))

                    # Устанавливаем уровни группировки для организации
                    sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": False})
                    sheet.set_row(row_number - 1, None, None, {"level": 2, "collapsed": False})
                    row_number += 1  # уровень организации

                    columns = ['segment', 'currency_name', 'customer', 'organization', 'contract_name']
                    contract_df =  await group_and_sum(df, columns)
                    filter_contract_df = contract_df[(contract_df['segment'] == segment)
                                                             & (contract_df['currency_name'] == currency)
                                                             & (contract_df['customer'] == customer)
                                                             & (contract_df['organization'] == organization)
                                                             ]
                    for ind3, row3 in filter_contract_df.iterrows():
                        contract = row3.get("contract_name")
                        contract_display = f"{contract} / {currency}"
                        sheet.write(f"A{row_number}", None, contract_format(wb))
                        sheet.write(f"B{row_number}", contract_display, contract_format(wb))
                        sheet.write_blank(f"C{row_number}", None, contract_format(wb))
                        sheet.write(f"D{row_number}", row3.get("sale"), contract_format(wb))
                        sheet.write(f"E{row_number}", row3.get("pay"), contract_format(wb))
                        sheet.write(f"F{row_number}", row3.get("customer_sum"), contract_format(wb))

                        # Устанавливаем уровни группировки для организации
                        sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": False})  # сегмент
                        sheet.set_row(row_number - 1, None, None, {"level": 2, "collapsed": False})  # клиент
                        sheet.set_row(row_number - 1, None, None, {"level": 3, "collapsed": False})  # организация

                        row_number += 1  # уровень контракта

                        # Устанавливаем уровень группировки для контракта (свернут по умолчанию)
                        sheet.set_row(row_number - 1, None, None, {"level": 3, "collapsed": True})
                        filter_details_df = df[(df['segment'] == segment)
                                                         & (df['currency_name'] == currency)
                                                         & (df['customer'] == customer)
                                                         & (df['organization'] == organization)
                                                         & (df['contract_name'] == contract)
                                                         ]
                        filter_details_df = filter_details_df.sort_values(by=['doc_date', 'doc_number'])

                        # Проверяем, что есть детализация для отображения
                        for ind4, row4 in filter_details_df.iterrows():
                            doc_info = f"{row4.get('recorder_type', '')} {row4.get('doc_number', '')}".strip()
                            sheet.write(f"B{row_number}", doc_info)
                            sheet.write(f"C{row_number}", row4.get("doc_date"), date_format(wb))
                            sheet.write(f"D{row_number}", row4.get("sale"), number_format(wb))
                            sheet.write(f"E{row_number}", row4.get("pay"), number_format(wb))
                            sheet.write(f"F{row_number}", row4.get("customer_sum"), number_format(wb))

                            # Группировка строк для детализации (свернута по умолчанию)
                            # уровень сегмента
                            sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": True})
                            # уровень клиента
                            sheet.set_row(row_number - 1, None, None, {"level": 2, "collapsed": True})
                            # уровень организации
                            sheet.set_row(row_number - 1, None, None, {"level": 3, "collapsed": True})
                            # уровень контракта
                            sheet.set_row(row_number - 1, None, None, {"level": 4, "collapsed": True})
                            row_number += 1  # уровень детализации

            row_number += 1  # Пустая строка между сегментами

            # Устанавливаем уровень группировки для сегмента (верхний уровень)
            sheet.set_row(row_number - 1, None, None, {"level": 0, "collapsed": False})

        sheet.freeze_panes(1, 0)  # Закрепление первой строки

        # Устанавливаем ширину столбцов вместо autofit (который не поддерживается в xlsxwriter)
        sheet.set_column('A:A', 30)  # Сегмент/клиент/организация
        sheet.set_column('B:B', 45)  # Договор/валюта
        sheet.set_column('C:C', 12)  # ДатаДок
        sheet.set_column('D:E', 15)  # Продажа/возврат и Оплата
        sheet.set_column('F:F', 15)  # ОбщийДолг

        sheet.hide_zero()  # Скрытие нулевых значений

        # Развернуть все уровни по умолчанию
        sheet.set_default_row(hide_unused_rows=False)

        # Устанавливаем настройки группировки
        sheet.outline_settings(symbols_below=False, auto_style=True)

        sheet.set_vba_name("Sheet1")
        wb.close()  # Закрываем и сохраняем файл

        logger.info(f"Файл {SAVE_TO_FILE} создан.")
        return True
    except Exception as e:
        logger.error(f"Error in create_excel function: {e}\n{traceback.format_exc()}")
        return False


async def is_manager(chatid):
    managers_name = managers_chatid_name.get(chatid)
    logger.info(f"managers_name: {managers_name}")
    return managers_name if managers_name else None


# contol date_text is date
async def is_date(parsed_date):
    try:
        if not parsed_date:
            return parse('01.01.1900', dayfirst=True).date()
        if isinstance(parsed_date, date):
            return parsed_date
        else:
            return parse(parsed_date, dayfirst=True).date()
    except ValueError:
        return None


async def load_data(manager_key, date_first: date):
    global SAVE_TO_FILE
    try:
        await send_telegram("Ожидайте. Вам будет выслан файл с балансом по клиентам.", sys.argv[-1])

        await main_cat_counterparties_async()

        # Catalog_ДоговорыКонтрагентов
        await main_cat_contracts_counterparties_async()

        # AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
        await main_doc_reciprocal_settlements_details_async()
        await create_views()

        # Используем только фильтр по manager_key без фильтрации по дате
        date_first = await is_date(date_first)
        df = await get_data_from_view(manager_key, date_first)
        if df.empty:
            logger.info("Error: Unable to load data to DataFrame.")
            await send_telegram("Ошибка: Не удалось загрузить данные.", sys.argv[-1])
            return None

        manager_name = df.get("manager").iloc[0]
        if manager_name is None:
            await send_telegram("Error: Не удалось получить имя менеджера.", sys.argv[-1])
            return None

        SAVE_TO_FILE = f"{manager_name}_balance_{date_to_str(datetime.today())}_{strftime('%H%M')}.xlsx"
        SAVE_TO_FILE = sanitize_filename(SAVE_TO_FILE)
        SAVE_TO_FILE = os.path.join(os.path.dirname(__file__), SAVE_TO_FILE)
        return await create_excel(df)

    except Exception as e:
        logger.error(f"Error in load_data function: {e}")
        await send_telegram(f"Произошла ошибка: {e}", sys.argv[-1])

    return None


async def main_debt_to_excel_xlsx_writer_no_date_async():
    global DATE_FIRST
    # добавим аргументы для запуска функции
    logger.info("Start DebtToExcelXlsxWriterNotPayDocsNoDate.py")
    cur_dir = os.path.dirname(__file__)
    dir_to_python = os.path.join(cur_dir, ".venv", "Scripts", "python.exe")
    dir_to_script = os.path.join(cur_dir, "DebtToExcelXlsxWriterNotPayDocsNoDate.py")
    if len(sys.argv) == 1:
        sys.argv = [f"{dir_to_python} {dir_to_script}", '01.03.2025', "490323168"]
        # sys.argv = [f"{dir_to_python} {dir_to_script}", None, "490323168"]

    logger.info(sys.argv[1:]) # добавим аргументы для запуска функции
    # Проверяем, что передан ID менеджера
    if len(sys.argv) < 2:
        err = "Необходимо указать ID менеджера"
        logger.info(err)
        await send_telegram(err, sys.argv[-1] if len(sys.argv) > 1 else "490323168")
        sys.exit(0)

    date_first = await is_date(sys.argv[1])
    DATE_FIRST = date_first if date_first else None
    chatid = sys.argv[2]
    manager_name = await is_manager(chatid)
    manager_key = managers_name_uuid.get(manager_name)
    if not manager_key:
        err = "Вы не зарегистрированы"
        logger.info(err)
        await send_telegram(err, chatid)
        sys.exit(0)

    result = await load_data(manager_key, date_first)

    if result:
        await telegram_bot_send_document(SAVE_TO_FILE, chatid)

    logger.info(f"End DebtToExcelXlsxWriterNotPayDocsNoDate.py: {result}")


def run_main():
    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    loop.run_until_complete(main_debt_to_excel_xlsx_writer_no_date_async())


if __name__ == '__main__':
    logger.info(f"Start {datetime.now()}")
    run_main()
    logger.info(f"End {datetime.now()}")
