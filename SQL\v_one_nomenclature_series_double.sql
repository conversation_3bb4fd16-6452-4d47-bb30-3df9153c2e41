
    CREATE OR REPLACE VIEW v_one_nomenclature_series_double AS 
    SELECT nom.code, nom.description, ser.*
    FROM t_one_cat_nomenclature AS nom
        INNER JOIN 
        (
            SELECT nomenclature_key, sell_by, count(sell_by)
            FROM t_one_cat_nomenclature_series
            GROUP BY nomenclature_key, sell_by
            HAVING count(sell_by) > 1
        ) AS ser
        ON nom.ref_key = ser.nomenclature_key
    ORDER BY description;

