import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_calculation_fss"
DOCUMENT = "Document_ЗаявлениеРасчетВФСС"
SELECT_COLUMNS = ("Ref_Key,DataVersion,DeletionMark,Number,Date,Posted,ПериодРегистрации,Организация_Key,Комментарий,"
                  "ВсегоПоБольничным,ВсегоПоБольничнымПоУходу,ВсегоПоБеременностиИРодам,"
                  "ВсегоПоДокументу,КоличествоДнейПоБольничным,КоличествоДнейПоБольничнымПоУходу,"
                  "КоличествоДнейПоБеременостиИРодам,ЗаСчетФССОтНС,РежимЗаполнения")

SQL_CREATE_TABLE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        ref_key varchar(50) NULL, -- Ref_Key
        dataversion varchar(50) NULL, -- DataVersion
        deletion_mark boolean NOT NULL DEFAULT FALSE, -- DeletionMark
        doc_number numeric(10,4) NOT NULL DEFAULT 0, -- Number
        doc_date timestamp(0) NOT NULL, -- Date
        posted boolean NOT NULL DEFAULT FALSE, -- Posted
        registration_period date NOT NULL, -- ПериодРегистрации
        organization_key varchar(50) NULL, -- Организация_Key
        description varchar(300) NULL, -- Комментарий
        amount_sick numeric(10,4) NOT NULL DEFAULT 0, -- ВсегоПоБольничным
        amount_sick_care numeric(10,4) NOT NULL DEFAULT 0, -- ВсегоПоБольничнымПоУходу
        amount_pregnancy numeric(10,4) NOT NULL DEFAULT 0, -- ВсегоПоБеременностиИРодам
        amount_document numeric(10,4) NULL DEFAULT 0, -- ВсегоПоДокументу
        days_sick int NOT NULL DEFAULT 0, -- КоличествоДнейПоБольничным
        days_sick_care int NOT NULL DEFAULT 0, -- КоличествоДнейПоБольничнымПоУходу
        days_pregnancy int NOT NULL DEFAULT 0, -- КоличествоДнейПоБеременостиИРодам
        fss_from_ns boolean NOT NULL DEFAULT FALSE, -- ЗаСчетФССОтНС
        fill_mode int NOT NULL DEFAULT 0, -- РежимЗаполнения
    --    documents_on_accruals varchar(50) NULL, -- ДокументыПоНачислениям
    --    sick_leave varchar(50) NULL, -- Больничные
    --    maternity_leave varchar(50) NULL, -- Декретные
    --    sick_leases_ varchar(50) NULL, -- БольничныеНС
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.registration_period IS 'ПериодРегистрации';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.amount_sick IS 'ВсегоПоБольничным';
    COMMENT ON COLUMN {TABLE_NAME}.amount_sick_care IS 'ВсегоПоБольничнымПоУходу';
    COMMENT ON COLUMN {TABLE_NAME}.amount_pregnancy IS 'ВсегоПоБеременностиИРодам';
    COMMENT ON COLUMN {TABLE_NAME}.amount_document IS 'ВсегоПоДокументу';
    COMMENT ON COLUMN {TABLE_NAME}.days_sick IS 'КоличествоДнейПоБольничным';
    COMMENT ON COLUMN {TABLE_NAME}.days_sick_care IS 'КоличествоДнейПоБольничнымПоУходу';
    COMMENT ON COLUMN {TABLE_NAME}.days_pregnancy IS 'КоличествоДнейПоБеременостиИРодам';
    COMMENT ON COLUMN {TABLE_NAME}.fss_from_ns IS 'ЗаСчетФССОтНС';
    COMMENT ON COLUMN {TABLE_NAME}.fill_mode IS 'РежимЗаполнения';
--    COMMENT ON COLUMN {TABLE_NAME}.documents_on_accruals IS 'ДокументыПоНачислениям';
--    COMMENT ON COLUMN {TABLE_NAME}.sick_leave IS 'Больничные';
--    COMMENT ON COLUMN {TABLE_NAME}.maternity_leave IS 'Декретные';
--    COMMENT ON COLUMN {TABLE_NAME}.sick_leases_ IS 'БольничныеНС';
    
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}    
    (
        ref_key, 
        dataversion, 
        deletion_mark, 
        doc_number, 
        doc_date, 
        posted, 
        registration_period, 
        organization_key, 
        description, 
        amount_sick, 
        amount_sick_care, 
        amount_pregnancy, 
        amount_document, 
        days_sick, 
        days_sick_care, 
        days_pregnancy, 
        fss_from_ns, 
        fill_mode 
    )
    VALUES {maket}
    ON CONFLICT (ref_key) DO NOTHING
    """
    return sql.replace("'", "")


async def main_doc_calculation_VFSS_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(18)
    sql = await sql_insert(maket)
    sql = sql.replace("$5,", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp(0),")
    sql = sql.replace("$7,", "to_timestamp($7, 'YYYY-MM-DDThh24:mi:ss')::timestamp(0),")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_calculation_VFSS_async())
