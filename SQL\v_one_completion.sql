DROP VIEW IF EXISTS v_one_completion;
CREATE OR REPLACE VIEW v_one_completion AS
SELECT
	row_number() OVER (Partition by comp.ref_key ORDER BY comp.nomenclature_key) AS id,
	comp.doc_date,
	comp.doc_number,
    'комплектация'::text AS customer,
    nom.sku,
    nom.inbox,
	comp.quantity,  -- количество которое которое заменили (пришло)
	comp.coefficient,
    (comp.quantity * comp.coefficient) AS ed,
    (comp.quantity * comp.coefficient / nom.inbox) AS tobox,
    'комплектация'::text AS doc_type,
	series.description AS series,
	unit.description AS unit,
	org.description AS organization,
    nom.supplier,
	depo.description AS warehouse,
	comp.ref_key,
	comp.nomenclature_key,
	comp.series_key,
	comp.unit_key,
    NULL::text AS customer_key,
    NULL::text AS currency_key,
    nom.supplier_key,
	comp.organization_key,
	comp.warehouse_key,
	row_number() OVER (Partition by comp.ref_key ORDER BY comp.nomenclature_key) AS line_number
FROM t_one_doc_completion AS comp
	LEFT JOIN t_one_cat_warehouses depo
		ON depo.ref_key = comp.warehouse_key
	LEFT JOIN t_one_cat_organizations org
        ON org.ref_key = comp.organization_key
	LEFT JOIN v_one_nomenclature_inbox_supplier nom
		ON nom.nomenclature_key = comp.nomenclature_key
	LEFT JOIN
	    (SELECT *
	    FROM t_one_cat_units
	    WHERE NOT deletion_mark
	    )
	AS unit
		ON unit.ref_key = comp.unit_key
	LEFT JOIN t_one_cat_nomenclature_series series
		ON series.ref_key = comp.series_key
WHERE comp.posted
	AND comp.is_management
UNION ALL
SELECT
    comp.id,
	comp_main.doc_date,
	comp_main.doc_number,
    'комплектация'::text AS customer,
    nom.sku,
    nom.inbox,
	-comp.quantity,  -- количество которое которое заменили (ушло)
	comp.coefficient,
    -(comp.quantity * comp.coefficient) AS ed,
    -(comp.quantity * comp.coefficient / nom.inbox) AS tobox,
    'комплектация'::text AS doc_type,
	series.description AS series,
	unit.description AS unit,
	org.description AS organization,
	nom.supplier,
	depo.description AS warehouse,
	comp.ref_key,
	comp.nomenclature_key,
	comp.series_key,
	comp.unit_key,
    NULL::text AS customer_key,
    NULL::text AS currency_key,
    nom.supplier_key,
	comp_main.organization_key,
	comp_main.warehouse_key,
	comp.line_number
FROM t_one_doc_completion AS comp_main
	LEFT JOIN t_one_doc_completion_details AS comp
		ON comp_main.ref_key = comp.ref_key
	LEFT JOIN t_one_cat_organizations org
        ON org.ref_key = comp_main.organization_key
	LEFT JOIN t_one_cat_warehouses depo
		ON depo.ref_key = comp_main.warehouse_key
	LEFT JOIN v_one_nomenclature_inbox_supplier nom
		ON nom.nomenclature_key = comp.nomenclature_key
	LEFT JOIN
	    (SELECT *
	    FROM t_one_cat_units
	    WHERE NOT deletion_mark
	    )
	 AS unit
		ON unit.ref_key = comp.unit_key
	LEFT JOIN t_one_cat_nomenclature_series series
		ON series.ref_key = comp.series_key
    WHERE comp_main.posted
        AND comp_main.is_management
ORDER BY doc_date, doc_number, sku
;

COMMENT ON VIEW v_one_completion IS 'Комплектация товаров. Н: делаем смотки из коробок чая';