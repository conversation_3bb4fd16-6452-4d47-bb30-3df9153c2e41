CREATE OR REPLACE VIEW v_service_tc AS
SELECT
    org.description AS organizations,
    clients.description AS client,
    serv.doc_date::date AS serv_date,
    serv.doc_date AS tarih,
    serv.doc_number,
    COALESCE(goods.tutar_goods, 0) as giris_tutar,
    round(CASE
        WHEN serv.currency_key = '3d7e2aca-8ac8-11e6-80c4-c936aa9c817c' THEN -- USD
            COALESCE(goods.tutar_goods, 0)
        WHEN serv.currency_key = '3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c' THEN -- UAH
            COALESCE(goods.tutar_goods, 0) / (SELECT rate_usd_nbu
                                                FROM t_rate_nbu
                                                WHERE rate_date = serv.doc_date::date)
        WHEN serv.currency_key = '3d7e2acb-8ac8-11e6-80c4-c936aa9c817c' THEN -- EURO
            (COALESCE(goods.tutar_goods, 0) /
            (SELECT rate_usd_nbu
            FROM t_rate_nbu
            WHERE rate_date = serv.doc_date::date)) *
            (SELECT rate_euro_nbu
            FROM t_rate_nbu
            WHERE rate_date = serv.doc_date::date)
    END,3) AS giris_tutar_usd,
    round(COALESCE(serv.rate_settlement, 0) / COALESCE(serv.multiplicity_of_mutual_settlements, 0),5)
    AS rate_settlement,
    cur.description AS cur_type,
    serv.ref_key AS batch_document,
    serv.account_key,
    serv.currency_key as currency_key
   FROM t_one_doc_receipt_of_goods_services serv
     JOIN (
        SELECT
            ref_key,
            sum(coalesce(quantity,0) * coalesce(price_tr,0)) AS tutar_goods
        FROM t_one_doc_receipt_of_goods_services_goods
        WHERE coalesce(price_tr,0) <> 0
            AND (nomenclature_key::text IN
                ( SELECT nom.ref_key
                   FROM t_one_cat_nomenclature nom
                  WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text)  -- Товары
                )
        GROUP BY ref_key
       ) goods
       ON goods.ref_key::text = serv.ref_key::text
     LEFT JOIN t_one_cat_currencies cur ON cur.ref_key::text = serv.currency_key::text
     LEFT JOIN t_one_cat_organizations org ON org.ref_key::text = serv.organization_key::text
     LEFT JOIN t_one_cat_counterparties clients ON clients.ref_key::text = serv.account_key::text
  WHERE serv.posted = true;
