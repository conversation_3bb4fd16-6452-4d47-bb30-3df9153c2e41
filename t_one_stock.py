import asyncio
import os

from Catalog.catNomenclature import main_cat_nomenclature_async
from CreateAndRunSQLScripts import create_views
from async_Postgres import full_vacuum_pg_async, async_save_pg, async_truncate_table, async_sql_create_index
from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
semaphore = asyncio.Semaphore(2)  # Ограничение на количество одновременных транзакций

TABLE_NAME = "t_one_stock"

SQL_CREATE = f"""
    DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
    
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,  -- id
        supplier varchar NULL,  -- поставщик
        customer varchar NULL,  -- конграгент
        sku varchar NOT NULL,  -- номенклатура
        warehouse varchar NULL,  -- склад
        doc_date timestamp NOT NULL,  -- дата док
        sell_by  date null default '01.01.2016',  -- годен до
        count_of_months_of_storage int default 0,  -- КоличествоМесяцевХранения
        doc_number varchar(15) NOT NULL,  -- номер док        
        inbox numeric(10, 3) NOT NULL DEFAULT 0,  -- ед в крб
        quantity numeric(10, 3) NOT NULL DEFAULT 0,  -- к-во по док
        coefficient numeric(10, 4) NOT NULL DEFAULT 0,  -- коэф
        tobox numeric(15, 4) NOT NULL DEFAULT 0,  -- к-во в крб
        entry_price numeric(10, 3) NULL DEFAULT 0,  -- цена входящая
        entry_amount numeric(15, 3) NULL DEFAULT 0 ,  -- сумма входящая
        maliyet numeric(10, 3) NULL DEFAULT 0,  -- себестоимость
        amount_maliyet numeric(15, 3) NULL DEFAULT 0,  -- сумма себестоимости
        stock numeric(10, 3) NOT NULL DEFAULT 0,  -- остаток
        doc_type varchar(30) NOT NULL,  -- Тип документа
        uktzt varchar(30) NULL,  -- УКТЗТ
        manager varchar(100) NULL,  -- менеджер
        customer_edrpou varchar(15) NULL,  -- ОКПО покупателя
        is_original_receipt bool NULL DEFAULT FALSE,  -- Приход от поставщика 
        organization varchar(50) NOT NULL,  -- organization
        contract_days int default 0,  -- количество дней отсрочки платежа
        organization_key varchar(50) NOT NULL,  -- organization_key
        document_key varchar(50) NOT NULL,  -- document_key
        nomenclature_key varchar(50) NOT NULL,  -- nomenclature_key
        nomenclature_series_key varchar(40) NULL,  -- СерияНоменклатуры_Key
        warehouse_key varchar(50) NOT NULL,  -- Склад_key
        supplier_key varchar(50) NULL,  -- поставщик_key
        customer_key varchar(50) NULL,  -- покупатель_key
        manager_key varchar(50) NULL,  -- покупатель_key
        contract_key varchar(50) NULL,  -- ДоговорКонтрагента_Key
        uktzt_key varchar(50) NULL,  -- UKTZT_key
        line_number numeric(10) NULL,  -- LineNumber
        auto_sale boolean DEFAULT FALSE,  -- АвтоматическиСоздаватьРеализацию
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY(id) --(document_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS 'остатки только товара, без лишних sku';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'ДоговорКонтрагента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.supplier IS 'поставщик';
    COMMENT ON COLUMN {TABLE_NAME}.customer IS 'конграгент';
    COMMENT ON COLUMN {TABLE_NAME}.sku IS 'номенклатура';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse IS 'склад';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'дата док';
    COMMENT ON COLUMN {TABLE_NAME}.sell_by IS 'годен до';
    COMMENT ON COLUMN {TABLE_NAME}.count_of_months_of_storage IS 'КоличествоМесяцевХранения';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'номер док';
    COMMENT ON COLUMN {TABLE_NAME}.inbox IS 'ед в крб';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'к-во по док';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'коэф';
    COMMENT ON COLUMN {TABLE_NAME}.tobox IS 'к-во в крб';
    COMMENT ON COLUMN {TABLE_NAME}.stock IS 'остаток';
    COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип документа';
    COMMENT ON COLUMN {TABLE_NAME}.is_original_receipt IS 'Приход от поставщика';
    COMMENT ON COLUMN {TABLE_NAME}.document_key IS 'document_key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'nomenclature_key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'nomenclature_series_key';
    COMMENT ON COLUMN {TABLE_NAME}.maliyet IS 'себестоимость';
    COMMENT ON COLUMN {TABLE_NAME}.amount_maliyet IS 'сумма себестоимости';
    COMMENT ON COLUMN {TABLE_NAME}.entry_price IS 'цена входящая';
    COMMENT ON COLUMN {TABLE_NAME}.entry_amount IS 'сумма входящая';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.uktzt_key IS 'UKTZT_key';
    COMMENT ON COLUMN {TABLE_NAME}.supplier_key IS 'поставщик_Key';
    COMMENT ON COLUMN {TABLE_NAME}.customer_key IS 'покупатель_key';
    COMMENT ON COLUMN {TABLE_NAME}.customer_edrpou IS 'ОКПО покупателя';
    COMMENT ON COLUMN {TABLE_NAME}.uktzt IS 'УКТЗТ';
    COMMENT ON COLUMN {TABLE_NAME}.manager IS 'менеджер';
    COMMENT ON COLUMN {TABLE_NAME}.manager_key IS 'manager_key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_days IS 'дней отсрочки';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.auto_sale IS 'АвтоматическиСоздаватьРеализацию';

    GRANT SELECT ON TABLE {TABLE_NAME} TO user_prestige;

    """

SQL_INSERT = f"""
    INSERT INTO {TABLE_NAME}(
        supplier,
        customer,
        sku,
        doc_date,
        doc_number,
        inbox,
        quantity,
        coefficient,
        tobox,
        stock,
        maliyet,
        entry_price,
        doc_type,
        document_key,
        nomenclature_key,
        nomenclature_series_key,
        warehouse,
        warehouse_key,
        supplier_key,
        organization,
        organization_key,
        customer_key,
        contract_key,
        line_number,
        auto_sale
        )
    SELECT 
        supplier,
        customer,
        sku,
        tarih,
        doc_number,
        inbox,
        quantity,
        coefficient,
        tobox,
        stock_box,
        maliyet,
        entry_price,
        doc_type,
        document_key,
        nomenclature_key, 
        nomenclature_series_key,        
        warehouse,
        warehouse_key,
        supplier_key,
        organization,
        organization_key,
        customer_key,
        contract_key,
        line_number,
        auto_sale
    FROM v_one_stock
    WHERE doc_date <= CURRENT_DATE 
        AND (nomenclature_key::text IN 
            ( SELECT nom.ref_key
              FROM t_one_cat_nomenclature nom
              WHERE nom.item_type_key::text = 'e5827eaf-8f86-11e6-80c4-c936aa9c817c'::text))
    ;    
    """

sql_change_foreign_key = f'''
    UPDATE {TABLE_NAME} as tos
    SET document_key = ex.ref_key_first
    FROM t_additional_expenses as ex
    WHERE ex.ref_key_second = tos.document_key
    '''

sql_delete_sku_no_supplies = f'''
    DELETE FROM {TABLE_NAME} AS tos
    WHERE tos.nomenclature_key IN (
            SELECT nom.ref_key 
            FROM t_one_cat_nomenclature AS nom
            WHERE nom.item_type_key != 'e5827eaf-8f86-11e6-80c4-c936aa9c817c')
    '''

# Update "date until"
sql_update_sell_by = f'''
    UPDATE {TABLE_NAME} AS stock
    SET sell_by = ser.sell_by
    FROM t_one_cat_nomenclature_series AS ser
    WHERE stock.nomenclature_series_key = ser.ref_key
'''

sql_update_entry_price_and_maliyet_receipt = f'''
    UPDATE {TABLE_NAME} AS stock
    SET entry_price = coalesce(mlt.amount_tr / mlt.quantity,0),
        entry_amount = coalesce((mlt.amount_tr / mlt.quantity) * stock.tobox,0),
        maliyet = coalesce(mlt.maliyet_tutar / mlt.quantity,0),
        amount_maliyet = coalesce((mlt.maliyet_tutar / mlt.quantity) * stock.tobox,0),
        is_original_receipt = true 
    FROM ( 
        SELECT DISTINCT 
            serv_date,
            sum(quantity) OVER(PARTITION BY nomenclature_key, batch_document) AS quantity,
            sum(amount_tr) OVER(PARTITION BY nomenclature_key, batch_document) AS amount_tr,
            sum(maliyet_tutar) OVER(PARTITION BY nomenclature_key, batch_document) AS maliyet_tutar,
            batch_document, 
            nomenclature_key
        FROM v_service_tc_maliyet
        ) AS mlt
    WHERE stock.nomenclature_key = mlt.nomenclature_key 
        AND mlt.batch_document = stock.document_key
    ;
'''

sql_update_entry_price_and_maliyet_avg = f'''
    UPDATE {TABLE_NAME} as upd_stock
    SET entry_price = coalesce(price_tr_avg,0),
        maliyet = coalesce(maliyet_avg,0)
    FROM (
        SELECT DISTINCT 
            stock.sku, stock.doc_number, stock.doc_date, stock.doc_date - INTERVAL '3' MONTH AS PERIOD, 
            entry_price, maliyet, sum(stock.tobox) 
            OVER(PARTITION BY stock.nomenclature_key, stock.document_key) AS tobox,	
            round(
                sum(entry_amount) OVER (PARTITION BY stock.nomenclature_key 
                    ORDER BY stock.doc_date DESC RANGE BETWEEN CURRENT ROW AND INTERVAL '3' MONTH FOLLOWING)
                 / sum(stock.tobox) FILTER (WHERE is_original_receipt = TRUE ) 
                    OVER (PARTITION BY stock.nomenclature_key 
                    ORDER BY stock.doc_date DESC RANGE BETWEEN CURRENT ROW AND INTERVAL '3' MONTH FOLLOWING)
            ,3) AS price_tr_avg,
            round(
                sum(amount_maliyet) OVER (PARTITION BY stock.nomenclature_key 
                    ORDER BY stock.doc_date DESC RANGE BETWEEN CURRENT ROW AND INTERVAL '3' MONTH FOLLOWING)
                / sum(stock.tobox) FILTER (WHERE is_original_receipt = TRUE ) 
                    OVER (PARTITION BY stock.nomenclature_key 
                    ORDER BY stock.doc_date DESC RANGE BETWEEN CURRENT ROW AND INTERVAL '3' MONTH FOLLOWING)
            ,3) AS maliyet_avg,
            stock.doc_type,
            is_original_receipt,
            stock.nomenclature_key, stock.document_key
        FROM {TABLE_NAME} AS stock
    ) AS t
        WHERE upd_stock.nomenclature_key = t.nomenclature_key
            AND upd_stock.document_key = t.document_key
            AND coalesce(price_tr_avg,0) <> 0 
            AND coalesce(upd_stock.entry_price,0) = 0
    ;
'''

sql_update_entry_price_and_maliyet_last = f'''
    UPDATE 	{TABLE_NAME} AS update_stock
    SET entry_price = coalesce(last_entry_price,0),
        maliyet = coalesce(last_maliyet,0)
    FROM (
        SELECT 
            id,
            CASE 
                WHEN COALESCE(stock.entry_price,0) = 0 THEN 
                    last_price
                ELSE
                    stock.entry_price
            END last_entry_price,
            CASE 	
                WHEN COALESCE(stock.maliyet,0) = 0 THEN 
                    last_maliyet
                ELSE
                    stock.maliyet
            END last_maliyet
        FROM (
                SELECT 
                    id,
                    max(doc_date) FILTER(WHERE is_original_receipt) 
                        OVER (PARTITION BY nomenclature_key ORDER BY doc_date) AS max_date,
                    entry_price,
                    maliyet,
                    nomenclature_key
                FROM {TABLE_NAME}
            ) AS stock
            LEFT JOIN 
            (
                SELECT 
                    doc_date,
                    nomenclature_key,
                    entry_price AS last_price,
                    maliyet AS last_maliyet
                FROM {TABLE_NAME}
                WHERE is_original_receipt = TRUE 
            ) AS receipt
            ON stock.max_date = receipt.doc_date
                AND stock.nomenclature_key = receipt.nomenclature_key
        ) AS stock_source
    WHERE stock_source.id = update_stock.id
        AND coalesce(update_stock.entry_price,0) = 0 
    ;
'''

sql_update_amount_maliyet_and_entry_amount = f'''
    UPDATE {TABLE_NAME}
    SET amount_maliyet = coalesce(tobox * maliyet,0),
        entry_amount = coalesce(tobox * entry_price,0)
'''

sql_update_count_of_months_of_storage_series = f'''
    UPDATE  {TABLE_NAME} stock
    SET count_of_months_of_storage = series.count_of_months_of_storage
    FROM t_one_cat_nomenclature_series AS series
    WHERE stock.nomenclature_series_key = series.ref_key 
        AND COALESCE(series.count_of_months_of_storage,0) <> 0
        AND COALESCE(stock.count_of_months_of_storage,0) = 0
    ;
'''

sql_update_count_of_months_of_storage_nomenclature = f'''
    UPDATE  {TABLE_NAME} stock
    SET count_of_months_of_storage = nom.count_of_months_of_storage
    FROM t_one_cat_nomenclature AS nom
    WHERE stock.nomenclature_key = nom.ref_key 
        AND COALESCE(nom.count_of_months_of_storage,0) <> 0
        AND COALESCE(stock.count_of_months_of_storage,0) = 0
    ;
'''

sql_update_uktzt_key = f'''
    UPDATE {TABLE_NAME} AS stock
    SET uktzt_key = nom.gtd_uktzt_key
    FROM  t_one_cat_nomenclature AS nom
    WHERE nom.ref_key = stock.nomenclature_key 
    ;
'''

sql_update_uktzt = f'''
    UPDATE {TABLE_NAME} AS stock
    SET uktzt = nom.code
    FROM t_one_cat_uktved  AS nom
    WHERE nom.ref_key = stock.uktzt_key 
    ;
'''

sql_update_gtd = f'''
    UPDATE {TABLE_NAME} AS stock
    SET uktzt = gtd.code_uktved_index
    FROM t_one_cat_nomenclature_uktved AS gtd
    WHERE gtd.ref_key = stock.uktzt_key
    ;
'''

sql_update_customer_edrpou = f'''
    UPDATE {TABLE_NAME} AS stock
    SET customer_edrpou = customer.edrpou 
    FROM t_one_cat_counterparties AS customer
    WHERE customer.ref_key = stock.customer_key 
    ;
'''

SQL_UPDATE_MANAGER_KEY = f'''
    UPDATE {TABLE_NAME} AS stock
    SET manager_key = client.parent_key
    FROM t_one_cat_counterparties AS client
    WHERE stock.customer_key = client.ref_key 
        AND customer_key IS NOT NULL 
        -- AND stock.doc_date::date >= '01.01.2023'
    ;
'''

SQL_UPDATE_MANAGER = f'''
    UPDATE {TABLE_NAME} AS stock
    SET manager = client.description
    FROM t_one_cat_counterparties AS client
    WHERE stock.manager_key = client.ref_key
        AND manager_key IS NOT NULL 
        -- AND stock.doc_date::date >= '01.01.2023'
    ;    
'''

SQL_UPDATE_CONTRACT_DAY = f'''
    UPDATE {TABLE_NAME} AS stock
    SET contract_days = contract.contract_days 
    FROM v_one_manager_counterparty_contracts_segments as contract 
    WHERE contract.contract_key = stock.contract_key
    ;
'''

ISNULL = "00000000-0000-0000-0000-000000000000"

SQL_UPDATE_SUPPLIER_KEY_IN_STOCK = f"""
    UPDATE {TABLE_NAME} AS stock
    SET supplier_key = nom.supplier_key 
    FROM t_one_cat_nomenclature AS nom
    WHERE stock.nomenclature_key = nom.ref_key 
        AND COALESCE(stock.supplier_key,'{ISNULL}') = '{ISNULL}'
    ;
"""

SQL_UPDATE_SUPPLIER_IN_STOCK = f"""
    UPDATE {TABLE_NAME} AS stock
    SET supplier = client.description
    FROM t_one_cat_counterparties AS client
    WHERE stock.supplier IS NULL
        AND COALESCE(stock.supplier_key,'{ISNULL}') <> '{ISNULL}'
        AND stock.supplier_key = client.ref_key 
    ;
"""

# обновление entry_price и maliyet и сумм для док-тов комплектации
# когда из нескольких коробок формируем одну коробку/спайку
SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET = f"""
    UPDATE {TABLE_NAME} AS stock
    SET entry_price = t.entry_price,
        entry_amount = t.entry_amount,
        maliyet = t.maliyet,
        amount_maliyet = t.amount_maliyet
    FROM (
    SELECT
        main.ref_key,
        main.nomenclature_key,
        round(sum(main.quantity * main.coefficient) / nom.inbox,2) AS quantity,
        -sum(stock.entry_amount) AS entry_amount,
        -sum(stock.amount_maliyet) AS amount_maliyet,
        -round(sum(stock.entry_amount) / sum(main.quantity * main.coefficient) * nom.inbox,2) AS entry_price,
        -round(sum(stock.amount_maliyet) / sum(main.quantity * main.coefficient) * nom.inbox,2)  AS maliyet
    FROM t_one_doc_completion AS main
        LEFT JOIN t_one_doc_completion_details AS det
            ON main.ref_key = det.ref_key 
        LEFT JOIN {TABLE_NAME} AS stock
            ON stock.nomenclature_key = det.nomenclature_key
                AND stock.document_key = det.ref_key
        LEFT JOIN v_one_nomenclature_inbox_supplier AS nom
            ON nom.nomenclature_key = main.nomenclature_key
    WHERE main.posted
        AND main.is_management
    GROUP BY 
        main.ref_key,
        main.nomenclature_key,
        nom.inbox
    ) AS t
    WHERE stock.nomenclature_key = t.nomenclature_key
        AND stock.document_key = t.ref_key
    ;
"""

SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET_REST = f"""
    DROP TABLE IF EXISTS temp_latest_price;

    CREATE TEMP TABLE temp_latest_price AS
    SELECT 
        t1.nomenclature_key, 
        t1.entry_price,
        t1.maliyet
    FROM  {TABLE_NAME} t1
        JOIN (
            SELECT nomenclature_key, MAX(doc_date) AS max_date
            FROM  {TABLE_NAME}
            WHERE entry_amount <> 0
            GROUP BY nomenclature_key
        ) t2 
        ON t1.nomenclature_key = t2.nomenclature_key 
            AND t1.doc_date = t2.max_date
    WHERE t1.entry_amount <> 0
    ;

    CREATE INDEX idx_t_one_stock_product_date ON t_one_stock (nomenclature_key, doc_date);

    UPDATE {TABLE_NAME} t
    SET entry_price = tlp.entry_price,
        maliyet = tlp.maliyet,
        entry_amount = tlp.entry_price * t.tobox,
        amount_maliyet = tlp.maliyet  * t.tobox
    FROM temp_latest_price tlp
    WHERE t.nomenclature_key = tlp.nomenclature_key
    AND t.entry_amount = 0
    ;

"""


async def main_t_one_stock_async():
    result = await async_save_pg(SQL_CREATE)
    logger.info(f"{result}, SQL_CREATE")

    result = await async_truncate_table(TABLE_NAME)
    logger.info(f"{result}, SQL_DELETE")

    # insert use a views, so run it before insert
    await create_views()

    result = await async_save_pg(SQL_INSERT)
    logger.info(f"{result}, SQL_INSERT")

    result = await async_save_pg(sql_delete_sku_no_supplies)
    logger.info(f"{result}, sql_delete_sku_no_supplies")

    result = await async_sql_create_index(TABLE_NAME, "doc_date")
    logger.info(f"{result}, SQL_CREATE_INDEX doc_date")
    result = await async_sql_create_index(TABLE_NAME, "sku")
    logger.info(f"{result}, SQL_CREATE_INDEX sku")
    result = await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    logger.info(f"{result}, SQL_CREATE_INDEX nomenclature_key")
    result = await async_sql_create_index(TABLE_NAME, "warehouse")
    logger.info(f"{result}, SQL_CREATE_INDEX warehouse")
    result = await async_sql_create_index(TABLE_NAME, "warehouse_key")
    logger.info(f"{result}, SQL_CREATE_INDEX warehouse_key")
    result = await async_sql_create_index(TABLE_NAME, "supplier")
    logger.info(f"{result}, SQL_CREATE_INDEX supplier")
    result = await async_sql_create_index(TABLE_NAME, "supplier_key")
    logger.info(f"{result}, SQL_CREATE_INDEX supplier_key")

    # **************** Update ON
    await main_cat_nomenclature_async()

    result = await async_save_pg(sql_update_customer_edrpou)
    logger.info(f"{result}, sql_update_customer_edrpou")

    result = await async_save_pg(sql_update_uktzt_key)
    logger.info(f"{result}, sql_update_uktzt_key")

    result = await async_save_pg(sql_update_uktzt)
    logger.info(f"{result}, sql_update_uktzt")

    result = await async_save_pg(sql_update_gtd)
    logger.info(f"{result}, sql_update_gtd")

    result = await async_save_pg(sql_update_sell_by)
    logger.info(f"{result}, sql_update_sell_by")

    result = await async_save_pg(sql_update_entry_price_and_maliyet_receipt)
    logger.info(f"{result}, sql_update_entry_price_and_maliyet_receipt")

    result = await async_save_pg(sql_update_entry_price_and_maliyet_avg)
    logger.info(f"{result}, sql_update_entry_price_and_maliyet_avg")

    result = await async_save_pg(sql_update_entry_price_and_maliyet_last)
    logger.info(f"{result}, sql_update_entry_price_and_maliyet_last")

    result = await async_save_pg(sql_update_amount_maliyet_and_entry_amount)
    logger.info(f"{result}, sql_update_amount_maliyet_and_entry_amount")

    # *************************** ON ОБНОВЛЕНИЕ ВХ ЦЕНЫ И СЕБЕСТОИМОСТИ ДЛЯ ДОК-ТОВ КОМПЛЕКТАЦИИ
    result = await async_save_pg(SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET)
    logger.info(f"{result}, SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET")

    result = await async_save_pg(SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET_REST)
    logger.info(f"{result}, SQL_UPDATE_ENTRY_AMOUNT_AND_AMOUNT_MALIYET_REST")
    # *************************** OFF ОБНОВЛЕНИЕ ВХ ЦЕНЫ И СЕБЕСТОИМОСТИ ДЛЯ ДОК-ТОВ КОМПЛЕКТАЦИИ

    result = await async_save_pg(sql_update_count_of_months_of_storage_series)
    logger.info(f"{result}, sql_update_count_of_months_of_storage_series")

    result = await async_save_pg(sql_update_count_of_months_of_storage_nomenclature)
    logger.info(f"{result}, sql_update_count_of_months_of_storage_nomenclature")

    result = await async_save_pg(SQL_UPDATE_MANAGER_KEY)
    logger.info(f"{result}, SQL_UPDATE_MANAGER_KEY")

    result = await async_save_pg(SQL_UPDATE_MANAGER)
    logger.info(f"{result}, SQL_UPDATE_MANAGER")

    result = await async_save_pg(SQL_UPDATE_CONTRACT_DAY)
    logger.info(f"{result}, SQL_UPDATE_CONTRACT_DAY")

    result = await async_save_pg(SQL_UPDATE_SUPPLIER_KEY_IN_STOCK)
    logger.info(f"{result}, SQL_UPDATE_SUPPLIER_KEY_IN_STOCK")

    result = await async_save_pg(SQL_UPDATE_SUPPLIER_IN_STOCK)
    logger.info(f"{result}, SQL_UPDATE_SUPPLIER_IN_STOCK")

    # **************** Update OFF

    await full_vacuum_pg_async()


if __name__ == '__main__':
    logger.info(f"START")
    # restart_postgresql_service()
    # asyncio.run(main_t_one_stock_async())
    asyncio.get_event_loop().run_until_complete(main_t_one_stock_async())
    logger.info(f"FINISH")
