import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_return_of_goods_from_customers_goods"
DOCUMENT = "Document_ВозвратТоваровОтПокупателя_Товары"
SELECT_COLUMNS = (
    "LineNumber,Коэффициент,Количество,Цена,Сумма,СуммаНДС,ПроцентАвтоматическихСкидок,"
    "ПроцентСкидкиНаценки,Ref_Key,ЕдиницаИзмерения_Key,ЕдиницаИзмеренияМест_Key,ЗаказПокупателя_Key,"
    "Номенклатура_Key,СерияНоменклатуры_Key,Склад_Key,ДокументПартии,ДокументПартии_Type"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial NOT NULL,
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        coefficient numeric(10, 4) NOT NULL DEFAULT 0,  --  Коэффициент
        quantity numeric(10, 4) NOT NULL DEFAULT 0,  --  Количество
        price numeric(10, 4) NOT NULL DEFAULT 0,  --  Цена
        amount numeric(10, 4) NOT NULL DEFAULT 0,  --  Сумма
        amount_vat numeric(10, 4) NOT NULL DEFAULT 0,  --  СуммаНДС
        percentage_of_automatic_discounts numeric(10, 4) NOT NULL DEFAULT 0,  --  ПроцентАвтоматическихСкидок
        markup_discount_percentage numeric(10, 4) NOT NULL DEFAULT 0,  --  ПроцентСкидкиНаценки
        doc_type varchar(50) NOT NULL,  --  ДокументПартии_Type
        ref_key varchar(50) NOT NULL,  --  Ref_Key
        unit_of_key varchar(50) NOT NULL,  --  ЕдиницаИзмерения_Key
        location_unit_key varchar(50) NOT NULL,  --  ЕдиницаИзмеренияМест_Key
        customer_order_key varchar(50) NOT NULL,  --  ЗаказПокупателя_Key
        nomenclature_key varchar(50) NOT NULL,  --  Номенклатура_Key
        nomenclature_series_key varchar(50) NOT NULL,  --  СерияНоменклатуры_Key
        warehouse_key varchar(50) NOT NULL,  --  Склад_Key,
        base_doc_key varchar(50) NOT NULL,  --  ДокументПартии,
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';   
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS ' LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS ' Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS ' Количество';
    COMMENT ON COLUMN {TABLE_NAME}.price IS ' Цена';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS ' Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS ' СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.percentage_of_automatic_discounts IS ' ПроцентАвтоматическихСкидок';
    COMMENT ON COLUMN {TABLE_NAME}.markup_discount_percentage IS ' ПроцентСкидкиНаценки';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS ' Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS ' ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.location_unit_key IS ' ЕдиницаИзмеренияМест_Key';
    COMMENT ON COLUMN {TABLE_NAME}.customer_order_key IS ' ЗаказПокупателя_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS ' Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS ' СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS ' Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.base_doc_key IS ' ДокументПартии';
    COMMENT ON COLUMN {TABLE_NAME}.doc_type IS ' ДокументПартии_Type';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        line_number,
        coefficient,
        quantity,
        price,
        amount,
        amount_vat,
        percentage_of_automatic_discounts,
        markup_discount_percentage,
        ref_key,
        unit_of_key,
        location_unit_key,
        customer_order_key,
        nomenclature_key,
        nomenclature_series_key,
        warehouse_key,
        base_doc_key,
        doc_type
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        coefficient = EXCLUDED.coefficient,
        quantity = EXCLUDED.quantity,
        price = EXCLUDED.price,
        amount = EXCLUDED.amount,
        amount_vat = EXCLUDED.amount_vat,
        percentage_of_automatic_discounts = EXCLUDED.percentage_of_automatic_discounts,
        markup_discount_percentage = EXCLUDED.markup_discount_percentage,
        ref_key = EXCLUDED.ref_key,
        unit_of_key = EXCLUDED.unit_of_key,
        location_unit_key = EXCLUDED.location_unit_key,
        customer_order_key = EXCLUDED.customer_order_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        nomenclature_series_key = EXCLUDED.nomenclature_series_key,
        warehouse_key = EXCLUDED.warehouse_key,
        base_doc_key = EXCLUDED.base_doc_key,
        doc_type = EXCLUDED.doc_type
    """
    return sql.replace("'", "")


async def main_doc_return_of_goods_from_customers_goods_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(17)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "ref_key")
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_return_of_goods_from_customers_goods_async())
