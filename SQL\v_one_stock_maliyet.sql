
    CREATE OR REPLACE VIEW v_one_stock_maliyet AS
    SELECT
        t.warehouse,
        t.supplier,   
        t.sku,
        CASE
            WHEN t.tarih = max(t.tarih) OVER (PARTITION BY t.warehouse, t.nomenclature_key, t.tarih::date) THEN 
                t.tarih::date
            ELSE NULL::date
        END AS date_max,
        t.tarih,
        t.doc_number,
        t.maliyet,        
        t.inbox,
        t.tobox,
        sum(t.tobox) OVER (PARTITION BY t.warehouse, t.nomenclature_key ORDER BY t.sku, t.tarih) 
        AS ostatok_krb,
        t.ed,
        sum(t.ed) OVER (PARTITION BY t.warehouse, t.nomenclature_key ORDER BY t.nomenclature_key, t.tarih) 
        AS ostatok_ed,
        sum(t.maliyet * t.tobox) OVER (PARTITION BY t.warehouse, t.nomenclature_key 
                ORDER BY t.nomenclature_key, t.tarih)::numeric(15,3) 
        AS kalan_tutar,
        t.doc_type,
        t.nomenclature_key,
        t.nomenclature_series_key,
        t.warehouse_key
       FROM (
            SELECT warehouse,
                supplier, 
                sku,
                inbox,
                doc_date as tarih,
                doc_number,
                sum(quantity * coefficient) AS ed,
                sum(tobox) AS tobox,
                doc_type,
                nomenclature_key,
                nomenclature_series_key,
                maliyet,
                warehouse_key
            FROM t_one_stock
            GROUP BY supplier, sku, inbox, doc_number, warehouse,
                doc_type, maliyet, nomenclature_key, nomenclature_series_key, doc_date, warehouse_key
       ) t
      ORDER BY t.sku, t.nomenclature_key, t.tarih;
    