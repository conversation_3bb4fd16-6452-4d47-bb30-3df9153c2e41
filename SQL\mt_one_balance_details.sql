DROP MATERIALIZED VIEW IF EXISTS  mt_one_balance_details CASCADE;
CREATE MATERIALIZED VIEW IF NOT EXISTS mt_one_balance_details AS
SELECT
    row_number() OVER (ORDER BY
            segment,
            manager,
            customer,
            organization,
            currency_name,
            contract_type,
            contract_name,
            last_date,
            doc_date,
            doc_number
        )
    AS row_no,
    *,
    COALESCE(sum(doc_sum)
        FILTER (WHERE last_date <= current_date)
        OVER (PARTITION BY
            COALESCE(trim(segment), ''),
            COALESCE(trim(manager), ''),
            COALESCE(trim(customer), ''),
            COALESCE(trim(organization), ''),
            COALESCE(trim(currency_name), '')
            ORDER BY last_date, doc_date, t1.doc_number
            ),0)
    AS customer_pdz,  -- долг клиента до текущей даты
    COALESCE(sum(doc_sum)
        OVER (PARTITION BY
            COALESCE(trim(segment), ''),
            COALESCE(trim(manager), ''),
            COALESCE(trim(customer), ''),
            COALESCE(trim(organization), ''),
            COALESCE(trim(currency_name), '')
            ORDER BY last_date, doc_date, t1.doc_number
            ),0)
    AS customer_sum,  -- общий долг клиента с иерархией
    CASE
        WHEN
            -- 1. Если дата последнего документа больше текущей даты,
            -- то разницу дней считаем между текущей датой и датой последнего документа
            last_date >= max(last_date)
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
                    )
            AND
            abs(COALESCE(sum(COALESCE(doc_sum,0))
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
--                    ORDER BY last_date
                    ),0)) > 1
            THEN
                current_date - last_date
        WHEN
            -- 2. Если дата последнего документа меньше текущей даты и ПДЗ последнее и текущее больше 1,
            -- то считаем разницу между датой последнего документа и последней датой продажи
            last_date < current_date
            AND
            COALESCE(sum(doc_sum)
                FILTER (WHERE last_date <= current_date)
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
                    ),0) > 1
            AND
            COALESCE(sum(doc_sum)
                FILTER (WHERE last_date <= current_date)
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
                    ORDER BY last_date, doc_date, t1.doc_number
                    ),0) > 1
            THEN
                last_date::date - (MAX(last_date) FILTER (WHERE recordtype = 'Receipt'
                    AND doc_sum > 0
                    AND last_date <= current_date
                    AND recorder_type IN ('РеализацияТоваровУслуг',
                                          'StandardODATA.Document_РеализацияТоваровУслуг'))
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
                    ORDER BY last_date
                    ))::date
        ELSE
            0
    END leave_days,
    CASE
        WHEN
            COALESCE(sum(doc_sum)
                FILTER (WHERE doc_date <= current_date)
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
                    ORDER BY doc_date, last_date, t1.doc_number
                    ),0) > 1
            THEN
                doc_date::date - (MAX(doc_date) FILTER (WHERE recordtype = 'Receipt'
                    AND doc_sum > 0
                    AND doc_date <= current_date
                    AND recorder_type IN ('РеализацияТоваровУслуг',
                                          'StandardODATA.Document_РеализацияТоваровУслуг'))
                OVER (PARTITION BY
                    COALESCE(trim(segment), ''),
                    COALESCE(trim(manager), ''),
                    COALESCE(trim(customer), ''),
                    COALESCE(trim(organization), ''),
                    COALESCE(trim(currency_name), '')
                    ORDER BY doc_date
                    ))::date
        ELSE
            0
        END leave_days_doc_date
FROM (
    SELECT
        id,
        COALESCE(trim(segment_folder),'') segment_folder,
        COALESCE(trim(segment),'') segment,
        COALESCE(trim(manager),'') manager,
        COALESCE(trim(customer),'') customer,
        edrpou,
        seg.contract_days,
        recordtype,
        req.dtperiod::date doc_date,
        CASE  -- Не учитываем образцы, где цена = 0,01
            WHEN req.amount > 1 AND req.recorder_type IN ('РеализацияТоваровУслуг','StandardODATA.Document_РеализацияТоваровУслуг') THEN
                (req.dtperiod::date + INTERVAL '1 DAYS' * seg.contract_days)::date
            ELSE
                req.dtperiod::date
        END
        AS last_date,
        req.doc_number,
        CASE
            WHEN req.recordtype = 'Receipt' THEN
                req.amount
            ELSE
                -req.amount
        END doc_sum,
        CASE
            WHEN recordtype = 'Receipt' THEN
                req.amount_uah
            ELSE
                -req.amount_uah
        END doc_sum_uah,
        req.is_accounting,
        REPLACE(recorder_type, 'StandardODATA.Document_', '') recorder_type,
        COALESCE(trim(organization), '') organization,
        seg.currency_name,
        seg.contract_name,
        seg.contract_type,
        seg.contract_key,
        seg.customer_key,
        req.recorder,
        seg.currency_key,
        seg.manager_key
    FROM t_one_doc_acc_reg_reciprocal_settlements_details as req
        LEFT JOIN v_one_manager_counterparty_contracts_segments AS seg
            ON req.contract_key = seg.contract_key
        LEFT JOIN v_one_organization_and_type as org
            ON org.ref_key = req.organization_key
    WHERE recorder NOT IN
        (
            SELECT
                main.ref_key
            FROM t_one_doc_debt_correction AS main
                INNER JOIN t_one_doc_debt_correction_debt_amount AS sub
                    ON main.ref_key = sub.ref_key
            WHERE main.posted
                AND NOT main.is_management  -- берем только УправленческийУчет (NOT is_management, т.к. WHERE recorder NOT IN)
                OR main.operation_type = 'ПроведениеВзаимозачета'
            GROUP BY
                main.ref_key,
                sub.counterparty_contract_key
            HAVING
                sum(CASE
                    WHEN sub.debt_type = 'Дебиторская' THEN
                        sub.amount
                    ELSE
                        -sub.amount
                END) = 0
        )
) AS t1
ORDER BY
    manager NULLS LAST,
    segment NULLS LAST,
    customer NULLS LAST,
    organization NULLS LAST,
    currency_name NULLS LAST,
    last_date,
    doc_date,
    t1.doc_number,
    id
;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_balance_details_long') THEN
        CREATE INDEX idx_balance_details_long ON mt_one_balance_details (
            segment, manager, customer, organization, currency_name,
            contract_type, contract_name, last_date, doc_date, doc_number
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_balance_details_short') THEN
        CREATE INDEX idx_balance_details_short ON mt_one_balance_details (
            segment, manager, customer, organization, currency_name,
            contract_type, contract_name
        );
    END IF;
END
$$;

GRANT SELECT ON TABLE mt_one_balance_details TO user_prestige;
