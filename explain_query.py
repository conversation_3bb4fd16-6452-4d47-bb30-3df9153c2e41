import asyncio
from async_Postgres import async_save_pg
from logger_prestige import get_logger

logger = get_logger("explain_query")

async def explain_query():
    TABLE_NAME = "t_one_stock_new"
    
    # Запрос EXPLAIN ANALYZE для анализа SQL_INSERT
    EXPLAIN_QUERY = f"""
    EXPLAIN ANALYZE
    INSERT INTO {TABLE_NAME} (
        doc_date,
        quantity,
        doc_type,
        document_key,
        nomenclature_key,
        nomenclature_series_key,
        warehouse_key
    )
    SELECT 
        dtperiod,
        quantity,
        replace(recorder_type,'StandardODATA.Document_',''),
        recorder,
        nomenclature_key,
        item_series_key,
        warehouse_key
    FROM t_one_accreg_good_in_warehauses_recordtype AS reg
    """
    
    result = await async_save_pg(EXPLAIN_QUERY)
    logger.info(f"EXPLAIN ANALYZE результат:\n{result}")

if __name__ == '__main__':
    asyncio.get_event_loop().run_until_complete(explain_query())
