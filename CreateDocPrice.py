# создание документа Document_УстановкаЦенНоменклатуры
import asyncio
from datetime import datetime
import pandas as pd
import json
from CreateNomenclatureFromExcel import get_nomenclature_key_from_sale
from async_Postgres import USER_KEY, URL_CONST
from async_request import get_data_1c, post_data_1c

# URL_CONST = rf"http://195.128.227.141/utp_prestige/odata/standard.odata/"
PRICE_TYPE = {"ДИСТРИБ’ЮТОР 2022 01.11": "911e681c-59c0-11ed-8150-001dd8b740bc"}  # тип цены
PRICE_COLUMN = "rm_block"  # из какой колонки Excel берем цену
CURRENCY_KEY = "3d7e2ac9-8ac8-11e6-80c4-c936aa9c817c"  # Ref_Key валюты (грн)

# наименование колонки в Excel с наименованием номенклатуры
NOMENCLATURE_COLUMN = '1c'


# формируем словарь с типами цен для документа
# если надо добавить несколько в один док, то вначале внести все в PRICE_TYPES
async def create_doc_price_types():
    data = [{
        "LineNumber": "1",
        "ТипЦен_Key": PRICE_TYPE.get("ДИСТРИБ’ЮТОР 2022 01.11")
    }]
    return data


# формируем данные для шапки документа
async def data_header(date_doc):
    price_type_data = await create_doc_price_types()
    data = {
        "Date": f"{date_doc}T00:00:01",
        "Posted": False,
        "Комментарий": "",
        "Ответственный_Key": USER_KEY,
        "НеПроводитьНулевыеЗначения": True,
        "ТипыЦен": price_type_data,
        "Товары": []
    }

    return data


# Если в продажах не нашли, ищем в Catalog_Номенклатура ref_key по sku.
# т.к. в справочнике могут дублироваться наименования sku, берем с макс code, т.е. последний добавленный
async def get_nomenclature_key_from_catalog_1c(sku) -> str:
    url = (f"{URL_CONST}Catalog_Номенклатура?$format=json"
           f"&$filter=Description eq '{sku}' "
           f"and DeletionMark eq false"
           "&$orderby=Code desc"
           "&$top=1"
           )
    result = get_data_1c(url)
    if result and result.get('value'):
        return result.get('value')[0].get('Ref_Key')
    return ''


# nomenclature_key начинаем искать в продажах,
# т.к. наименования в справочнике 1С могут дублироваться. соответственно будут разные ref_key
# напротив каждого sku вставляем nomenclature_key.
async def get_nomenclature_key(df: pd.DataFrame) -> pd.DataFrame:
    for index, row in df.iterrows():
        sku = row.get(NOMENCLATURE_COLUMN)
        sku = sku.replace("&", "%26")  # заменяем & на %26, иначе url не работает
        # ищем вначале в продажах. т.к. в каталоге могут дублироваться наименования sku
        nomenclature_key = await get_nomenclature_key_from_sale(sku)
        if not nomenclature_key:
            # если товара нет в продажах, то берем его ref_key из Catalog_Номенклатура
            nomenclature_key = await get_nomenclature_key_from_catalog_1c(sku)

        if nomenclature_key:
            df.at[index, 'nomenclature_key'] = nomenclature_key
            df.at[index, NOMENCLATURE_COLUMN] = sku
            continue

        if not nomenclature_key:
            print(f"!!! Не найден: '{sku}'")
    return df


# формируем данные для табличной части документа
async def data_table(df: pd.DataFrame):
    data = []
    price_type_key = PRICE_TYPE.get("ДИСТРИБ’ЮТОР 2022 01.11")
    for i, row in df.iterrows():
        sku = row[NOMENCLATURE_COLUMN]
        print(f"sku: {sku}")
        nomenclature_key = row['nomenclature_key']
        unit_key = await get_nomenclature_and_unit_key(nomenclature_key)
        data.append({
            "LineNumber": f"{int(i) + 1}",
            "Номенклатура_Key": nomenclature_key,
            "Цена": row[PRICE_COLUMN],
            "Валюта_Key": CURRENCY_KEY,
            "ЕдиницаИзмерения_Key": unit_key,
            "ПроцентСкидкиНаценки": 0,
            "ТипЦен_Key": price_type_key,
            "ИндексСтрокиТаблицыЦен": "0",
            "СпособРасчетаЦены": "",
        })
    return data


# найдем ref_key (единицы измерения + номенклатуры)
# берем ref_key с самым маленьким коэффициентом
# !!! если в Excel берем цену блока, то надо найти ref_key для блока, а не для штуки
# скрипт работает только для цены блока/банки
async def get_nomenclature_and_unit_key(nomenclature_key: str) -> str:
    url = (f"{URL_CONST}Catalog_ЕдиницыИзмерения?$format=json"
           f"&$filter=Owner eq cast(guid'{nomenclature_key}', 'Catalog_Номенклатура') "
           f"and Коэффициент eq 1"
           f"&$select=Ref_Key,Description"
           f"&$inlinecount=allpages")
    unit_key = get_data_1c(url)
    if unit_key and unit_key['value']:
        return unit_key['value'][0]['Ref_Key']
    return ''


# данные для табличной части документа берем из файла и сохраняем в df
async def create_data_table():
    df = pd.read_excel('PriceSlim.xlsx')

    # добавляем колонку для nomenclature_key
    df = await get_nomenclature_key(df)

    # проверяем, что все sku из Excel найдены в 1С
    df_isnull = df[df['nomenclature_key'].isnull()].reset_index(drop=True)
    if not df_isnull.empty:
        print(f"!!! Не найдены в 1С: {df[NOMENCLATURE_COLUMN]}")
        return

    table_data = await data_table(df)
    print(f"table_data: {table_data}")
    return table_data


# точка входа
async def main_create_doc_price_async():
    date_doc = datetime.now().strftime('%Y-%m-%d')
    header_data = await data_header(date_doc)
    print(f"header_data: {header_data}")
    table_data = await create_data_table()
    header_data.update({"Товары": table_data})
    response = post_data_1c(f'{URL_CONST}Document_УстановкаЦенНоменклатуры?$format=json', header_data)
    print(f"response: {response}")


if __name__ == '__main__':
    asyncio.run(main_create_doc_price_async())
    # fg = json.dumps(header_data, ensure_ascii=False)
