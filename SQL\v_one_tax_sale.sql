    DROP VIEW IF EXISTS v_one_tax_sale CASCADE;
    CREATE OR REPLACE VIEW v_one_tax_sale AS
    SELECT
        doc_date::date AS doc_date,
        doc_number,
        regexp_replace(trim(doc_number)::text, '.*?(\d+)$'::text, '\1'::text)::int::text doc_number_short,
--        CAST(regexp_replace(t_one_doc_tax_sale.doc_number::text, '.*?(\d+)$', '\1') AS INTEGER)::text doc_number_short,
        document_amount,
        is_consolidated_invoice,
        CASE
            WHEN (document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг')
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************')
                        <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN
                            document_base_key
            WHEN (deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг')
                AND (COALESCE(deal_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************') AND (COALESCE(deal_key, '') <> '') THEN
                        deal_key
            WHEN (document_base_type <> 'StandardODATA.Undefined')
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN
                        document_base_key
            WHEN (deal_type <> 'StandardODATA.Undefined')
                AND (COALESCE(deal_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************') AND (COALESCE(deal_key, '') <> '') THEN
                        deal_key
            ELSE Null
        END base_key,
        CASE
            WHEN (document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг')
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN
                        document_base_type
            WHEN (deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг')
                AND (COALESCE(deal_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************') AND (COALESCE(deal_key, '') <> '') THEN
                        deal_type
            WHEN (document_base_type <> 'StandardODATA.Undefined')
                AND (COALESCE(document_base_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************') AND (COALESCE(document_base_key, '') <> '') THEN
                        document_base_type
            WHEN deal_type <> 'StandardODATA.Undefined'
                AND COALESCE(deal_key, '********-0000-0000-0000-************')
                    <> '********-0000-0000-0000-************' AND COALESCE(deal_key, '') <> '' THEN
                        deal_type
            ELSE Null
        END base_type,
        ref_key,
        account_key,
        organization_key
    FROM t_one_doc_tax_sale
    ORDER BY doc_date DESC
    ;
