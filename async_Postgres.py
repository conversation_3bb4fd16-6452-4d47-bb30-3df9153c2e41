# pip install asyncpg
# pip install pandas
# import aiopg  # pip install psycopg2-binary
import ast
import asyncio
import inspect
import os
import re
import sys
import warnings
from pathlib import Path
from sqlalchemy import create_engine
from dateutil.parser import parse
from datetime import datetime
import aiohttp
import asyncpg
import pandas as pd
from sqlalchemy.ext.asyncio import create_async_engine  # pip install sqlalchemy[asyncio]

from logger_prestige import get_logger

my_path = os.path.dirname(os.path.abspath(__file__))
path = Path(my_path)
components = path.parts
python_index = None
if "Python" in components:
    python_index = components.index("Python")
my_path_list = str(my_path).split('\\')[1:python_index + 1]
config_path = os.path.join(os.path.splitdrive(my_path)[0], '\\', *my_path_list, "Config")
sys.path.append(my_path)
sys.path.append(config_path)
sys.path.append(path.parent.__str__())
from configPrestige import username, psw, basename, hostname_public, hostname, port, TELEGRAM_TOKEN, C_LOGIN, C_PSW, \
    chatid_rasim, URL_CONST, USER_KEY
from error_log import add_to_log

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]

logger = get_logger(FILE_NAME)

CONN_PARAMS = {
    "database": basename,
    "user": username,
    "password": psw,
    "host": hostname_public,
    "port": port,
}

engine = create_engine('postgresql://%s:%s@%s:%s/%s' % (username, psw, hostname_public, port, basename),
                       pool_pre_ping=True,
                       connect_args={
                           "keepalives": 1,
                           "keepalives_idle": 30,
                           "keepalives_interval": 10,
                           "keepalives_count": 5,
                       })

pg_engine_async = create_async_engine(
    "postgresql+asyncpg://%s:%s@%s:%s/%s"
    % (username, psw, hostname_public, port, basename),
    echo=True,
)

warnings.filterwarnings("ignore", message="pandas only supports SQLAlchemy connectable")


async def create_model_async(fields_count):
    """
    Создает кортеж, содержащий строки, которые представляют собой параметры-заполнители для SQL-запроса.
    """
    return tuple(f"${i + 1}" for i in range(fields_count))


async def full_vacuum_pg_async():
    result = True
    try:
        conpg = await asyncpg.connect(**CONN_PARAMS)
        result = await conpg.execute("VACUUM FULL;")

    except Exception as e:
        result = False
        sms = "table_vacuum_pg: %s" % e
        logger.info(f"ERROR full_vacuum_pg_async {sms}")

    finally:
        logger.info(f"{result}, full_vacuum_pg_async")

    return result


# обработчик, для получения из базы одной записи/строки
async def get_result_one_column(sql, *args):
    result = None
    conn = await asyncpg.connect(**CONN_PARAMS)
    try:
        if len(args) == 0:
            result = await conn.fetchval(sql)
        else:
            result = await conn.fetchval(sql, *args)

    except Exception as e:
        sms = "ERROR:get_result_one_column: %s " % e
        logger.info(sms)

    finally:
        if conn and not conn.is_closed():
            await conn.close()

    return result


async def get_variable_name(sql):
    frame = inspect.currentframe().f_back.f_back  # Поднимаемся на два уровня вверх
    code = frame.f_code
    call_line = frame.f_lineno
    with open(code.co_filename, "r", encoding="utf-8") as f:
        lines = f.readlines()

    call_source = lines[call_line - 1].strip()
    call_ast = ast.parse(call_source)

    # Поскольку вызов функции асинхронный, нам нужно учитывать, что call_ast.body[0].value может быть Await
    call_node = call_ast.body[0].value
    if isinstance(call_node, ast.Await):
        call_node = call_node.value

    arg_name = call_node.args[0].id if isinstance(call_node.args[0], ast.Name) else None
    return arg_name if arg_name else str(sql)


def get_caller_name():
    frame = inspect.currentframe()
    while frame:
        co_name = frame.f_code.co_name if frame.f_code.co_name != 'get_caller_name' else frame.f_back
        return co_name


async def run_sql_file(error_message):
    # Разделяем строку по пробелам
    file_path = error_message.split()

    # Ищем часть, которая начинается и заканчивается кавычками
    for part in file_path:
        if part.startswith("\"") and part.endswith("\""):
            view_name = part.strip("\"")
            break

    logger.info(f"Running: {os.path.abspath(file_path)}")
    # Откройте файл и прочитайте его содержимое
    with open(file_path, 'r', encoding='utf-8') as file:
        sql_query = file.read()

    # Выполните SQL-запрос
    result = await async_save_pg(sql_query)
    logger.info(f"{result}: {os.path.abspath(file_path)}")


async def async_save_pg(sql, *args, retry_count=3):
    conn = None
    for attempt in range(retry_count):
        try:
            if not conn or conn.is_closed():
                conn = await asyncpg.connect(**CONN_PARAMS, timeout=300)
            async with conn.transaction():
                if args:
                    await conn.executemany(sql, *args)
                else:
                    await conn.execute(sql)
            return True
        except asyncpg.exceptions.DuplicateTableError as e:  # если уже есть индексы
            return True
        except (asyncpg.exceptions.ConnectionDoesNotExistError, asyncpg.exceptions.CannotConnectNowError) as e:
            logger.warning(f"Connection error, retrying {attempt + 1}/{retry_count}: {e}")
            if attempt + 1 == retry_count:
                raise
        except asyncpg.exceptions.DuplicateObjectError as e:
            # logger.warning(f"Duplicate object error: {e}")
            return True
        except asyncpg.exceptions.UndefinedTableError as e:
            frame = inspect.currentframe()
            outer_frames = inspect.getouterframes(frame)
            current_func_name = outer_frames[0].function
            if len(outer_frames) > 1:
                calling_func_name = outer_frames[1].function
                calling_file_name = outer_frames[1].filename
            else:
                calling_func_name = '<unknown>'
                calling_file_name = '<unknown>'

            logger.error(f"Exception:{calling_file_name}\{calling_func_name}: {e}")
            # if 'CreateAndRunSQLScripts.py' in calling_file_name:
            #     await run_sql_file(str(e))
            raise
        except asyncpg.exceptions.DeadlockDetectedError as e:
            logger.warning(f"Deadlock detected, retrying {attempt + 1}/{retry_count}: {e}")
            await asyncio.sleep(3)
            if attempt + 1 == retry_count:
                raise
        except Exception as e:
            frame = inspect.currentframe()
            outer_frames = inspect.getouterframes(frame)
            current_func_name = outer_frames[0].function
            if len(outer_frames) > 1:
                calling_func_name = outer_frames[1].function
                calling_file_name = outer_frames[1].filename
            else:
                calling_func_name = '<unknown>'
                calling_file_name = '<unknown>'

            logger.error(f"Exception:{calling_file_name}/{calling_func_name}: {e}")
            logger.error(f"SQL: {sql}")

            arg_name = await get_variable_name(sql)
            err = f"calling_file_name: {calling_file_name}; calling_func_name: {calling_func_name}"
            await add_to_log(err)
            raise
        finally:
            if conn and not conn.is_closed():
                await conn.close()

    return False


def get_filename_from_function_name(func_name):
    frame = inspect.currentframe()
    while frame:
        if frame.f_code.co_name == func_name:
            return frame.f_code.co_filename
        frame = frame.f_back


async def sql_to_dataframe_async(sql_query, *args):
    df = pd.DataFrame()
    conn = await asyncpg.connect(**CONN_PARAMS)
    try:
        if len(args) == 0:
            result = await conn.fetch(sql_query)
        else:
            result = await conn.fetch(sql_query, *args)
        if result:
            df = pd.DataFrame(result, columns=result[0].keys())
    except Exception as e:
        logger.info(f"ERROR: sql_to_dataframe_async - {e}")
    finally:
        if conn and not conn.is_closed():
            await conn.close()
    return df


def change_value_to_datetime(value):
    if isinstance(value, datetime):
        return value
    elif isinstance(value, str):
        return parse(value)
    elif isinstance(value, pd.Timestamp):
        return value.to_pydatetime()
    elif isinstance(value, int):
        return datetime.fromtimestamp(value)
    else:
        return None
    
    
async def select_pg(sql, *args):
    result = []
    conn = await asyncpg.connect(**CONN_PARAMS)
    try:
        if len(args) == 0:
            rows = await conn.fetch(sql)
        else:
            rows = await conn.fetch(sql, *args)

        # result = [dict(row) for row in rows]
        result = [dict(row) async for row in rows]
        return result
    except Exception as e:
        logger.info(str(e))
    finally:
        if conn and not conn.is_closed():
            await conn.close()
    return result


async def async_sql_create_index(table_name, column_name):
    idx_column = column_name.replace(' ', '_').replace('(', '').replace(')', '')
    sql = '''CREATE INDEX IF NOT EXISTS %s_%s_idx ON %s (%s);''' % \
          (table_name, idx_column, table_name, column_name)
    return await async_save_pg(sql)


async def async_sql_create_index_unique(table_name, *column_name):
    columns = ', '.join(column_name)
    sql = f"""
        DO $$
        BEGIN
            CREATE UNIQUE INDEX {table_name}_key_idx ON {table_name} USING btree ({columns});
        EXCEPTION
            WHEN duplicate_table THEN
            -- Индекс уже существует, ничего не делаем
        END
        $$ LANGUAGE plpgsql;    
    """
    return await async_save_pg(sql)


async def async_truncate_table(table_name):
    sql = '''TRUNCATE TABLE %s;''' % table_name
    return await async_save_pg(sql)


async def dict_to_sql_unqkey_async(table_name, mydict, unqkey):
    # данные json переводит в sql insert формат с учетом уникальности данных
    # данные в базу НЕ заносит!!!

    strsql = ''
    odata = list()
    try:
        placeholders = await create_model_async(len(mydict))
        placeholders = re.sub(r"'", "", str(placeholders))  # remove all quotes
        columns = ', '.join(mydict.keys())
        odata = list(mydict.values())
        strsql = f'''INSERT INTO {table_name} ({columns}) VALUES {placeholders}
                ON CONFLICT ON CONSTRAINT {unqkey}
                DO NOTHING             
            '''

    except Exception as e:
        msj = "ERROR:ConnectToBase:dict_to_sql: %s" % e
        logger.info(msj)
        await add_to_log(msj)

    finally:
        return strsql.lower(), odata


async def drop_function(function_name, function_args):
    result = False
    conn = await asyncpg.connect(**CONN_PARAMS)
    try:
        # Создаем SQL-запрос для удаления функции
        drop_query = f"DROP FUNCTION IF EXISTS {function_name}({', '.join(function_args)})"

        # Выполняем запрос
        await conn.execute(drop_query)
        logger.info(f"Function {function_name}({', '.join(function_args)}) dropped successfully.")
        result = True
    except Exception as e:
        logger.info(f"Error dropping function: {e}")
    finally:
        if conn and not conn.is_closed():
            await conn.close()
    return result


def run_in_executor(executor, func, *args):
    def wrapper():
        asyncio.run(func(*args))

    executor.submit(wrapper)


async def create_view_async(sql, view_name):
    frame = inspect.currentframe()
    outer_frames = inspect.getouterframes(frame)
    current_func_name = outer_frames[0].function
    await async_save_pg(sql)
    logger.info(f"{current_func_name} created {view_name}")


async def send_telegram(text: str, chatid=chatid_rasim):
    chatid = str(chatid)
    token = TELEGRAM_TOKEN
    url = "https://api.telegram.org/bot"
    url += token
    method = url + "/sendMessage"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(method, data={"chat_id": chatid, "text": text}) as response:
                if response.status != 200:
                    raise Exception("post_text error")
    except Exception as e:
        logger.info(f"Error in send_telegram: {e}")
        await add_to_log(f"Error in send_telegram: {e}")


if __name__ == '__main__':
    logger.info(pg_engine_async.name)
    result = asyncio.run(get_result_one_column("SELECT * FROM payments;"))
    print(result)
