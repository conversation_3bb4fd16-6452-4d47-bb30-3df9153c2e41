    CREATE OR REPLACE VIEW v_one_order_with_multi_payments AS
    SELECT  dtl."source" источ<PERSON>ик,
        dtl.pay_date датаОплаты, dtl.pay_number номерДокОплаты,
        dtl.order_date датаЗаказа, dtl.order_number номерЗаказа,
        dtl.sell_date AS датаПродажи,
        dtl.sell_number AS номерПродажи,
        dtl.amount_sell AS суммаПродажи,
        dtl.amount_pay AS оплатаПоДок,
        sum(dtl.amount_pay) OVER (PARTITION BY dtl.sell_key ORDER BY dtl.pay_date) AS оплаченоВсегоЗаРН
    FROM (
        SELECT order_key
        FROM (
            SELECT DISTINCT
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                orders.ref_key AS order_key,
                'ПКО' AS source
            FROM t_one_doc_cash_warrant_receipt AS main -- Document_ПриходныйКассовыйОрдер
                INNER JOIN t_one_doc_cash_warrant_receipt_details AS sub -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.base_key = orders.ref_key
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
            UNION ALL
            SELECT DISTINCT
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                orders.ref_key AS order_key,
                'ППВ' AS source
            FROM t_one_doc_cash_order_receipt AS main -- Document_ПлатежноеПоручениеВходящее
                INNER JOIN t_one_doc_cash_order_receipt_details AS sub -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.deal_key  = orders.ref_key
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
            ) AS t
        GROUP BY order_key
        HAVING count(*) > 1
        ) AS qnt
        LEFT JOIN
            (
            SELECT DISTINCT
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                sell.doc_date AS sell_date,
                sell.doc_number AS sell_number,
                sell.document_amount AS amount_sell,
                sub.amount_of_payment AS amount_pay,
                orders.ref_key AS order_key,
                sell.ref_key AS sell_key,
                'ПКО' AS source
            FROM t_one_doc_cash_warrant_receipt AS main -- Document_ПриходныйКассовыйОрдер
                INNER JOIN t_one_doc_cash_warrant_receipt_details AS sub -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.base_key = orders.ref_key
                INNER JOIN t_one_doc_sale_of_goods_services AS sell
                    ON sell.deal = orders.ref_key
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted
                AND sell.is_management AND sell.posted
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
            UNION ALL
            SELECT DISTINCT
                main.doc_date AS pay_date,
                main.doc_number AS pay_number,
                orders.doc_date AS order_date,
                orders.doc_number AS order_number,
                sell.doc_date AS sell_date,
                sell.doc_number AS sell_number,
                sell.document_amount AS amount_sell,
                sub.amount_of_payment AS amount_pay,
                orders.ref_key AS order_key,
                sell.ref_key AS sell_key,
                'ППВ' AS source
            FROM t_one_doc_cash_order_receipt AS main -- Document_ПлатежноеПоручениеВходящее
                INNER JOIN t_one_doc_cash_order_receipt_details AS sub -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                    ON main.ref_key = sub.ref_key
                INNER JOIN t_one_doc_buyer_order AS orders -- Document_ЗаказПокупателя
                    ON sub.deal_key  = orders.ref_key
                INNER JOIN t_one_doc_sale_of_goods_services AS sell
                    ON sell.deal = orders.ref_key
            WHERE main.doc_date >= '01.01.2023'::date
                AND main.posted
                AND sell.is_management AND sell.posted
                AND orders.account_key = '163e78bd-2b01-11eb-80fc-001dd8b72b55' -- Сотрудники Офис
        ) AS dtl
        ON qnt.order_key = dtl.order_key
    ORDER BY dtl.order_key, pay_date, pay_number
    ;

    COMMENT ON VIEW v_one_order_with_multi_payments IS 'Заказы, по которым есть несколько оплат';

    GRANT SELECT ON TABLE v_one_order_with_multi_payments TO user_prestige;
