# Description: Сохраняет данные о задолженности в Excel.
# Дебиторка по клиентам.

import os
import sys
import numpy as np
from Catalog.catContractsCounterparties import main_cat_contracts_counterparties_async
from Catalog.catCounterparties import main_cat_counterparties_async
from CreateAndRunSQLScripts import create_views
from Document.docAccRegReciprocalSettlementsDetails import main_doc_reciprocal_settlements_details_async
# для отлавливания номера строки ошибки
import traceback
sys.path.append(os.path.dirname(__file__))
import asyncio
import aiohttp
import aiofiles
import json
from aiohttp import FormData
from datetime import datetime, date
from dateutil.parser import parse
from pathvalidate import sanitize_filename
import pandas as pd
import xlsxwriter
from dateutil import parser
from time import strftime
from LoadManagerBonusToExcel import managers_chatid_name, managers_name_uuid
from logger_prestige import get_logger
from async_Postgres import (
    sql_to_dataframe_async,
    send_telegram,
    TELEGRAM_TOKEN,
    chatid_rasim,
)

# Очистка консоли (работает как в Windows, так и в Unix-подобных системах)
os.system('cls' if os.name == 'nt' else 'clear')

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
DATE_FIRST = parser.parse("01.04.2025", dayfirst=True).date()
SAVE_TO_FILE = "Config.xlsx"

# Установите опцию отображения максимального количества столбцов
pd.set_option('display.max_columns', None)


def head_format(wb):
    return wb.add_format({"bg_color": "#778899", "bold": True, "align": "center"})


def segment_format(wb):
    return wb.add_format(
        {
            "bg_color": "#808080",  # color - gray
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 14,
        }
    )


def customer_format(wb):
    return wb.add_format(
        {
            "bg_color": "#C0C0C0",  # color - silver
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 13,
        }
    )


def organization_format(wb):
    return wb.add_format(
        {
            "bg_color": "#f2ebf1",  # color - lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 11,
        }
    )


def contract_format(wb):
    return wb.add_format(
        {
            "bg_color": "#e6e6fa",  # color - light lavender
            "num_format": "# ### ##0.00;[Red]# ### ##0.00",
            "bold": True,
            "font_size": 10,
        }
    )


def date_format(wb):
    return wb.add_format({"num_format": "dd.mm.yyyy"})


def number_format(wb):
    return wb.add_format({"num_format": "# ### ##0.00;[Red]# ### ##0.00"})


def int_format(wb):
    return wb.add_format({"num_format": "# ### ##0;[Red]# ### ##0"})


def date_to_str(period):
    return datetime.strftime(period, "%Y%m%d")


# отправка файла в телеграм
async def telegram_bot_send_document(path_doc, chat_id):
    chat_id = str(chat_id)
    logger.info(f"файл для отправки: {path_doc}")
    async with aiofiles.open(path_doc, "rb") as doc:
        data = FormData()
        data.add_field("chat_id", chat_id)
        data.add_field("document", doc, filename=path_doc)

        async with aiohttp.ClientSession() as session:
            url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendDocument"
            async with session.post(url, data=data) as response:
                logger.info(response.status)
                content = await response.text()
                result = json.loads(content)
                filename = {result.get("result").get("document").get("file_name")}
                logger.info(f"filename: {filename}")
                await send_telegram(f"Вам отправлен файл: {filename}", chat_id)

    if chat_id != str(chatid_rasim):
        await telegram_bot_send_document(os.path.abspath(path_doc), str(chatid_rasim))

    return response.status


def cell_address(row, col):
    # Преобразуем номер колонки в букву
    column_letter = ""
    while col >= 0:
        column_letter = chr(col % 26 + ord("A")) + column_letter
        col = col // 26 - 1
    # Возвращаем адрес ячейки в формате Excel
    return f"{column_letter}{row + 1}"


def set_column_width_cm(worksheet, col_num, width_in_cm):
    cm_to_pixels = 3.78
    width_in_pixels = width_in_cm * cm_to_pixels
    worksheet.set_column(col_num, col_num, width_in_pixels)


async def colored_range(ws, row, cell_format):
    # Закрашиваем диапазон ячеек
    for col in range(6):
        cell = cell_address(row, col)
        ws.write_blank(cell, None, cell_format)


def get_sql():
    return f"""
        WITH
        -- документы перемещения денежных средств. в отчет не выводим, чтобы не увеличивал сумму по оплатам
        -- не используем, т.к. сумма указывается в колонке оплата как приход по одному договору и расход по другому договору, итого = 0
        except_doc_move AS (
            SELECT doc.ref_key
            FROM (
                SELECT
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_order_receipt main  -- Document_ПлатежноеПоручениеВходящее_РасшифровкаПлатежа
                INNER JOIN t_one_doc_cash_order_receipt_details AS sub
                    ON main.ref_key = sub.ref_key
                WHERE '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
                UNION ALL
                SELECT
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_order_expense main  -- Document_ПлатежноеПоручениеИсходящее_РасшифровкаПлатежа
                INNER JOIN t_one_doc_cash_order_expense_details sub
                    ON main.ref_key = sub.ref_key
                WHERE '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
                UNION ALL
                SELECT
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_warrant_receipt main  -- Document_ПриходныйКассовыйОрдер_РасшифровкаПлатежа
                INNER JOIN t_one_doc_cash_warrant_receipt_details sub
                    ON main.ref_key = sub.ref_key
                WHERE '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
                UNION ALL
                SELECT
                    main.ref_key,
                    main.doc_date,
                    main.doc_number,
                    sub.contract_key contract_key,
                    sub.cash_flow_item sub_cash_flow_item
                FROM t_one_doc_cash_warrant_expense main  -- Document_РасходныйКассовыйОрдер_РасшифровкаПлатежа
                INNER JOIN t_one_doc_cash_warrant_expense_details sub
                    ON main.ref_key = sub.ref_key
                WHERE '2740a4a6-f88e-11ed-8184-001dd8b740bc' IN (sub.cash_flow_item, main.cash_flow_item)  -- Перемещение денежных средств
            ) doc
            INNER JOIN v_one_manager_counterparty_contracts_segments s
                ON doc.contract_key = s.contract_key
        ),
        register AS (
            SELECT
                req.dtperiod::date AS doc_date,
                CASE
                    WHEN recordtype = 'Receipt' THEN sum(amount)
                    ELSE -sum(amount)
                END doc_sum,
                CASE
                    WHEN req.recorder_type NOT IN (
                        'StandardODATA.Document_ПлатежноеПоручениеВходящее',
                        'StandardODATA.Document_ПлатежноеПоручениеИсходящее',
                        'StandardODATA.Document_ПриходныйКассовыйОрдер',
                        'StandardODATA.Document_РасходныйКассовыйОрдер',
                        'StandardODATA.Document_КорректировкаДолга'
                    ) THEN sum(req.amount)
                    ELSE 0
                END sale,
                CASE
                    WHEN req.recorder_type IN (
                        'StandardODATA.Document_ПлатежноеПоручениеВходящее',
                        'StandardODATA.Document_ПлатежноеПоручениеИсходящее',
                        'StandardODATA.Document_ПриходныйКассовыйОрдер',
                        'StandardODATA.Document_РасходныйКассовыйОрдер',
                        'StandardODATA.Document_КорректировкаДолга'
                    ) THEN -sum(req.amount)
                    ELSE 0
                END pay,
                counterparty_key,
                is_accounting,
                currency_key
            FROM (
                SELECT 
                    *
                FROM t_one_doc_acc_reg_reciprocal_settlements_details
                WHERE organization_key NOT IN (
                    '92a5f889-1eef-11ed-8143-001dd8b72b55',  -- ТОВ "А-НОВА"
                    'b8a54612-9351-11ed-815e-001dd8b72b55',  -- ТОВ "ГК ПРЕСТИЖ"
                    '936f461a-48de-11eb-80fd-001dd8b72b55',  -- ТОВ "КЕНТОН УКРАИНА"
                    '3d7e2ad1-8ac8-11e6-80c4-c936aa9c817c'  -- ТОВ "ПРЕСТИЖ ПРОДУКТ-К"
                    )
                ) AS req  -- AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
            WHERE recorder NOT IN (
                -- Exclude debt correction documents with zero net debt
                SELECT main.ref_key
                FROM t_one_doc_debt_correction AS main  -- Document_КорректировкаДолга
                INNER JOIN t_one_doc_debt_correction_debt_amount AS sub  -- Document_КорректировкаДолга_СуммыДолга
                    ON main.ref_key = sub.ref_key
                WHERE main.posted
                    -- берем только УправленческийУчет (NOT is_management, т.к. WHERE recorder NOT IN)
                    AND (NOT main.is_management OR main.operation_type = 'ПроведениеВзаимозачета')
                GROUP BY main.ref_key
                HAVING sum(CASE
                            WHEN sub.debt_type = 'Дебиторская' THEN sub.amount
                            ELSE -sub.amount
                        END) = 0
                UNION ALL
                -- Не отображаем виртуальные оплаты клиента с организации на организацию
                -- кассир делает для закрытия висящего долга с организации на организацию
                SELECT ref_key
                FROM except_doc_move
            )
            GROUP BY
                req.dtperiod::date,
                req.recorder_type,
                recordtype,
                is_accounting,
                counterparty_key,
                currency_key
            HAVING abs(CASE
                        WHEN recordtype = 'Receipt' THEN sum(amount)
                        ELSE -sum(amount)
                    END) >= 1
        ),
        segment AS (
            SELECT DISTINCT
                COALESCE(trim(segment), '') AS segment,
                COALESCE(trim(manager), '') AS manager,
                COALESCE(trim(customer), '') AS customer,
                COALESCE(trim(currency_name), '') AS currency_name,
                customer_key,
                currency_key,
                manager_key
            FROM v_one_manager_counterparty_contracts_segments
        ),
        add_name AS (
            SELECT
                req.*,
                seg.segment,
                seg.manager,
                seg.customer,
                seg.currency_name,
                seg.manager_key
            FROM register AS req
            LEFT JOIN segment AS seg
                ON req.counterparty_key = seg.customer_key
                AND req.currency_key = seg.currency_key
        ),
        balance_first AS (
            SELECT
                0 AS sort,
                segment,
                currency_name,
                manager,
                is_accounting,
                customer,
                $2::date - 1 AS doc_date,
                0 sale,
                0 pay,
                sum(doc_sum) doc_sum,
                manager_key
            FROM add_name
            WHERE
                manager_key = $1
                AND doc_date < $2::date
            GROUP BY
                segment,
                currency_name,
                manager, is_accounting,
                customer,
                manager_key
        ),
        balance_middle AS (
            SELECT
                1 AS sort,
                manager, is_accounting,
                segment,
                currency_name,
                customer,
                doc_date,
                sum(sale) AS sale,
                sum(pay) AS pay,
                sum(doc_sum) AS doc_sum,
                manager_key
            FROM add_name
            WHERE
                manager_key = $1
                AND doc_date >= $2::date
            GROUP BY
                manager, is_accounting,
                segment,
                currency_name,
                customer,
                doc_date,
                manager_key
        ),
        balance AS (
            SELECT
                sort,
                manager, is_accounting,
                segment,
                currency_name,
                customer,
                doc_date,
                0 AS sale,
                0 AS pay,
                doc_sum,
                manager_key
            FROM balance_first
            UNION ALL
            SELECT
                sort,
                manager, is_accounting,
                segment,
                currency_name,
                customer,
                doc_date,
                sale,
                pay,
                doc_sum,
                manager_key
            FROM balance_middle
        ),
        final_balance AS (
            SELECT
                sort,
                manager, is_accounting,
                segment,
                currency_name,
                customer,
                doc_date,
                sale,
                pay,
                count(*) OVER (PARTITION BY
                    segment,
                    currency_name,
                    manager, is_accounting,
                    customer
                ) AS row_count,
                sum(doc_sum) OVER (PARTITION BY
                    manager, is_accounting,
                    segment,
                    currency_name,
                    customer
                    ORDER BY doc_date
                ) AS customer_sum,
                manager_key
            FROM balance
        )
        SELECT
        --    sort,
            manager, is_accounting,
            segment,
            currency_name,
            customer,
            doc_date,
            sale,
            pay,
            customer_sum,
        --    row_count,
            CASE
                WHEN doc_date = max(doc_date) OVER (PARTITION BY
                        segment,
                        currency_name,
                        manager,
                        is_accounting,
                        customer
                    ) AND
                    sort = max(sort) OVER (PARTITION BY
                        segment,
                        currency_name,
                        manager,
                        is_accounting,
                        customer
                    )
                    THEN
                        customer_sum
                ELSE
                    0
            END last_customer_sum,
            manager_key
        FROM final_balance
        WHERE NOT (row_count = 1 AND abs(customer_sum) < 1)
        ORDER BY
            manager, is_accounting,
            segment,
            currency_name,
            customer,
            sort,
            doc_date
;
"""

# получение данных из представления v_one_customer_balance_no_date
# и запись их в DataFrame
async def get_data_from_view(manager_key: str, date_first: date):
    try:
        sql = get_sql()
        args = [manager_key, date_first]
        args = [arg for arg in args if arg is not None]
        df = await sql_to_dataframe_async(sql, *args)
        if df.empty:
            logger.info(f"Данные не найдены: {args}")
            return df

        # Преобразование колонок в нужные форматы
        df["doc_date"] = pd.to_datetime(df["doc_date"], errors="coerce")  # дата документа
        df["customer_sum"] = df["customer_sum"].astype(float)  # сумма долга по клиенту
        df["sale"] = df["sale"].astype(float)  # поставка/возврат
        df["pay"] = df["pay"].astype(float)  # оплата
        df = df.fillna("")
        return df
    except Exception as e:
        logger.error(f"Error in get_data_from_view function: {e}\n{traceback.format_exc()}")
        return pd.DataFrame()


async def get_sum_form_df(df, column_name, filter_column=None, filter_value=None):
    """
    Parameters:
    df - DataFrame,
    column_name - название столбца/ов для группировки.
        По нему будет считаться сумма. Может быть несколько столбцов(список),
    filter_column - название столбца для фильтрации,
    filter_value - значение для фильтрации
    """
    df.loc[:, column_name] = df[column_name].fillna("")  # Заполняем пустые значения в column_name пустой строкой
    if filter_column:
        df = df[
            df[filter_column] == filter_value
            ].copy()  # Используем .copy() для создания копии DataFrame

    # Группировка и суммирование
    group_df = df[df['is_max_date']== True].groupby([*column_name])["customer_sum"].sum().reset_index()
    return group_df


# сумма по статьям до текущей даты
async def get_data_before(df, columns_by_group):
    df_sum = await get_sum_form_df(df, columns_by_group)
    return df_sum


# сумма по статьям между датами
async def get_data_between(df, columns_by_group):
    # Разделяем данные на продажи и оплаты
    df_sale = df[df["sale"] != 0].copy()
    segment_df_sale = await get_sum_form_df(df_sale, columns_by_group)
    segment_df_sale.rename(columns={"customer_sum": "sale"}, inplace=True)

    df_pay = df[df["pay"] != 0].copy()
    segment_df_pay = await get_sum_form_df(df_pay, columns_by_group)
    segment_df_pay.rename(columns={"customer_sum": "pay"}, inplace=True)

    merged_df = segment_df_sale.merge(segment_df_pay, on=[*columns_by_group], how="outer")
    # Удаление дублирующихся колонок
    merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]
    return merged_df


async def add_head_to_excel(wb, sheet):
    # Создаём формат с переносом текста
    sheet.set_row(0, 20)  # установка высоты строки
    combined_format = head_format(wb)
    combined_format.set_text_wrap()
    sheet.write("A1", "сегмент/клиент", head_format(wb))
    sheet.write("B1", "валюта", head_format(wb))
    sheet.write("C1", "датаДок", head_format(wb))
    sheet.write("D1", "продажа/возврат", head_format(wb))
    sheet.write("E1", "оплата", head_format(wb))
    sheet.write("F1", "ОбщийДолг", head_format(wb))


async def group_and_sum(df, columns:list, filter_value:bool = False):
    # Фильтруем строки для customer_sum, но не для sale и pay
    filtered_df = df.copy()

    # Для customer_sum используем только строки с is_max_date == True
    customer_sum_df = df.groupby(columns).agg({'last_customer_sum': 'sum'})
    customer_sum_df.rename(columns={"last_customer_sum": "customer_sum"}, inplace=True)

    # Для sale и pay используем все строки
    sale_pay_df = filtered_df.groupby(columns).agg({
        'sale': 'sum',
        'pay': 'sum'
    })

    # Объединяем результаты
    result = pd.merge(customer_sum_df, sale_pay_df, left_index=True, right_index=True)
    result = result.reset_index()
    return result

async def create_excel(df):
    try:
        # Создаем новый Excel файл и добавляем лист
        wb = xlsxwriter.Workbook(SAVE_TO_FILE, {'nan_inf_to_errors': True})
        sheet = wb.add_worksheet()
        sheet.set_column("H:H", 15)

        # создаем шапку
        await add_head_to_excel(wb, sheet)
        columns = ['segment', 'currency_name']
        segment_df = await group_and_sum(df, columns, True)
        segment_df = segment_df.sort_values(by=['segment'])
        row_number = 2  # Начинаем с 2 строки, т.к. 1 строка - наименование столбцов
        for index, row in segment_df.iterrows():
            segment = row.get("segment")
            currency = row.get("currency_name")
            sheet.write(f"A{row_number}", segment, segment_format(wb))
            sheet.write(f"B{row_number}", currency, segment_format(wb))
            sheet.write_blank(f"C{row_number}", None, segment_format(wb))
            sheet.write(f"D{row_number}", row.get("sale"), segment_format(wb))
            sheet.write(f"E{row_number}", row.get("pay"), segment_format(wb))
            sheet.write(f"F{row_number}", row.get("customer_sum"), segment_format(wb))

            row_number += 1  # уровень сегмента
            columns = ['segment', 'currency_name', 'customer']
            customer_df = await group_and_sum(df, columns)
            filter_customer_df = customer_df[(customer_df['segment'] == segment)
                                             & (customer_df['currency_name'] == currency)]
            for ind1, row1 in filter_customer_df.iterrows():
                customer = row1.get("customer")
                sheet.write(f"A{row_number}", customer, customer_format(wb))
                sheet.write_blank(f"B{row_number}", currency, customer_format(wb))
                sheet.write_blank(f"C{row_number}", None, customer_format(wb))
                sheet.write(f"D{row_number}", row1.get("sale"), customer_format(wb))
                sheet.write(f"E{row_number}", row1.get("pay"), customer_format(wb))
                sheet.write(f"F{row_number}", row1.get("customer_sum"), customer_format(wb))

                # Устанавливаем уровень группировки для клиента.
                # "collapsed": False - не сворачивать группу по умолчанию
                # sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": False})
                row_number += 1  # уровень клиента

                # Добавляем группировку по is_accounting (учет)
                columns = ['segment', 'currency_name', 'customer', 'is_accounting']
                accounting_df = await group_and_sum(df, columns)
                filter_accounting_df = accounting_df[(accounting_df['segment'] == segment)
                                                  & (accounting_df['currency_name'] == currency)
                                                  & (accounting_df['customer'] == customer)
                                                  ]
                for ind2, row2 in filter_accounting_df.iterrows():
                    is_accounting = row2.get("is_accounting")
                    doc_info = f"{is_accounting.upper()}/{currency}".replace(" ", "")
                    sheet.write(f"A{row_number}", None, organization_format(wb))
                    sheet.write(f"B{row_number}", doc_info, organization_format(wb))
                    sheet.write_blank(f"C{row_number}", None, organization_format(wb))
                    sheet.write(f"D{row_number}", row2.get("sale"), organization_format(wb))
                    sheet.write(f"E{row_number}", row2.get("pay"), organization_format(wb))
                    sheet.write(f"F{row_number}", row2.get("customer_sum"), organization_format(wb))

                    # Устанавливаем уровни группировки для учета
                    # sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": False})
                    # sheet.set_row(row_number - 1, None, None, {"level": 2, "collapsed": False})
                    row_number += 1  # уровень учета

                    # Получаем детализацию после учета
                    filter_details_df = df[(df['segment'] == segment)
                                             & (df['currency_name'] == currency)
                                             & (df['customer'] == customer)
                                             & (df['is_accounting'] == is_accounting)
                                             ]
                    filter_details_df = filter_details_df.sort_values(by=['doc_date'])

                    # Проверяем, что есть детализация для отображения
                    for ind4, row4 in filter_details_df.iterrows():
                        doc_info = f"{row4.get('recorder_type', '')} {row4.get('doc_number', '')}".strip()
                        sheet.write(f"B{row_number}", doc_info)
                        sheet.write(f"C{row_number}", row4.get("doc_date"), date_format(wb))
                        sheet.write(f"D{row_number}", row4.get("sale"), number_format(wb))
                        sheet.write(f"E{row_number}", row4.get("pay"), number_format(wb))
                        sheet.write(f"F{row_number}", row4.get("customer_sum"), number_format(wb))

                        # Группировка строк для детализации (свернута по умолчанию)
                        # уровень сегмента
                        # sheet.set_row(row_number - 1, None, None, {"level": 1, "collapsed": True})
                        # уровень клиента
                        # sheet.set_row(row_number - 1, None, None, {"level": 2, "collapsed": True})
                        # уровень учета
                        # sheet.set_row(row_number - 1, None, None, {"level": 3, "collapsed": True})
                        row_number += 1  # уровень детализации

            row_number += 1  # Пустая строка между сегментами

            # Устанавливаем уровень группировки для сегмента (верхний уровень)
            sheet.set_row(row_number - 1, None, None, {"level": 0, "collapsed": False})

        sheet.freeze_panes(1, 0)  # Закрепление первой строки

        # Устанавливаем ширину столбцов вместо autofit (который не поддерживается в xlsxwriter)
        sheet.set_column('A:A', 10)  # Сегмент/клиент
        sheet.set_column('B:B', 10)  # Договор/валюта
        sheet.set_column('C:C', 12)  # ДатаДок
        sheet.set_column('D:E', 15)  # Продажа/возврат и Оплата
        sheet.set_column('F:F', 15)  # ОбщийДолг

        sheet.hide_zero()  # Скрытие нулевых значений

        # Развернуть все уровни по умолчанию
        sheet.set_default_row(hide_unused_rows=False)

        # Устанавливаем настройки группировки
        sheet.outline_settings(symbols_below=False, auto_style=True)

        sheet.set_vba_name("Sheet1")
        wb.close()  # Закрываем и сохраняем файл

        logger.info(f"Файл {SAVE_TO_FILE} создан.")
        return True
    except Exception as e:
        logger.error(f"Error in create_excel function: {e}\n{traceback.format_exc()}")
        return False


async def is_manager(chatid):
    managers_name = managers_chatid_name.get(chatid)
    logger.info(f"managers_name: {managers_name}")
    return managers_name if managers_name else None


# contol date_text is date
async def is_date(parsed_date):
    try:
        if not parsed_date:
            return parse('01.01.1900', dayfirst=True).date()
        if isinstance(parsed_date, date):
            return parsed_date
        else:
            return parse(parsed_date, dayfirst=True).date()
    except ValueError:
        return None


async def load_data(manager_key, date_first: date):
    global SAVE_TO_FILE
    try:
        await send_telegram("Ожидайте. Вам будет выслан файл с балансом по клиентам.", sys.argv[-1])

        await main_cat_counterparties_async()

        # Catalog_ДоговорыКонтрагентов
        await main_cat_contracts_counterparties_async()

        # AccumulationRegister_ВзаиморасчетыСКонтрагентами_RecordType
        await main_doc_reciprocal_settlements_details_async()
        await create_views()

        # Используем только фильтр по manager_key без фильтрации по дате
        date_first = await is_date(date_first)
        df = await get_data_from_view(manager_key, date_first)
        if df.empty:
            logger.info("Error: Unable to load data to DataFrame.")
            await send_telegram("Ошибка: Не удалось загрузить данные.", sys.argv[-1])
            return None

        manager_name = df.get("manager").iloc[0]
        if manager_name is None:
            await send_telegram("Error: Не удалось получить имя менеджера.", sys.argv[-1])
            return None

        SAVE_TO_FILE = f"{manager_name}_balance_{date_to_str(datetime.today())}_{strftime('%H%M')}.xlsx"
        SAVE_TO_FILE = sanitize_filename(SAVE_TO_FILE)
        SAVE_TO_FILE = os.path.join(os.path.dirname(__file__), SAVE_TO_FILE)
        return await create_excel(df)

    except Exception as e:
        logger.error(f"Error in load_data function: {e}")
        await send_telegram(f"Произошла ошибка: {e}", sys.argv[-1])

    return None


async def main_debt_to_excel_1с_async():
    global DATE_FIRST
    # добавим аргументы для запуска функции
    logger.info(f"Start {FILE_NAME}")
    cur_dir = os.path.dirname(__file__)
    dir_to_python = os.path.join(cur_dir, ".venv", "Scripts", "python.exe")
    dir_to_script = os.path.join(cur_dir, FILE_NAME)
    if len(sys.argv) == 1:
        sys.argv = [f"{dir_to_python} {dir_to_script}", '01.04.2025', "490323168"]
        # sys.argv = [f"{dir_to_python} {dir_to_script}", None, "490323168"]

    logger.info(sys.argv[1:]) # добавим аргументы для запуска функции
    # Проверяем, что передан ID менеджера
    if len(sys.argv) < 2:
        err = "Необходимо указать ID менеджера"
        logger.info(err)
        await send_telegram(err, sys.argv[-1] if len(sys.argv) > 1 else "490323168")
        sys.exit(0)

    date_first = await is_date(sys.argv[1])
    DATE_FIRST = date_first if date_first else None
    chatid = sys.argv[2]
    manager_name = await is_manager(chatid)
    manager_key = managers_name_uuid.get(manager_name)
    if not manager_key:
        err = "Вы не зарегистрированы"
        logger.info(err)
        await send_telegram(err, chatid)
        sys.exit(0)

    result = await load_data(manager_key, date_first)

    if result:
        await telegram_bot_send_document(SAVE_TO_FILE, chatid)

    logger.info(f"End {FILE_NAME}: {result}")


def run_main():
    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    loop.run_until_complete(main_debt_to_excel_1с_async())


if __name__ == '__main__':
    logger.info(f"Start {datetime.now()}")
    run_main()
    logger.info(f"End {datetime.now()}")
