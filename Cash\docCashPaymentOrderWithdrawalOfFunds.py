import asyncio
import os

from async_Postgres import async_save_pg, create_model_async
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_doc_cash_withdrawal_of_funds'
DOCUMENT = "Document_ПлатежныйОрдерСписаниеДенежныхСредств"
SELECT_COLUMNS = "Ref_Key,DataVersion,DeletionMark,Number,Date,Posted,Организация_Key,СчетОрганизации_Key," \
                 "Контрагент_Key,СуммаДокумента,ВалютаДокумента_Key,ОтражатьВУправленческомУчете," \
                 "СтатьяДвиженияДенежныхСредств_Key,О<PERSON>лачено,НазначениеПлатежа,ВалютаВзаиморасчетовРаботника_Key," \
                 "СчетБанк_Key,ВидОперации,Комментарий,ДоговорКонтрагента_Key"

SQL_CREATE_TABLE = f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_number varchar(15) NULL,  -- Number
        doc_date timestamp NOT NULL,  -- Date
        posted bool NOT NULL DEFAULT false,  -- Posted
        amount numeric(10, 4) NOT NULL DEFAULT 0,  -- СуммаДокумента
        paid bool NOT NULL DEFAULT false,  -- Оплачено
        operation_type varchar(250) NOT NULL,  -- ВидОперации
        assignments varchar(50) NULL,  -- НазначениеПлатежа
        deletion_mark bool NOT NULL DEFAULT false,  -- DeletionMark
        is_management bool NOT NULL DEFAULT false,  -- ОтражатьВУправленческомУчете
        dataversion varchar(15) NULL,  -- DataVersion
        a_comment varchar NULL,  -- Комментарий
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        organization_account_key varchar(50) NULL,  -- СчетОрганизации_Key
        account_key varchar(50) NULL,  -- Контрагент_Key
        currency_key varchar(50) NULL,  -- ВалютаДокумента_Key
        cash_flow_item varchar(50) NULL,  -- СтатьяДвиженияДенежныхСредств_Key
        worker_settlement_currency_key varchar(50) NULL,  -- ВалютаВзаиморасчетовРаботника_Key
        account_bank_key varchar(50) NULL,  -- СчетБанк_Key
        contract_key varchar(50) NULL,  -- Договор_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
    COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
    COMMENT ON COLUMN {TABLE_NAME}.operation_type IS 'ВидОперации';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_account_key IS 'СчетОрганизации_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'СуммаДокумента';
    COMMENT ON COLUMN {TABLE_NAME}.currency_key IS 'ВалютаДокумента_Key';
    COMMENT ON COLUMN {TABLE_NAME}.is_management IS 'ОтражатьВУправленческомУчете';
    COMMENT ON COLUMN {TABLE_NAME}.cash_flow_item IS 'СтатьяДвиженияДенежныхСредств_Key';
    COMMENT ON COLUMN {TABLE_NAME}.paid IS 'Оплачено';
    COMMENT ON COLUMN {TABLE_NAME}.assignments IS 'НазначениеПлатежа';
    COMMENT ON COLUMN {TABLE_NAME}.worker_settlement_currency_key IS 'ВалютаВзаиморасчетовРаботника_Key';
    COMMENT ON COLUMN {TABLE_NAME}.account_bank_key IS 'СчетБанк_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_key IS 'Договор_Key';
'''


async def sql_insert(maket):
    sql = f'''
    INSERT INTO {TABLE_NAME}
    (
        ref_key,
        dataversion,
        deletion_mark,
        doc_number,
        doc_date,
        posted,
        organization_key,
        organization_account_key,
        account_key,
        amount,
        currency_key,
        is_management,
        cash_flow_item,
        paid,
        assignments,
        worker_settlement_currency_key,
        account_bank_key,
        operation_type,
        a_comment,
        contract_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        doc_number = EXCLUDED.doc_number,
        doc_date = EXCLUDED.doc_date,
        posted = EXCLUDED.posted,
        organization_key = EXCLUDED.organization_key,
        organization_account_key = EXCLUDED.organization_account_key,
        account_key = EXCLUDED.account_key,
        amount = EXCLUDED.amount,
        currency_key = EXCLUDED.currency_key,
        is_management = EXCLUDED.is_management,
        cash_flow_item = EXCLUDED.cash_flow_item,
        paid = EXCLUDED.paid,
        assignments = EXCLUDED.assignments,
        worker_settlement_currency_key = EXCLUDED.worker_settlement_currency_key,
        account_bank_key = EXCLUDED.account_bank_key,
        operation_type = EXCLUDED.operation_type,
        a_comment = EXCLUDED.a_comment,
        contract_key = EXCLUDED.contract_key
    '''
    return sql.replace("'", "")


async def main_doc_cash_payment_order_withdrawal_of_funds_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True)
    maket = await create_model_async(20)
    sql = await sql_insert(maket)
    sql = sql.replace("$5,", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_cash_payment_order_withdrawal_of_funds_async())
