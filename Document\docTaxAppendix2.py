import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_tax_appendix_2"
DOCUMENT = "Document_Приложение2КНалоговойНакладной"
SELECT_COLUMNS = (
    "Ref_Key,DataVersion,DeletionMark,Number,Date,Posted,ВалютаДокумента_Key,ДокументОснование,"
    "ДокументОснование_Type,Комментарий,Контрагент_Key,КратностьВзаиморасчетов,КурсВзаиморасчетов,"
    "НалоговаяНакладная_Key,Организация_Key,СуммаВключаетНДС,СуммаДокумента,СуммаНДСДокумента,УчитыватьНДС,"
    "Сделка,Сделка_Type"
)

SQL_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            dataversion varchar(50) NULL,  -- DataVersion
            deletionmark boolean NOT NULL DEFAULT FALSE,  -- DeletionMark
            doc_number varchar(25) NULL,  -- Number
            doc_date timestamp NOT NULL,  -- Date
            posted boolean NOT NULL DEFAULT FALSE,  -- Posted
            document_amount numeric(10,4) NOT NULL DEFAULT 0,  -- СуммаДокумента
            amount_vat_document numeric(10,4) NOT NULL DEFAULT 0,  -- СуммаНДСДокумента
            consider_vat boolean NOT NULL DEFAULT FALSE,  -- УчитыватьНДС
            a_comment varchar(350) NULL,  -- Комментарий
            multiplicity_of_mutual_settlements numeric(10,4) NOT NULL DEFAULT 0,  -- КратностьВзаиморасчетов
            settlement_rate numeric(10,4) NOT NULL DEFAULT 0,  -- КурсВзаиморасчетов
            amount_includes_vat boolean NOT NULL DEFAULT FALSE,  -- СуммаВключаетНДС
            ref_key varchar(50) NULL,  -- Ref_Key
            document_base_key varchar(50) NOT NULL,  -- Сделка
            document_base_type varchar(250) NOT NULL,  -- Сделка_Type 
            deal_key varchar(50) NOT NULL,  -- ДокументОснование
            deal_type varchar(250) NOT NULL,  -- ДокументОснование_Type
            document_currency_key varchar(50) NULL,  -- ВалютаДокумента_Key
            account_key varchar(50) NULL,  -- Контрагент_Key
            tax_invoice_key varchar(50) NULL,  -- НалоговаяНакладная_Key
            organization_key varchar(50) NULL,  -- Организация_Key
            CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
        );

        COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';
        COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
        COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
        COMMENT ON COLUMN {TABLE_NAME}.deletionmark IS 'DeletionMark';
        COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Number';
        COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Date';
        COMMENT ON COLUMN {TABLE_NAME}.posted IS 'Posted';
        COMMENT ON COLUMN {TABLE_NAME}.document_currency_key IS 'ВалютаДокумента_Key';
        COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
        COMMENT ON COLUMN {TABLE_NAME}.account_key IS 'Контрагент_Key';
        COMMENT ON COLUMN {TABLE_NAME}.multiplicity_of_mutual_settlements IS 'КратностьВзаиморасчетов';
        COMMENT ON COLUMN {TABLE_NAME}.settlement_rate IS 'КурсВзаиморасчетов';
        COMMENT ON COLUMN {TABLE_NAME}.tax_invoice_key IS 'НалоговаяНакладная_Key';
        COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
        COMMENT ON COLUMN {TABLE_NAME}.amount_includes_vat IS 'СуммаВключаетНДС';
        COMMENT ON COLUMN {TABLE_NAME}.document_amount IS 'СуммаДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.amount_vat_document IS 'СуммаНДСДокумента';
        COMMENT ON COLUMN {TABLE_NAME}.consider_vat IS 'УчитыватьНДС';
        COMMENT ON COLUMN {TABLE_NAME}.document_base_key IS 'ДокументОснование';
        COMMENT ON COLUMN {TABLE_NAME}.document_base_type IS 'ДокументОснование_Type';
        COMMENT ON COLUMN {TABLE_NAME}.deal_key IS 'Сделка';
        COMMENT ON COLUMN {TABLE_NAME}.deal_type IS 'Сделка_Type';
    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        ref_key,
        dataversion,
        deletionmark,
        doc_number,
        doc_date,
        posted,
        document_currency_key,
        document_base_key,
        document_base_type,
        a_comment,
        account_key,
        multiplicity_of_mutual_settlements,
        settlement_rate,
        tax_invoice_key,
        organization_key,
        amount_includes_vat,
        document_amount,
        amount_vat_document,
        consider_vat,
        deal_key,
        deal_type
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        dataversion = EXCLUDED.dataversion,
        deletionmark = EXCLUDED.deletionmark,
        doc_number = EXCLUDED.doc_number,
        doc_date = EXCLUDED.doc_date,
        posted = EXCLUDED.posted,
        document_currency_key = EXCLUDED.document_currency_key,
        document_base_key = EXCLUDED.document_base_key,
        document_base_type = EXCLUDED.document_base_type,
        a_comment = EXCLUDED.a_comment,
        account_key = EXCLUDED.account_key,
        multiplicity_of_mutual_settlements = EXCLUDED.multiplicity_of_mutual_settlements,
        settlement_rate = EXCLUDED.settlement_rate,
        tax_invoice_key = EXCLUDED.tax_invoice_key,
        organization_key = EXCLUDED.organization_key,
        amount_includes_vat = EXCLUDED.amount_includes_vat,
        document_amount = EXCLUDED.document_amount,
        amount_vat_document = EXCLUDED.amount_vat_document,
        consider_vat = EXCLUDED.consider_vat,
        deal_key = EXCLUDED.deal_key,
        deal_type = EXCLUDED.deal_type
    ;
    """
    return sql.replace("'", "")


async def main_doc_tax_appendix2_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS, True, False, True)
    maket = await create_model_async(21)
    sql = await sql_insert(maket)
    sql = sql.replace("$5,", "to_timestamp($5, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "doc_date")
    await async_sql_create_index(TABLE_NAME, "doc_number")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_tax_appendix2_async())
