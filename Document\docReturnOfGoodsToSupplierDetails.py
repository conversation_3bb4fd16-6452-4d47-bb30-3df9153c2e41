import asyncio
import os

from async_Postgres import async_sql_create_index, async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)
TABLE_NAME = "t_one_doc_return_of_goods_to_supplier_details"
DOCUMENT = "Document_ВозвратТоваровПоставщику_Товары"
SELECT_COLUMNS = (
    "LineNumber, ДокументПоступления, ДокументПоступления_Type, Количество, Коэффициент, Сумма, СуммаНДС,"
    " Цена, Ref_Key, ЕдиницаИзмерения_Key, Номенклатура_Key, СерияНоменклатуры_Key, Склад_Key, "
    "ХарактеристикаНоменклатуры_Key"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id bigserial not null,
        line_number numeric NOT NULL DEFAULT 0,  -- LineNumber
        quantity numeric(10, 4) NOT NULL DEFAULT 0,  -- Количество
        coefficient numeric(10, 4) NOT NULL DEFAULT 0,  -- Коэффициент
        price numeric(10, 4) NOT NULL DEFAULT 0,  -- Цена
        amount numeric(15, 4) NOT NULL DEFAULT 0,  -- Сумма
        amount_vat numeric(10, 4) NOT NULL DEFAULT 0,  -- СуммаНДС
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        unit_of_key varchar(50) NOT NULL,  -- ЕдиницаИзмерения_Key
        nomenclature_key varchar(50) NOT NULL,  -- Номенклатура_Key
        nomenclature_series_key varchar(50) NOT NULL,  -- СерияНоменклатуры_Key
        warehouse_key varchar(50) NOT NULL,  -- Склад_Key
        item_characteristic_key varchar(50) NOT NULL,  -- ХарактеристикаНоменклатуры_Key
        receipt_document_type varchar(50) NOT NULL,  -- ДокументПоступления_Type
        receipt_document varchar(50) NOT NULL,  -- ДокументПоступления
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key, line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.price IS 'Цена';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'Сумма';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'СуммаНДС';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.unit_of_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.warehouse_key IS 'Склад_Key';
    COMMENT ON COLUMN {TABLE_NAME}.item_characteristic_key IS 'ХарактеристикаНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_document_type IS 'ДокументПоступления_Type';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_document IS 'ДокументПоступления';

    """


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        line_number,
        receipt_document,
        receipt_document_type,
        quantity,
        coefficient,
        amount,
        amount_vat,
        price,
        ref_key,
        unit_of_key,
        nomenclature_key,
        nomenclature_series_key,
        warehouse_key,
        item_characteristic_key
    )
    VALUES {maket}
    ON CONFLICT (ref_key, line_number)
    DO UPDATE SET
        line_number = EXCLUDED.line_number,
        receipt_document = EXCLUDED.receipt_document,
        receipt_document_type = EXCLUDED.receipt_document_type,
        quantity = EXCLUDED.quantity,
        coefficient = EXCLUDED.coefficient,
        amount = EXCLUDED.amount,
        amount_vat = EXCLUDED.amount_vat,
        price = EXCLUDED.price,
        ref_key = EXCLUDED.ref_key,
        unit_of_key = EXCLUDED.unit_of_key,
        nomenclature_key = EXCLUDED.nomenclature_key,
        nomenclature_series_key = EXCLUDED.nomenclature_series_key,
        warehouse_key = EXCLUDED.warehouse_key,
        item_characteristic_key = EXCLUDED.item_characteristic_key
    """
    return sql.replace("'", "")


async def main_doc_return_of_goods_to_supplier_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(14)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "ref_key")
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_sql_create_index(TABLE_NAME, "warehouse_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_return_of_goods_to_supplier_details_async())
