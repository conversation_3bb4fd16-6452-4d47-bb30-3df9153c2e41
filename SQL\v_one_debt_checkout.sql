-- период с 01.01.2024 года

DROP VIEW IF EXISTS v_one_debt_checkout CASCADE;

CREATE OR REPLACE VIEW v_one_debt_checkout AS
SELECT
	segment.manager,
	segment.segment_folder,
	segment.segment,
	COALESCE(segment.customer, COALESCE(main.give_out,ind.description)) customer,
	main.doc_date::date doc_date,
	main.doc_number,
	main.amount debt,
	0 credit,
	clause.description AS operation_type,
	cur.description currency,
	main.is_management
FROM t_one_doc_cash_warrant_expense AS main  -- Document_РасходныйКассовыйОрдер
	INNER JOIN
	    (SELECT DISTINCT
            ref_key,
            cash_flow_item
	    FROM t_one_doc_cash_warrant_expense_details
	    ) AS det
		ON main.ref_key = det.ref_key
	LEFT JOIN t_one_cat_cash_flow_item AS clause
		ON clause.ref_key = det.cash_flow_item
	LEFT JOIN t_one_cat_currencies AS cur
		ON cur.ref_key = main.currency_key
	LEFT JOIN
	(
		SELECT DISTINCT
			manager,
	        segment_folder,
			segment,
			customer,
			customer_key
		FROM v_one_manager_counterparty_contracts_segments
	) AS segment
		ON segment.customer_key = main.contractor_key
	LEFT JOIN t_one_cat_individuals AS ind
		ON ind.ref_key = main.contractor_key
WHERE main.posted
--	AND main.doc_date::date >= '01.01.2024'::date
--    AND main.doc_date::date <= current_date
UNION ALL
SELECT
	segment.manager,
	segment.segment_folder,
	segment.segment,
	COALESCE(segment.customer,ind.description) customer,
	main.doc_date::date,
	main.doc_number,
	0 debt,
	main.amount credit,
	clause.description AS operation_type,
	cur.description currency,
	is_management
FROM t_one_doc_cash_warrant_receipt AS main  -- Document_ПриходныйКассовыйОрдер
	INNER JOIN
	 (
	 SELECT DISTINCT
         ref_key,
         cash_flow_item
	 FROM t_one_doc_cash_warrant_receipt_details
	 ) AS det
		ON main.ref_key = det.ref_key
	LEFT JOIN t_one_cat_cash_flow_item AS clause
		ON clause.ref_key = det.cash_flow_item
	LEFT JOIN t_one_cat_currencies AS cur
		ON cur.ref_key = main.currency_key
	LEFT JOIN
	(
		SELECT DISTINCT
			manager,
	        segment_folder,
			segment,
			customer,
			customer_key
		FROM v_one_manager_counterparty_contracts_segments
	) AS segment
	ON segment.customer_key = main.contractor_key
	LEFT JOIN t_one_cat_individuals AS ind
		ON ind.ref_key = main.contractor_key
WHERE main.posted
--	AND main.doc_date::date >= '01.01.2024'::date
--	AND main.doc_date::date <= current_date
ORDER BY
	manager,
	segment_folder,
	segment,
	customer,
	doc_date
;

COMMENT ON VIEW v_one_debt_checkout IS 'Касса: выдача и прием денег. Период с 01.01.2024 года';