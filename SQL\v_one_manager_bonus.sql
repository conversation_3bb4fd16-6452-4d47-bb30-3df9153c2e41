-- бонусы менеджерам
DROP VIEW IF EXISTS v_one_manager_bonus CASCADE;
CREATE OR REPLACE VIEW v_one_manager_bonus AS
WITH mt as (
    SELECT
        manager::TEXT as менеджер,
        customer::TEXT as клиент,
        segment::TEXT as сегмент,
        recorder_type::TEXT,
        CASE
            WHEN recorder_type IN ('ПлатежноеПоручениеВходящее',
                                   'ПлатежноеПоручениеИсходящее',
                                   'ПриходныйКассовыйОрдер',
                                   'РасходныйКассовыйОрдер') AND contract_type = 'СПокупателем' THEN
                'оплата'
            WHEN recorder_type IN ('ПлатежноеПоручениеВходящее',
                                   'ПлатежноеПоручениеИсходящее',
                                   'ПриходныйКассовыйОрдер',
                                   'РасходныйКассовыйОрдер') AND contract_type = 'СПоставщиком' THEN
                'маркетинг'
            WHEN recorder_type IN ('ПлатежноеПоручениеВходящее',
                                   'ПлатежноеПоручениеИсходящее',
                                   'ПриходныйКассовыйОрдер',
                                   'РасходныйКассовыйОрдер') AND contract_type NOT IN ('СПоставщиком', 'СПокупателем') THEN
                'прочаяОплата'
            WHEN recorder_type IN ('ПоступлениеТоваровУслуг','ВозвратТоваровПоставщику') THEN
                'поступление'
            WHEN recorder_type IN ('КорректировкаДолга','КорректировкаЗаписейРегистров') THEN
                'взаиморасчеты'
            WHEN recorder_type IN ('РеализацияТоваровУслуг','ВозвратТоваровОтПокупателя') THEN
                'продажа/возврат'
        END "типДок",
        doc_date::date датаДок,
        doc_number::TEXT номерДок,
        TO_CHAR(doc_date, 'YYYY-MM')::TEXT AS месяц,
        CASE
            WHEN (recorder_type = 'КорректировкаДолга'
                        AND contract_type = 'СПоставщиком')
                 OR recorder_type <> 'КорректировкаДолга' THEN
                -COALESCE(SUM(doc_sum), 0)::numeric
            ELSE
                0
        END AS сумма,
        manager_key,  -- нужен в отчете Excel
        recorder
    FROM mt_one_balance_details
--    WHERE doc_date >= (date_trunc('month', current_date) - INTERVAL '6 months')::date
    GROUP BY
        manager,
        segment,
        customer,
        recorder_type,
        contract_type,
        doc_date::date,
        doc_number,
        manager_key,
        recorder,
        TO_CHAR(doc_date, 'YYYY-MM')
),
sale_costs as
        (
            SELECT
                ref_key,
                -- себестоимость считаем только по продажам, т.к. возвраты - это наша потеря, уже никому не продадим
                -- исключения (склад ПЕРЕОЦІНКА), когда себестоимость учитывается при возврате переоценке
                -- или переброски с контрагента на контрагента
                COALESCE(sum(costs),0) costs
            FROM v_one_debt_sale_salereturn
            GROUP BY
                ref_key
        )
SELECT
    mt.менеджер,
    mt.сегмент,
    mt.клиент,
    mt.месяц,
    mt.recorder_type,
    mt.типДок,
    mt.датаДок,
    mt.номерДок,
    mt.сумма,
    -COALESCE(sale_costs.costs,0) costs,
    manager_key  -- нужен в отчете Excel при начислении бонусов менеджерам
FROM mt
    LEFT JOIN sale_costs
        ON mt.recorder = sale_costs.ref_key
;

COMMENT ON VIEW public.v_one_manager_bonus IS 'бонусы менеджерам';
GRANT SELECT ON TABLE public.v_one_manager_bonus TO user_prestige;
