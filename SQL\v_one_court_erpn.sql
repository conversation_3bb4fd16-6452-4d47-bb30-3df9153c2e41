--РН/ВН + НН/РК + ЕРПН из кабинета налоговой + суд
DROP VIEW IF EXISTS public.v_one_court_erpn CASCADE;
CREATE OR REPLACE VIEW v_one_court_erpn AS
WITH
sale_not_consolided AS (
SELECT
	regexp_replace(TRIM(BOTH FROM tax.doc_number), '.*?(\d+)$'::text, '\1'::text)::integer AS tax_number_short,
    tax.doc_date::date tax_date,
    tax.doc_number tax_number,
    tax.document_amount AS tax_amount,
    sale.doc_date::date AS sale_date,
    sale.doc_number AS sale_number,
    sale.document_amount AS sale_amount,
    sale.posted,
    sale.is_accounting,
    sale.document_amount,
    tax.is_consolidated_invoice,
    tax.account_key,
    sale.organization_key
FROM (
    SELECT
        *,
    CASE
        WHEN document_base_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
            AND COALESCE(document_base_key, '********-0000-0000-0000-************')
                <> '********-0000-0000-0000-************' THEN
                    document_base_key
        WHEN deal_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
            AND COALESCE(deal_key, '********-0000-0000-0000-************')
                <> '********-0000-0000-0000-************' THEN
                    deal_key
        WHEN document_base_type = 'StandardODATA.StandardODATA.Document_ЗаказПокупателя'
            AND COALESCE(document_base_key, '********-0000-0000-0000-************')
                <> '********-0000-0000-0000-************' THEN
                    (SELECT ref_key
                    FROM t_one_doc_sale_of_goods_services
                    WHERE deal = document_base_key
                    ORDER BY doc_date
                    Limit 1)
        WHEN deal_type = 'StandardODATA.Document_ЗаказПокупателя'
            AND COALESCE(deal_key, '********-0000-0000-0000-************')
                <> '********-0000-0000-0000-************' THEN
                    (SELECT ref_key
                    FROM t_one_doc_sale_of_goods_services
                    WHERE deal = deal_key
                    ORDER BY doc_date
                    Limit 1)
    END base_key
    FROM t_one_doc_tax_sale
    ) AS tax
    LEFT JOIN public.t_one_doc_sale_of_goods_services AS sale
        ON tax.base_key = sale.ref_key
            AND tax.account_key = sale.account_key
            AND tax.organization_key = sale.organization_key
WHERE tax.posted
    AND NOT tax.is_consolidated_invoice
	AND tax.organization_key = '56d4e33a-1f59-11e7-80cc-001dd8b79079'  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
),
sale_consolided AS (
SELECT
	regexp_replace(TRIM(BOTH FROM tax.doc_number), '.*?(\d+)$'::text, '\1'::text)::integer AS tax_number_short,
    tax.doc_date::date tax_date,
    tax.doc_number tax_number,
    tax.document_amount AS tax_amount,
    sale.doc_date::date AS sale_date,
    sale.doc_number AS sale_number,
    sale.document_amount AS sale_amount,
    sale.posted,
    sale.is_accounting,
    sum(sale.document_amount) OVER (
        PARTITION BY tax.account_key, tax.organization_key, tax.doc_number
        ORDER BY sale.doc_date, sale.doc_number
    ) AS total,
    tax.is_consolidated_invoice,
    tax.account_key,
    sale.organization_key
FROM t_one_doc_tax_sale AS tax
    LEFT JOIN public.t_one_doc_sale_of_goods_services AS sale
        ON EXTRACT('year' FROM tax.doc_date) = EXTRACT('year' FROM sale.doc_date)
            AND EXTRACT('month' FROM tax.doc_date) = EXTRACT('month' FROM sale.doc_date)
            AND tax.account_key = sale.account_key
            AND tax.organization_key = sale.organization_key
WHERE tax.posted
    AND tax.is_consolidated_invoice
	AND tax.organization_key = '56d4e33a-1f59-11e7-80cc-001dd8b79079'  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
),
appendix AS (
SELECT
	regexp_replace(TRIM(BOTH FROM tax.doc_number), '.*?(\d+)$'::text, '\1'::text)::integer AS tax_number_short,
    tax.doc_date::date tax_date,
    tax.doc_number tax_number,
    -tax.document_amount AS tax_amount,
    sale.doc_date::date AS sale_date,
    sale.doc_number AS sale_number,
    -sale.document_amount AS sale_amount,
    sale.posted,
    sale.is_accounting,
    sale.document_amount,
    FALSE is_consolidated_invoice,
    tax.account_key,
    sale.organization_key
FROM (
    SELECT
        *,
    CASE
        WHEN document_base_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
            AND COALESCE(document_base_key, '********-0000-0000-0000-************')
                <> '********-0000-0000-0000-************' THEN
                    document_base_key
        WHEN deal_type = 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
            AND COALESCE(deal_key, '********-0000-0000-0000-************')
                <> '********-0000-0000-0000-************' THEN
                    deal_key
    END base_key
    FROM t_one_doc_tax_appendix_2
    ) AS tax
    FULL JOIN public.t_one_doc_return_of_goods_from_customers AS sale
        ON tax.base_key = sale.ref_key
            AND tax.account_key = sale.account_key
            AND tax.organization_key = sale.organization_key
WHERE tax.posted
	AND tax.organization_key = '56d4e33a-1f59-11e7-80cc-001dd8b79079'  -- ТОВ "ПРЕСТИЖ ПРОДУКТ.К"
)
SELECT DISTINCT
	segment.manager менеджер,
	COALESCE(segment.customer, erpn.hnamebuy) Конртрагент,
    erpn.code AS "код",
    court.court_name AS "суд",
    erpn.pkpinn AS "ИНН_Покуп",
    erpn.crtdate AS "дата_НН_РК",
    erpn.nmr AS "ном_НН_РК",
    erpn.r4100g11 AS "сумма",
    erpn.ndssm AS "НДС",
    erpn.tin AS "ОКПО_прод",
    erpn.cptin AS "ОКПО_Покуп",
    erpn.corrnmr AS "ном_НН_ориг",
    erpn.ftype AS "0-НН;1-РК;",
    erpn.impdate AS "дата_Рег",
    erpn.docrnn AS "номер_Рег",
--    erpn.hsmcstt AS "типСтатуса",
    erpn.hsmcsttname AS "Статус",
    tax.sale_date "дата_РН_ВН",
	tax.sale_number "номер_РН_ВН",
	tax.sale_amount "сумма_РН_ВН",
	CASE
	    WHEN tax.posted THEN
            '+'
        ELSE
            ''
	END	AS "РН_ВН_проведена",
	CASE
        WHEN tax.is_accounting THEN
            '+'
        ELSE
            ''
    END	AS "РН_ВН_бухучет",
	CASE
        WHEN tax.is_consolidated_invoice THEN
            '+'
        ELSE
            ''
    END	AS "НН_сводная"
--    erpn.kvt2,
--    erpn.kvt3,
--    erpn.kvt4
FROM
(
	SELECT
	    *
	FROM sale_consolided
	UNION ALL
	SELECT
	    *
	FROM sale_not_consolided
	UNION ALL
	SELECT
		*
	FROM appendix
) AS tax
	LEFT JOIN (
		SELECT DISTINCT
			manager,
			customer,
			edrpou,
			inn,
			customer_key
		FROM v_one_manager_counterparty_contracts_segments
		) AS segment
		ON segment.customer_key = tax.account_key
	RIGHT JOIN t_tax_cabinet_erpn_api AS erpn
		ON erpn.nmr::numeric = tax.tax_number_short::numeric
			AND erpn.crtdate = tax.tax_date
			AND CASE
					WHEN segment.inn = '' OR segment.inn IS NULL THEN
						1********000
					ELSE
						segment.inn::int8
				END::bigint = erpn.pkpinn::bigint
	LEFT JOIN t_court court ON court.doc_date::date = erpn.crtdate::date
		AND court.doc_number::bigint = erpn.nmr::bigint
;

COMMENT ON VIEW public.v_one_court_erpn IS 'РН/ВН + НН/РК + ЕРПН из кабинета налоговой + суд';
GRANT SELECT ON TABLE public.v_one_court_erpn TO user_prestige;
