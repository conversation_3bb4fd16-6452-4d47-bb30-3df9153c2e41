-- Данные по клиентам: продажам, возвратам, списаниям, банковским операциям
DROP VIEW IF EXISTS v_one_customers_profit2 CASCADE;

CREATE OR REPLACE VIEW v_one_customers_profit2 AS
WITH
-- Документы по Document_РеализацияТоваровУслуг
doc_sale AS (
	SELECT DISTINCT
		doc.ref_key recorder,
		doc.tobox * doc.tobox  AS sale_pcs,
		doc.amount AS sale_amount,
		doc.amount_maliyet_uah AS sale_costs
	FROM t_one_sale AS doc
	WHERE doc.issale = 1  -- 'StandardODATA.Document_РеализацияТоваровУслуг'
),
-- Документы по Document_ВозвратТоваровОтПокупателя
doc_return AS (
	SELECT DISTINCT
		doc.ref_key recorder,
		doc.tobox * doc.tobox  AS rtrn_pcs,
		doc.amount AS rtrn_amount,
		doc.amount_maliyet_uah AS rtrn_costs
	FROM t_one_sale AS doc
	WHERE doc.issale = -1 -- 'StandardODATA.Document_ВозвратТоваровОтПокупателя'
),
-- Документы по Document_КорректировкаДолга
correction_receipt AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS corr_receipt
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_КорректировкаДолга'
		AND recordtype = 'Receipt'
),
-- Документы по Document_КорректировкаДолга
correction_expense AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS corr_expense
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_КорректировкаДолга'
		AND recordtype = 'Expense'
),
-- Документы по Document_ПлатежноеПоручениеВходящее
bank_receipt AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS bank_in
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_ПлатежноеПоручениеВходящее'
),
-- Документы по Document_ПлатежноеПоручениеИсходящее
bank_expense AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS bank_out
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_ПлатежноеПоручениеИсходящее'
),
-- Документы по Document_ПриходныйКассовыйОрдер
cash_receipt AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS cash_in
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_ПриходныйКассовыйОрдер'
),
-- Документы по Document_РасходныйКассовыйОрдер
cash_expense AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS cash_out
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_РасходныйКассовыйОрдер'
),
-- Документы по Document_КорректировкаЗаписейРегистров
registry_correction AS (
	SELECT DISTINCT
		recorder,
		reg.amount AS reg_correction
	FROM t_one_doc_acc_reg_reciprocal_settlements_details reg
	WHERE reg.recorder_type = 'StandardODATA.Document_КорректировкаЗаписейРегистров'
)
SELECT
	mng.manager,
	mng.segment_folder,
	mng.segment,
	mng.customer,
	EXTRACT('YEAR' FROM reg.dtperiod) AS years,
	to_char(reg.dtperiod, 'TMMonth') AS months,
	mng.currency_name,
	ROUND(COALESCE(sum(doc_sale.sale_pcs),0),2) AS sale_pcs,
	ROUND(COALESCE(sum(doc_sale.sale_amount),0),2) AS sale_amount,
	ROUND(COALESCE(sum(doc_sale.sale_costs),0),2) AS sale_costs,
	ROUND(COALESCE(sum(doc_return.rtrn_pcs),0),2) AS rtrn_pcs,
	ROUND(COALESCE(sum(doc_return.rtrn_amount),0),2) AS rtrn_amount,
	ROUND(COALESCE(sum(doc_return.rtrn_costs),0),2) AS rtrn_costs,
	ROUND(COALESCE(sum(correction_receipt.corr_receipt),0),2) AS corr_receipt,
	ROUND(COALESCE(sum(correction_expense.corr_expense),0),2) AS corr_expense,
	ROUND(COALESCE(sum(bank_receipt.bank_in),0),2) AS bank_in,
	ROUND(COALESCE(sum(bank_expense.bank_out),0),2) AS bank_out,
	ROUND(COALESCE(sum(cash_receipt.cash_in),0),2) AS cash_in,
	ROUND(COALESCE(sum(cash_expense.cash_out),0),2) AS cash_out,
	ROUND(COALESCE(sum(registry_correction.reg_correction),0),2) AS reg_correction
FROM t_one_doc_acc_reg_reciprocal_settlements_details AS reg
	LEFT JOIN
        (
            SELECT DISTINCT
                manager,
                segment,
                segment_folder,
                customer,
                customer_key,
                currency_key,
                currency_name
            FROM v_one_manager_counterparty_contracts_segments
        ) AS mng
		ON reg.counterparty_key = mng.customer_key
			ANd reg.currency_key = mng.currency_key
	LEFT JOIN doc_sale
		ON reg.recorder = doc_sale.recorder
	LEFT JOIN doc_return
		ON reg.recorder = doc_return.recorder
	LEFT JOIN correction_receipt
		ON reg.recorder = correction_receipt.recorder
	LEFT JOIN correction_expense
		ON reg.recorder = correction_expense.recorder
	LEFT JOIN bank_receipt
		ON reg.recorder = bank_receipt.recorder
	LEFT JOIN bank_expense
		ON reg.recorder = bank_expense.recorder
	LEFT JOIN cash_receipt
		ON reg.recorder = cash_receipt.recorder
	LEFT JOIN cash_expense
		ON reg.recorder = cash_expense.recorder
	LEFT JOIN registry_correction
		ON reg.recorder = registry_correction.recorder
WHERE reg.dtperiod >= '01.01.2024'::date
GROUP BY
	mng.manager,
	mng.segment_folder,
	mng.segment,
	mng.customer,
	EXTRACT('YEAR' FROM reg.dtperiod),
	to_char(reg.dtperiod, 'TMMonth'),
	EXTRACT('MONTH' FROM reg.dtperiod),
	mng.currency_name
ORDER BY
	mng.manager,
	mng.segment_folder,
	mng.segment,
	mng.customer,
	mng.currency_name,
  EXTRACT('YEAR' FROM reg.dtperiod),
	EXTRACT('MONTH' FROM reg.dtperiod)
 ;