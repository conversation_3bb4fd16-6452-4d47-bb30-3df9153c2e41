--DROP MATERIALIZED VIEW IF EXISTS v_one_balance_erpn CASCADE;
--CREATE MATERIALIZED VIEW IF NOT EXISTS v_one_balance_erpn AS
DROP VIEW IF EXISTS v_one_balance_erpn CASCADE;
CREATE OR REPLACE VIEW v_one_balance_erpn AS
WITH balance AS (
    SELECT
        COALESCE(trim(seg.segment), '') AS segment,
        COALESCE(trim(seg.manager), '') AS manager,
        COALESCE(trim(seg.customer), '') AS customer,
        COALESCE(trim(org.organization), '') AS organization,
        COALESCE(trim(seg.currency_name), '') AS currency_name,
        dtperiod::date AS doc_date,
        -- Расчет суммы документа (доход + расход)
        CASE WHEN recordtype = 'Receipt' THEN sum(amount) ELSE -sum(amount) END AS doc_sum,
        -- Определение крайней даты оплаты
        CASE
            WHEN recorder_type = 'StandardODATA.Document_РеализацияТоваровУслуг'
            THEN (dtperiod::date + INTERVAL '1 DAYS' * seg.contract_days)::date
            ELSE dtperiod::date
        END AS last_date,
        seg.edrpou,
        seg.manager_key
    FROM t_one_doc_acc_reg_reciprocal_settlements_details AS req
    LEFT JOIN v_one_manager_counterparty_contracts_segments AS seg
        ON req.contract_key = seg.contract_key
    LEFT JOIN v_one_organization_and_type AS org
        ON org.ref_key = req.organization_key
    GROUP BY dtperiod::date, recordtype, recorder_type,
             seg.segment, seg.manager, seg.customer, org.organization, seg.currency_name,
             seg.contract_days, seg.edrpou, seg.manager_key
),
aggregated AS (
    SELECT
        segment, manager, customer, organization, currency_name,
        doc_sum,
        SUM(doc_sum) OVER (PARTITION BY segment, manager, customer, organization, currency_name) AS customer_sum,
        COALESCE(
            SUM(doc_sum) FILTER (WHERE doc_sum < 0) OVER (PARTITION BY segment, manager, customer, organization, currency_name),
            0
        ) +
        COALESCE(
            SUM(doc_sum) FILTER (WHERE doc_sum > 0)
                OVER (PARTITION BY segment, manager, customer, organization, currency_name ORDER BY last_date, doc_date),
            0
        ) AS customer_sum_pdz,
        COALESCE(
            SUM(doc_sum)
                OVER (PARTITION BY segment, manager, customer, organization, currency_name ORDER BY last_date, doc_date),
            0
        ) AS customer_pdz,
        doc_date, last_date, edrpou, manager_key
    FROM balance
),
final_calc AS (
    SELECT *,
        -- Определение конечной даты
        CASE
            WHEN last_date = MAX(last_date) OVER (PARTITION BY segment, manager, customer, organization, currency_name)
                AND ROW_NUMBER() OVER (PARTITION BY segment, manager, customer, organization, currency_name ORDER BY last_date DESC) = 1
                AND last_date <= CURRENT_DATE
            THEN CURRENT_DATE
            ELSE last_date
        END AS finish_date
    FROM aggregated
)
SELECT
    segment, manager, customer, organization, currency_name, doc_sum,
    customer_pdz, customer_sum_pdz, doc_date, last_date,
    -- Расчет leave_days
    CASE
        WHEN last_date >= CURRENT_DATE THEN CURRENT_DATE - finish_date
        ELSE SUM(between_days) OVER (PARTITION BY segment, manager, customer, organization, currency_name ORDER BY last_date, doc_date)
    END AS leave_days,
    edrpou,
    manager_key
FROM (
    SELECT
        segment, manager, customer, organization, currency_name, doc_sum,
        customer_pdz, doc_date, last_date, finish_date, customer_sum_pdz,
        -- Оптимизированный расчет between_days
        CASE
            WHEN last_date > CURRENT_DATE OR customer_sum_pdz <= 10 THEN 0
            WHEN finish_date = MAX(finish_date) OVER (PARTITION BY segment, manager, customer, organization, currency_name)
            THEN finish_date - COALESCE(LAG(finish_date) OVER (PARTITION BY segment, manager, customer, organization, currency_name), last_date)
            WHEN COALESCE(LAG(customer_sum_pdz) OVER (PARTITION BY segment, manager, customer, organization, currency_name), 0) > 0
            THEN finish_date - COALESCE(LAG(finish_date) OVER (PARTITION BY segment, manager, customer, organization, currency_name ORDER BY last_date, doc_date), last_date)
        END AS between_days,
        edrpou,
        manager_key
    FROM final_calc
) AS tl
ORDER BY segment, manager, customer, organization, currency_name, last_date, doc_date;

--COMMENT ON MATERIALIZED VIEW v_one_balance_erpn IS 'Дебиторская задолженности с учетом блокированныхНН/РК';
COMMENT ON VIEW v_one_balance_erpn IS 'Дебиторская задолженности с учетом блокированныхНН/РК';
ALTER TABLE v_one_balance_erpn OWNER TO postgres;
GRANT ALL ON TABLE v_one_balance_erpn TO postgres;
GRANT SELECT ON TABLE v_one_balance_erpn TO user_prestige;
