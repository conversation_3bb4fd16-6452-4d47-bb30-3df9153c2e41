DROP VIEW IF EXISTS v_service_tc_maliyet;
CREATE OR REPLACE VIEW v_service_tc_maliyet AS
SELECT
    goods.id,
    serv.client,
    serv.serv_date,
    serv.tarih,
    serv.doc_number,
    goods.sku,
    goods.unit,
    COALESCE(goods.quantity, 0::numeric) AS quantity_orig,
    (COALESCE(goods.quantity, 0::numeric)
        * goods.coef_doc
            / ( SELECT units.coefficient
                   FROM ( 
                        SELECT t_one_cat_units.nomenclature_key,
                            max(t_one_cat_units.coefficient) AS coefficient
                        FROM t_one_cat_units
                        WHERE NOT deletion_mark
                        GROUP BY t_one_cat_units.nomenclature_key
                        ) units
                  WHERE units.nomenclature_key::text = goods.nomenclature_key::text))::numeric(10,3)
    AS quantity,
    (( SELECT units.coefficient
           FROM ( 
                SELECT t_one_cat_units.nomenclature_key,
                    max(t_one_cat_units.coefficient) AS coefficient
                FROM t_one_cat_units
                WHERE NOT deletion_mark
                GROUP BY t_one_cat_units.nomenclature_key
                ) units
          WHERE units.nomenclature_key::text = goods.nomenclature_key::text)
      / goods.coef_doc
      * COALESCE(goods.price_tr, 0::numeric)
    )::numeric(10,3)
    AS price_tr,  -- price for box
    COALESCE(goods.amount_tr, 0::numeric) AS amount_tr,
    COALESCE(goods.gider_koef, 0::numeric)::numeric(10,4) AS gider_koef,
    COALESCE(goods.maliyet, 0::numeric)::numeric(10,4) AS maliyet_orig,
    (( SELECT units.coefficient
           FROM ( 
                SELECT t_one_cat_units.nomenclature_key,
                    max(t_one_cat_units.coefficient) AS coefficient
                FROM t_one_cat_units
                WHERE NOT deletion_mark
                GROUP BY t_one_cat_units.nomenclature_key
                ) units
          WHERE units.nomenclature_key::text = goods.nomenclature_key::text)
      / goods.coef_doc
      * COALESCE(goods.maliyet, 0::numeric))::numeric(10,3)
    AS maliyet,
    (COALESCE(goods.maliyet, 0::numeric) * COALESCE(goods.quantity, 0::numeric))::numeric(10,4) AS maliyet_tutar,
    ( SELECT units.coefficient
           FROM ( 
                SELECT t_one_cat_units.nomenclature_key,
                    max(t_one_cat_units.coefficient) AS coefficient
                FROM t_one_cat_units
                WHERE NOT deletion_mark
                GROUP BY t_one_cat_units.nomenclature_key
                ) units
          WHERE units.nomenclature_key::text = goods.nomenclature_key::text) AS inbox,
    (( SELECT units.coefficient
           FROM ( SELECT t_one_cat_units.nomenclature_key,
                    max(t_one_cat_units.coefficient) AS coefficient
                   FROM t_one_cat_units
                   WHERE NOT deletion_mark
                  GROUP BY t_one_cat_units.nomenclature_key) units
          WHERE units.nomenclature_key::text = goods.nomenclature_key::text)) = goods.coef_cart AS f,
    goods.coef_cart,
    goods.coef_doc,
    goods.coef_cart - goods.coef_doc AS diff,
    serv.cur_type AS doviz,
    goods.batch_document,
    goods.nomenclature_key,
    goods.nomenclature_series_key,
    serv.account_key,
    goods.unit_of_key,
    serv.currency_key
FROM v_service_tc serv
   LEFT JOIN v_service_tc_goods goods
        ON serv.batch_document::text = goods.batch_document::text
ORDER BY serv.serv_date DESC;

GRANT SELECT ON TABLE v_service_tc_maliyet TO user_prestige;


