import asyncio
import os
import sys

sys.path.append(os.path.dirname(__file__))

from async_Postgres import async_save_pg, create_model_async, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = 't_one_cat_contracts_counterparties'
DOCUMENT = "Catalog_ДоговорыКонтрагентов"

SELECT_COLUMNS = (
    "_НКС_ДнівВідтермінуванняОплати,Code,DataVersion,DeletionMark,Description,IsFolder,Predefined,"
    "ВедениеВзаиморасчетов,ВестиПоДокументамРасчетовСКонтрагентом,"
    "ВестиПоДокументамРасчетовСКонтрагентомРегл,ВидДоговора,ВидУсловийДоговора,Внешнеэкономический,"
    "ДопустимаяСуммаЗадолженности,ДопустимоеЧислоДнейЗадолженности,Комментарий,"
    "КонтролироватьСуммуЗадолженности,КонтролироватьЧислоДнейЗадолженности,Номер,"
    "ПроцентКомиссионногоВознаграждения,ПроцентПредоплаты,Owner_Key,Parent_Key,Ref_Key,"
    "ВалютаВзаиморасчетов_Key,ВидДоговораПоГК_Key,Организация_Key,"
    "ОсновнаяСтатьяДвиженияДенежныхСредств_Key,Дата,СрокДействия"
)

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        code varchar(15) NULL DEFAULT 0,  -- Code
        dataversion varchar(50) NOT NULL,  -- DataVersion
        deletion_mark bool NULL DEFAULT false,  -- DeletionMark
        description varchar(250) NULL,  -- Description
        isfolder bool NULL DEFAULT false,  -- IsFolder
        predefined bool NULL DEFAULT false,  -- Predefined
        maintenance_of_settlements varchar(50) NULL,  -- ВедениеВзаиморасчетов
        is_with_counterparty bool NULL DEFAULT false,  -- ВестиПоДокументамРасчетовСКонтрагентом
        is_with_counterparties_reg bool NULL DEFAULT false,  -- ВестиПоДокументамРасчетовСКонтрагентомРегл
        contract_type varchar NULL,  -- ВидДоговора
        type_of_agreement_terms varchar NULL,  -- ВидУсловийДоговора
        foreign_economic bool NULL DEFAULT false,  -- Внешнеэкономический
        limit_days_credit_ua numeric NULL DEFAULT 0,  -- _НКС_ДнівВідтермінуванняОплати
        limit_amount_credit numeric NULL DEFAULT 0,  -- ДопустимаяСуммаЗадолженности
        limit_days_credit numeric NULL DEFAULT 0,  -- ДопустимоеЧислоДнейЗадолженности
        a_comment varchar NULL,  -- Комментарий
        is_control_amount bool NULL DEFAULT false,  -- КонтролироватьСуммуЗадолженности
        is_control_days bool NULL DEFAULT false,  -- КонтролироватьЧислоДнейЗадолженности
        room varchar NULL,  -- Номер
        start_date date NULL,  -- дата контракта,
        end_date date NULL,  -- срок действия
        commission_percentage numeric(10, 4) NULL DEFAULT 0,  -- ПроцентКомиссионногоВознаграждения
        prepayment_percentage numeric(10, 4) NULL DEFAULT 0,  -- ПроцентПредоплаты
        owner_key varchar(50) NULL,  -- Owner_Key
        parent_key varchar(50) NULL,  -- Parent_Key
        ref_key varchar(50) NOT NULL,  -- Ref_Key
        settlement_currency_key varchar(50) NULL,  -- ВалютаВзаиморасчетов_Key
        contract_type_key varchar(50) NULL,  -- ВидДоговораПоГК_Key
        organization_key varchar(50) NULL,  -- Организация_Key
        main_item_of_cash_flow_key varchar(50) NULL,  -- ОсновнаяСтатьяДвиженияДенежныхСредств_Key
        CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (ref_key)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';    
    COMMENT ON COLUMN {TABLE_NAME}.limit_days_credit_ua IS '_НКС_ДнівВідтермінуванняОплати';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'Code';
    COMMENT ON COLUMN {TABLE_NAME}.dataversion IS 'DataVersion';
    COMMENT ON COLUMN {TABLE_NAME}.deletion_mark IS 'DeletionMark';
    COMMENT ON COLUMN {TABLE_NAME}.description IS 'Description';
    COMMENT ON COLUMN {TABLE_NAME}.isfolder IS 'IsFolder';
    COMMENT ON COLUMN {TABLE_NAME}.start_date IS 'ДатаКонтракта';
    COMMENT ON COLUMN {TABLE_NAME}.end_date IS 'СрокДействия';
    COMMENT ON COLUMN {TABLE_NAME}.predefined IS 'Predefined';
    COMMENT ON COLUMN {TABLE_NAME}.maintenance_of_settlements IS 'ВедениеВзаиморасчетов';
    COMMENT ON COLUMN {TABLE_NAME}.is_with_counterparty IS 'ВестиПоДокументамРасчетовСКонтрагентом';
    COMMENT ON COLUMN {TABLE_NAME}.is_with_counterparties_reg IS 'ВестиПоДокументамРасчетовСКонтрагентомРегл';
    COMMENT ON COLUMN {TABLE_NAME}.contract_type IS 'ВидДоговора';
    COMMENT ON COLUMN {TABLE_NAME}.type_of_agreement_terms IS 'ВидУсловийДоговора';
    COMMENT ON COLUMN {TABLE_NAME}.foreign_economic IS 'Внешнеэкономический';
    COMMENT ON COLUMN {TABLE_NAME}.limit_amount_credit IS 'ДопустимаяСуммаЗадолженности';
    COMMENT ON COLUMN {TABLE_NAME}.limit_days_credit IS 'ДопустимоеЧислоДнейЗадолженности';
    COMMENT ON COLUMN {TABLE_NAME}.a_comment IS 'Комментарий';
    COMMENT ON COLUMN {TABLE_NAME}.is_control_amount IS 'КонтролироватьСуммуЗадолженности';
    COMMENT ON COLUMN {TABLE_NAME}.is_control_days IS 'КонтролироватьЧислоДнейЗадолженности';
    COMMENT ON COLUMN {TABLE_NAME}.room IS 'Номер';
    COMMENT ON COLUMN {TABLE_NAME}.commission_percentage IS 'ПроцентКомиссионногоВознаграждения';
    COMMENT ON COLUMN {TABLE_NAME}.prepayment_percentage IS 'ПроцентПредоплаты';
    COMMENT ON COLUMN {TABLE_NAME}.owner_key IS 'Owner_Key';
    COMMENT ON COLUMN {TABLE_NAME}.parent_key IS 'Parent_Key';
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.settlement_currency_key IS 'ВалютаВзаиморасчетов_Key';
    COMMENT ON COLUMN {TABLE_NAME}.contract_type_key IS 'ВидДоговораПоГК_Key';
    COMMENT ON COLUMN {TABLE_NAME}.organization_key IS 'Организация_Key';
    COMMENT ON COLUMN {TABLE_NAME}.main_item_of_cash_flow_key IS 'ОсновнаяСтатьяДвиженияДенежныхСредств_Key';
"""


async def sql_insert(maket):
    sql = f"""
    INSERT INTO {TABLE_NAME}(
        limit_days_credit_ua,
        code,
        dataversion,
        deletion_mark,
        description,
        isfolder,
        predefined,
        maintenance_of_settlements,
        is_with_counterparty,
        is_with_counterparties_reg,
        contract_type,
        type_of_agreement_terms,
        foreign_economic,
        limit_amount_credit,
        limit_days_credit,
        a_comment,
        is_control_amount,
        is_control_days,
        room,
        commission_percentage,
        prepayment_percentage,
        owner_key,
        parent_key,
        ref_key,
        settlement_currency_key,
        contract_type_key,
        organization_key,
        main_item_of_cash_flow_key,
        start_date,
        end_date
    )
    VALUES {maket}
    ON CONFLICT (ref_key)
    DO UPDATE SET
        limit_days_credit_ua = EXCLUDED.limit_days_credit_ua,
        code = EXCLUDED.code,
        dataversion = EXCLUDED.dataversion,
        deletion_mark = EXCLUDED.deletion_mark,
        description = EXCLUDED.description,
        isfolder = EXCLUDED.isfolder,
        predefined = EXCLUDED.predefined,
        maintenance_of_settlements = EXCLUDED.maintenance_of_settlements,
        is_with_counterparty = EXCLUDED.is_with_counterparty,
        is_with_counterparties_reg = EXCLUDED.is_with_counterparties_reg,
        contract_type = EXCLUDED.contract_type,
        type_of_agreement_terms = EXCLUDED.type_of_agreement_terms,
        foreign_economic = EXCLUDED.foreign_economic,
        limit_amount_credit = EXCLUDED.limit_amount_credit,
        limit_days_credit = EXCLUDED.limit_days_credit,
        a_comment = EXCLUDED.a_comment,
        is_control_amount = EXCLUDED.is_control_amount,
        is_control_days = EXCLUDED.is_control_days,
        room = EXCLUDED.room,
        commission_percentage = EXCLUDED.commission_percentage,
        prepayment_percentage = EXCLUDED.prepayment_percentage,
        owner_key = EXCLUDED.owner_key,
        parent_key = EXCLUDED.parent_key,
        settlement_currency_key = EXCLUDED.settlement_currency_key,
        contract_type_key = EXCLUDED.contract_type_key,
        organization_key = EXCLUDED.organization_key,
        main_item_of_cash_flow_key = EXCLUDED.main_item_of_cash_flow_key,
        start_date = EXCLUDED.start_date,
        end_date = EXCLUDED.end_date
    ;
    """
    return sql.replace("'", "")


async def main_cat_contracts_counterparties_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(30)
    sql = await sql_insert(maket)
    sql = sql.replace("$29,", "to_timestamp($29, 'YYYY-MM-DDThh24:mi:ss')::timestamp,")
    sql = sql.replace("$30", "to_timestamp($30, 'YYYY-MM-DDThh24:mi:ss')::timestamp")
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    logger.info(f"FINISH")


if __name__ == '__main__':
    asyncio.run(main_cat_contracts_counterparties_async())
