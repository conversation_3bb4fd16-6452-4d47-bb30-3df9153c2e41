import os
import pandas as pd
import asyncio

from multiThread import get_json_from_url
from InvoiceToOrder1C.Customers import main_customers_async

from logger_prestige import get_logger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

DOCUMENT = "Document_ПоступлениеДопРасходов"
SELECT_COLUMNS = "Number,Date,Posted,КурсВзаиморасчетов,СуммаДокумента,Ref_Key,Контрагент_Key"


async def get_response(ref_key: str):
    url = f"http://192.168.1.254/utp_prestige/odata/standard.odata/{DOCUMENT}" \
          f"/?$format=json&$inlinecount=allpages&$select={SELECT_COLUMNS}"
    filter_ref_key = f"&$filter=(Товары/any(d: d/ДокументПартии eq cast(guid'{ref_key}','Document_ПоступлениеТоваровУслуг')))"
    url += filter_ref_key
    response = await get_json_from_url(url)
    return response


async def add_customers(df: pd.DataFrame()):
    dfsku = pd.DataFrame()
    for i, row in df.iterrows():
        customers_key = row['customer_key']
        df_customers = await main_customers_async(customers_key)
        dfsku = pd.concat([dfsku, df_customers], axis='rows', ignore_index=True)

    return dfsku


async def main_extra_expenses_async(document_key, customer_key, foreign_key=''):
    df = pd.DataFrame()
    df_customers = pd.DataFrame()
    json_data = await get_response(document_key)
    if json_data.get('value'):
        df = pd.json_normalize(json_data.get('value'))
        df = df.rename({'Контрагент_Key': 'customer_key'}, axis='columns')
        df = df.query(f"customer_key != '{customer_key}'")
        df = df.query(f"Posted == True")
        df_customers['customer_key'] = df['customer_key'].drop_duplicates().reset_index(drop=True)
        df_customers = await add_customers(df_customers)
        df = pd.merge(df, df_customers, on="customer_key")
        df['суммаUAH'] = df['СуммаДокумента'] * df['КурсВзаиморасчетов']
        df = df[['Date', 'Number', 'КурсВзаиморасчетов', 'СуммаДокумента', 'суммаUAH', 'Контрагент', 'Ref_Key',
                 'customer_key']]

    return df


if __name__ == "__main__":
    asyncio.run(
        main_extra_expenses_async("e89e9095-cd4f-11ed-816c-001dd8b740bc", "b27d0b0a-c4fb-11eb-810a-001dd8b72b55"))
