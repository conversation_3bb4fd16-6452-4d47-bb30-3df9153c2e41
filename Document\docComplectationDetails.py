import asyncio
import os

from async_Postgres import async_save_pg, create_model_async, async_sql_create_index, async_truncate_table
from logger_prestige import get_logger
from multiThread import main_thread

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
logger = get_logger(FILE_NAME)

TABLE_NAME = "t_one_doc_completion_details"
DOCUMENT = "Document_КомплектацияНоменклатуры_Комплектующие"
SELECT_COLUMNS = """Ref_Key,LineNumber,Номенклатура_Key,Количество,ЕдиницаИзмерения_Key,Коэффициент,
    ХарактеристикаНоменклатуры_Key,СерияНоменклатуры_Key,НоменклатурнаяПозиция_Key
"""

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    id bigserial NOT NULL,
    line_number numeric(6) NOT NULL DEFAULT 0,  -- LineNumber
    quantity numeric(10,4) NOT NULL DEFAULT 0,  -- Количество
    ref_key varchar(50) NULL,  -- Ref_Key
    nomenclature_key varchar(50) NULL,  -- Номенклатура_Key
    series_key varchar(50) NULL,  -- СерияНоменклатуры_Key
    unit_key varchar(50) NULL,  -- ЕдиницаИзмерения_Key
    coefficient numeric(10,4) NOT NULL DEFAULT 0,  -- Коэффициент
    characteristic_key varchar(50) NULL,  -- ХарактеристикаНоменклатуры_Key
    item_key varchar(50) NULL,  -- НоменклатурнаяПозиция_Key
    CONSTRAINT {TABLE_NAME}_pkey PRIMARY KEY (id),
    CONSTRAINT {TABLE_NAME}_unq UNIQUE (ref_key,line_number)
    );
    
    COMMENT ON TABLE {TABLE_NAME} IS '{DOCUMENT}';       
    COMMENT ON COLUMN {TABLE_NAME}.ref_key IS 'Ref_Key';
    COMMENT ON COLUMN {TABLE_NAME}.line_number IS 'LineNumber';
    COMMENT ON COLUMN {TABLE_NAME}.nomenclature_key IS 'Номенклатура_Key';
    COMMENT ON COLUMN {TABLE_NAME}.quantity IS 'Количество';
    COMMENT ON COLUMN {TABLE_NAME}.unit_key IS 'ЕдиницаИзмерения_Key';
    COMMENT ON COLUMN {TABLE_NAME}.coefficient IS 'Коэффициент';
    COMMENT ON COLUMN {TABLE_NAME}.characteristic_key IS 'ХарактеристикаНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.series_key IS 'СерияНоменклатуры_Key';
    COMMENT ON COLUMN {TABLE_NAME}.item_key IS 'НоменклатурнаяПозиция_Key';

"""


async def sql_insert(maket):
    sql = f"""
        INSERT INTO {TABLE_NAME} (
            ref_key, 
            line_number, 
            nomenclature_key, 
            quantity, 
            unit_key, 
            coefficient, 
            characteristic_key, 
            series_key, 
            item_key
        )
        VALUES {maket}
        ON CONFLICT (ref_key,line_number)
        DO UPDATE SET
            nomenclature_key = EXCLUDED.nomenclature_key,
            quantity = EXCLUDED.quantity,
            unit_key = EXCLUDED.unit_key,
            coefficient = EXCLUDED.coefficient,
            characteristic_key = EXCLUDED.characteristic_key,
            series_key = EXCLUDED.series_key,
            item_key = EXCLUDED.item_key
    """
    return sql.replace("'", "")


async def main_doc_complectation_details_async():
    logger.info(f"START")
    await async_save_pg(SQL_CREATE_TABLE)
    # await async_truncate_table(TABLE_NAME)
    query_lists = await main_thread(DOCUMENT, SELECT_COLUMNS)
    maket = await create_model_async(9)
    sql = await sql_insert(maket)
    tasks = [async_save_pg(sql, data) for data in query_lists]
    await asyncio.gather(*tasks)
    await async_sql_create_index(TABLE_NAME, "nomenclature_key")
    await async_sql_create_index(TABLE_NAME, "series_key")

    logger.info(f"FINISH")


if __name__ == "__main__":
    asyncio.run(main_doc_complectation_details_async())
