# 20250413
# Скрипт для импорта изображений в Excel с использованием VBA макроса
import os
import shutil
import datetime
from fdb import create_database
import win32com.client
from pathlib import Path
import time
import asyncio
import aiohttp
import aiosqlite
import requests
import pandas as pd
from openpyxl import load_workbook


def clear_console():
    """Очистка консоли в зависимости от операционной системы"""
    # Для Windows
    if os.name == 'nt':
        os.system('cls')
    # Для Unix/Linux/MacOS
    else:
        os.system('clear')


async def import_data_from_url(url):
    """Асинхронная функция для получения данных из API"""
    headers = {'Authorization': 'Basic cmFzaW0gMTUwMDI5NzI6MTUwMDI5NzI='}

    async with aiohttp.ClientSession() as session:
        async with session.get(url, auth=aiohttp.BasicAuth('rasim', '15021972')) as response:
            data = await response.json()
            return data


def process_excel_file(photo_folder):
    # Очистка консоли при запуске функции
    clear_console()

    # 1) Копирование файла с новым именем на основе текущей даты и времени
    current_datetime = datetime.datetime.now().strftime("%Y%m%d_%H%M")
    source_file = "PricePrestigeWithPhoto.xls"
    new_file_name = f"Price_{current_datetime}.xls"

    # Копирование файла
    shutil.copy2(source_file, new_file_name)
    print(f"Файл скопирован: {new_file_name}")

    # Полный путь к новому файлу
    full_path = os.path.abspath(new_file_name)

    # Получение списка фотографий
    photo_files = [f for f in os.listdir(photo_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]

    # Создаем объект Excel через COM
    excel = win32com.client.Dispatch("Excel.Application")
    excel.Visible = False  # Делаем Excel невидимым

    # Открываем созданный файл
    workbook = excel.Workbooks.Open(full_path)
    sheet = workbook.Sheets(1)  # Использование первого листа

    # 2) Установка размеров ячеек в колонке A
    # В Excel VBA высота строки измеряется в points, а ширина столбца в characters
    # Устанавливаем высоту для строк, где будут изображения
    for i in range(1, len(photo_files) + 1):
        sheet.Rows(i).RowHeight = 100

    # Устанавливаем ширину колонки A
    sheet.Columns("A:A").ColumnWidth = 18  # Примерно соответствует 100 пикселям

    # Устанавливаем ширину колонки B
    sheet.Columns("B:B").ColumnWidth = 0.5  # Примерно соответствует 100 пикселям

    # 3 и 4) Загрузка фото и добавление имени файла в колонку B
    for i, photo_file in enumerate(photo_files, 1):
        # Полный путь к фото
        photo_path = os.path.join(photo_folder, photo_file)

        # Номер строки
        row = i

        # Ячейка для вставки фото
        cell = sheet.Cells(row, 1)  # Колонка A

        # Вставка изображения
        picture = sheet.Shapes.AddPicture(
            Filename=photo_path,
            LinkToFile=False,
            SaveWithDocument=True,
            Left=cell.Left,
            Top=cell.Top,
            Width=100,
            Height=100
        )

        # 5) Вписываем фото в размер ячейки
        picture.Width = 100
        picture.Height = 100

        # 6) Назначаем макрос
        picture.OnAction = "ToggleImageSize"

        # 7) Устанавливаем свойство - перемещать и изменять размеры вместе с ячейками
        picture.Placement = 1  # xlMoveAndSize (1) - перемещать и изменять размеры вместе с ячейками

        # 4) Записываем имя файла без расширения в колонку C
        file_name_without_ext = os.path.splitext(photo_file)[0]
        sheet.Cells(row, 3).NumberFormat = "@"
        sheet.Cells(row, 3).Value = str(file_name_without_ext)

        # Небольшая пауза для стабильности
        time.sleep(0.1)

    # Сохраняем и закрываем файл
    workbook.Save()
    workbook.Close()
    excel.Quit()

    print(f"Обработка файла {new_file_name} завершена успешно!")


async def create_stock_table(db):
    """Создание таблицы stock в базе данных"""
    # SQLite не позволяет выполнять несколько запросов за раз, поэтому разделяем их
    await db.execute('DROP TABLE IF EXISTS stock')
    await db.execute('''
    CREATE TABLE IF NOT EXISTS stock (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        Склад_Key TEXT,
        Номенклатура_Key TEXT,
        СерияНоменклатуры_Key TEXT,
        Количество REAL
    )
    ''')
    await db.commit()
    print("Таблица stock создана успешно!")


async def create_nomenclature_table(db):
    """Создание таблицы nomenclature в базе данных"""
    # SQLite не позволяет выполнять несколько запросов за раз, поэтому разделяем их
    await db.execute('DROP TABLE IF EXISTS nomenclature')
    await db.execute('''
    CREATE TABLE IF NOT EXISTS nomenclature (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ref_key TEXT,
        code TEXT,
        description TEXT
    )
    ''')
    await db.commit()
    print("Таблица nomenclature создана успешно!")


async def get_nomenclature_data(url):
    """Получение данных о номенклатуре из API"""
    url = """http://***************/utp_prestige/odata/standard.odata/Catalog_Номенклатура?
        $format=json&$inlinecount=allpages&$filter=IsFolder eq false&$select=Ref_Key,Code,Description"""
    data = await import_data_from_url(url)
    return data['value']


async def main():
    """Основная асинхронная функция программы"""
    # Текущая дата и время для имени файла и базы данных
    current_datetime = datetime.datetime.now().strftime('%Y%m%d_%H%M')
    new_file_name = f"AccumulationRegister_ТоварыНаСкладах_RecordType_{current_datetime}.xlsx"

    # 1. Создание временной базы данных SQLite
    db_name = f"prestige_{current_datetime}.db"
    print(f"Создание временной базы данных SQLite: {db_name}")

    # Создаем соединение с базой данных (асинхронно)
    async with aiosqlite.connect(db_name) as db:
        # 2. Создание таблицы stock
        print("Создание таблицы stock...")
        await create_stock_table(db)

        # Загрузка данных из API
        select_column = "Period, RecordType,Склад_Key, Номенклатура_Key,СерияНоменклатуры_Key,Количество"
        url = (f"http://***************/utp_prestige/odata/standard.odata/"
                f"AccumulationRegister_ТоварыНаСкладах_RecordType?$format=json"
                f"&$select={select_column}")

        print("Загрузка данных из API...")
        try:
            data = await import_data_from_url(url)

            # Проверка структуры данных
            print(f"Получены данные из API: {data.keys() if isinstance(data, dict) else 'Not a dictionary'}")

            # Используем пустой DataFrame, если нет ключа 'value'
            if isinstance(data, dict) and 'value' in data:
                df_stock = pd.DataFrame(data['value'])
            else:
                print("Структура данных не содержит ключ 'value'. Создан пустой DataFrame.")
                print(f"Данные: {data}")
                # Создаем пустой DataFrame с нужными колонками
                df_stock = pd.DataFrame(columns=['Склад_Key', 'Номенклатура_Key', 'СерияНоменклатуры_Key', 'Количество', 'RecordType'])

            # Обрабатываем данные только если DataFrame не пустой
            if not df_stock.empty and 'RecordType' in df_stock.columns and 'Количество' in df_stock.columns:
                # Если 'RecordType'=='Expense' тогда меняем знак у Количество на противоположный
                df_stock['Количество'] = df_stock.apply(
                    lambda row: -row['Количество'] if row['RecordType'] == 'Expense' else row['Количество'],
                    axis=1
                )

                # Группировка данных по складу, номенклатуре и серии
                df_stock = df_stock.groupby(
                    ['Склад_Key', 'Номенклатура_Key', 'СерияНоменклатуры_Key']
                ).agg({'Количество': 'sum'}).reset_index()
            else:
                print("Пустой DataFrame или отсутствуют необходимые колонки. Пропускаем обработку.")

            # 3. Добавление данных в таблицу stock
            print("Добавление данных в таблицу stock...")

            # Проверяем, что DataFrame не пустой
            if not df_stock.empty:
                # Преобразуем DataFrame в список кортежей для вставки
                records = df_stock[['Склад_Key', 'Номенклатура_Key', 'СерияНоменклатуры_Key', 'Количество']].to_records(index=False)
                data_to_insert = [(r[0], r[1], r[2], r[3]) for r in records]

                # Вставляем данные асинхронно
                async with db.executemany(
                    "INSERT INTO stock (Склад_Key, Номенклатура_Key, СерияНоменклатуры_Key, Количество) VALUES (?, ?, ?, ?)",
                    data_to_insert
                ):
                    await db.commit()
            else:
                print("Пустой DataFrame, нет данных для вставки в базу данных.")

            # Проверка данных в таблице
            async with db.execute("SELECT COUNT(*) FROM stock") as cursor:
                count = await cursor.fetchone()
                print(f"Добавлено {count[0]} записей в таблицу stock")

            # Сохранение данных в Excel
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, lambda: df_stock.to_excel(new_file_name, index=False))
            print(f"Обработка файла {new_file_name} завершена успешно!")

        except Exception as e:
            print(f"Ошибка при обработке данных: {e}")

    # 4. Удаление базы данных после закрытия соединения
    if os.path.exists(db_name):
        os.remove(db_name)
        print(f"Временная база данных {db_name} удалена")


if __name__ == "__main__":
    # Очистка консоли при запуске
    clear_console()

    print(f"Start: {datetime.datetime.now()}")
    asyncio.run(main())
    # photo_folder = r"C:\Rasim\Python\Prestige"
    # process_excel_file(photo_folder)
    print(f"End: {datetime.datetime.now()}")
